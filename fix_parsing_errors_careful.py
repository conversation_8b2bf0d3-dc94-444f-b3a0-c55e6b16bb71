#!/usr/bin/env python3
"""
Careful and systematic approach to fixing parsing errors.
This script analyzes each error individually and applies targeted fixes.
"""

import os
import re
import subprocess
from typing import List, Tuple, Dict, Optional
import json

def run_lint() -> str:
    """Run ESLint and return the output."""
    cmd = ["yarn", "lint"]
    result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.path.dirname(os.path.abspath(__file__)))
    return result.stdout + '\n' + result.stderr

def parse_lint_output(output: str) -> List[Dict[str, any]]:
    """Parse ESLint output to extract error information."""
    errors = []
    current_file = None
    
    for line in output.split('\n'):
        # Check if this is a file path line
        if line.strip() and '/src/' in line and (line.endswith('.ts') or line.endswith('.tsx')):
            current_file = line.strip()
        # Check if this is an error line
        elif current_file and 'error' in line:
            # Extract line number, column, and error message
            match = re.search(r'(\d+):(\d+)\s+error\s+(.+)', line)
            if match:
                line_num = int(match.group(1))
                col_num = int(match.group(2))
                error_msg = match.group(3).strip()
                
                # Categorize the error
                if 'Parsing error:' in error_msg:
                    error_type = 'parsing'
                    error_detail = error_msg.split('Parsing error:')[1].strip()
                else:
                    error_type = 'other'
                    error_detail = error_msg
                
                errors.append({
                    'file': current_file,
                    'line': line_num,
                    'column': col_num,
                    'type': error_type,
                    'message': error_detail,
                    'full_message': error_msg
                })
    
    return errors

def fix_declaration_expected(filepath: str, line_num: int) -> bool:
    """Fix 'Declaration or statement expected' errors."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if 0 <= line_num - 1 < len(lines):
            line = lines[line_num - 1]
            
            # Common patterns for this error
            # 1. Extra closing braces/brackets
            if line.strip() in ['})', '});', '));', '}));']:
                # Check if it's needed by looking at context
                indent_level = len(line) - len(line.lstrip())
                prev_line = lines[line_num - 2] if line_num - 2 >= 0 else ''
                next_line = lines[line_num] if line_num < len(lines) else ''
                
                # If previous line has closing brace and next line starts new statement
                if prev_line.strip().endswith('}') and next_line.strip() and not next_line.strip().startswith('}'):
                    lines[line_num - 1] = ''  # Remove the line
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.writelines(lines)
                    return True
            
            # 2. Missing closing parenthesis for function calls
            if 'it(' in line or 'describe(' in line or 'test(' in line:
                # Check if it's missing closing
                open_count = line.count('(')
                close_count = line.count(')')
                if open_count > close_count:
                    lines[line_num - 1] = line.rstrip() + ')' * (open_count - close_count) + '\n'
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.writelines(lines)
                    return True
        
    except Exception as e:
        print(f"Error fixing {filepath}:{line_num} - {e}")
    
    return False

def fix_comma_expected(filepath: str, line_num: int) -> bool:
    """Fix '',' expected' errors."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if 0 <= line_num - 1 < len(lines):
            line = lines[line_num - 1]
            
            # Check if this is in an object or array context
            # Look for patterns like "key: value" without comma
            if ':' in line and not line.strip().endswith(',') and not line.strip().endswith('{') and not line.strip().endswith('}'):
                # Check next line - if it has another property, add comma
                if line_num < len(lines):
                    next_line = lines[line_num]
                    if ':' in next_line or '}' in next_line:
                        lines[line_num - 1] = line.rstrip() + ',\n'
                        with open(filepath, 'w', encoding='utf-8') as f:
                            f.writelines(lines)
                        return True
        
    except Exception as e:
        print(f"Error fixing {filepath}:{line_num} - {e}")
    
    return False

def fix_property_assignment_expected(filepath: str, line_num: int) -> bool:
    """Fix 'Property assignment expected' errors."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if 0 <= line_num - 1 < len(lines):
            line = lines[line_num - 1]
            
            # Check for incomplete property definitions
            if line.strip() and not ':' in line and not line.strip().startswith('//'):
                # This might be a property name without value
                # Check context
                prev_line = lines[line_num - 2] if line_num - 2 >= 0 else ''
                if '{' in prev_line or ',' in prev_line:
                    # Add a placeholder value
                    property_name = line.strip().rstrip(',')
                    lines[line_num - 1] = line.replace(property_name, f'{property_name}: undefined')
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.writelines(lines)
                    return True
        
    except Exception as e:
        print(f"Error fixing {filepath}:{line_num} - {e}")
    
    return False

def fix_expression_expected(filepath: str, line_num: int) -> bool:
    """Fix 'Expression expected' errors."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if 0 <= line_num - 1 < len(lines):
            line = lines[line_num - 1]
            
            # Check for empty parentheses, brackets, etc.
            patterns = [
                (r'\(\s*\)', '()'),
                (r'\[\s*\]', '[]'),
                (r'\{\s*\}', '{}'),
                (r',\s*\)', ')'),
                (r',\s*\]', ']'),
                (r',\s*\}', '}'),
            ]
            
            for pattern, replacement in patterns:
                if re.search(pattern, line):
                    lines[line_num - 1] = re.sub(pattern, replacement, line)
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.writelines(lines)
                    return True
        
    except Exception as e:
        print(f"Error fixing {filepath}:{line_num} - {e}")
    
    return False

def main():
    print("🔍 Analyzing parsing errors with careful approach...")
    
    # Get current errors
    output = run_lint()
    errors = parse_lint_output(output)
    parsing_errors = [e for e in errors if e['type'] == 'parsing']
    
    print(f"Found {len(parsing_errors)} parsing errors")
    
    # Group by error type
    error_types = {}
    for error in parsing_errors:
        msg = error['message']
        if msg not in error_types:
            error_types[msg] = []
        error_types[msg].append(error)
    
    print("\n📊 Error breakdown:")
    for error_type, errors_list in sorted(error_types.items(), key=lambda x: len(x[1]), reverse=True):
        print(f"  {error_type}: {len(errors_list)} occurrences")
    
    # Fix errors by type
    fixed_count = 0
    
    print("\n🔧 Fixing errors...")
    
    # Fix 'Declaration or statement expected' errors
    if 'Declaration or statement expected' in error_types:
        print("\n  Fixing 'Declaration or statement expected' errors...")
        for error in error_types['Declaration or statement expected']:
            if fix_declaration_expected(error['file'], error['line']):
                print(f"    ✅ Fixed {error['file']}:{error['line']}")
                fixed_count += 1
            else:
                print(f"    ⚠️  Could not fix {error['file']}:{error['line']}")
    
    # Fix '',' expected' errors
    if "',' expected" in error_types:
        print("\n  Fixing '',' expected' errors...")
        for error in error_types["',' expected"]:
            if fix_comma_expected(error['file'], error['line']):
                print(f"    ✅ Fixed {error['file']}:{error['line']}")
                fixed_count += 1
            else:
                print(f"    ⚠️  Could not fix {error['file']}:{error['line']}")
    
    # Fix 'Property assignment expected' errors
    if 'Property assignment expected' in error_types:
        print("\n  Fixing 'Property assignment expected' errors...")
        for error in error_types['Property assignment expected']:
            if fix_property_assignment_expected(error['file'], error['line']):
                print(f"    ✅ Fixed {error['file']}:{error['line']}")
                fixed_count += 1
            else:
                print(f"    ⚠️  Could not fix {error['file']}:{error['line']}")
    
    # Fix 'Expression expected' errors
    if 'Expression expected' in error_types:
        print("\n  Fixing 'Expression expected' errors...")
        for error in error_types['Expression expected']:
            if fix_expression_expected(error['file'], error['line']):
                print(f"    ✅ Fixed {error['file']}:{error['line']}")
                fixed_count += 1
            else:
                print(f"    ⚠️  Could not fix {error['file']}:{error['line']}")
    
    print(f"\n✨ Fixed {fixed_count} errors")
    
    # Check remaining errors
    print("\n🔍 Checking remaining errors...")
    output = run_lint()
    errors = parse_lint_output(output)
    remaining_parsing_errors = [e for e in errors if e['type'] == 'parsing']
    
    print(f"Remaining parsing errors: {len(remaining_parsing_errors)}")
    
    # Show sample of remaining errors for manual fixing
    if remaining_parsing_errors:
        print("\n📋 Sample of remaining errors:")
        for error in remaining_parsing_errors[:5]:
            print(f"  {error['file']}:{error['line']} - {error['message']}")

if __name__ == "__main__":
    main()