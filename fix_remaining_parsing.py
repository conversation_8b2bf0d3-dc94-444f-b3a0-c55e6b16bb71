#!/usr/bin/env python3

import subprocess
import re

def get_parsing_errors():
    """Get all parsing errors from lint"""
    try:
        result = subprocess.run(['yarn', 'lint'], capture_output=True, text=True, cwd='.')
        output = result.stdout + result.stderr
        
        errors = []
        current_file = None
        
        for line in output.split('\n'):
            # Match file paths
            if line.strip() and not line.startswith(' ') and ('.tsx' in line or '.ts' in line):
                current_file = line.strip()
            # Match parsing errors
            elif 'Parsing error' in line and 'error' in line:
                match = re.match(r'\s*(\d+):(\d+)\s+error\s+Parsing error: (.+)', line)
                if match and current_file:
                    line_num = int(match.group(1))
                    col_num = int(match.group(2))
                    error_msg = match.group(3)
                    errors.append((current_file, line_num, col_num, error_msg))
        
        return errors
    except Exception as e:
        print(f"Error getting lint output: {e}")
        return []

def fix_parsing_error(file_path, line_num, error_msg):
    """Fix parsing errors based on the error type"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.splitlines()
        
        if line_num > len(lines):
            print(f"Line {line_num} out of range in {file_path}")
            return False
        
        # Handle different parsing error types
        if "'}' expected" in error_msg:
            # Add missing closing brace
            if line_num <= len(lines):
                # Find the right place to add the brace
                indent = 0
                for i in range(line_num - 1, max(0, line_num - 10), -1):
                    if i < len(lines):
                        line = lines[i]
                        if line.strip():
                            indent = len(line) - len(line.lstrip())
                            break
                
                # Insert closing brace with proper indentation
                if line_num - 1 < len(lines):
                    lines.insert(line_num - 1, ' ' * max(0, indent - 2) + '}')
                else:
                    lines.append(' ' * max(0, indent - 2) + '}')
                    
        elif "Declaration or statement expected" in error_msg:
            # Fix incomplete statements
            if line_num - 1 < len(lines):
                target_line = lines[line_num - 1]
                # Add semicolon if missing
                if not target_line.strip().endswith((';', '}', '{')):
                    lines[line_num - 1] = target_line.rstrip() + ';'
                    
        elif "Unexpected token" in error_msg and "rbrace" in error_msg:
            # Fix JSX brace issues
            if line_num - 1 < len(lines):
                target_line = lines[line_num - 1]
                # Fix common JSX brace patterns
                target_line = target_line.replace('{ }', '{}')
                target_line = target_line.replace('{  }', '{}')
                lines[line_num - 1] = target_line
                
        elif "Expression expected" in error_msg:
            # Fix incomplete expressions
            if line_num - 1 < len(lines):
                target_line = lines[line_num - 1]
                # Add placeholder for incomplete expressions
                if target_line.strip().endswith(','):
                    lines[line_num - 1] = target_line.rstrip()[:-1]  # Remove trailing comma
        
        # Write the fixed content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines) + '\n')
        
        print(f"Fixed parsing error in {file_path}:{line_num}")
        return True
        
    except Exception as e:
        print(f"Error fixing {file_path}:{line_num} - {e}")
        return False

def main():
    print("🔍 Finding parsing errors...")
    errors = get_parsing_errors()
    
    if not errors:
        print("✅ No parsing errors found!")
        return
    
    print(f"Found {len(errors)} parsing errors")
    
    # Group by file for better processing
    files_to_fix = {}
    for file_path, line_num, col_num, error_msg in errors:
        if file_path not in files_to_fix:
            files_to_fix[file_path] = []
        files_to_fix[file_path].append((line_num, col_num, error_msg))
    
    print(f"🔧 Fixing {len(errors)} parsing errors across {len(files_to_fix)} files...")
    
    fixed_count = 0
    for file_path, file_errors in files_to_fix.items():
        # Sort by line number in reverse order to avoid line shifts
        file_errors.sort(key=lambda x: x[0], reverse=True)
        
        for line_num, col_num, error_msg in file_errors[:5]:  # Limit to 5 fixes per file to avoid cascading issues
            if fix_parsing_error(file_path, line_num, error_msg):
                fixed_count += 1
    
    print(f"✅ Fixed {fixed_count} parsing errors")
    
    # Test the fixes
    print("\n🧪 Testing fixes...")
    result = subprocess.run(['yarn', 'lint', '--max-warnings=1000'], capture_output=True, text=True)
    
    parsing_errors = len([line for line in result.stdout.split('\n') if 'Parsing error' in line and 'error' in line])
    print(f"⚠️  {parsing_errors} parsing errors remain")

if __name__ == '__main__':
    main()