#!/usr/bin/env python3
"""
Final pass to fix remaining parsing errors.
"""

import os
import re
import subprocess
from typing import List, <PERSON><PERSON>

def fix_vi_mock_syntax(content: str) -> str:
    """Fix vi.mock() syntax - () => ({)) should be () => ({"""
    # Fix vi.mock('module', () => ({))
    pattern = r"vi\.mock\((.*?),\s*\(\)\s*=>\s*\(\{\}\)\)"
    replacement = r"vi.mock(\1, () => ({"
    content = re.sub(pattern, replacement, content)
    
    # Fix any arrow functions that have () => ()
    # but should be () => (
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # Look for patterns like: WBox: ({ children, ...props }: any) => ()
        if ': (' in line and ') => ()' in line and i + 1 < len(lines):
            next_line = lines[i + 1]
            # If next line starts with JSX, fix it
            if next_line.strip().startswith('<'):
                line = line.replace(') => ()', ') => (')
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_trailing_commas_in_function_calls(content: str) -> str:
    """Fix trailing commas in function calls within arrow functions."""
    # Fix patterns like: void fireEvent.change(selectBox, { target: { value: 'contains' } }),
    # when they appear inside arrow functions
    pattern = r'(void\s+\w+\.[^;]+),\s*\n\s*\}\)'
    replacement = r'\1\n          })'
    content = re.sub(pattern, replacement, content)
    
    # Also fix without void
    pattern = r'(\w+\.[^;]+),\s*\n\s*\}\)'
    
    def check_replacement(match):
        line = match.group(1)
        # Only replace if it looks like a function call
        if '(' in line and ')' in line:
            return line + '\n          })'
        return match.group(0)
    
    content = re.sub(pattern, check_replacement, content)
    
    return content

def fix_test_file_closings(filepath: str) -> bool:
    """Fix test file closing braces."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply fixes
        content = fix_vi_mock_syntax(content)
        content = fix_trailing_commas_in_function_calls(content)
        
        # Remove excessive closing braces at end of file
        lines = content.split('\n')
        
        # Count closing patterns at the end
        closing_count = 0
        for i in range(len(lines) - 1, -1, -1):
            line = lines[i].strip()
            if line and (line == '})' * (line.count('})') // 2) or
                        line == '}' * line.count('}') or
                        line.startswith('</')):
                closing_count += 1
            else:
                break
        
        # If we have more than 20 consecutive closing lines, truncate
        if closing_count > 20:
            lines = lines[:-closing_count]
            
            # Add proper closing based on open braces
            open_count = 0
            for line in lines:
                open_count += line.count('{') - line.count('}')
                open_count += line.count('(') - line.count(')')
            
            # Add necessary closings
            if open_count > 0:
                # Group closings properly
                while open_count > 0:
                    if open_count >= 3:
                        lines.append('  })' * min(3, open_count))
                        open_count -= 3
                    else:
                        lines.append('}' if open_count == 1 else '})')
                        open_count -= 1 if open_count == 1 else 2
        
        content = '\n'.join(lines)
        
        # Write back if changed
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
    except Exception as e:
        print(f"Error fixing {filepath}: {e}")
    
    return False

def fix_regular_file(filepath: str) -> bool:
    """Fix non-test files with parsing errors."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        changed = False
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # Fix missing commas in objects
            if ':' in line and not line.strip().endswith(',') and not line.strip().endswith('{') and not line.strip().endswith('('):
                if i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if next_line.startswith('}') or ':' in next_line:
                        line = line.rstrip() + ',\n'
                        changed = True
            
            fixed_lines.append(line)
        
        if changed:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.writelines(fixed_lines)
            return True
        
    except Exception as e:
        print(f"Error fixing {filepath}: {e}")
    
    return False

def main():
    print("🔍 Running final parsing error fixes...")
    
    # Get files with parsing errors
    cmd = ["yarn", "lint", "--format", "stylish"]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    files_to_fix = set()
    current_file = None
    
    for line in result.stdout.split('\n'):
        if line.strip() and '/src/' in line and '.ts' in line and not line.startswith(' '):
            current_file = line.strip()
        elif current_file and 'Parsing error' in line:
            files_to_fix.add(current_file)
    
    print(f"Found {len(files_to_fix)} files with parsing errors")
    
    fixed_count = 0
    
    for filepath in files_to_fix:
        if os.path.exists(filepath):
            print(f"  Fixing {filepath}...")
            
            if '.test.ts' in filepath or '.test.tsx' in filepath:
                if fix_test_file_closings(filepath):
                    print(f"    ✅ Fixed test file")
                    fixed_count += 1
            else:
                if fix_regular_file(filepath):
                    print(f"    ✅ Fixed regular file")
                    fixed_count += 1
    
    print(f"\n✨ Fixed {fixed_count} files")

if __name__ == "__main__":
    main()