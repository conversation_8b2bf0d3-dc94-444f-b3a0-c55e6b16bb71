#!/usr/bin/env python3
"""
Comprehensive fix for all parsing errors across the codebase.
"""

import os
import re
import subprocess
from typing import List, Tuple, Dict

def get_files_with_parsing_errors() -> Dict[str, List[Tuple[int, str]]]:
    """Get all files with parsing errors and their line numbers."""
    cmd = ["yarn", "lint", "--format", "stylish"]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    files_errors = {}
    current_file = None
    
    for line in result.stdout.split('\n'):
        # Check if this is a file path
        if line.strip() and '/src/' in line and '.ts' in line and not line.startswith(' '):
            current_file = line.strip()
            files_errors[current_file] = []
        # Check if this is a parsing error
        elif current_file and 'Parsing error' in line:
            # Extract line number
            match = re.match(r'\s*(\d+):\d+\s+error\s+Parsing error:\s*(.+)', line)
            if match:
                line_num = int(match.group(1))
                error_msg = match.group(2)
                files_errors[current_file].append((line_num, error_msg))
    
    return {k: v for k, v in files_errors.items() if v}  # Only return files with errors

def fix_vi_mock_issues(content: str) -> str:
    """Fix all vi.mock() related issues."""
    # Fix vi.mock('module', () => ({}))
    content = re.sub(r"(vi\.mock\([^,]+,\s*\(\)\s*=>\s*\(\{)\)\)", r"\1", content)
    
    # Fix vi.mock patterns with extra closing
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # Fix mock patterns
        if 'vi.mock(' in line and ')) => ((' in line:
            line = line.replace(')) => ((', ') => ({')
        
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_waitfor_blocks(content: str) -> str:
    """Fix incomplete waitFor blocks."""
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        fixed_lines.append(line)
        
        # Check for await waitFor(() => { without closing
        if 'await waitFor(() => {' in line:
            # Count open/close braces in following lines to find where to close
            open_count = 1
            j = i + 1
            while j < len(lines) and open_count > 0:
                open_count += lines[j].count('{') - lines[j].count('}')
                j += 1
            
            # If we hit another await waitFor or it/describe before closing, we need to close
            if j < len(lines) and ('await waitFor' in lines[j] or 'it(' in lines[j] or 'describe(' in lines[j]):
                # Insert closing before the next statement
                if j > 0 and open_count > 0:
                    # Check if previous line needs closing
                    prev_line_idx = j - 1
                    while prev_line_idx > i and not lines[prev_line_idx].strip():
                        prev_line_idx -= 1
                    
                    if prev_line_idx > i and not lines[prev_line_idx].strip().endswith('})'):
                        lines[prev_line_idx] = lines[prev_line_idx] + '\n          })'
    
    return '\n'.join(lines)

def fix_object_missing_commas(content: str) -> str:
    """Fix missing commas in object literals."""
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # Check if line has a property assignment without comma
        if ':' in line and not line.strip().endswith(',') and not line.strip().endswith('{') and not line.strip().endswith('('):
            # Check next line to see if it's another property or closing brace
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if (next_line.startswith('}') or 
                    (':' in next_line and not next_line.startswith('//') and not next_line.startswith('*')) or
                    next_line.startswith(')')):
                    # Add comma
                    line = line.rstrip() + ','
        
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_function_trailing_commas(content: str) -> str:
    """Fix trailing commas in function calls."""
    # Fix fireEvent and similar patterns
    content = re.sub(r'(void\s+fireEvent\.[^;]+),\s*\n\s*\}\)', r'\1\n          })', content)
    content = re.sub(r'(fireEvent\.[^;]+),\s*\n\s*\}\)', r'\1\n          })', content)
    
    # Fix expect patterns
    content = re.sub(r'(expect\([^)]+\)\.[^;]+),\s*\n', r'\1\n', content)
    
    return content

def fix_arrow_function_issues(content: str) -> str:
    """Fix arrow function syntax issues."""
    # Fix patterns like: default: (date?: any) => ({),
    content = re.sub(r'(:\s*\([^)]*\)\s*=>\s*\(\{),(\s*[,}\n])', r'\1\2', content)
    
    # Fix return statements with trailing commas
    lines = content.split('\n')
    fixed_lines = []
    
    for line in lines:
        if 'return ' in line and line.strip().endswith(',') and not '{' in line:
            line = line.rstrip(',')
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_jsx_issues(content: str) -> str:
    """Fix JSX-related parsing issues."""
    # Fix incomplete JSX tags
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # Check for JSX with expression issues
        if re.search(r'<\w+[^>]*\{[^}]*$', line):
            # Incomplete JSX expression
            if i + 1 < len(lines) and '}' in lines[i + 1]:
                # Expression continues on next line, that's ok
                pass
            else:
                # Missing closing
                line = line + '}'
        
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_file(filepath: str, errors: List[Tuple[int, str]]) -> bool:
    """Fix a single file based on its parsing errors."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply general fixes
        content = fix_vi_mock_issues(content)
        content = fix_waitfor_blocks(content)
        content = fix_object_missing_commas(content)
        content = fix_function_trailing_commas(content)
        content = fix_arrow_function_issues(content)
        content = fix_jsx_issues(content)
        
        # Apply specific fixes based on error messages
        for line_num, error_msg in errors:
            if "'}' expected" in error_msg:
                # Missing closing brace
                lines = content.split('\n')
                if line_num <= len(lines):
                    # Add closing brace at the end of the line
                    lines[line_num - 1] = lines[line_num - 1] + '}'
                    content = '\n'.join(lines)
            
            elif "Expression expected" in error_msg:
                # Various expression issues
                lines = content.split('\n')
                if line_num <= len(lines):
                    line = lines[line_num - 1]
                    
                    # Check for specific patterns
                    if ',\n' in line or line.strip().endswith(','):
                        # Remove trailing comma
                        lines[line_num - 1] = line.rstrip(',')
                    
                    content = '\n'.join(lines)
            
            elif "Property assignment expected" in error_msg:
                # Mock or object literal issues
                lines = content.split('\n')
                if line_num <= len(lines):
                    line = lines[line_num - 1]
                    
                    if 'vi.mock' in line and ')) => ((' in line:
                        lines[line_num - 1] = line.replace(')) => ((', ') => ({')
                    
                    content = '\n'.join(lines)
        
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
    
    except Exception as e:
        print(f"Error fixing {filepath}: {e}")
    
    return False

def main():
    """Main function to fix all parsing errors."""
    print("🔍 Analyzing parsing errors across the codebase...")
    
    files_with_errors = get_files_with_parsing_errors()
    print(f"Found {len(files_with_errors)} files with parsing errors")
    
    fixed_count = 0
    
    for filepath, errors in files_with_errors.items():
        if os.path.exists(filepath):
            print(f"\n📄 {os.path.basename(filepath)} ({len(errors)} errors)")
            for line_num, error_msg in errors[:3]:  # Show first 3 errors
                print(f"   Line {line_num}: {error_msg}")
            
            if fix_file(filepath, errors):
                print(f"   ✅ Fixed")
                fixed_count += 1
            else:
                print(f"   ❌ No changes made")
    
    print(f"\n✨ Fixed {fixed_count} files")
    
    # Run lint again to check remaining errors
    print("\n🔍 Checking remaining errors...")
    result = subprocess.run(["yarn", "lint", "--format", "stylish"], capture_output=True, text=True)
    
    # Count remaining parsing errors
    parsing_error_count = result.stdout.count("Parsing error")
    print(f"Remaining parsing errors: {parsing_error_count}")

if __name__ == "__main__":
    main()