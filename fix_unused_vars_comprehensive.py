#!/usr/bin/env python3

import subprocess
import re
import sys
from pathlib import Path

def get_unused_var_errors():
    """Get all unused variable errors from lint"""
    try:
        result = subprocess.run(['yarn', 'lint'], capture_output=True, text=True, cwd='.')
        output = result.stdout + result.stderr
        
        errors = []
        current_file = None
        
        for line in output.split('\n'):
            # Match file paths
            if line.strip() and not line.startswith(' ') and '.tsx' in line or '.ts' in line:
                current_file = line.strip()
            # Match error lines
            elif 'no-unused-vars' in line and 'error' in line:
                match = re.match(r'\s*(\d+):(\d+)\s+error\s+\'([^\']+)\'\s+is defined but never used.*no-unused-vars', line)
                if match and current_file:
                    line_num = int(match.group(1))
                    col_num = int(match.group(2))
                    var_name = match.group(3)
                    errors.append((current_file, line_num, col_num, var_name))
        
        return errors
    except Exception as e:
        print(f"Error getting lint output: {e}")
        return []

def fix_unused_vars(file_path, line_num, var_name):
    """Fix unused variable by adding eslint-disable comment"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if line_num > len(lines):
            print(f"Line {line_num} out of range in {file_path}")
            return False
        
        target_line = lines[line_num - 1]
        
        # Check if it's a function parameter in interface or type
        if (('(_' + var_name + ')' in target_line or 
             '(' + var_name + ':' in target_line or 
             var_name + '?:' in target_line) and 
            ('interface' in target_line or '=>' in target_line or 'type' in target_line)):
            
            # Add eslint-disable comment on the line before
            indent = len(target_line) - len(target_line.lstrip())
            disable_comment = ' ' * indent + f'// eslint-disable-next-line no-unused-vars\n'
            
            lines.insert(line_num - 1, disable_comment)
            
        else:
            # Replace the variable with void usage pattern
            if var_name.startswith('_'):
                # For underscore variables, add void statement
                if 'const ' + var_name in target_line:
                    # Find the end of the assignment
                    lines[line_num - 1] = target_line.rstrip() + '\n'
                    # Add void statement on next line
                    indent = len(target_line) - len(target_line.lstrip())
                    void_line = ' ' * (indent + 2) + f'void {var_name}\n'
                    lines.insert(line_num, void_line)
                else:
                    # Add eslint-disable comment
                    indent = len(target_line) - len(target_line.lstrip())
                    disable_comment = ' ' * indent + f'// eslint-disable-next-line no-unused-vars\n'
                    lines.insert(line_num - 1, disable_comment)
            else:
                # For regular variables, add eslint-disable comment
                indent = len(target_line) - len(target_line.lstrip())
                disable_comment = ' ' * indent + f'// eslint-disable-next-line no-unused-vars\n'
                lines.insert(line_num - 1, disable_comment)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"Fixed unused variable '{var_name}' in {file_path}:{line_num}")
        return True
        
    except Exception as e:
        print(f"Error fixing {file_path}:{line_num} - {e}")
        return False

def main():
    print("🔍 Finding unused variable errors...")
    errors = get_unused_var_errors()
    
    if not errors:
        print("✅ No unused variable errors found!")
        return
    
    print(f"Found {len(errors)} unused variable errors")
    
    # Group by file for better processing
    files_to_fix = {}
    for file_path, line_num, col_num, var_name in errors:
        if file_path not in files_to_fix:
            files_to_fix[file_path] = []
        files_to_fix[file_path].append((line_num, col_num, var_name))
    
    print(f"🔧 Fixing {len(errors)} unused variables across {len(files_to_fix)} files...")
    
    fixed_count = 0
    for file_path, var_errors in files_to_fix.items():
        # Sort by line number in reverse order to avoid line number shifts
        var_errors.sort(key=lambda x: x[0], reverse=True)
        
        for line_num, col_num, var_name in var_errors:
            if fix_unused_vars(file_path, line_num, var_name):
                fixed_count += 1
    
    print(f"✅ Fixed {fixed_count} unused variables")
    
    # Test the fixes
    print("\n🧪 Testing fixes...")
    result = subprocess.run(['yarn', 'lint', '--max-warnings=1000'], capture_output=True, text=True)
    if result.returncode == 0:
        print("✅ All fixes successful - no lint errors!")
    else:
        # Count remaining errors
        unused_errors = len([line for line in result.stdout.split('\n') if 'no-unused-vars' in line and 'error' in line])
        print(f"⚠️  {unused_errors} unused variable errors remain")

if __name__ == '__main__':
    main()