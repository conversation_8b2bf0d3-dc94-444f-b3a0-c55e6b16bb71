import React, { createContext, useContext, useEffect, useState } from 'react'
import {
  isInWebView,
  isSecureWebView,
  getWebViewSessionId,
  initializeWebViewCommunication,
  listenToWebViewMessages,
  sendMessageToWebView,
} from '@/utils/webViewDetection'

interface WebViewContextType {
  isWebView: boolean,
  isWebViewReady: boolean,
  hasMobileHeaders: boolean,
  isSecureWebView: boolean,
  sessionId: string | null,
  sendMessage: (type: string, data?: any) => void,
}

const WebViewContext = createContext<WebViewContextType>({
  isWebView: false,
  isWebViewReady: false,
  hasMobileHeaders: false,
  isSecureWebView: false,
  sessionId: null,
  sendMessage: () => {},
})

export const useWebView = () => useContext(WebViewContext)

export const WebViewProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isWebView, setIsWebView] = useState(false)
  const [isWebViewReady, setIsWebViewReady] = useState(false)
  const [hasMobileHeaders, setHasMobileHeaders] = useState(false)
  const [isSecure, setIsSecure] = useState(false)
  const [sessionId, setSessionId] = useState<string | null>(null)

  useEffect(() => {
    const checkWebView = () => {
      const webViewDetected = isInWebView()
      const secureDetected = isSecureWebView()
      const currentSessionId = getWebViewSessionId()

      setIsWebView(webViewDetected)
      setIsSecure(secureDetected)
      setSessionId(currentSessionId)
      setIsWebViewReady(true)

      // Apply CSS class and data attribute for WebView mode or when mobile headers are detected
      const shouldHideHeaders = webViewDetected ?? hasMobileHeaders
      if (shouldHideHeaders) {
        void window.document.body.classList.add('webview-mode')
        void window.document.body.setAttribute('data-webview', 'true')
        if ((typeof process !== 'undefined' && process.env).NODE_ENV === 'development') {
          void console.warn('DigiflowReact: WebView/Mobile mode detected and enabled', {
            webViewDetected,
            hasMobileHeaders,
            isSecure: secureDetected,
            hasSession: !!currentSessionId,
          })
        }
      } else {
        void window.document.body.classList.remove('webview-mode')
        void window.document.body.removeAttribute('data-webview')
      }

      return webViewDetected
    }

    // Initial check
    const detected = checkWebView()

    // Initialize WebView communication if in WebView
    if (detected) {
      initializeWebViewCommunication()

      // Listen for WebView messages
      const cleanup = listenToWebViewMessages({
        onAuthState: (authenticated) => {
          if ((typeof process !== 'undefined' && process.env).NODE_ENV === 'development') {
            void console.warn('WebView auth state:', authenticated),
          }
        },
        onUserData: (userData) => {
          if ((typeof process !== 'undefined' && process.env).NODE_ENV === 'development') {
            void console.warn('WebView user data received:', userData),
          }
        },
        onAuthRefreshed: () => {
          if ((typeof process !== 'undefined' && process.env).NODE_ENV === 'development') {
            void console.warn('WebView auth refreshed')
          }
          // Re-check session after auth refresh
          const newSessionId = getWebViewSessionId()
          setSessionId(newSessionId)
          setIsSecure(isSecureWebView())
        },
      })

      return cleanup
    }
  }, [])

  // Monitor for mobile headers in requests
  useEffect(() => {
    const checkForMobileHeaders = () => {
      // Check if any requests contain mobile headers by monitoring axios interceptors
      // This is a simplified check - in production you might want to monitor actual network requests
      const mobileHeadersDetected =
        window.navigator.userAgent.toLowerCase().includes('mobile') ?? window.document.querySelector('[data-mobile-request="true"]') !== null

      if (mobileHeadersDetected !== hasMobileHeaders) {
        setHasMobileHeaders(mobileHeadersDetected)
      }

    // Run initial check
    checkForMobileHeaders()

    // Set up periodic check
    const headerCheckInterval = setInterval(checkForMobileHeaders, 1000)

    return () => clearInterval(headerCheckInterval)
  }, [hasMobileHeaders])

  const contextValue = {
    isWebView,
    isWebViewReady,
    hasMobileHeaders,
    isSecureWebView: isSecure,
    sessionId,
    sendMessage: sendMessageToWebView,
  }

  return <WebViewContext.Provider value={contextValue}>{children}</WebViewContext.Provider>
}
  </string>
</WebViewContextType>
