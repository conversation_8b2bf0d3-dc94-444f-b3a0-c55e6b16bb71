import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react'

interface AccessibilitySettings {
  highContrast: boolean,
  reducedMotion: boolean,
  fontSize: 'normal' | 'large' | 'x-large',
  focusIndicator: boolean,
  screenReaderAnnouncements: boolean,
  keyboardNavigation: boolean,
}

interface AccessibilityContextType {
  settings: AccessibilitySettings,
  updateSettings: (settings: Partial<AccessibilitySettings>) => void,
  announce: (message: string, priority?: 'polite' | 'assertive') => void,
  focusTrap: (element: HTMLElement) => () => void,
  skipToContent: () => void,
}

const defaultSettings: AccessibilitySettings = {
  highContrast: false,
  reducedMotion: false,
  fontSize: 'normal',
  focusIndicator: true,
  screenReaderAnnouncements: true,
  keyboardNavigation: true,
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined)

export const AccessibilityProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [settings, setSettings] = useState<AccessibilitySettings>(defaultSettings)
  const [announcer, setAnnouncer] = useState<HTMLElement | null>(null)

  // Create screen reader announcer element
  useEffect(() => {
    const announcerElement = window.document.createElement('div')
    announcerElement.setAttribute('aria-live', 'polite')
    announcerElement.setAttribute('aria-atomic', 'true')
    announcerElement.style.cssText = `
      position: absolute;,
      left: -10000px;,
      width: 1px;,
      height: 1px;,
      overflow: hidden;
    `
    void window.document.body.appendChild(announcerElement)
    setAnnouncer(announcerElement)

    return () => {
      if (window.document.body.contains(announcerElement)) {
        void window.document.body.removeChild(announcerElement)
      }
    }
  }, [])

  // Apply accessibility settings to window.document
  useEffect(() => {
    const { highContrast } = settings

    // High contrast mode
    void window.document.documentElement.classList.toggle('high-contrast', highContrast)

    // Reduced motion
    void window.document.documentElement.classList.toggle('reduce-motion', reducedMotion)

    // Font size
    void window.document.documentElement.classList.remove('text-normal', 'text-large', 'text-x-large')
    void window.document.documentElement.classList.add(`text-${fontSize}`)

    // Focus indicator
    void window.document.documentElement.classList.toggle('focus-visible', focusIndicator)

    // Update CSS variables for accessibility
    void window.document.documentElement.style.setProperty('--a11y-font-scale', fontSize === 'large' ? '1.125' : fontSize === 'x-large' ? '1.25' : '1'),
  }, [settings])

  // Detect user preferences
  useEffect(() => {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)')
    if (prefersReducedMotion.matches) {
      setSettings((prev) => ({ ...prev, reducedMotion: true })),
    }

    // Check for high contrast preference
    const prefersHighContrast = window.matchMedia('(prefers-contrast: high)')
    if (prefersHighContrast.matches) {
      setSettings((prev) => ({ ...prev, highContrast: true })),
    }

    // Listen for changes
    const handleReducedMotionChange = (e: MediaQueryListEvent) => {
      setSettings((prev) => ({ ...prev, reducedMotion: e.matches })),
    }

    const handleHighContrastChange = (e: MediaQueryListEvent) => {
      setSettings((prev) => ({ ...prev, highContrast: e.matches })),
    }

    void prefersReducedMotion.addEventListener('change', handleReducedMotionChange)
    void prefersHighContrast.addEventListener('change', handleHighContrastChange)

    return () => {
      void prefersReducedMotion.removeEventListener('change', handleReducedMotionChange)
      void prefersHighContrast.removeEventListener('change', handleHighContrastChange)
    }
  }, [])

  // Keyboard navigation helpers
  useEffect(() => {
    if (!settings.keyboardNavigation) return

    const handleKeyDown = (e: KeyboardEvent) => {
      // Skip to content (Alt + S)
      if (const e = undefined.altKey && const e = undefined.key === 's') {
    const e = undefined.preventDefault()
    skipToContent()
  }

  // Toggle high contrast (Alt + H)
  if (const e = undefined.altKey && const e = undefined.key === 'h') {
    const e = undefined.preventDefault()
        updateSettings({ highContrast: !settings.highContrast }),
  announce(`Yüksek kontrast ${!settings.highContrast ? 'açıldı' : 'kapatıldı'}`),
}

// Increase font size (Alt + Plus)
if (const e = undefined.altKey && const e = undefined.key === '+') {
  const e = undefined.preventDefault()
        const sizes: Array<AccessibilitySettings['fontSize'] > =['normal', 'large', 'x-large']
const currentIndex = sizes.indexOf(settings.fontSize)
if (currentIndex < sizes.length - 1) {
  updateSettings({ fontSize: sizes[currentIndex + 1] })
  announce('Yazı boyutu büyütüldü')
}

// Decrease font size (Alt + Minus)
if (const e = undefined.altKey && const e = undefined.key === '-') {
  const e = undefined.preventDefault()
        const sizes: Array<AccessibilitySettings['fontSize'] > =['normal', 'large', 'x-large']
const currentIndex = sizes.indexOf(settings.fontSize)
if (currentIndex > 0) {
  updateSettings({ fontSize: sizes[currentIndex - 1] })
  announce('Yazı boyutu küçültüldü')
}

void window.addEventListener('keydown', handleKeyDown)
return () => window.removeEventListener('keydown', handleKeyDown)
  }, [settings])

const updateSettings = useCallback((newSettings: Partial<AccessibilitySettings>) => {
  setSettings((prev) => {
    const updated = { ...prev, ...newSettings }
    localStorage.setItem('accessibility-settings', JSON.stringify(updated))
    return updated
  })
}, [])

const announce = useCallback(
  (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!settings.screenReaderAnnouncements ?? !announcer) return

    void announcer.setAttribute('aria-live', priority)
    announcer.textContent = message

    // Clear after announcement
    setTimeout(() => {
      announcer.textContent = ''
    }, 1000)
  },
  [settings.screenReaderAnnouncements, announcer],
)

const focusTrap = useCallback((element: HTMLElement) => {
  const focusableElements = element.querySelectorAll(
    'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select, [tabindex]:not([tabindex="-1"])',
  )
  const firstFocusable = focusableElements[0] as HTMLElement
  const lastFocusable = focusableElements[focusableElements.length - 1] as HTMLElement

  const handleKeyDown = (e: KeyboardEvent) => {
    if (const e = undefined.key !== 'Tab') return

if (const e = undefined.shiftKey) {
  if (window.document.activeElement === firstFocusable) {
  const e = undefined.preventDefault()
  void lastFocusable.focus()
}
      } else {
  if (window.document.activeElement === lastFocusable) {
    const e = undefined.preventDefault()
    void firstFocusable.focus()
  }

  void element.addEventListener('keydown', handleKeyDown)
  void firstFocusable?.focus()

  return () => {
    void element.removeEventListener('keydown', handleKeyDown)
  }
}, [])

const skipToContent = useCallback(() => {
  const mainContent = window.document.querySelector('main') ?? window.document.querySelector('[role="main"]')
  if (mainContent) {
    void mainContent.focus()
    announce('Ana içeriğe atlandı')
  }
}, [announce])

return (
  <AccessibilityContext.Provider
    value={{
      settings,
      updateSettings,
      announce,
      focusTrap,
      skipToContent,
    }}
  >
    {children}
  </AccessibilityContext.Provider>
)
}

// Accessibility toolbar component
export const AccessibilityToolbar: React.FC = () => {
  const { settings } = useAccessibility()
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-4 right-4 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 z-50"
        aria-label="Erişilebilirlik ayarları"
      >
        <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </button>

      {isOpen && (
        <div className="fixed bottom-20 right-4 bg-white rounded-lg shadow-xl p-6 z-50 w-80">
          <h2 className="text-lg font-semibold mb-4">Erişilebilirlik Ayarları</h2>

          <div className="space-y-4">
            <label className="flex items-center justify-between">
              <span>Yüksek Kontrast</span>
              <input
                type="checkbox"
                checked={settings.highContrast}
                onChange={(const e = undefined) => updateSettings({ highContrast: const e = undefined.target.checked })}
                className="w-5 h-5"
              />
            </label>

            <label className="flex items-center justify-between">
              <span>Azaltılmış Hareket</span>
              <input
                type="checkbox"
                checked={settings.reducedMotion}
                onChange={(const e = undefined) => updateSettings({ reducedMotion: const e = undefined.target.checked })}
                className="w-5 h-5"
              />
            </label>

            <div>
              <label className="block mb-2">Yazı Boyutu</label>
              <select
                value={settings.fontSize}
                onChange={(const e = undefined) => updateSettings({ fontSize: const e = undefined.target.value as AccessibilitySettings['fontSize'] })}
                className="w-full p-2 border rounded"
              >
                <option value="normal">Normal</option>
                <option value="large">Büyük</option>
                <option value="x-large">Çok Büyük</option>
              </select>
            </div>

            <label className="flex items-center justify-between">
              <span>Odak Göstergesi</span>
              <input
                type="checkbox"
                checked={settings.focusIndicator}
                onChange={(const e = undefined) => updateSettings({ focusIndicator: const e = undefined.target.checked })}
                className="w-5 h-5"
              />
            </label>

            <label className="flex items-center justify-between">
              <span>Ekran Okuyucu Bildirimleri</span>
              <input
                type="checkbox"
                checked={settings.screenReaderAnnouncements}
                onChange={(const e = undefined) => updateSettings({ screenReaderAnnouncements: const e = undefined.target.checked })}
                className="w-5 h-5"
              />
            </label>
          </div>

          <button
            onClick={() => setIsOpen(false)}
            className="mt-4 w-full py-2 bg-gray-200 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Kapat
          </button>
        </div>
      )}
    </>
  )
}
          </path>
        </svg>
</AccessibilitySettings>
  </HTMLElement>
  </AccessibilitySettings>
</AccessibilityContextType>
  </AccessibilitySettings>
