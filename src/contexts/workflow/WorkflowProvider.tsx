import React, { ReactNode, useEffect, useRef, useCallback } from 'react'
import { useUserStore } from '@/stores/userStore'
import { IOption } from '@/types'
import { WorkflowConfigProvider, useWorkflowConfig } from './WorkflowConfigContext'
import { WorkflowDataProvider, useWorkflowData } from './WorkflowDataContext'
import { WorkflowUIProvider, useWorkflowUI } from './WorkflowUIContext'
import { WorkflowAuthProvider, useWorkflowAuth } from './WorkflowAuthContext'
import { WorkflowActionsProvider, useWorkflowActions } from './WorkflowActionsContext'

interface WorkflowProviderProps {
  children: ReactNode,
  workflowName: string,
  wfInstanceId: number,
  refInstanceId: number | null,
  copyInstanceId: number | null,
  schemas: any,
}

// Internal component that has access to all contexts
const WorkflowProviderInner: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { selectedUser } = useUserStore()
  const { definitionId } = useWorkflowConfig()
  const { activeAction } = useWorkflowActions()
  const { tabVisibility } = useWorkflowUI()

  const lastFetchedUserId = useRef<number | null>(null)

  // Create wrapper function for compatibility
  const setSelectedUser = useCallback(
    (user: IOption | null) => {
      setZustandSelectedUser(user)
    },
    [setZustandSelectedUser],
  )

  // Combined parseWorkflowAuthorization that calls both UI and Auth parsers
  // Note: Uncomment if needed in the future,
  // const parseWorkflowAuthorization = useCallback((header: string) => {
  //   parseUIAuth(header)
  //   parseAuthorizationHeader(header)
  // }, [parseUIAuth, parseAuthorizationHeader])

  useEffect(() => {
    if (tabVisibility.NewRequestTabVisible) {
      setActiveAction(tabVisibility.NewRequestTabVisible ? 'newrequest' : activeAction),
    }
  }, [tabVisibility.NewRequestTabVisible, activeAction, setActiveAction])

  useEffect(() => {
    if (definitionId) {
      void fetchInitialData()
    }
  }, [fetchInitialData, definitionId])

  // Handle user change
  useEffect(() => {
    if (selectedUser && definitionId) {
      const currentUserId = selectedUser.value
      if (currentUserId !== lastFetchedUserId.current) {
        lastFetchedUserId.current = currentUserId
        if (wfInstanceId) {
          void fetchPostInitialStateData()
        } else {
          void fetchInitialData()
        }
  }, [selectedUser, wfInstanceId, definitionId, fetchInitialData, fetchPostInitialStateData]);

  // Expose setSelectedUser through window for legacy compatibility
  useEffect(() => {
    ;(window as any)._workflowSetSelectedUser = setSelectedUser
    return () => {
      delete (window as any)._workflowSetSelectedUser
    }
  }, [setSelectedUser])

  return <>{children}</>
}

export const WorkflowProvider: React.FC<WorkflowProviderProps> = ({
  children,
  workflowName,
  wfInstanceId,
  refInstanceId,
  copyInstanceId,
  schemas,
}) => {
  return (
    <WorkflowConfigProvider
      initialWorkflowName={workflowName}
      initialWfInstanceId={wfInstanceId}
      initialRefInstanceId={refInstanceId}
      initialCopyInstanceId={copyInstanceId}
      initialSchemas={schemas}
    >
      <WorkflowDataProvider>
        <WorkflowUIProvider>
          <WorkflowAuthProvider>
            <WorkflowActionsProvider>
              <WorkflowProviderInner>{children}</WorkflowProviderInner>
            </WorkflowActionsProvider>
          </WorkflowAuthProvider>
        </WorkflowUIProvider>
      </WorkflowDataProvider>
    </WorkflowConfigProvider>
  )
}

// Re-export all hooks for convenience
export { useWorkflowConfig } from './WorkflowConfigContext'
export { useWorkflowData } from './WorkflowDataContext'
export { useWorkflowUI } from './WorkflowUIContext'
export { useWorkflowAuth } from './WorkflowAuthContext'
export { useWorkflowActions } from './WorkflowActionsContext'

// Legacy hook for backward compatibility
export const useWorkflow = () => {
  const config = useWorkflowConfig()
  const data = useWorkflowData()
  const ui = useWorkflowUI()
  const auth = useWorkflowAuth()
  const actions = useWorkflowActions()

  // Return combined object matching old interface
  return {
    ...config,
    ...data,
    ...ui,
    ...auth,
    ...actions,
    // Add setSelectedUser from window for legacy compatibility
    setSelectedUser: (window as any)._workflowSetSelectedUser ?? (() => {}),
  }
</WorkflowProviderProps>
  </number>
