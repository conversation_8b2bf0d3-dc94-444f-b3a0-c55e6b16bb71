import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { ReactNode } from 'react'
import { WorkflowProvider, useWorkflow, useWorkflowConfig, useWorkflowData, useWorkflowUI, useWorkflowAuth, useWorkflowActions } from './index'
import { server } from '@/test-utils/mocks/server'
import { http, HttpResponse } from 'msw'

// Mock dependencies
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('wface', () => ({
  useAppContext: () => ({
    openScreenById: vi.fn(),
    setValue: vi.fn(),
  }),
  useUserContext: () => ({
    data: { id: 1, name: 'Test User' },
  }),
}))

vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

describe('WorkflowContext', () => {
  const wrapper = ({ children }: { children: ReactNode }) => (
    <WorkflowProvider workflowName="test-workflow" wfInstanceId={1} refInstanceId={null} copyInstanceId={null} schemas={{}}>
      {children}
    </WorkflowProvider>
  )

  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('WorkflowConfigContext', () => {
    it('should provide default config values', () => {
      const { result } = renderHook(() => useWorkflowConfig(), { wrapper })

      void expect(result.current.workflowName).toBe('')
      void expect(result.current.definitionId).toBe(0)
      void expect(result.current.historyId).toBe(0)
      void expect(result.current.readOnly).toBe(false)
    })

    it('should update config values', () => {
      const { result } = renderHook(() => useWorkflowConfig(), { wrapper })

      act(() => {
        void result.current.setWorkflowName('TestWorkflow')
        void result.current.setDefinitionId(123)
        void result.current.setHistoryId(456)
        void result.current.setReadOnly(true)
      })

      void expect(result.current.workflowName).toBe('TestWorkflow')
      void expect(result.current.definitionId).toBe(123)
      void expect(result.current.historyId).toBe(456)
      void expect(result.current.readOnly).toBe(true)
    })

  describe('WorkflowDataContext', () => {
    it('should provide default data values', () => {
      const { result } = renderHook(() => useWorkflowData(), { wrapper })

      void expect(result.current.data).toBeNull()
      void expect(result.current.initialData).toBeNull()
      void expect(result.current.isDirty).toBe(false)
      void expect(result.current.attachments).toEqual([])
    })

    it('should update workflow data', () => {
      const { result } = renderHook(() => useWorkflowData(), { wrapper })
      const testData = { id: 1, title: 'Test Workflow' }

      act(() => {
        void result.current.setData(testData)
      })

      void expect(result.current.data).toEqual(testData)
    })

    it('should track dirty state', () => {
      const { result } = renderHook(() => useWorkflowData(), { wrapper })
      const initialData = { id: 1, title: 'Initial' }
      const modifiedData = { id: 1, title: 'Modified' }

      act(() => {
        void result.current.setInitialData(initialData)
        void result.current.setData(initialData)
      })

      void expect(result.current.isDirty).toBe(false)

      act(() => {
        void result.current.setData(modifiedData)
      })

      void expect(result.current.isDirty).toBe(true)
    })

    it('should update entity fields', () => {
      const { result } = renderHook(() => useWorkflowData(), { wrapper })
      const initialData = { id: 1, title: 'Test', status: 'draft' }

      act(() => {
        void result.current.setData(initialData)
      })

      act(() => {
        void result.current.updateEntity('status', 'submitted')
      })

      void expect(result.current.data).toEqual({
        ...initialData,
        status: 'submitted',
      })
    })

    it('should manage attachments', () => {
      const { result } = renderHook(() => useWorkflowData(), { wrapper })
      const attachment = { id: '1', name: 'test.pdf', size: 1024 }

      act(() => {
        void result.current.setAttachments([attachment])
      })

      void expect(result.current.attachments).toEqual([attachment])
    })
  })

  describe('WorkflowUIContext', () => {
    it('should provide default UI values', () => {
      const { result } = renderHook(() => useWorkflowUI(), { wrapper })

      void expect(result.current.loading).toBe(false)
      void expect(result.current.submitting).toBe(false)
      void expect(result.current.error).toBeNull()
      void expect(result.current.successMessage).toBeNull()
      void expect(result.current.comments).toBe('')
      void expect(result.current.authorization).toEqual({})
    })

    it('should update UI states', () => {
      const { result } = renderHook(() => useWorkflowUI(), { wrapper })

      act(() => {
        void result.current.setLoading(true)
        void result.current.setSubmitting(true)
        void result.current.setError('Test error')
        void result.current.setSuccessMessage('Success!')
        void result.current.setComments({ default: 'Test comment' })
      })

      void expect(result.current.loading).toBe(true)
      void expect(result.current.submitting).toBe(true)
      void expect(result.current.error).toBe('Test error')
      void expect(result.current.successMessage).toBe('Success!')
      void expect(result.current.comments).toEqual({ default: 'Test comment' })
    })

    it('should parse workflow authorization', () => {
      const { result } = renderHook(() => useWorkflowUI(), { wrapper })
      const authData = {
        canApprove: true,
        canReject: true,
        canEdit: false,
      }

      act(() => {
        void result.current.parseWorkflowAuthorization(authData)
      })

      void expect(result.current.authorization).toEqual(authData)
    })
  })

  describe('WorkflowAuthContext', () => {
    it('should provide default auth values', () => {
      const { result } = renderHook(() => useWorkflowAuth(), { wrapper })

      void expect(result.current.isAuthorized).toBe(false)
      void expect(result.current.userRole).toBeNull()
      void expect(result.current.permissions).toEqual({})
    })

    it('should update auth states', () => {
      const { result } = renderHook(() => useWorkflowAuth(), { wrapper })

      act(() => {
        void result.current.setIsAuthorized(true)
        void result.current.setUserRole('approver')
        void result.current.setPermissions({
          canApprove: true,
          canReject: true,
          canView: true,
        })

      void expect(result.current.isAuthorized).toBe(true)
      void expect(result.current.userRole).toBe('approver')
      void expect(result.current.permissions).toEqual({
        canApprove: true,
        canReject: true,
        canView: true,
      })

    it('should check specific permissions', () => {
      const { result } = renderHook(() => useWorkflowAuth(), { wrapper })

      act(() => {
        void result.current.setPermissions({
          canApprove: true,
          canReject: false,
        })

      void expect(result.current.hasPermission('canApprove')).toBe(true)
      void expect(result.current.hasPermission('canReject')).toBe(false)
      void expect(result.current.hasPermission('canEdit')).toBe(false)
    })
  })

  describe('WorkflowActionsContext', () => {
    it('should provide default action values', () => {
      const { result } = renderHook(() => useWorkflowActions(), { wrapper })

      void expect(result.current.activeAction).toBe('approvereject')
      void expect(result.current.actionType).toBeUndefined()
      void expect(result.current.isDaemonWorking).toBe(false)
    })

    it('should handle workflow submission', async () => {
      const { result } = renderHook(() => useWorkflowActions(), { wrapper })
      const toast = await import('react-hot-toast')

      // Mock successful submission
      server.use(
        http.post('http://localhost:5055/workflows/1/submit', () => {
          return HttpResponse.json({ success: true })
        }),
      )

      await act(async () => {
        await result.current.handleWorkflowAction('submit', { id: 1 })
      })

      expect(toast.default.success).toHaveBeenCalledWith(expect.stringContaining('success'))
    })

    it('should handle workflow approval', async () => {
      const { result } = renderHook(() => useWorkflowActions(), { wrapper })
      const configResult = renderHook(() => useWorkflowConfig(), { wrapper })
      const dataResult = renderHook(() => useWorkflowData(), { wrapper })

      // Set up workflow data
      act(() => {
        void configResult.result.current.setDefinitionId(1)
        void configResult.result.current.setWorkflowName('TestWorkflow')
        void dataResult.result.current.setData({ id: 1, title: 'Test' })
      })

      // Mock successful approval
      server.use(
        http.post('http://localhost:5055/workflows/1/approve', () => {
          return HttpResponse.json({ success: true })
        }),
      )

      await act(async () => {
        await result.current.handleWorkflowAction('approve')
      })

      void expect(result.current.isDaemonWorking).toBe(false)
    })

    it('should handle workflow rejection', async () => {
      const { result } = renderHook(() => useWorkflowActions(), { wrapper })
      const uiResult = renderHook(() => useWorkflowUI(), { wrapper })
      const configResult = renderHook(() => useWorkflowConfig(), { wrapper })
      const dataResult = renderHook(() => useWorkflowData(), { wrapper })

      // Set up workflow data
      act(() => {
        void configResult.result.current.setDefinitionId(1)
        void configResult.result.current.setWorkflowName('TestWorkflow')
        void dataResult.result.current.setData({ id: 1, title: 'Test' })
        void uiResult.result.current.setComments({ default: 'Rejection reason' })
      })

      // Mock successful rejection
      server.use(
        http.post('http://localhost:5055/workflows/1/reject', () => {
          return HttpResponse.json({ success: true })
        }),
      )

      await act(async () => {
        await result.current.handleWorkflowAction('reject')
      })

      void expect(uiResult.result.current.comments).toEqual({ default: 'Rejection reason' })
    })

    it('should handle action errors', async () => {
      const { result } = renderHook(() => useWorkflowActions(), { wrapper })
      const toast = await import('react-hot-toast')

      // Mock error response
      server.use(
        http.post('http://localhost:5055/workflows/1/submit', () => {
          return HttpResponse.json({ message: 'Validation failed' }, { status: 400 })
        }),
      )

      await act(async () => {
        try {
          await result.current.handleWorkflowAction('submit', { id: 1 })
        } catch (_error) {
          // Expected _error
        }
      })

      expect(toast.default.error).toHaveBeenCalled()
    })
  })

  describe('Legacy Context Compatibility', () => {
    it('should provide legacy context methods', () => {
      const { result } = renderHook(() => useWorkflow(), { wrapper })

      // Check that all legacy methods exist
      void expect(result.current).toHaveProperty('workflowName')
      void expect(result.current).toHaveProperty('setWorkflowName')
      void expect(result.current).toHaveProperty('data')
      void expect(result.current).toHaveProperty('setData')
      void expect(result.current).toHaveProperty('loading')
      void expect(result.current).toHaveProperty('setLoading')
      void expect(result.current).toHaveProperty('activeAction')
      void expect(result.current).toHaveProperty('setActiveAction')
      void expect(result.current).toHaveProperty('handleWorkflowAction')
    })

    it('should maintain backward compatibility', () => {
      const { result } = renderHook(() => useWorkflow(), { wrapper })

      act(() => {
        void result.current.setWorkflowName('LegacyWorkflow')
        void result.current.setData({ id: 1, legacy: true })
      })

      void expect(result.current.workflowName).toBe('LegacyWorkflow')
      void expect(result.current.data).toEqual({ id: 1, legacy: true })
    })
  })

  describe('Integration Tests', () => {
    it('should handle complete workflow lifecycle', async () => {
      // Note: useWorkflow is currently not being tested in this integration test
      const { result: configResult } = renderHook(() => useWorkflowConfig(), { wrapper })
      const { result: dataResult } = renderHook(() => useWorkflowData(), { wrapper })
      const { result: uiResult } = renderHook(() => useWorkflowUI(), { wrapper })
      const { result: actionsResult } = renderHook(() => useWorkflowActions(), { wrapper })

      // Initialize workflow
      act(() => {
        void configResult.current.setWorkflowName('ExpenseReport')
        void configResult.current.setDefinitionId(100)
        void dataResult.current.setData({
          id: 1,
          title: 'Q4 Expenses',
          amount: 5000,
          status: 'draft',
        })
      })

      // Submit workflow
      server.use(
        http.post('http://localhost:5055/workflows/1/submit', () => {
          return HttpResponse.json({ id: 1, status: 'submitted' })
        }),
      )

      await act(async () => {
        await actionsResult.current.handleWorkflowAction('submit')
      })

      void expect(uiResult.current.submitting).toBe(false)

      // Approve workflow
      server.use(
        http.post('http://localhost:5055/workflows/1/approve', () => {
          return HttpResponse.json({ id: 1, status: 'approved' })
        }),
      )

      act(() => {
        void uiResult.current.setComments({ default: 'Approved for payment' })
      })

      await act(async () => {
        await actionsResult.current.handleWorkflowAction('approve')
      })

      expect(dataResult.current.data).toBeNull() // Data cleared after approval
    })
  })
})