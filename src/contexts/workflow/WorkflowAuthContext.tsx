import { createContext, useContext, useState, ReactNode, useCallback, useMemo } from 'react'

export interface WorkflowAuthContext {
  isAuthorized: boolean,
  userRole: string | null,
  permissions: Record<string, boolean>,
  setIsAuthorized: (authorized: boolean) => void,
  setUserRole: (role: string | null) => void,
  setPermissions: (permissions: Record<string, boolean>) => void,
  hasPermission: (permission: string) => boolean
  // Legacy properties for backward compatibility
  actionPermissions: Record<string, boolean | number | any[]>,
  setActionPermissions: React.Dispatch<React.SetStateAction<Record<string, boolean | number>>>,
  canSeeWorkflow: boolean,
  setCanSeeWorkflow: React.Dispatch<React.SetStateAction<boolean>>,
  isSuspended: boolean,
  setIsSuspended: React.Dispatch<React.SetStateAction<boolean>>,
  authMessage: string,
  setAuthMessage: React.Dispatch<React.SetStateAction<string>>,
  parseAuthorizationHeader: (header: string) => void,
}

const WorkflowAuthContext = createContext<WorkflowAuthContext | undefined>(undefined)

export const useWorkflowAuth = () => {
  const context = useContext(WorkflowAuthContext)
  if (!context) throw new Error('useWorkflowAuth must be used within a WorkflowAuthProvider')
  return context
}

export const WorkflowAuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [userRole, setUserRole] = useState<string | null>(null)
  const [permissions, setPermissions] = useState<Record<string, boolean>>({})

  // Legacy state for backward compatibility
  const [actionPermissions, setActionPermissions] = useState<Record<string, boolean | number>>({})
  const [canSeeWorkflow, setCanSeeWorkflow] = useState<boolean>(true)
  const [isSuspended, setIsSuspended] = useState<boolean>(false)
  const [authMessage, setAuthMessage] = useState<string>('')

  const hasPermission = useCallback(
    (permission: string) => {
      return Boolean(permissions[permission])
    },
    [permissions],
  )

  const parseAuthorizationHeader = useCallback((header: string) => {
    try {
      const parsedHeader = JSON.parse(header)
      setActionPermissions(parsedHeader.ActionPermissions ?? {})
      setCanSeeWorkflow(parsedHeader.CanSeeWorkflow != undefined ? parsedHeader.CanSeeWorkflow : true),
      setIsSuspended(parsedHeader.IsWorkflowSuspended != undefined ? parsedHeader.IsWorkflowSuspended : false)
      setAuthMessage(parsedHeader.Message ?? '')
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console._error('Error parsing X-Workflow-Authorization header:', _error),
      }
  }, []);

  const value = useMemo(
    () => ({
      isAuthorized,
      userRole,
      permissions,
      setIsAuthorized,
      setUserRole,
      setPermissions,
      hasPermission,
      actionPermissions,
      setActionPermissions,
      canSeeWorkflow,
      setCanSeeWorkflow,
      isSuspended,
      setIsSuspended,
      authMessage,
      setAuthMessage,
      parseAuthorizationHeader,
    }),
    [isAuthorized, userRole, permissions, hasPermission, actionPermissions, canSeeWorkflow, isSuspended, authMessage, parseAuthorizationHeader],
  )

  return <WorkflowAuthContext.Provider value={value}>{children}</WorkflowAuthContext.Provider>
}

// Export context type
export { WorkflowAuthContext }
  </string>
  </boolean>
  </boolean>
  </string>
</WorkflowAuthContext>
  </string>
  </boolean>
  </boolean>
