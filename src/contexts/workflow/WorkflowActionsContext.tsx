import { createContext, useContext, useState, ReactNode, useCallback, useMemo, startTransition } from 'react'
import { UserActionHistoryActionType } from '@/types/UserActionType'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import api from '@/api'
import { useAppContext } from 'wface'
// Import contexts directly to avoid circular dependencies
import { WorkflowDataContext } from './WorkflowDataContext'
import { WorkflowUIContext } from './WorkflowUIContext'
import { WorkflowConfigContext } from './WorkflowConfigContext'

interface WorkflowActionsContextType {
  activeAction: string
  setActiveAction: React.Dispatch<React.SetStateAction<string>>
  actionType: UserActionHistoryActionType | undefined
  setActionType: React.Dispatch<React.SetStateAction<UserActionHistoryActionType | undefined>>
  isDaemonWorking: boolean
  setIsDaemonWorking: React.Dispatch<React.SetStateAction<boolean>>
  handleWorkflowAction: (action: string, data?: any) => Promise<any>
  fetchInitialData: () => Promise<void>
  refetchInitialData: () => Promise<void>
  fetchPostInitialStateData: () => Promise<void>
}

const WorkflowActionsContext = createContext<WorkflowActionsContextType | undefined>(undefined)

export const useWorkflowActions = () => {
  const context = useContext(WorkflowActionsContext)
  if (!context) throw new Error('useWorkflowActions must be used within a WorkflowActionsProvider')
  return context
}

export const WorkflowActionsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { t } = useTranslation(['errors'])
  const [activeAction, setActiveAction] = useState<string>('approvereject')
  const [actionType, setActionType] = useState<UserActionHistoryActionType>()
  const [isDaemonWorking, setIsDaemonWorking] = useState(false)
  const { openScreenById } = useAppContext()

  // Access other contexts
  const dataContext = useContext(WorkflowDataContext)
  const uiContext = useContext(WorkflowUIContext)
  const configContext = useContext(WorkflowConfigContext)

  if (!dataContext || !uiContext || !configContext) {
    throw new Error('WorkflowActionsProvider must be used within all required workflow providers')
  }

  const { setData, updateEntity, setInitialData } = dataContext
  const { comments, setComments, parseWorkflowAuthorization } = uiContext
  const { workflowName, wfInstanceId, definitionId, copyInstanceId, refInstanceId } = configContext

  const formatDataByAction = useCallback(
    (action: string, data: any, comment: string, workflowName: string, definitionId: number | null) => {
      const baseData = {
        InstanceId: wfInstanceId,
        WorkflowName: workflowName,
        LoginUserId: parseInt(localStorage?.getItem('UserId')?.toString() || '0'),
        Comment: comment,
      }

      if (action != 'create' && comment.trim() == '') {
        toast.error(t('commentError'))
        return null
      }

      switch (action) {
        case 'create':
          const formattedData = {
            EntityJson: data,
            WorkFlowDefinitionId: definitionId,
            WorkflowName: workflowName,
            LoginId: parseInt(localStorage?.getItem('UserId')?.toString() || '0'),
            DetailJson: data.DetailJson || null,
            Detail2Json: data.Detail2Json || null,
            Detail3Json: data.Detail3Json || null,
            Detail4Json: data.Detail4Json || null,
          }
          return formattedData

        case 'approve':
          return {
            ...baseData,
            IsWfContextSave: true,
            ActionType: Number(data.conditionalApproval),
            UpdateEntity: updateEntity,
          }
        case 'forward':
          if (!data.ForwardLoginId) {
            toast.error(t('forwardLoginIdRequired'))
            return null
          }
          return { ...baseData, ForwardUserId: data.ForwardLoginId }
        case 'sendToComment':
          if (!data.SendToCommentLoginId) {
            toast.error(t('sendToCommentLoginIdRequired'))
            return null
          }
          return { ...baseData, SendToCommentUserId: data.SendToCommentLoginId }
        case 'suspend':
          if (!data.suspendUntil) {
            toast.error(t('suspendDateRequired'))
            return null
          }
          if (new Date(data.suspendUntil) <= new Date()) {
            toast.error(t('invalidSuspendDate'))
            return null
          }
          return { ...baseData, SuspendDate: data.suspendUntil }
        case 'fileUpload':
          return { ...baseData, Files: data.files }
        case 'finalize':
        case 'cancel':
        case 'resume':
        case 'reject':
        case 'send-back':
        case 'sendRequestToComment':
          return baseData
        default:
          toast.error(t('invalidAction'))
          return null
      }
    },
    [wfInstanceId, updateEntity, t],
  )

  const handleWorkflowAction = useCallback(
    async (action: string, data: any) => {
      const url = `/workflows/${action}`
      const activeComment = action === 'fileUpload' ? data.comment : comments[activeAction] || ''
      const formattedData = formatDataByAction(action, data, activeComment, workflowName, definitionId)

      if (!formattedData) {
        return
      }

      try {
        const response = await api.post(url, formattedData)

        if (response.data.successMessage) {
          toast.success(response.data.successMessage.replaceAll('"', ''), {
            duration: 5000,
          })

          // Clear comments after successful action
          setComments((prevComments) =>
            Object.keys(prevComments).reduce(
              (acc, key) => {
                acc[key] = '.'
                return acc
              },
              {} as Record<string, string>,
            ),
          )

          setData(null)

          // Handle navigation based on action type
          if (action !== 'fileUpload' && action !== 'sendRequestToComment' && action !== 'suspend' && action !== 'resume') {
            startTransition(() => {
              openScreenById('inbox')
            })
          } else {
            await refetchInitialData()
            setIsDaemonWorking(true)
            window.dispatchEvent(new CustomEvent('refreshWorkflowScreen'))
          }
        } else if (response.data.errorMessage) {
          toast.error(response.data.errorMessage, {
            duration: 5000,
          })
          throw new Error(response.data.errorMessage)
        }

        return response.data
      } catch (error) {
        console.error('Error in workflow action:', error)
        throw error
      }
    },
    [comments, activeAction, definitionId, workflowName, formatDataByAction, setComments, setData, openScreenById],
  )

  const fetchInitialData = useCallback(async () => {
    if (wfInstanceId === 0 && definitionId) {
      try {
        const response = await api.get('/workflows', {
          params: {
            workflowName,
            ...(copyInstanceId && { copyInstanceId }),
            ...(refInstanceId && { refInstanceId }),
          },
        })

        if (response.data.errorMessage !== '') {
          toast.error(response.data.errorMessage, { duration: 5000 })
        } else {
          const { enableControlsDto, loadEntityToControlsDto, loadDataBindingDto, newWorkflowLoadingDto } = response.data.detail || {}
          const combinedData = {
            ...enableControlsDto,
            ...loadEntityToControlsDto,
            ...loadDataBindingDto,
            ...newWorkflowLoadingDto,
          }
          if (newWorkflowLoadingDto?.workflowAdmins) {
            combinedData.workflowAdmins = newWorkflowLoadingDto.workflowAdmins
          } else if (newWorkflowLoadingDto?.WorkflowAdmins) {
            combinedData.workflowAdmins = newWorkflowLoadingDto.WorkflowAdmins
          }
          setInitialData(combinedData)

          const workflowAuthorizationHeader = response.headers['X-Workflow-Authorization'] || response.headers['x-workflow-authorization']
          if (workflowAuthorizationHeader) {
            parseWorkflowAuthorization(workflowAuthorizationHeader)
          }
        }
      } catch (error) {
        console.error('Error fetching initial data:', error)
      }
    }
  }, [workflowName, wfInstanceId, definitionId, copyInstanceId, refInstanceId, setInitialData, parseWorkflowAuthorization])

  const refetchInitialData = fetchInitialData

  const fetchPostInitialStateData = useCallback(async () => {
    if (wfInstanceId !== 0 && definitionId) {
      try {
        const response = await api.get(`/workflows?workflowName=${workflowName}&wfInstanceId=${wfInstanceId}`, {}, definitionId?.toString())
        if (response.data.errorMessage !== '') {
          toast.error(response.data.errorMessage, { duration: 5000 })
        } else {
          const { enableControlsDto, loadEntityToControlsDto, loadDataBindingDto, newWorkflowLoadingDto } = response.data.detail || {}
          const combinedData = {
            ...enableControlsDto,
            ...loadEntityToControlsDto,
            ...loadDataBindingDto,
            ...newWorkflowLoadingDto,
          }
          if (newWorkflowLoadingDto?.workflowAdmins) {
            combinedData.workflowAdmins = newWorkflowLoadingDto.workflowAdmins
          } else if (newWorkflowLoadingDto?.WorkflowAdmins) {
            combinedData.workflowAdmins = newWorkflowLoadingDto.WorkflowAdmins
          }
          setInitialData(combinedData)

          const workflowAuthorizationHeader = response.headers['X-Workflow-Authorization'] || response.headers['x-workflow-authorization']
          if (workflowAuthorizationHeader) {
            parseWorkflowAuthorization(workflowAuthorizationHeader)
          }
        }
      } catch (error) {
        console.error('Error fetching post initial state data:', error)
      }
    }
  }, [workflowName, wfInstanceId, definitionId, setInitialData, parseWorkflowAuthorization])

  const value = useMemo(
    () => ({
      activeAction,
      setActiveAction,
      actionType,
      setActionType,
      isDaemonWorking,
      setIsDaemonWorking,
      handleWorkflowAction,
      fetchInitialData,
      refetchInitialData,
      fetchPostInitialStateData,
    }),
    [activeAction, actionType, isDaemonWorking, handleWorkflowAction, fetchInitialData, refetchInitialData, fetchPostInitialStateData],
  )

  return <WorkflowActionsContext.Provider value={value}>{children}</WorkflowActionsContext.Provider>
}

// Forward declaration exports to avoid circular dependencies
export { WorkflowActionsContext }
