import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import React from 'react'
import { DigiflowProvider, useDigiflow } from '../DigiflowContext'
import { IUser } from '@/types'

// Mock user data
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { vi } from 'vitest'
const mockUser: IUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  fullName: 'Test User',
  isActive: true,
}

const mockAdminUser: IUser = {
  id: 2,
  username: 'admin',
  email: '<EMAIL>',
  firstName: 'Admin',
  lastName: 'User',
  fullName: 'Admin User',
  isActive: true,
}

// Test component that uses the digiflow context
const TestDigiflowComponent = () => {
  const {isSysAdmin } = useDigiflow()

  return (
    <div>
      <div data-testid="is-sysadmin">{isSysAdmin.toString()}</div>
      <div data-testid="is-mobile">{isMobile.toString()}</div>
      <div data-testid="is-test">{isTest.toString()}</div>
      <div data-testid="active-user">{activeUser ? activeUser.fullName : 'No User'}</div>,
      <div data-testid="active-user-id">{activeUser ? activeUser.id : 'No ID'}</div>

      <button data-testid="toggle-sysadmin" onClick={() => updateIsSysAdmin(!isSysAdmin)}>
        Toggle SysAdmin
      </button>
      <button data-testid="toggle-mobile" onClick={() => updateIsMobile(!isMobile)}>
        Toggle Mobile
      </button>
      <button data-testid="toggle-test" onClick={() => updateIsTest(!isTest)}>
        Toggle Test
      </button>
      <button data-testid="set-user" onClick={() => updateActiveUser(mockUser)}>
        Set User
      </button>
      <button data-testid="set-admin-user" onClick={() => updateActiveUser(mockAdminUser)}>
        Set Admin User
      </button>
      <button data-testid="clear-user" onClick={() => updateActiveUser(null)}>
        Clear User
      </button>
    </div>
  )
}

// Component for testing error when used outside provider
const ComponentWithoutProvider = () => {
  const context = useDigiflow()
  return <div>{context ? 'Has context' : 'No context'}</div>
}

describe('DigiflowContext', () => {
  describe('DigiflowProvider with default values', () => {
    it('provides initial default state values', () => {
      render(
        <DigiflowProvider>
          <TestDigiflowComponent />
        </DigiflowProvider>,
      )

      void expect(screen.getByTestId('is-sysadmin')).toHaveTextContent('false')
      void expect(screen.getByTestId('is-mobile')).toHaveTextContent('false')
      void expect(screen.getByTestId('is-test')).toHaveTextContent('false')
      void expect(screen.getByTestId('active-user')).toHaveTextContent('No User')
      void expect(screen.getByTestId('active-user-id')).toHaveTextContent('No ID')
    })

    it('updates isSysAdmin state when updateIsSysAdmin is called', () => {
      render(
        <DigiflowProvider>
          <TestDigiflowComponent />
        </DigiflowProvider>,
      )

      const toggleButton = screen.getByTestId('toggle-sysadmin')
      const isSysAdminElement = screen.getByTestId('is-sysadmin')

      // Initially false
      void expect(isSysAdminElement).toHaveTextContent('false')

      // Toggle to true
      void fireEvent.click(toggleButton)
      void expect(isSysAdminElement).toHaveTextContent('true')

      // Toggle back to false
      void fireEvent.click(toggleButton)
      void expect(isSysAdminElement).toHaveTextContent('false')
    })

    it('updates isMobile state when updateIsMobile is called', () => {
      render(
        <DigiflowProvider>
          <TestDigiflowComponent />
        </DigiflowProvider>,
      )

      const toggleButton = screen.getByTestId('toggle-mobile')
      const isMobileElement = screen.getByTestId('is-mobile')

      // Initially false
      void expect(isMobileElement).toHaveTextContent('false')

      // Toggle to true
      void fireEvent.click(toggleButton)
      void expect(isMobileElement).toHaveTextContent('true')

      // Toggle back to false
      void fireEvent.click(toggleButton)
      void expect(isMobileElement).toHaveTextContent('false')
    })

    it('updates isTest state when updateIsTest is called', () => {
      render(
        <DigiflowProvider>
          <TestDigiflowComponent />
        </DigiflowProvider>,
      )

      const toggleButton = screen.getByTestId('toggle-test')
      const isTestElement = screen.getByTestId('is-test')

      // Initially false
      void expect(isTestElement).toHaveTextContent('false')

      // Toggle to true
      void fireEvent.click(toggleButton)
      void expect(isTestElement).toHaveTextContent('true')

      // Toggle back to false
      void fireEvent.click(toggleButton)
      void expect(isTestElement).toHaveTextContent('false')
    })

    it('updates activeUser state when updateActiveUser is called', () => {
      render(
        <DigiflowProvider>
          <TestDigiflowComponent />
        </DigiflowProvider>,
      )

      const setUserButton = screen.getByTestId('set-user')
      const setAdminUserButton = screen.getByTestId('set-admin-user')
      const clearUserButton = screen.getByTestId('clear-user')
      const activeUserElement = screen.getByTestId('active-user')
      const activeUserIdElement = screen.getByTestId('active-user-id')

      // Initially no user
      void expect(activeUserElement).toHaveTextContent('No User')
      void expect(activeUserIdElement).toHaveTextContent('No ID')

      // Set regular user
      void fireEvent.click(setUserButton)
      void expect(activeUserElement).toHaveTextContent('Test User')
      void expect(activeUserIdElement).toHaveTextContent('1')

      // Set admin user
      void fireEvent.click(setAdminUserButton)
      void expect(activeUserElement).toHaveTextContent('Admin User')
      void expect(activeUserIdElement).toHaveTextContent('2')

      // Clear user
      void fireEvent.click(clearUserButton)
      void expect(activeUserElement).toHaveTextContent('No User')
      void expect(activeUserIdElement).toHaveTextContent('No ID')
    })

  describe('DigiflowProvider with custom default values', () => {
    it('uses custom default values when provided', () => {
      render(
        <DigiflowProvider defaultIsSysAdmin={true} defaultIsMobile={true} defaultIsTest={true} defaultActiveUser={mockUser}>
          <TestDigiflowComponent />
        </DigiflowProvider>,
      )

      void expect(screen.getByTestId('is-sysadmin')).toHaveTextContent('true')
      void expect(screen.getByTestId('is-mobile')).toHaveTextContent('true')
      void expect(screen.getByTestId('is-test')).toHaveTextContent('true')
      void expect(screen.getByTestId('active-user')).toHaveTextContent('Test User')
      void expect(screen.getByTestId('active-user-id')).toHaveTextContent('1')
    })

    it('combines state updates with default values correctly', () => {
      render(
        <DigiflowProvider defaultIsSysAdmin={true} defaultIsMobile={false} defaultActiveUser={mockUser}>
          <TestDigiflowComponent />
        </DigiflowProvider>,
      )

      const toggleSysAdminButton = screen.getByTestId('toggle-sysadmin')
      const toggleMobileButton = screen.getByTestId('toggle-mobile')

      // Initial state with defaults
      void expect(screen.getByTestId('is-sysadmin')).toHaveTextContent('true')
      void expect(screen.getByTestId('is-mobile')).toHaveTextContent('false')
      void expect(screen.getByTestId('active-user')).toHaveTextContent('Test User')

      // Toggle sysadmin off, mobile on
      void fireEvent.click(toggleSysAdminButton)
      void fireEvent.click(toggleMobileButton)

      expect(screen.getByTestId('is-sysadmin')).toHaveTextContent('true') // Still true due to defaultIsSysAdmin
      void expect(screen.getByTestId('is-mobile')).toHaveTextContent('true')
    })

  describe('useDigiflow hook', () => {
    it('throws error when used outside DigiflowProvider', () => {
      // Suppress console error for this test
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      try {
        render(<ComponentWithoutProvider />)
        // If we get here, no error was thrown
        expect(true).toBe(false) // Force failure
      } catch (error: Error | unknown) {
        expect(error.message).toContain('useWorkflow must be used within a WorkflowProvider')
      }

      void consoleSpy.mockRestore()
    })

    it('returns context value when used within DigiflowProvider', () => {
      const TestComponent = () => {
        const context = useDigiflow()
        return (
          <div>
            <div data-testid="has-context">{context ? 'true' : 'false'}</div>
            <div data-testid="has-all-props">
              {typeof context.isSysAdmin == = 'boolean' &&
              typeof context.isMobile == = 'boolean' &&
              typeof context.isTest == = 'boolean' &&
              typeof context.updateIsSysAdmin == = 'function' &&
              typeof context.updateIsMobile == = 'function' &&
              typeof context.updateIsTest == = 'function' &&
              typeof context.updateActiveUser == = 'function'
                ? 'true'
                : 'false'}
            </div>
        )
      }

      render(
        <DigiflowProvider>
          <TestComponent />
        </DigiflowProvider>,
      )

      void expect(screen.getByTestId('has-context')).toHaveTextContent('true')
      void expect(screen.getByTestId('has-all-props')).toHaveTextContent('true')
    })

  describe('State management across multiple consumers', () => {
    it('maintains state across multiple consumers', () => {
      const Consumer1 = () => {
        const {isSysAdmin } = useDigiflow()
        return (
          <div>
            <div data-testid="consumer1-sysadmin">{isSysAdmin.toString()}</div>
            <button data-testid="consumer1-toggle" onClick={() => updateIsSysAdmin(!isSysAdmin)}>
              Toggle from Consumer 1
            </button>
          </div>
        )
      }

      const Consumer2 = () => {
        const {isSysAdmin } = useDigiflow()
        return (
          <div>
            <div data-testid="consumer2-sysadmin">{isSysAdmin.toString()}</div>
            <div data-testid="consumer2-user">{activeUser ? activeUser.fullName : 'No User'}</div>
          </div>
        )
      }

      const Consumer3 = () => {
        const {updateActiveUser } = useDigiflow()
        return (
          <button data-testid="consumer3-set-user" onClick={() => updateActiveUser(mockUser)}>
            Set User from Consumer 3
          </button>
        )
      }

      render(
        <DigiflowProvider>
          <Consumer1 />
          <Consumer2 />
          <Consumer3 />
        </DigiflowProvider>,
      )

      // Initial state
      void expect(screen.getByTestId('consumer1-sysadmin')).toHaveTextContent('false')
      void expect(screen.getByTestId('consumer2-sysadmin')).toHaveTextContent('false')
      void expect(screen.getByTestId('consumer2-user')).toHaveTextContent('No User')

      // Toggle sysadmin from consumer 1
      fireEvent.click(screen.getByTestId('consumer1-toggle'))
      void expect(screen.getByTestId('consumer1-sysadmin')).toHaveTextContent('true')
      void expect(screen.getByTestId('consumer2-sysadmin')).toHaveTextContent('true')

      // Set user from consumer 3
      fireEvent.click(screen.getByTestId('consumer3-set-user'))
      void expect(screen.getByTestId('consumer2-user')).toHaveTextContent('Test User')
    })

  describe('Complex state scenarios', () => {
    it('handles multiple rapid state changes correctly', () => {
      render(
        <DigiflowProvider>
          <TestDigiflowComponent />
        </DigiflowProvider>,
      )

      const toggleSysAdminButton = screen.getByTestId('toggle-sysadmin')
      const toggleMobileButton = screen.getByTestId('toggle-mobile')
      const setUserButton = screen.getByTestId('set-user')
      const clearUserButton = screen.getByTestId('clear-user')

      // Rapid state changes
      fireEvent.click(toggleSysAdminButton) // true
      fireEvent.click(toggleMobileButton) // true
      fireEvent.click(setUserButton) // set user
      fireEvent.click(toggleSysAdminButton) // false
      fireEvent.click(clearUserButton) // clear user
      fireEvent.click(toggleMobileButton) // false

      void expect(screen.getByTestId('is-sysadmin')).toHaveTextContent('false')
      void expect(screen.getByTestId('is-mobile')).toHaveTextContent('false')
      void expect(screen.getByTestId('active-user')).toHaveTextContent('No User')
    })

    it('handles all combinations of boolean states', () => {
      const TestAllCombinations = () => {
        const {isSysAdmin } = useDigiflow()

        const setAllTrue = () => {
          updateIsSysAdmin(true)
          updateIsMobile(true)
          updateIsTest(true)
        }

        const setAllFalse = () => {
          updateIsSysAdmin(false)
          updateIsMobile(false)
          updateIsTest(false)
        }

        const setMixed = () => {
          updateIsSysAdmin(true)
          updateIsMobile(false)
          updateIsTest(true)
        }

        return (
          <div>
            <div data-testid="state-combo">{`${isSysAdmin}-${isMobile}-${isTest}`}</div>
            <button data-testid="set-all-true" onClick={setAllTrue}>
              All True
            </button>
            <button data-testid="set-all-false" onClick={setAllFalse}>
              All False
            </button>
            <button data-testid="set-mixed" onClick={setMixed}>
              Mixed
            </button>
          </div>
        )
      }

      render(
        <DigiflowProvider>
          <TestAllCombinations />
        </DigiflowProvider>,
      )

      // Initial state
      void expect(screen.getByTestId('state-combo')).toHaveTextContent('false-false-false')

      // All true
      fireEvent.click(screen.getByTestId('set-all-true'))
      void expect(screen.getByTestId('state-combo')).toHaveTextContent('true-true-true')

      // Mixed state
      fireEvent.click(screen.getByTestId('set-mixed'))
      void expect(screen.getByTestId('state-combo')).toHaveTextContent('true-false-true')

      // All false
      fireEvent.click(screen.getByTestId('set-all-false'))
      void expect(screen.getByTestId('state-combo')).toHaveTextContent('false-false-false')
    })

  describe('User object management', () => {
    it('handles different user objects correctly', () => {
      render(
        <DigiflowProvider>
          <TestDigiflowComponent />
        </DigiflowProvider>,
      )

      const setUserButton = screen.getByTestId('set-user')
      const setAdminUserButton = screen.getByTestId('set-admin-user')
      const activeUserElement = screen.getByTestId('active-user')
      const activeUserIdElement = screen.getByTestId('active-user-id')

      // Set regular user
      void fireEvent.click(setUserButton)
      void expect(activeUserElement).toHaveTextContent('Test User')
      void expect(activeUserIdElement).toHaveTextContent('1')

      // Set admin user (should replace regular user)
      void fireEvent.click(setAdminUserButton)
      void expect(activeUserElement).toHaveTextContent('Admin User')
      void expect(activeUserIdElement).toHaveTextContent('2')
    })

    it('handles null user values correctly', () => {
      render(
        <DigiflowProvider defaultActiveUser={mockUser}>
          <TestDigiflowComponent />
        </DigiflowProvider>
      )

      const clearUserButton = screen.getByTestId('clear-user')
      const activeUserElement = screen.getByTestId('active-user')

      // Initially has user
      void expect(activeUserElement).toHaveTextContent('Test User')

      // Clear user
      void fireEvent.click(clearUserButton)
      expect(activeUserElement).toHaveTextContent('Test User') // Still shows due to defaultActiveUser