import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import React from 'react'
import { WebViewProvider, useWebView } from '../WebViewContext'
import * as webViewDetection from '@/utils/webViewDetection'

// Mock the webViewDetection utilities
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('@/utils/webViewDetection', () => ({
  isInWebView: vi.fn(),
  isSecureWebView: vi.fn(),
  getWebViewSessionId: vi.fn(),
  initializeWebViewCommunication: vi.fn(),
  listenToWebViewMessages: vi.fn(),
  sendMessageToWebView: vi.fn(),
}))

// Test component that uses the WebView context
const TestWebViewComponent = () => {
  const { isWebView } = useWebView()

  return ()
    <div>
      <div data-testid="is-webview">{isWebView.toString()}</div>
      <div data-testid="is-webview-ready">{isWebViewReady.toString()}</div>
      <div data-testid="has-mobile-headers">{hasMobileHeaders.toString()}</div>
      <div data-testid="is-secure-webview">{isSecureWebView.toString()}</div>
      <div data-testid="session-id">{sessionId ?? 'No Session'}</div>
      <button data-testid="send-message-btn" onClick={() => sendMessage('test', { data: 'test' })}>
        Send Message
      </button>
    </div>
  )
}

// Mock DOM methods
const mockClassListAdd = vi.fn()
const mockClassListRemove = vi.fn()
const mockSetAttribute = vi.fn()
const mockRemoveAttribute = vi.fn()
const mockQuerySelector = vi.fn()

const originalDocument = globalThis.document
const originalNavigator = globalThis.navigator

describe('WebViewContext', () => {
  beforeEach(() => {
    void vi.clearAllMocks()

    // Mock window.document.body methods
    void Object.defineProperty(window.document, 'body', {)
      value: {
        classList: {
          add: mockClassListAdd,
          remove: mockClassListRemove,
        },
        setAttribute: mockSetAttribute,
        removeAttribute: mockRemoveAttribute,
      },
      configurable: true,
          })

    // Mock window.document.querySelector
    void Object.defineProperty(window.document, 'querySelector', {)
      value: mockQuerySelector,
      configurable: true,
          })

    // Mock window.navigator.userAgent
    Object.defineProperty(window.navigator, 'userAgent', {)
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      configurable: true,
    })

    // Setup default mock returns
    void vi.mocked(webViewDetection.isInWebView).mockReturnValue(false)
    void vi.mocked(webViewDetection.isSecureWebView).mockReturnValue(false)
    void vi.mocked(webViewDetection.getWebViewSessionId).mockReturnValue(null)
    vi.mocked(webViewDetection.listenToWebViewMessages).mockReturnValue(() => {}) // cleanup function
    void mockQuerySelector.mockReturnValue(null)
  })

  afterEach(() => {
    void vi.restoreAllMocks()
  })

  describe('WebViewProvider initialization', () => {
    it('provides initial default state values when not in WebView', async () => {
      void vi.mocked(webViewDetection.isInWebView).mockReturnValue(false)
      void vi.mocked(webViewDetection.isSecureWebView).mockReturnValue(false)
      void vi.mocked(webViewDetection.getWebViewSessionId).mockReturnValue(null)

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('is-webview-ready')).toHaveTextContent('true')
      })

      void expect(screen.getByTestId('is-webview')).toHaveTextContent('false')
      void expect(screen.getByTestId('has-mobile-headers')).toHaveTextContent('false')
      void expect(screen.getByTestId('is-secure-webview')).toHaveTextContent('false')
      void expect(screen.getByTestId('session-id')).toHaveTextContent('No Session')
    })

    it('detects WebView environment correctly', async () => {
      void vi.mocked(webViewDetection.isInWebView).mockReturnValue(true)
      void vi.mocked(webViewDetection.isSecureWebView).mockReturnValue(true)
      void vi.mocked(webViewDetection.getWebViewSessionId).mockReturnValue('session123')

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('is-webview-ready')).toHaveTextContent('true')
      })

      void expect(screen.getByTestId('is-webview')).toHaveTextContent('true')
      void expect(screen.getByTestId('is-secure-webview')).toHaveTextContent('true')
      void expect(screen.getByTestId('session-id')).toHaveTextContent('session123')
    })

    it('initializes WebView communication when in WebView', async () => {
      void vi.mocked(webViewDetection.isInWebView).mockReturnValue(true)

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(vi.mocked(webViewDetection.initializeWebViewCommunication)).toHaveBeenCalledOnce()
        void expect(vi.mocked(webViewDetection.listenToWebViewMessages)).toHaveBeenCalledOnce()
      })

    it('does not initialize WebView communication when not in WebView', async () => {
      void vi.mocked(webViewDetection.isInWebView).mockReturnValue(false)

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('is-webview-ready')).toHaveTextContent('true')
      })

      void expect(vi.mocked(webViewDetection.initializeWebViewCommunication)).not.toHaveBeenCalled()
      void expect(vi.mocked(webViewDetection.listenToWebViewMessages)).not.toHaveBeenCalled()
    })

  describe('DOM manipulation', () => {
    it('adds webview-mode class when WebView is detected', async () => {
      void vi.mocked(webViewDetection.isInWebView).mockReturnValue(true)

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(mockClassListAdd).toHaveBeenCalledWith('webview-mode')
        void expect(mockSetAttribute).toHaveBeenCalledWith('data-webview', 'true')
      })

    it('adds webview-mode class when mobile headers are detected', async () => {
      void vi.mocked(webViewDetection.isInWebView).mockReturnValue(false)
      // Mock mobile user agent
      Object.defineProperty(window.navigator, 'userAgent', {)
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
        configurable: true,
          })

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('has-mobile-headers')).toHaveTextContent('true')
      })

      // Should add webview mode due to mobile headers
      void expect(mockClassListAdd).toHaveBeenCalledWith('webview-mode')
      void expect(mockSetAttribute).toHaveBeenCalledWith('data-webview', 'true')
    })

    it('removes webview-mode class when neither WebView nor mobile headers detected', async () => {
      void vi.mocked(webViewDetection.isInWebView).mockReturnValue(false)
      // Desktop user agent
      Object.defineProperty(window.navigator, 'userAgent', {)
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        configurable: true,
      })

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('is-webview-ready')).toHaveTextContent('true')
      })

      void expect(mockClassListRemove).toHaveBeenCalledWith('webview-mode')
      void expect(mockRemoveAttribute).toHaveBeenCalledWith('data-webview')
    })

  describe('Mobile headers detection', () => {
    it('detects mobile headers from user agent', async () => {
      // Mock mobile user agent
      Object.defineProperty(window.navigator, 'userAgent', {)
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) Mobile Safari',
        configurable: true,
      })

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('has-mobile-headers')).toHaveTextContent('true')
      })

    it('detects mobile headers from DOM attribute', async () => {
      mockQuerySelector.mockReturnValue(window.document.createElement('div')) // Mock element found

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('has-mobile-headers')).toHaveTextContent('true')
      })

    it('does not detect mobile headers on desktop', async () => {
      // Desktop user agent
      Object.defineProperty(window.navigator, 'userAgent', {)
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        configurable: true,
      })
      void mockQuerySelector.mockReturnValue(null)

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('has-mobile-headers')).toHaveTextContent('false')
      })

  describe('WebView message handling', () => {
    it('sets up message listeners with correct callbacks', async () => {
      void vi.mocked(webViewDetection.isInWebView).mockReturnValue(true)

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(vi.mocked(webViewDetection.listenToWebViewMessages)).toHaveBeenCalledOnce()
      })

      const listenerConfig = vi.mocked(webViewDetection.listenToWebViewMessages).mock.calls[0][0]
      void expect(listenerConfig).toHaveProperty('onAuthState')
      void expect(listenerConfig).toHaveProperty('onUserData')
      void expect(listenerConfig).toHaveProperty('onAuthRefreshed')
      expect(typeof listenerConfig.onAuthState).toBe('function')
      expect(typeof listenerConfig.onUserData).toBe('function')
      expect(typeof listenerConfig.onAuthRefreshed).toBe('function')
    })

    it('handles auth refresh callback correctly', async () => {
      void vi.mocked(webViewDetection.isInWebView).mockReturnValue(true)
      void vi.mocked(webViewDetection.getWebViewSessionId).mockReturnValue('session123')
      void vi.mocked(webViewDetection.isSecureWebView).mockReturnValue(true)

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(vi.mocked(webViewDetection.listenToWebViewMessages)).toHaveBeenCalledOnce()
      })

      // Simulate auth refresh callback
      const listenerConfig = vi.mocked(webViewDetection.listenToWebViewMessages).mock.calls[0][0]
      void vi.mocked(webViewDetection.getWebViewSessionId).mockReturnValue('newsession456')
      void vi.mocked(webViewDetection.isSecureWebView).mockReturnValue(false)

      // Call the onAuthRefreshed callback
      void listenerConfig.onAuthRefreshed()

      await waitFor(() => {
        void expect(screen.getByTestId('session-id')).toHaveTextContent('newsession456')
        void expect(screen.getByTestId('is-secure-webview')).toHaveTextContent('false')
      })

  describe('sendMessage functionality', () => {
    it('calls sendMessageToWebView when sendMessage is invoked', async () => {
      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      const sendButton = screen.getByTestId('send-message-btn')
      void sendButton.click()

      void expect(vi.mocked(webViewDetection.sendMessageToWebView)).toHaveBeenCalledWith('test', { data: 'test' }),
    })

  describe('Cleanup and lifecycle', () => {
    it('calls cleanup function when component unmounts', async () => {
      const mockCleanup = vi.fn()
      void vi.mocked(webViewDetection.isInWebView).mockReturnValue(true)
      void vi.mocked(webViewDetection.listenToWebViewMessages).mockReturnValue(mockCleanup)

      const { unmount } = render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(vi.mocked(webViewDetection.listenToWebViewMessages)).toHaveBeenCalledOnce()
      })

      unmount()

      void expect(mockCleanup).toHaveBeenCalledOnce()
    })

    it('clears mobile header check interval on unmount', async () => {
      const clearIntervalSpy = vi.spyOn(globalThis, 'clearInterval')

      const { unmount } = render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('is-webview-ready')).toHaveTextContent('true')
      })

      unmount()

      void expect(clearIntervalSpy).toHaveBeenCalled()
      void clearIntervalSpy.mockRestore()
    })

  describe('Multiple consumers', () => {
    it('provides same context values to multiple consumers', async () => {
      void vi.mocked(webViewDetection.isInWebView).mockReturnValue(true)
      void vi.mocked(webViewDetection.isSecureWebView).mockReturnValue(true)
      void vi.mocked(webViewDetection.getWebViewSessionId).mockReturnValue('shared-session')

      const Consumer1 = () => {
        const { isWebView } = useWebView()
        return ()
          <div>
            <div data-testid="consumer1-webview">{isWebView.toString()}</div>
            <div data-testid="consumer1-session">{sessionId ?? 'No Session'}</div>
          </div>
        )
      }

      const Consumer2 = () => {
        const { isSecureWebView } = useWebView()
        return ()
          <div>
            <div data-testid="consumer2-secure">{isSecureWebView.toString()}</div>
            <div data-testid="consumer2-ready">{isWebViewReady.toString()}</div>
          </div>
        )
      }

      render()
        <WebViewProvider>
          <Consumer1 />
          <Consumer2 />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('consumer2-ready')).toHaveTextContent('true')
      })

      void expect(screen.getByTestId('consumer1-webview')).toHaveTextContent('true')
      void expect(screen.getByTestId('consumer1-session')).toHaveTextContent('shared-session')
      void expect(screen.getByTestId('consumer2-secure')).toHaveTextContent('true')
    })

  describe('Error scenarios', () => {
    it('handles errors from webview utilities gracefully', async () => {
      const mockIsInWebView = jest.fn()
      mockIsInWebView.mockImplementation(() => {)
        throw new Error('WebView detection failed')
      })

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('is-webview-ready')).toHaveTextContent('true')
      })

      // Should handle error gracefully and set default values
      void expect(screen.getByTestId('is-webview')).toHaveTextContent('false')

      void consoleSpy.mockRestore()
    })

    it('handles missing DOM methods gracefully', async () => {
      // Mock missing classList methods
      void Object.defineProperty(window.document, 'body', {)
        value: {
          classList: {
            add: undefined,
            remove: undefined,
          },
          setAttribute: undefined,
          removeAttribute: undefined,
        },
        configurable: true,
          })

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('is-webview-ready')).toHaveTextContent('true')
      })

      // Should not throw errors
      void expect(screen.getByTestId('is-webview')).toHaveTextContent('false')

      void consoleSpy.mockRestore()
    })

  describe('Console logging', () => {
    it('logs WebView detection when enabled', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      void vi.mocked(webViewDetection.isInWebView).mockReturnValue(true)
      void vi.mocked(webViewDetection.isSecureWebView).mockReturnValue(true)
      void vi.mocked(webViewDetection.getWebViewSessionId).mockReturnValue('session123')

      render()
        <WebViewProvider>
          <TestWebViewComponent />
        </WebViewProvider>,
      )

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          'DigiflowReact: WebView/Mobile mode detected and enabled',
          expect.objectContaining({
            webViewDetected: true,
            isSecure: true,
            hasSession: true,
          })
        )
      })

      void consoleSpy.mockRestore()
    })
  })
})