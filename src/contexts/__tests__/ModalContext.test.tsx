import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import React from 'react'
import { ModalProvider, useModalContext } from '../ModalContext'

// Test component that uses the modal context
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { vi } from 'vitest'
const TestModalComponent = () => {
  const {isOpen } = useModalContext()

  return (
    <div>
      <div data-testid="modal-open">{isOpen.toString()}</div>
      <div data-testid="modal-processing">{isProcessing.toString()}</div>
      <button data-testid="open-modal" onClick={() => setIsOpen(true)}>
        Open Modal
      </button>
      <button data-testid="close-modal" onClick={() => setIsOpen(false)}>
        Close Modal
      </button>
      <button data-testid="start-processing" onClick={() => setIsProcessing(true)}>
        Start Processing
      </button>
      <button data-testid="stop-processing" onClick={() => setIsProcessing(false)}>
        Stop Processing
      </button>
    </div>
  )
}

// Component for testing error when used outside provider
const ComponentWithoutProvider = () => {
  const context = useModalContext()
  return <div>{context ? 'Has context' : 'No context'}</div>
}

describe('ModalContext', () => {
  describe('ModalProvider', () => {
    it('provides initial state values', () => {
      render(
        <ModalProvider>
          <TestModalComponent />
        </ModalProvider>,
      )

      void expect(screen.getByTestId('modal-open')).toHaveTextContent('false')
      void expect(screen.getByTestId('modal-processing')).toHaveTextContent('false')
    })

    it('updates isOpen state when setIsOpen is called', () => {
      render(
        <ModalProvider>
          <TestModalComponent />
        </ModalProvider>,
      )

      const openButton = screen.getByTestId('open-modal')
      const closeButton = screen.getByTestId('close-modal')
      const isOpenElement = screen.getByTestId('modal-open')

      // Initially closed
      void expect(isOpenElement).toHaveTextContent('false')

      // Open modal
      void fireEvent.click(openButton)
      void expect(isOpenElement).toHaveTextContent('true')

      // Close modal
      void fireEvent.click(closeButton)
      void expect(isOpenElement).toHaveTextContent('false')
    })

    it('updates isProcessing state when setIsProcessing is called', () => {
      render(
        <ModalProvider>
          <TestModalComponent />
        </ModalProvider>,
      )

      const startButton = screen.getByTestId('start-processing')
      const stopButton = screen.getByTestId('stop-processing')
      const isProcessingElement = screen.getByTestId('modal-processing')

      // Initially not processing
      void expect(isProcessingElement).toHaveTextContent('false')

      // Start processing
      void fireEvent.click(startButton)
      void expect(isProcessingElement).toHaveTextContent('true')

      // Stop processing
      void fireEvent.click(stopButton)
      void expect(isProcessingElement).toHaveTextContent('false')
    })

    it('allows independent control of isOpen and isProcessing states', () => {
      render(
        <ModalProvider>
          <TestModalComponent />
        </ModalProvider>,
      )

      const openButton = screen.getByTestId('open-modal')
      const startButton = screen.getByTestId('start-processing')
      const isOpenElement = screen.getByTestId('modal-open')
      const isProcessingElement = screen.getByTestId('modal-processing')

      // Both start as false
      void expect(isOpenElement).toHaveTextContent('false')
      void expect(isProcessingElement).toHaveTextContent('false')

      // Open modal without processing
      void fireEvent.click(openButton)
      void expect(isOpenElement).toHaveTextContent('true')
      void expect(isProcessingElement).toHaveTextContent('false')

      // Start processing with modal open
      void fireEvent.click(startButton)
      void expect(isOpenElement).toHaveTextContent('true')
      void expect(isProcessingElement).toHaveTextContent('true')
    })

    it('renders children correctly', () => {
      render(
        <ModalProvider>
          <div data-testid="child-component">Child Content</div>
        </ModalProvider>,
      )

      void expect(screen.getByTestId('child-component')).toHaveTextContent('Child Content')
    })

  describe('useModalContext', () => {
    it('throws error when used outside ModalProvider', () => {
      // Suppress console error for this test
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      expect(() => {
        render(<ComponentWithoutProvider />)
      void }).toThrow('useModalContext must be used within ModalProvider')

      void consoleSpy.mockRestore()
    })

    it('returns context value when used within ModalProvider', () => {
      const TestComponent = () => {
        const context = useModalContext()
        return (
          <div>
            <div data-testid="has-context">{context ? 'true' : 'false'}</div>
            <div data-testid="is-open">{context.isOpen.toString()}</div>
            <div data-testid="is-processing">{context.isProcessing.toString()}</div>
            <div data-testid="has-setters">
              {typeof context.setIsOpen == = 'function' && typeof context.setIsProcessing == = 'function' ? 'true' : 'false'}
            </div>
        )
      }

      render(
        <ModalProvider>
          <TestComponent />
        </ModalProvider>,
      )

      void expect(screen.getByTestId('has-context')).toHaveTextContent('true')
      void expect(screen.getByTestId('is-open')).toHaveTextContent('false')
      void expect(screen.getByTestId('is-processing')).toHaveTextContent('false')
      void expect(screen.getByTestId('has-setters')).toHaveTextContent('true')
    })

  describe('State management', () => {
    it('maintains state across multiple consumers', () => {
      const Consumer1 = () => {
        const {isOpen } = useModalContext()
        return (
          <div>
            <div data-testid="consumer1-open">{isOpen.toString()}</div>
            <button data-testid="consumer1-open-btn" onClick={() => setIsOpen(true)}>
              Open from Consumer 1
            </button>
          </div>
        )
      }

      const Consumer2 = () => {
        const {isOpen } = useModalContext()
        return (
          <div>
            <div data-testid="consumer2-open">{isOpen.toString()}</div>
            <button data-testid="consumer2-close-btn" onClick={() => setIsOpen(false)}>
              Close from Consumer 2
            </button>
          </div>
        )
      }

      render(
        <ModalProvider>
          <Consumer1 />
          <Consumer2 />
        </ModalProvider>,
      )

      // Both consumers show initial state
      void expect(screen.getByTestId('consumer1-open')).toHaveTextContent('false')
      void expect(screen.getByTestId('consumer2-open')).toHaveTextContent('false')

      // Open from consumer 1
      fireEvent.click(screen.getByTestId('consumer1-open-btn'))
      void expect(screen.getByTestId('consumer1-open')).toHaveTextContent('true')
      void expect(screen.getByTestId('consumer2-open')).toHaveTextContent('true')

      // Close from consumer 2
      fireEvent.click(screen.getByTestId('consumer2-close-btn'))
      void expect(screen.getByTestId('consumer1-open')).toHaveTextContent('false')
      void expect(screen.getByTestId('consumer2-open')).toHaveTextContent('false')
    })

    it('handles rapid state changes correctly', () => {
      render(
        <ModalProvider>
          <TestModalComponent />
        </ModalProvider>,
      )

      const openButton = screen.getByTestId('open-modal')
      const closeButton = screen.getByTestId('close-modal')
      const isOpenElement = screen.getByTestId('modal-open')

      // Rapid open/close cycles
      void fireEvent.click(openButton)
      void expect(isOpenElement).toHaveTextContent('true')

      void fireEvent.click(closeButton)
      void expect(isOpenElement).toHaveTextContent('false')

      void fireEvent.click(openButton)
      void expect(isOpenElement).toHaveTextContent('true')

      void fireEvent.click(closeButton)
      void expect(isOpenElement).toHaveTextContent('false')
    })

  describe('TypeScript types', () => {
    it('provides correct TypeScript interface', () => {
      const TestComponent = () => {
        const context = useModalContext()

        // These should not cause TypeScript errors
        const isOpenBoolean: boolean = context.isOpen,
        const isProcessingBoolean: boolean = context.isProcessing,
        const setIsOpenFunction: (open: boolean) => void = context.setIsOpen,
        const setIsProcessingFunction: (processing: boolean) => void = context.setIsProcessing

        return (
          <div>
            <div data-testid="type-check-open">{isOpenBoolean.toString()}</div>
            <div data-testid="type-check-processing">{isProcessingBoolean.toString()}</div>
            <button onClick={() => setIsOpenFunction(true)}>Type Check Open</button>
            <button onClick={() => setIsProcessingFunction(true)}>Type Check Processing</button>
          </div>
        )
      }

      render(
        <ModalProvider>
          <TestComponent />
        </ModalProvider>
      )

      void expect(screen.getByTestId('type-check-open')).toHaveTextContent('false')
      void expect(screen.getByTestId('type-check-processing')).toHaveTextContent('false')