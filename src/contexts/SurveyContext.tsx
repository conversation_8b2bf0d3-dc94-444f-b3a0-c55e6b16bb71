import React, { createContext, useContext, useState, ReactNode } from 'react'

interface SurveyContextType {
  surveyData: any // This will hold the fetched survey structure
  // eslint-disable-next-line no-unused-vars
  setSurveyData: (data: unknown) => void
  answers: { [questionId: string]: any } // This will hold user answers
  // eslint-disable-next-line no-unused-vars
  setAnswers: (answers: { [questionId: string]: any }) => void
  loading: boolean
  // eslint-disable-next-line no-unused-vars
  setLoading: (isLoading: boolean) => void
  error: string | null
  // eslint-disable-next-line no-unused-vars
  setError: (errorMessage: string | null) => void
  // Add other context-related states as needed
}

const SurveyContext = createContext<SurveyContextType | undefined>(undefined)

export const SurveyProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [surveyData, setSurveyData] = useState<any>(null)
  const [answers, setAnswers] = useState<{ [questionId: string]: any }>({})
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  return (
    <SurveyContext.Provider
      value={{
        surveyData,
        setSurveyData,
        answers,
        setAnswers,
        loading,
        setLoading,
        error,
        setError,
      }}
    >
      {children}
    </SurveyContext.Provider>
  )
}

export const useSurvey = () => {
  const context = useContext(SurveyContext)
  if (context === undefined) {
    throw new Error('useSurvey must be used within a SurveyProvider')
  }
  return context
}
