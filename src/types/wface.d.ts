/**
 * Complete wface type declarations
 *
 * This file provides comprehensive type definitions for wface components
 * that work for everyone without requiring changes to the wface library.
 */

declare module 'wface' {
  import { ComponentType, ReactNode } from 'react'
  import type {
    AppBarProps,
    ToolbarProps,
    ButtonProps,
    ButtonGroupProps,
    IconButtonProps,
    ToggleButtonProps,
    ToggleButtonGroupProps,
    DialogProps,
    PopoverProps,
    PopperProps,
    CheckboxProps,
    RadioGroupProps,
    SliderProps,
    SwitchProps,
    TextFieldProps,
    BoxProps,
    CardProps,
    CollapseProps,
    ContainerProps,
    DividerProps,
    DrawerProps,
    GridProps,
    PaperProps,
    TabsProps,
    ListProps,
    MenuProps,
    AvatarProps,
    BadgeProps,
    ChipProps,
    ClickAwayListenerProps,
    TooltipProps,
    TypographyProps,
    CircularProgressProps,
    LinearProgressProps,
    SkeletonProps,
    LinkProps,
    RatingProps,
    IconProps,
  } from '@mui/material'
  import type { LoadingButtonProps } from '@mui/lab'
  import type { SpeedDialProps } from '@mui/material/SpeedDial'
  import type { DatePickerProps, DateTimePickerProps, TimePickerProps } from '@mui/x-date-pickers'
  import type { MaterialTableProps } from '@material-table/core'
  import type { FormikHelpers, FormikErrors } from 'formik'

  // Base component props
  interface BaseComponentProps {
    id?: string
  }

  // Core exports
  export const WFace: ComponentType<any>
  export function useAppContext(): any
  export function useUserContext(): any
  export function useConfigContext(): any
  export function useApiContext(): any
  export function useTranslationContext(): any

  // Configuration interfaces
  export interface IConfiguration {
    [key: string]: any
  }

  export interface IMenuTreeItem {
    [key: string]: any
  }

  // All wface components with full Material-UI props support
  export const WAppBar: ComponentType<AppBarProps & BaseComponentProps>
  export const WToolBar: ComponentType<ToolbarProps & BaseComponentProps>
  export const WNotificationBar: ComponentType<any>
  export const WScrollBar: ComponentType<any>

  export const WButton: ComponentType<ButtonProps & BaseComponentProps & { children?: ReactNode }>
  export const WButtonGroup: ComponentType<ButtonGroupProps & BaseComponentProps>
  export const WIconButton: ComponentType<IconButtonProps & BaseComponentProps>
  export const WLoadingButton: ComponentType<LoadingButtonProps & BaseComponentProps>
  export const WToggleButton: ComponentType<ToggleButtonProps & BaseComponentProps>
  export const WToggleButtonGroup: ComponentType<ToggleButtonGroupProps & BaseComponentProps>
  export const WSpeedDial: ComponentType<SpeedDialProps & BaseComponentProps>
  export const WSpeedDialAction: ComponentType<any>
  export const WSpeedDialIcon: ComponentType<any>

  export const WChart: ComponentType<any>

  export const WBasicDialog: ComponentType<any>
  export const WDialog: ComponentType<DialogProps & BaseComponentProps>
  export const WDialogTitle: ComponentType<any>
  export const WDialogContent: ComponentType<any>
  export const WDialogContentText: ComponentType<any>
  export const WDialogActions: ComponentType<any>
  export const WMessageDialog: ComponentType<any>
  export const WPopover: ComponentType<PopoverProps & BaseComponentProps & { onClick?: (event: any) => any; className?: string }>
  export const WPopper: ComponentType<PopperProps & BaseComponentProps>

  // Form components
  export interface WFormActions extends FormikHelpers<any> {}
  export interface WFormErrors extends FormikErrors<any> {}
  export interface WFormProps {
    initialValues: any
    onSubmit: (values: any, formikActions?: WFormActions) => void
    validationSchema?: any
    validate?: (values: any) => void
    enableReinitialize?: boolean
    onChange?: (values: any) => void
    formStyle?: React.CSSProperties
    children: ReactNode
  }
  export const WForm: ComponentType<WFormProps>
  export const WFormValidation: any

  export namespace WFormField {
    export const Checkbox: ComponentType<any>
    export const Custom: ComponentType<any>
    export const DatePicker: ComponentType<any>
    export const DateTimePicker: ComponentType<any>
    export const RadioGroup: ComponentType<any>
    export const Reset: ComponentType<any>
    export const Select: ComponentType<any>
    export const Submit: ComponentType<any>
    export const Switch: ComponentType<any>
    export const TextField: ComponentType<any>
    export const TimePicker: ComponentType<any>
  }

  export const WCheckbox: ComponentType<CheckboxProps & BaseComponentProps & { label?: string }>
  export const WDatePicker: ComponentType<
    DatePickerProps<any> & BaseComponentProps & { fullWidth?: boolean; format?: string; helperText?: string; error?: boolean | string }
  >
  export const WDateTimePicker: ComponentType<
    DateTimePickerProps<any> & BaseComponentProps & { fullWidth?: boolean; format?: string; helperText?: string; error?: boolean | string }
  >
  export const WRadioGroup: ComponentType<RadioGroupProps & BaseComponentProps & { label?: string }>
  export const WRadio: ComponentType<any>
  export const WSelect: ComponentType<any>
  export const WSlider: ComponentType<SliderProps & BaseComponentProps>
  export const WSwitch: ComponentType<SwitchProps & BaseComponentProps>
  export const WTextField: ComponentType<TextFieldProps & BaseComponentProps & { leftButtons?: unknown[]; rightButtons?: unknown[] }>
  export const WTimePicker: ComponentType<
    TimePickerProps<any> & BaseComponentProps & { fullWidth?: boolean; format?: string; helperText?: string; error?: boolean | string }
  >

  export const WBox: ComponentType<BoxProps & BaseComponentProps & { children?: ReactNode }>
  export const WCard: ComponentType<CardProps & BaseComponentProps & { children?: ReactNode }>
  export const WCardActionArea: ComponentType<any>
  export const WCardActions: ComponentType<any>
  export const WCardContent: ComponentType<any>
  export const WCardHeader: ComponentType<any>
  export const WCarousel: ComponentType<any>
  export const WCollapse: ComponentType<CollapseProps & BaseComponentProps>
  export const WContainer: ComponentType<ContainerProps & BaseComponentProps & { children?: ReactNode }>
  export const WDivider: ComponentType<DividerProps & BaseComponentProps>
  export const WDrawer: ComponentType<DrawerProps & BaseComponentProps>
  export const WExpansionPanel: ComponentType<any>
  export const WGrid: ComponentType<GridProps & BaseComponentProps & { children?: ReactNode }>
  export const WNestedPageLayout: ComponentType<any>
  export const WPaper: ComponentType<PaperProps & BaseComponentProps & { children?: ReactNode }>
  export const WSwipeableViews: ComponentType<any>
  export const WTabContainer: ComponentType<any>
  export const WTabPage: ComponentType<any>
  export const WTabs: ComponentType<TabsProps & BaseComponentProps>
  export const WTab: ComponentType<any>
  export const WWizard: ComponentType<any>

  export const WList: ComponentType<ListProps & BaseComponentProps>
  export const WListItem: ComponentType<any>
  export const WListItemButton: ComponentType<any>
  export const WListItemIcon: ComponentType<any>
  export const WListItemSecondaryAction: ComponentType<any>
  export const WListItemText: ComponentType<any>
  export const WListSubheader: ComponentType<any>
  export const WMenu: ComponentType<MenuProps & BaseComponentProps>
  export const WMenuItem: ComponentType<any>

  export const WAvatar: ComponentType<AvatarProps & BaseComponentProps>
  export const WBadge: ComponentType<BadgeProps & BaseComponentProps>
  export const WIcon: ComponentType<
    IconProps & BaseComponentProps & { icon?: string; iconSource?: 'material-icons' | 'fontawesome'; iconSize?: 'small' | 'default' | 'large' }
  >
  export const WLink: ComponentType<LinkProps & BaseComponentProps>
  export const WRating: ComponentType<RatingProps & BaseComponentProps>

  export const WChip: ComponentType<ChipProps & BaseComponentProps>
  export const WClickAwayListener: ComponentType<ClickAwayListenerProps & BaseComponentProps>
  export const WSnackbarProvider: ComponentType<any>
  export const WThemeProvider: ComponentType<any>
  export const WTooltip: ComponentType<TooltipProps & BaseComponentProps>
  export const WTypography: ComponentType<TypographyProps & BaseComponentProps & { children?: ReactNode }>

  export const WCircularProgress: ComponentType<CircularProgressProps & BaseComponentProps>
  export const WLinearProgress: ComponentType<LinearProgressProps & BaseComponentProps>
  export const WSkeleton: ComponentType<SkeletonProps & BaseComponentProps>

  export const WTable: ComponentType<MaterialTableProps<any> & BaseComponentProps & { style?: React.CSSProperties }>

  // Theme types
  export interface WTheme {
    [key: string]: any
  }

  export interface WPalette {
    [key: string]: any
  }

  export interface WDesignDetails {
    [key: string]: any
  }

  // Utility types
  type RecursivePartial<T> = {
    [P in keyof T]?: RecursivePartial<T[P]>
  }
}
