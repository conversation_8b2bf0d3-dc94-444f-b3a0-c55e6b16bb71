import { RadioGroupProps } from '@mui/material/RadioGroup'
import { RadioProps } from '@mui/material/Radio'
import { FormControlLabelProps } from '@mui/material/FormControlLabel'

export interface IInbox {
  entityRefId: number
  flowName: string
  stateName: string
  wfActionStatusTypeCd: string
  route: string
  ownerLoginId: number
  wfActionTaskInstanceId: number
  wfActionTaskStatusTCd: string
  startTime: Date
  wfInsId: number
  taskScreen: string
  wfActionDefId: number
  wfInstanceDef: string
  wfOwner: string
  wfWorkflowDefId: number
  lastLoginId: number
  wfLastModifiedBy: string
  wfLastModifiedByNom: string
  wfDate: Date
  flowDesc: string
  atanan: number
  stateDesc: string
  bolum: string
  bolumEn: string
  detail: string
  amount: string
  currency: string
  amounttl: number
  topluOnayDurum: boolean
  wfWorkflowEntity: string
  wfWorkflowEntityValue: string
  wwfInstanceLink: string
  wfInstanceLink: string
}

export interface InboxData {
  inbox: IInbox[]
  delegated: IInbox[]
  commented: IInbox[]
}

export interface BaseComponentProps {
  className?: string
  style?: React.CSSProperties
}

export interface DigiRadioGroupOption {
  label: string
  value: any
  disabled?: boolean
}

export interface DigiRadioGroupProps extends BaseComponentProps, Omit<RadioGroupProps, 'onChange'> {
  axis?: 'horizontal' | 'vertical'
  label: string
  options?: DigiRadioGroupOption[]
  error?: boolean
  helperText?: string
  // eslint-disable-next-line no-unused-vars
  onChange?: (value: any) => void
  disabled?: boolean
}

export interface DigiRadioProps extends Omit<RadioProps, 'onChange'> {
  label: string
  value: any
  checked?: boolean
  // eslint-disable-next-line no-unused-vars
  onChange?: (value: any) => void
  FormControlLabelProps?: Partial<FormControlLabelProps>
}
