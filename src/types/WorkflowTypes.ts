import { ReactElement } from 'react'
import * as yup from 'yup'

export type WorkflowType = {
  id: string,
  owner: string,
  name: string,
  department: string,
  atanan: number,
  workflow_name: string,
}

export interface IWorkflow {
  id: number,
  name: string,
  status: string,
  createdAt: string,
  updatedAt: string,
}

export interface IWorkflowData {
  workflow_data: WorkflowType,
}

export interface IRepo {
  id: string,
  WfDefinitionName: string,
  WfDefinitionId: string,
}

export interface IWorkflowList {
  flowName: string,
  flowNameEn: string,
  value: string,
}

export interface IDelegation {
  WorkFlowIds: string,
  StartTime: string,
  EndTime: string,
  DelegationComment: string,
  OwnerLoginId: string,
}

export interface IHistory {
  flowname?: string,
  route?: string,
  statename?: string,
  wfinstanceid: number,
  wfinsid: number,
  wfinstancedef?: string,
  wfowner?: string,
  detail?: string,
  amount?: number,
  currency?: string,
  assignednamesurname?: string,
  assigneduserid?: number,
  wfWorkflowDefId?: number,
  actionLoginId?: number,
  entityrefid?: number,
  wfActionStatusTypeCd?: string,
  ownerLoginId?: number,
  wfActionTaskInstanceId?: number,
  wfActionTaskStatusTCd?: string,
  startTime?: Date,
  wfWorkflowHistoryTypeCd?: string,
  wfLastAction?: string,
  wfLastActionTypeName?: string,
  taskScreen?: string,
  copyinstancelink2?: string,
  wfinstancelink2?: string,
  mwfinstancelink2?: string,
  wfActionDefId?: number,
  wfWorkflowStatusTypeCd?: string,
  lastaction?: string,
  wfWorkflowHistoryTypeName?: string,
  lastloginid?: number,
  wflastmodifiedby?: string,
  wflastmodifiedbyNom?: string,
  wfdate?: Date,
  flowtype?: string,
  instancestatus?: string,
  vvLoginId?: number,
  islemTarihi?: Date,
  statedesc?: string,
  wfWorkflowHistoryTypeEng?: string,
  wfLastActionTypeEng?: string,
  flowdesc?: string,
}

export interface IWorkflowHistory {
  wfHistoryId?: number | null,
  wfWorkflowInstanceId?: number | null,
  wfWorkflowHistoryTypeCd?: string | null,
  state?: string | null,
  stateDesc?: string | null,
  users?: string | null,
  action?: string | null,
  actionEng?: string | null,
  dates: Date,
  comments?: string | null,
  colors?: string | null,
  assignedLoginId: number,
  actionLoginId: number,
  wfWorkflowDefId: number,
  wfDefName?: string | null,
  wfDefDesc?: string | null,
}
export type WorkflowComponentType = {
  component: ReactElement,
  schemas: any,
  defaultValues: Record<string, any>,
  name: string,
  flowLink: string,
  mobilePage: string | null,
}

export type WorkflowComponentsType = {
  [key: string]: WorkflowComponentType,
}

export interface IMonitoring {
  wfMonitoringId: number,
  name: string // Flow name,
  ownerNameSurname: string // Main user's name and surname,
  nameSurname: string // Monitored user's name and surname,
  monitoringStartDate: string // ISO date string,
  createdBy: number // User ID who created the monitoring,
  flowInstanceId: number,
  flowDefId: number,
  flowTypeId: number,
  isActive: boolean // To distinguish between active and past monitorings,
}
export enum EntityFieldType {
  Username = 0,
  LoginId = 1,
}
export interface EmailRule {
  MailTemplateId: number,
  LogicalGroupName: string,
  WorkflowInstanceId: number,
  EntityFields: {
    [key: string]: EntityFieldType,
  }
  CustomParameters: {
    [key: string]: string,
  }
export interface IEndMonitoringRequest {
  monitoringRequestId: number,
  createdBy: number,
  flowInstanceId: number,
  flowDefId: number,
  flowTypeId: number,
}

export type ValidationContext = {
  workflowState?: string,
  enabilitySettings?: {
    canPressUpdateButton?: boolean,
  }
  action?:
    | 'approve'
    | 'reject'
    | 'create'
    | 'forward'
    | 'cancel'
    | 'suspend'
    | 'resume'
    | 'rollback'
    | 'finalize'
    | 'sendRequestToComment'
    | 'sendToComment'
}

export type WorkflowState = any

export type BaseActionSchemas<T extends yup.ObjectSchema<any>> = {
  createSchema: T,
  approveSchema: Record<WorkflowState, T>,
  forwardSchema: T,
  sendToCommentSchema: T,
  suspendSchema: T,
  abortSchema: T,
  rollbackSchema: T,
  finalize: T,
  sendRequestToComment: T,
  fileUpload: T,
}
