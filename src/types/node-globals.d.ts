/**
 * Node.js global type declarations for browser/Node.js compatibility
 */

declare global {
  // Node.js global object
  const global: typeof globalThis

  // Node.js Buffer object
  const Buffer: {
    // eslint-disable-next-line no-unused-vars
    from(str: string, encoding?: string): Buffer
    // eslint-disable-next-line no-unused-vars
    alloc(size: number): Buffer
    // eslint-disable-next-line no-unused-vars
    concat(buffers: Buffer[]): Buffer
  }

  // Base64 encoding functions
  // eslint-disable-next-line no-unused-vars
  const btoa: (str: string) => string
  // eslint-disable-next-line no-unused-vars
  const atob: (str: string) => string

  interface Buffer {
    // eslint-disable-next-line no-unused-vars
    toString(encoding?: string): string
    length: number
  }
}

export {}
