export enum UserActionHistoryActionType {
  // eslint-disable-next-line no-unused-vars
  STARTED = 0,
  // eslint-disable-next-line no-unused-vars
  ACCEPTED = 1,
  // eslint-disable-next-line no-unused-vars
  REJECTED = 2,
  // eslint-disable-next-line no-unused-vars
  SENDBACK = 3,
  // eslint-disable-next-line no-unused-vars
  ROLLBACK = 4,
  // eslint-disable-next-line no-unused-vars
  FORWARD = 5,
  // eslint-disable-next-line no-unused-vars
  SENDTOCOMMENT = 6,
  // eslint-disable-next-line no-unused-vars
  COMMENTED = 7,
  // eslint-disable-next-line no-unused-vars
  ADDTOCOMMEND = 8,
  // eslint-disable-next-line no-unused-vars
  CANCEL = 9,
  // eslint-disable-next-line no-unused-vars
  SUSPEND = 10,
  // eslint-disable-next-line no-unused-vars
  RESUME = 11,
  // eslint-disable-next-line no-unused-vars
  ASSIGN = 12,
  // eslint-disable-next-line no-unused-vars
  FLOWREJECTED = 13,
  // eslint-disable-next-line no-unused-vars
  FLOWACCEPTED = 14,
  // eslint-disable-next-line no-unused-vars
  SENDTASK = 15,
  // eslint-disable-next-line no-unused-vars
  CNDACCCEPT = 16,
  // eslint-disable-next-line no-unused-vars
  CORRECTION = 17,
}
