import { Column } from '@/types/WFaceTypes'

export interface FilterState {
  columnId: string
  type: string
  value: any
}

export interface FilterValueRange {
  start: string | number
  end: string | number
}

export type FilterType =
  | 'equals'
  | 'contains'
  | 'startsWith'
  | 'endsWith'
  | 'isLessThan'
  | 'isGreaterThan'
  | 'on'
  | 'before'
  | 'after'
  | 'between'
  | 'selectMultiple'

export interface ColumnConfig {
  column: Column<any>
  type: 'string' | 'number' | 'date'
  uniqueValues: unknown[]
}

export interface FilterConfig {
  type: FilterType
  label: string
  labelEn: string
  applicableTypes: ('string' | 'number' | 'date')[]
}

export interface FilterComponentProps {
  columnConfig: ColumnConfig
  value: any
  // eslint-disable-next-line no-unused-vars
  onChange: (value: any) => void
  languageFile?: any
}

export interface FilterOption {
  type: FilterType
  config: FilterConfig
  component: React.ComponentType<FilterComponentProps>
}
