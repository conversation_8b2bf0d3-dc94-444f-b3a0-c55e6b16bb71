import { ReactElement } from 'react'

export interface WorkflowComponentConfig {
  flowLink: string
  component: ReactElement
  schemas: any // The schema hooks return complex objects with multiple yup schemas
  defaultValues: Record<string, any>
  name: string
  mobilePage: string | null
}

export interface WorkflowComponentsType {
  [key: string]: WorkflowComponentConfig
}

export interface WorkflowRegistry {
  // eslint-disable-next-line no-unused-vars
  getWorkflow: (name: string) => WorkflowComponentConfig | null
  getAllWorkflows: () => WorkflowComponentsType

  // eslint-disable-next-line no-unused-vars
  registerWorkflow: (name: string, config: WorkflowComponentConfig) => void
}
