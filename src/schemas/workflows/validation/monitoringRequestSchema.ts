import * as yup from 'yup'
import { useTranslation } from 'react-i18next'
import { BaseActionSchemas } from '@/types'

type ValidationContext = {
  workflowState?: string,
  enabilitySettings?: {
    canSelectWorkflowOwner?: boolean,
    canEditFlowType?: boolean,
    canEditWorkflow?: boolean,
    canEditWorkflowInstanceId?: boolean,
    canEditDescription?: boolean,
    canEditOrgTree?: boolean,
  }
  action?:
    | 'approve'
    | 'reject'
    | 'create'
    | 'forward'
    | 'cancel'
    | 'suspend'
    | 'resume'
    | 'rollback'
    | 'finalize'
    | 'sendRequestToComment'
    | 'sendToComment'
}

type WorkflowState = 'InitialSchema'

const useMonitoringRequestSchema = () => {
  const { t } = useTranslation('monitoringRequestSchema')

  // Base field validations based on the ASP.NET validation rules
  const baseFields = {
    FlowTypeId: yup.number().required(t('flowTypeRequired')).oneOf([1, 2, 3], t('invalidFlowType')),
    OwnerLoginId: yup
      .number()
      .nullable()
      .when('$enabilitySettings.canSelectWorkflowOwner', {
        is: true,
        then: (schema) =>
          schema
            .required(t('workflowOwnerRequired'))
            .typeError(t('workflowOwnerRequired'))
            .test('not-self', t('cannotBeOwner'), function (value) {
              return value !== this.parent.CreatedBy
            }),
    FlowTypeName: yup
      .string()
      .required(t('descriptionRequired'))
      .max(1024, t('maxLength', { max: 1024 })),
  }

  const createSchema = yup.object().shape({
    ...baseFields,
    FlowInstanceId: yup.number().when('FlowTypeId', {
      is: 1,
      then: (schema) => schema.required(t('workflowInstanceIdRequired')).test('valid-instance', t('invalidWorkflowInstance'), (value) => value > 0),
      otherwise: (schema) => schema.nullable(),
    }),

    FlowDefId: yup.number().when('FlowTypeId', {
      is: 2,
      then: (schema) => schema.required(t('workflowRequired')).test('valid-workflow', t('pleaseSelectWorkflow'), (value) => value > 0),
      otherwise: (schema) => schema.nullable(),
    }),

    FlowDefIdList: yup.string().when('FlowTypeId', {
      is: 3,
      then: (schema) =>
        schema.required(t('workflowListRequired')).test('at-least-one', t('atLeastOneWorkflowRequired'), (value) => {
          return value !== undefined && value !== null && value !== ''
        }),
      otherwise: (schema) => schema.nullable(),
    }),

    PersonelId: yup.number().when('FlowTypeId', {
      is: 3,
      then: (schema) =>
        schema.required(t('userSelectionRequired')).test('not-self', t('cannotCreateSelfWorkflow'), function (value) {
          return value !== this.parent.CreatedBy
        }),
      otherwise: (schema) => schema.nullable(),
    }),

    IsActive: yup.number().default(1),
    Created: yup.date().default(() => new Date()),
    LastUpdated: yup.date().default(() => new Date()),
    CreatedBy: yup.number().required(),
    LastUpdatedBy: yup.number().required(),
    VersionId: yup.number().default(1),
  })

  // Additional schemas for other actions
  const actionSchemas: BaseActionSchemas<yup.ObjectSchema<any>> = {
    createSchema,
    approveSchema: {
      InitialSchema: yup.object({}),
    },
    forwardSchema: yup.object(baseFields),
    sendToCommentSchema: yup.object({
      SendToCommentLoginId: yup.number().required(t('sendToCommentLoginIdRequired')),
    }),
    suspendSchema: yup.object({
      ...baseFields,
      suspendUntil: yup.date().required(t('suspendCommentRequired')).typeError(t('startDateInvalid')).min(new Date(), t('suspendDateMustBeFuture')),
    }),
    abortSchema: yup.object(baseFields),
    rollbackSchema: yup.object(baseFields),
    finalize: yup.object({}),
    sendRequestToComment: yup.object({}),
    fileUpload: yup.object({}),
  }

  return {
    ...actionSchemas,
    getSchemaForState: (action: string | undefined, state: string | undefined) => {
      if (!action ?? !state) return actionSchemas.createSchema
      if (action === 'approve') {
        return actionSchemas.approveSchema[state as WorkflowState] ?? actionSchemas.approveSchema.InitialSchema
      }
      const schemaKey = `${action}Schema` as keyof typeof actionSchemas
      return actionSchemas[schemaKey] ?? actionSchemas.createSchema
    },
  }

export type { ValidationContext }
export { useMonitoringRequestSchema }
