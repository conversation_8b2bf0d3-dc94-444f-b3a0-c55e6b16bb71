import * as yup from 'yup'
import { useTranslation } from 'react-i18next'
import { BaseActionSchemas } from '@/types'

type ValidationContext = {
  workflowState?: string
  enabilitySettings?: {
    canPressUpdateButton?: boolean
  }
  action?:
    | 'approve'
    | 'reject'
    | 'create'
    | 'forward'
    | 'cancel'
    | 'suspend'
    | 'resume'
    | 'rollback'
    | 'finalize'
    | 'sendRequestToComment'
    | 'sendToComment'
}

type WorkflowState = 'InitialSchema' | 'JumpState'

const useJumpToStateSchema = () => {
  const { t } = useTranslation('jumpToStateSchema')

  // Base field validations
  const baseFields = {
    Description: yup.string().required(t('descriptionRequired')).max(200, t('descriptionMaxLength')),
  }

  // Define all schemas
  const actionSchemas: BaseActionSchemas<yup.ObjectSchema<any>> = {
    createSchema: yup.object().shape(baseFields),
    approveSchema: {
      InitialSchema: yup.object().shape({}),
      JumpState: yup.object().shape(baseFields),
    },
    forwardSchema: yup.object().shape(baseFields),
    sendToCommentSchema: yup.object({
      SendToCommentLoginId: yup.number().required(t('sendToCommentLoginIdRequired')),
    }),
    suspendSchema: yup.object().shape({
      ...baseFields,
      suspendUntil: yup.date().required(t('suspendCommentRequired')).typeError(t('startDateInvalid')),
    }),
    abortSchema: yup.object().shape(baseFields),
    rollbackSchema: yup.object().shape(baseFields),
    finalize: yup.object({}),
    sendRequestToComment: yup.object({}),
    fileUpload: yup.object({}),
  }

  // Function to get schema based on state and action
  const getSchemaForState = (action: string | undefined, state: string | undefined) => {
    if (!action || !state) return actionSchemas.createSchema

    if (action === 'approve') {
      if (state in actionSchemas.approveSchema) {
        return actionSchemas.approveSchema[state as WorkflowState]
      }
      return actionSchemas.approveSchema.InitialSchema
    }

    const schemaKey = `${action}Schema` as keyof typeof actionSchemas
    return actionSchemas[schemaKey] ?? actionSchemas.createSchema
  }

  return {
    ...actionSchemas,
    getSchemaForState,
  }

  export type { ValidationContext }
  export { useJumpToStateSchema }
}
