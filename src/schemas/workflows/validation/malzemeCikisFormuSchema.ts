import * as yup from 'yup'
import { useTranslation } from 'react-i18next'
import { BaseActionSchemas } from '@/types'

type ValidationContext = {
  workflowState?: string
  enabilitySettings?: {
    canPressUpdateButton?: boolean
  }
  action?:
    | 'approve'
    | 'reject'
    | 'create'
    | 'forward'
    | 'cancel'
    | 'suspend'
    | 'resume'
    | 'rollback'
    | 'finalize'
    | 'sendRequestToComment'
    | 'sendToComment'
}

type WorkflowState = 'SistemOnay' | 'BirinciYoneticiOnayi'

const useMalzemeCikisFormuSchema = () => {
  const { t } = useTranslation('malzemeCikisFormuSchema')

  // Base field validations
  const baseFields = {}

  // Define all schemas
  const actionSchemas: BaseActionSchemas<yup.ObjectSchema<any>> = {
    createSchema: yup.object().shape(baseFields),
    approveSchema: {
      InitialSchema: yup.object().shape({}),
      SistemOnay: yup.object().shape(baseFields),
      BirinciYoneticiOnayi: yup.object().shape(baseFields),
    },
    forwardSchema: yup.object().shape(baseFields),
    sendToCommentSchema: yup.object({
      SendToCommentLoginId: yup.number().required(t('sendToCommentLoginIdRequired')),
    }),
    suspendSchema: yup.object().shape({
      ...baseFields,
      suspendUntil: yup.date().required(t('suspendCommentRequired')).typeError(t('startDateInvalid')),
    }),
    abortSchema: yup.object().shape(baseFields),
    rollbackSchema: yup.object().shape(baseFields),
    finalize: yup.object({}),
    sendRequestToComment: yup.object({}),
    fileUpload: yup.object({}),
  }

  // Function to get schema based on state and action
  const getSchemaForState = (action: string | undefined, state: string | undefined) => {
    // Default to createSchema if no action or state
    if (!action || !state) return actionSchemas.createSchema

    // Handle approve case separately
    if (action === 'approve') {
      // Check if state is a valid workflow state
      if (state in actionSchemas.approveSchema) {
        return actionSchemas.approveSchema[state as WorkflowState]
      }
      return actionSchemas.approveSchema.InitialSchema
    }

    // For all other actions, return the corresponding action schema
    const schemaKey = `${action}Schema` as keyof typeof actionSchemas
    return actionSchemas[schemaKey] ?? actionSchemas.createSchema
  }

  return {
    ...actionSchemas,
    getSchemaForState,
  }

  export type { ValidationContext }
  export { useMalzemeCikisFormuSchema }
}
