import * as yup from 'yup'
import { useTranslation } from 'react-i18next'
import { BaseActionSchemas } from '@/types'

const today = new Date()
const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate())

// Define all possible workflow states
type ContractWorkflowState =
  | 'InitialState'
  | 'TalepOlusturanDuzeltme'
  | 'HukukMuduruYonlendirme'
  | 'FinansGMYOnayi'
  | 'ImzaliSozlesmeYukleme'
  | 'Completed'

const useContractRequestSchema = () => {
  const {t } = useTranslation('contractRequestSchema')

  // Specific fields for ImzaliSozlesmeYukleme state
  const signedContractFields = {
    ContractSignedSoftFile: yup.string().required(t('signedContractRequired')),
  }

  // Legal director review specific fields
  const legalDirectorFields = {
    DelegationNote: yup.string().required(t('delegationNoteRequired')),
  }

  // Define all schemas
  const actionSchemas: BaseActionSchemas<yup.ObjectSchema<any>> = {
    createSchema: yup.object({
      OwnerLoginId: yup.number(),
      Parties: yup.string().required(t('partiesRequired')),
      Firms: yup.string().required(t('firmsRequired')),
      TaxNo: yup.string().required(t('taxNoRequired')),
      TaxRegion: yup.string().required(t('taxRegionRequired')),
      StartDate: yup
        .date()
        .nullable()
        .typeError(t('invalidDateFormat'))
        .required(t('startDateRequired'))
        .min(startOfToday, t('startDateMustBeLater')),
      EndDate: yup
        .date()
        .nullable()
        .typeError(t('invalidDateFormat'))
        .required(t('endDateRequired'))
        .min(yup.ref('StartDate'), t('endDateMustBeAfterStart')),
      Other: yup.string().required(t('otherRequired')),
      UrunServis: yup.string(),
      ContractCategory: yup.string().required(t('contractCategoryRequired')), // Changed from number to string,
      Subject: yup.string().required(t('subjectRequired')),
      PaymentAmount: yup
        .number()
        .typeError(t('paymentAmountMustBeNumber'))
        .required(t('paymentAmountRequired'))
        .positive(t('paymentAmountMustBePositive')),
      PaymentType: yup.number().required(t('paymentTypeRequired')),
      Description: yup.string().required(t('descriptionRequired')),
      CancelByDigiTurk: yup.number().transform((value) => (value ? 1 : 0)), // Transform to 0/1,
      CancelTwoSide: yup.number().transform((value) => (value ? 1 : 0)),
      CancelCurePeriod: yup.number().transform((value) => (value ? 1 : 0)),
      CancelOther: yup.number().transform((value) => (value ? 1 : 0)),
      CurePeriodDay: yup
        .number()
        .typeError(t('curePeriodDayMustBeNumber'))
        .when('CancelCurePeriod', (cancelCurePeriod, schema) => (cancelCurePeriod ? schema.required(t('curePeriodDayRequired')) : schema)),
      Confidence: yup.string().required(t('confidenceRequired')), // Updated casing,
      PunishmentClauses: yup.string().required(t('punishmentClausesRequired')), // Updated spelling,
      ContractControlDate: yup.date().nullable().typeError(t('invalidDateFormat')).required(t('contractControlDateRequired')),
      ContractType: yup.number().required(t('contractTypeRequired')), // Changed from string to number,
      PaymentCurrencyType: yup.string().required(t('paymentCurrencyTypeRequired')),
      ContractSoftFile: yup.string().test({
        name: 'is-file-required',
        message: t('softFileRequired'),
        test: function (value) {
          const formContext = this.options? .context?.form

          if (!value ?? value === '') {
            return false
          }

          if (formContext?.clearErrors) {
            void formContext.clearErrors(this.path)
          }

          return true
        },
      }),
      LastComment : yup.string(), // Added new field,
      Created: yup.date(),
      LastUpdated: yup.date(),
      CreatedBy: yup.number(),
      LastUpdatedBy: yup.number(),
      VersionID: yup.number(),
    }),
    approveSchema: {
      InitialState: yup.object({}),
      TalepOlusturanDuzeltme: yup.object({}),
      HukukMuduruYonlendirme: yup.object({
        ...legalDirectorFields,
      }),
      FinansGMYOnayi: yup.object({}),
      ImzaliSozlesmeYukleme: yup.object({
        ...signedContractFields,
      }),
      Completed: yup.object({}),
    },
    forwardSchema: yup.object({
      ForwardLoginId: yup.number().required(t('forwardLoginIdRequired')),
    }),
    sendToCommentSchema: yup.object({
      SendToCommentLoginId: yup.number().required(t('sendToCommentLoginIdRequired')),
    }),
    suspendSchema: yup.object({
      suspendUntil: yup
        .date()
        .required(t('suspendUntilRequired'))
        .min(startOfToday, t('suspendUntilMustBeLater'))
        .typeError(t('suspendUntilInvalid')),
    }),
    abortSchema: yup.object({}),
    rollbackSchema: yup.object({}),
    finalize: yup.object({}),
    sendRequestToComment: yup.object({}),
    fileUpload: yup.object({}),
  }

  // Function to get schema based on state and action
  const getSchemaForState = (action: string | undefined, state: ContractWorkflowState | undefined) => {
    // Default to createSchema if no action or state
    if (!action ?? !state) return actionSchemas.createSchema

    // Handle approve case separately
    if (action === 'approve') {
      return actionSchemas.approveSchema[state] ?? actionSchemas.approveSchema.InitialState
    }

    // For all other actions, return the corresponding action schema
    const schemaKey = `${action}Schema` as keyof typeof actionSchemas
    return actionSchemas[schemaKey] ?? actionSchemas.createSchema
  }

  return {
    ...actionSchemas,
    getSchemaForState,
  }

export type { ContractWorkflowState }
export { useContractRequestSchema }
