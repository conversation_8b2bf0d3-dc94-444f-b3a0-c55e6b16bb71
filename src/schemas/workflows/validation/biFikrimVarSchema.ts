import * as yup from 'yup'
import { useTranslation } from 'react-i18next'
import dayjs from 'dayjs'
import { BaseActionSchemas } from '@/types'

type ValidationContext = {
  workflowState?: string
  enabilitySettings?: {
    canPressUpdateButton?: boolean
  }
  action?:
    | 'approve'
    | 'reject'
    | 'create'
    | 'forward'
    | 'cancel'
    | 'suspend'
    | 'resume'
    | 'rollback'
    | 'finalize'
    | 'sendRequestToComment'
    | 'sendToComment'
}
type WorkflowState = 'InitialSchema' | 'DegerlendirmeHavuzu' | 'HeyetDegerlendirme'

const useBiFikrimVarSchema = () => {
  const { t } = useTranslation('biFikrimVarSchema')

  // Base field validations
  const baseFields = {
    Subject: yup.mixed().required(t('subjectRequired')).notOneOf(['0'], t('subjectRequired')),
    SuggestionDetail: yup
      .string()
      .required(t('suggestionDetailRequired'))
      .max(2000, t('maxLength', { max: 2000 })),
    Benefit: yup
      .string()
      .required(t('benefitRequired'))
      .max(2000, t('maxLength', { max: 2000 })),
    Location: yup.string().nullable(),
    Suggester: yup.string().when('Location', {
      is: (value?: string) => value !== null && value !== undefined && value !== '' && value !== '0',
      then: () =>
        yup.string().test({
          name: 'suggester-required',
          message: t('suggesterRequired'),
          test: (value) => value !== null && value !== undefined && value !== '' && value !== '0',
        }),
      otherwise: () => yup.string().nullable(),
    }),
  }

  // Create validation fields for HeyetDegerlendirme state
  const heyetFields = {
    DelegationNote: yup.string().required(t('delegationNoteRequired')),
    PlanDate: yup
      .date()
      .required(t('PlanTarihiZorunlu'))
      .test('plan-date-after-created', t('PlanTarihKontrolu'), function (value) {
        const created = this.parent.Created
        if (!value || !created) return false
        return !dayjs(created).isAfter(dayjs(value).add(1, 'day'))
      }),
    ExecutionDate: yup
      .date()
      .required(t('GerceklesmeTarihiZorunlu'))
      .test('execution-date-after-created', t('GerceklesmeTarihiKontrolu'), function (value) {
        const created = this.parent.Created
        if (!value ?? !created) return false
        return !dayjs(created).isAfter(dayjs(value).add(1, 'day'))
      }),
  }

  // Define all schemas
  const actionSchemas: BaseActionSchemas<yup.ObjectSchema<any>> = {
    createSchema: yup.object(baseFields),
    approveSchema: {
      InitialSchema: yup.object({}),
      DegerlendirmeHavuzu: yup.object(baseFields),
      HeyetDegerlendirme: yup.object().shape({
        ...baseFields,
        ...heyetFields,
      }),
    },
    forwardSchema: yup.object(baseFields),
    sendToCommentSchema: yup.object({
      SendToCommentLoginId: yup.number().required(t('sendToCommentLoginIdRequired')),
    }),
    suspendSchema: yup.object({
      ...baseFields,
      suspendUntil: yup.date().required(t('suspendCommentRequired')).typeError(t('startDateInvalid')).min(new Date(), t('suspendDateMustBeFuture')),
    }),
    abortSchema: yup.object(baseFields),
    rollbackSchema: yup.object(baseFields),
    finalize: yup.object({}),
    sendRequestToComment: yup.object({}),
    fileUpload: yup.object({}),
  }

  // Function to get schema based on state and action
  const getSchemaForState = (action: string | undefined, state: string | undefined) => {
    // Default to createSchema if no action or state
    if (!action || !state) return actionSchemas.createSchema

    // Handle approve case separately
    if (action === 'approve') {
      // Check if state is a valid workflow state
      if (state in actionSchemas.approveSchema) {
        return actionSchemas.approveSchema[state as WorkflowState]
      }
      return actionSchemas.approveSchema.InitialSchema
    }
    // For all other actions, return the corresponding action schema
    const schemaKey = `${action}Schema` as keyof typeof actionSchemas
    return actionSchemas[schemaKey] ?? actionSchemas.createSchema
  }

  return {
    ...actionSchemas,
    getSchemaForState,
  }

  export type { ValidationContext }
  export { useBiFikrimVarSchema }
}
