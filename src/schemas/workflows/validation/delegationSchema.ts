import * as yup from 'yup'
import { useTranslation } from 'react-i18next'
import dayjs from 'dayjs'
import { BaseActionSchemas } from '@/types'
import { useUserStore } from '@/stores/userStore'

type ValidationContext = {
  workflowState?: string
  enabilitySettings?: {
    canPressUpdateButton?: boolean
  }
  action?:
    | 'approve'
    | 'reject'
    | 'create'
    | 'forward'
    | 'cancel'
    | 'suspend'
    | 'resume'
    | 'rollback'
    | 'finalize'
    | 'sendRequestToComment'
    | 'sendToComment'
}

type WorkflowState = 'InitialSchema' | 'DelegationState'

const useDelegationSchema = () => {
  const { t } = useTranslation('delegationSchema')
  const today = dayjs().add(-1, 'day').format('YYYY-MM-DD')
  // Get userId from store instead of localStorage for security
  const userStore = useUserStore()
  const userId = userStore.selectedUser?.value ?? userStore.user?.id

  // Base field validations
  const baseFields = {
    StartTime: yup.date().required(t('requiredStartDate')).min(today, t('startDateMustBeLater')).typeError(t('startDateInvalid')),
    EndTime: yup.date().required(t('requiredFinishDate')).min(yup.ref('StartTime'), t('finishDateMustBeLater')).typeError(t('finishDateInvalid')),
    DelegatedLoginId: yup.number().required(t('requiredUser')).min(1, t('requiredUser')),
    WorkFlowIds: yup.string().required(t('atLeastOneWorkflow')),
    DelegationComment: yup.string().required(t('delegationCommentRequired')),
  }

  // Define all schemas
  const actionSchemas: BaseActionSchemas<yup.ObjectSchema<any>> = {
    createSchema: yup.object().shape(baseFields),
    approveSchema: {
      InitialSchema: yup.object().shape({}),
      DelegationState: yup.object().shape(baseFields),
    },
    forwardSchema: yup.object().shape({}),
    sendToCommentSchema: yup.object({
      SendToCommentLoginId: yup.number().required(t('sendToCommentLoginIdRequired')),
    }),
    suspendSchema: yup.object().shape({
      ...baseFields,
      suspendUntil: yup.date().required(t('suspendCommentRequired')).typeError(t('startDateInvalid')),
    }),
    abortSchema: yup.object().shape({}),
    rollbackSchema: yup.object().shape({}),
    finalize: yup.object({}),
    sendRequestToComment: yup.object({}),
    fileUpload: yup.object({}),
  }

  // Function to get schema based on state and action
  const getSchemaForState = (action: string | undefined, state: string | undefined) => {
    if (!action || !state) return actionSchemas.createSchema

    if (action === 'approve') {
      if (state in actionSchemas.approveSchema) {
        return actionSchemas.approveSchema[state as WorkflowState]
      }
      return actionSchemas.approveSchema.InitialSchema
    }

    const schemaKey = `${action}Schema` as keyof typeof actionSchemas
    return actionSchemas[schemaKey] ?? actionSchemas.createSchema
  }

  const customValidation = (values: any) => {
    const errors: any = {}
    const selectedUserIds = values.selectedUsers.map((user: any) => user.value)
    if (selectedUserIds.includes(userId)) {
      errors.selectedUsers = t('selfDelegationError')
    }
    return errors
  }

  return {
    ...actionSchemas,
    getSchemaForState,
    customValidation,
  }

  export type { ValidationContext }
  export { useDelegationSchema }
}
