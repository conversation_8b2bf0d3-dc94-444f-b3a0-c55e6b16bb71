import { WorkflowComponentConfig, WorkflowComponentsType, WorkflowRegistry } from '@/types/workflow'
import { DelegationScreen } from '@/screens/Workflow/Screens'
import BiFikrimVarScreen from '@/screens/Workflow/Screens/BiFikrimVarScreen'
import ContractRequestScreen from '@/screens/Workflow/Screens/ContractRequestScreen'
import JumpToStateScreen from '@/screens/Workflow/Screens/JumpToStateScreen'
import MonitoringRequestScreen from '@/screens/Workflow/Screens/MonitoringScreen'
import LeaveScreen from '@/screens/Workflow/Screens/LeaveScreen'
import StationeryRequestScreen from '@/screens/Workflow/Screens/StationeryRequestScreen'

import { useDelegationSchema } from '@/schemas/workflows/validation/delegationSchema'
import { useBiFikrimVarSchema } from '@/schemas/workflows/validation/biFikrimVarSchema'
import { useContractRequestSchema } from '@/schemas/workflows/validation/contractRequestSchema'
import { useJumpToStateSchema } from '@/schemas/workflows/validation/jumpToStateSchema'
import { useMonitoringRequestSchema } from '@/schemas/workflows/validation/monitoringRequestSchema'
import useStationeryRequestSchema from '@/schemas/workflows/validation/stationeryRequestSchema'

import dayjs from 'dayjs'
import { useMalzemeCikisFormuSchema } from '@/schemas/workflows/validation/malzemeCikisFormuSchema'
import MalzemeCikisFormuScreen from '@/screens/Workflow/Screens/MalzemeCikisFormu'

import { it } from 'vitest'
/**
 * Hook for managing workflow configurations
 */
export const useWorkflowRegistry = (activeUserId: number, t: (key: string) => string): void => {
  const delegationSchema = useDelegationSchema()
  const biFikrimVarSchema = useBiFikrimVarSchema()
  const contractRequestSchema = useContractRequestSchema()
  const jumpToStateSchema = useJumpToStateSchema()
  const monitoringRequestSchema = useMonitoringRequestSchema()
  const malzemeCikisFormuSchema = useMalzemeCikisFormuSchema()
  const stationeryRequestSchema = useStationeryRequestSchema()

  const workflowComponents: WorkflowComponentsType = {
    delegation: {
      flowLink: 'NDelegation.aspx',
      component: <DelegationScreen />,
      schemas: delegationSchema,
      defaultValues: {
        StartTime: '',
        EndTime: '',
        DelegatedLoginId: null,
        WorkFlowIds: '',
        DelegationComment: '',
      },
      name: t('delegationWorkflow'),
      mobilePage: 'NDelegation_Mobil.aspx',
    },
    bifikrimvar: {
      flowLink: 'BiFikrimVar.aspx',
      component: <BiFikrimVarScreen />,
      schemas: biFikrimVarSchema,
      defaultValues: {
        Subject: '0',
        UpdateEntity: null,
      },
      name: t('biFikrimVarWorkflow'),
      mobilePage: null,
    },
    contract: {
      flowLink: 'ContratRequest.aspx',
      component: <ContractRequestScreen />,
      schemas: contractRequestSchema,
      defaultValues: {
        OwnerLoginId: activeUserId,
        Firms: 'DT',
        TaxNo: '',
        TaxRegion: '',
        StartDate: null,
        EndDate: null,
        Other: '',
        UrunServis: '',
        ContractCategory: 0,
        Subject: '',
        PaymentAmount: 0,
        PaymentType: 0,
        Description: '',
        CancelByDigiTurk: 0,
        CancelTwoSide: 0,
        CancelCurePeriod: 0,
        CancelOther: 0,
        CurePeriodDay: 0,
        Confidence: '',
        PunishmentClauses: '',
        ContractControlDate: null,
        ContractType: '0',
        PaymentCurrencyType: 'TL',
        Parties: '',
        ContractSoftFile: '',
        ContractSignedSoftFile: '',
        Created: null,
        CreatedBy: null,
        VersionID: 0,
      },
      name: t('contractRequestWorkflow'),
      mobilePage: 'ContratRequest_Mobil.aspx',
    },
    jumptostate: {
      flowLink: 'JumpToState.aspx',
      component: <JumpToStateScreen />,
      schemas: jumpToStateSchema,
      defaultValues: {
        Description: '',
        JumpToStateRequestId: 0,
      },
      name: t('jumpToStateWorkflow'),
      mobilePage: null,
    },
    monitoring: {
      flowLink: 'MonitoringRequest.aspx',
      component: <MonitoringRequestScreen />,
      name: t('monitoringRequestWorkflow'),
      mobilePage: null,
      schemas: monitoringRequestSchema,
      defaultValues: {
        FlowTypeName: '',
        IsActive: 1,
        OwnerLoginId: activeUserId,
        CreatedBy: activeUserId,
        LastUpdatedBy: activeUserId,
        Created: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
        LastUpdated: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
        RequestId: null,
        FlowTypeId: 1,
        FlowInstanceId: null,
        Name: '',
        FlowDefId: null,
        FlowDefIdList: '',
        PersonelId: null,
      },
      'malzeme-cikis': {
        flowLink: 'MalzemeCikisFormu.aspx',
        component: <MalzemeCikisFormuScreen />,
        schemas: malzemeCikisFormuSchema,
        defaultValues: {
          WorkflowDefId: 2892,
          CreatedBy: activeUserId,
          Created: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
          LastUpdatedBy: activeUserId,
          LastUpdated: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
          // İş akışınıza özel diğer varsayılan değerleri buraya ekleyin
        },
        name: t('malzemeCikisFormuWorkflow'),
        mobilePage: null,
      },
      leave: {
        flowLink: 'Leave.aspx',
        component: <LeaveScreen />,
        schemas: {}, // TODO: Add leave schema when it's created,
        defaultValues: {
          WorkflowDefId: 1313,
          CreatedBy: activeUserId,
          Created: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
          LastUpdatedBy: activeUserId,
          LastUpdated: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
        },
        name: t('leaveWorkflow'),
        mobilePage: null,
      },
      stationery: {
        flowLink: 'StationeryRequest.aspx',
        component: <StationeryRequestScreen />,
        schemas: stationeryRequestSchema,
        defaultValues: {
          WorkflowDefId: null, // Will be set based on workflow definition,
          CreatedBy: activeUserId,
          Created: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
          LastUpdatedBy: activeUserId,
          LastUpdated: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
          RequestDate: dayjs().format('YYYY-MM-DD'),
          RequestingDepartment: null,
          RequestingPerson: '',
          RequestReason: '',
          DeliveryDate: null,
          DeliveryType: null,
          Notes: '',
          TotalAmount: 0,
          StationeryRequestDetailDetails: [],
          StationeryAddressDetailRequestDetails: [],
        },
        name: t('stationeryRequestWorkflow'),
        mobilePage: null,
      },
    },
  }

  const getWorkflow = (name: string): WorkflowComponentConfig | null => {
    return workflowComponents[name] ?? null
  }

  const getAllWorkflows = (): Record<string, WorkflowComponentConfig> => {
    return workflowComponents
  }

  const registerWorkflow = (name: string, config: WorkflowComponentConfig): void => {
    workflowComponents[name] = config
  }

  return {
    getWorkflow,
    getAllWorkflows,
    registerWorkflow,
  }

}
        </StationeryRequestScreen>
        </LeaveScreen>
        </MalzemeCikisFormuScreen>
      </MonitoringRequestScreen>
      </JumpToStateScreen>
      </ContractRequestScreen>
      </BiFikrimVarScreen>
      </DelegationScreen>