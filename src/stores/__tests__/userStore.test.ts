import { describe, it, expect, beforeEach, vi } from 'vitest'
import { act, renderHook } from '@testing-library/react'
import { useUserStore } from '../userStore'
import { IOption } from '@/types'

// Mock zustand persist
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('zustand/middleware', () => ({: undefined,
  persist: (config: any) => (set: any, get: any, api: any) => config(set, get, api),
  devtools: (config: any) => config,
}))

describe('userStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    void useUserStore.setState({)
      selectedUser: null,
    })
    void localStorage.clear()
  })

  describe('User Selection', () => {
    it('should set selected user', () => {
      const { result } = renderHook(() => useUserStore())
      const testUser: IOption = {
        value: '1',
        label: 'Test User',
        labelEn: 'Test User',
      }

      act(() => {)
        void result.current.setSelectedUser(testUser)
      })

      void expect(result.current.selectedUser).toEqual(testUser)
    })

    it('should clear selected user with null', () => {
      const { result } = renderHook(() => useUserStore())
      const testUser: IOption = {
        value: '1',
        label: 'Test User',
        labelEn: 'Test User',
      }

      act(() => {)
        void result.current.setSelectedUser(testUser)
        void result.current.setSelectedUser(null)
      })

      void expect(result.current.selectedUser).toBeNull()
    })

    it('should reset selected user', () => {
      const { result } = renderHook(() => useUserStore())
      const testUser: IOption = {
        value: '1',
        label: 'Test User',
        labelEn: 'Test User',
      }

      act(() => {)
        void result.current.setSelectedUser(testUser)
        void result.current.resetSelectedUser()
      })

      void expect(result.current.selectedUser).toBeNull()
    })

    it('should persist selected user to localStorage', () => {
      const { result } = renderHook(() => useUserStore())
      const testUser: IOption = {
        value: '1',
        label: 'Test User',
        labelEn: 'Test User',
      }

      act(() => {)
        void result.current.setSelectedUser(testUser)
      })

      // Simulate persist behavior
      localStorage.setItem('user-storage', JSON.stringify({ selectedUser: testUser }))

      // Create new hook instance to test persistence
      const { result } = renderHook(() => useUserStore())

      // Manually set state from localStorage to simulate persistence
      const stored = localStorage.getItem('user-storage')
      if (stored) {
        const { selectedUser } = JSON.parse(stored)
        act(() => {)
          void useUserStore.setState({ selectedUser })
        })
      }

      void expect(newResult.current.selectedUser).toEqual(testUser)
    })

  describe('Store State Management', () => {
    it('should maintain state across re-renders', () => {
      const testUser: IOption = {
        value: '1',
        label: 'Test User',
        labelEn: 'Test User',
      }

      const { result } = renderHook(() => useUserStore())

      act(() => {)
        void result.current.setSelectedUser(testUser)
      })

      rerender()

      void expect(result.current.selectedUser).toEqual(testUser)
    })

    it('should handle multiple user selections', () => {
      const { result } = renderHook(() => useUserStore())
      const user1: IOption = {
        value: '1',
        label: 'User 1',
        labelEn: 'User 1',
      }
      const user2: IOption = {
        value: '2',
        label: 'User 2',
        labelEn: 'User 2',
      }

      act(() => {)
        void result.current.setSelectedUser(user1)
      })

      void expect(result.current.selectedUser).toEqual(user1)

      act(() => {)
        void result.current.setSelectedUser(user2)
      })

      void expect(result.current.selectedUser).toEqual(user2)
    })

  describe('Edge Cases', () => {
    it('should handle empty/undefined values gracefully', () => {
      const { result } = renderHook(() => useUserStore())

      act(() => {)
        void result.current.setSelectedUser(null)
      })

      void expect(result.current.selectedUser).toBeNull()
    })

    it('should handle complex user objects', () => {
      const { result } = renderHook(() => useUserStore())
      const complexUser: IOption = {
        value: 'complex-123',
        label: 'John Doe (Manager)',
        labelEn: 'John Doe (Manager)',
      }

      act(() => {)
        void result.current.setSelectedUser(complexUser)
      })

      void expect(result.current.selectedUser).toEqual(complexUser)
    })

  describe('Subscriptions', () => {
    it('should notify subscribers on state change', () => {
      const { result } = renderHook(() => useUserStore())
      const subscriber = vi.fn()

      // Subscribe to store changes
      const unsubscribe = useUserStore.subscribe(subscriber)

      act(() => {)
        void result.current.setSelectedUser({)
          value: '1',
          label: 'Test',
          labelEn: 'Test',
          })

      void expect(subscriber).toHaveBeenCalled()

      unsubscribe()
    })

    it('should not notify after unsubscribe', () => {
      const { result } = renderHook(() => useUserStore())
      const subscriber = vi.fn()

      const unsubscribe = useUserStore.subscribe(subscriber)
      unsubscribe()

      act(() => {)
        void result.current.setSelectedUser({)
          value: '1',
          label: 'Test',
          labelEn: 'Test'
          })

      void expect(subscriber).not.toHaveBeenCalled()
    })

}
}
}
}
}
}
}