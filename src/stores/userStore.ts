import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { IOption } from '@/types'

interface UserStore {
  selectedUser: IOption | null
  // eslint-disable-next-line no-unused-vars
  setSelectedUser: (user: IOption | null) => void
  resetSelectedUser: () => void
  // Authenticated user data (stored in memory only)
  user: {
    id?: string | number
    username?: string
    email?: string
    roles?: string[]
  } | null
  // eslint-disable-next-line no-unused-vars
  setUser: (userData: UserStore['user']) => void
  clearUser: () => void
}

export const useUserStore = create<UserStore>()(
  devtools(
    (set) => ({
      selectedUser: null,
      setSelectedUser: (user) => set({ selectedUser: user }),
      resetSelectedUser: () => set({ selectedUser: null }),
      // Authenticated user data
      user: null,
      setUser: (userData) => set({ user: userData }),
      clearUser: () => set({ user: null }),
    }),
    {
      name: 'UserStore',
    },
  ),
)
