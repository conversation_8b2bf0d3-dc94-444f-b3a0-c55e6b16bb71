import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { I18nextProvider } from 'react-i18next'
import { MultiSelectFilter, TextFilter, NumberFilter, DateF<PERSON>er, DateRangeFilter } from '../MultiSelectFilter'
import { FilterValueRange } from '@/types'
import i18n from '@/i18n'

// Mock the wface components
vi.mock('wface', () => ({
  WBox: ({ children, ...props }: Record<string, unknown> & { children?: React.ReactNode }) => (
    <div data-testid="wbox" {...props}>
      {children}
    </div>
  ),
}))

// Mock the form components
vi.mock('@/components/formElements', () => ({
  SelectBox: ({
    value,
    onChange,
    options,
    multiple,
    label,
    ...props
  }: Record<string, unknown> & {
    value?: { value?: string } | { value?: string }[]
    onChange?: (value: unknown) => void
    options?: { value: string; label: string }[]
    multiple?: boolean
    label?: string
  }) => (
    <div data-testid="select-box" data-label={label}>
      <select
        value={multiple ? undefined : (value?.value ?? '')}
        onChange={(e) => {
          if (multiple) {
            const selectedOptions = Array.from(e.target.selectedOptions, (option: HTMLOptionElement) =>
              options?.find((opt) => opt.value === option.value),
            ).filter(Boolean)
            onChange?.(selectedOptions)
          } else {
            const selectedOption = options?.find((opt) => opt.value === e.target.value)
            onChange?.(selectedOption)
          }
        }}
        multiple={multiple}
        {...props}
      >
        {options?.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  ),
}))

vi.mock('@/components/formElements/DigiTextField/DigiTextField', () => ({
  DigiTextField: ({
    value,
    onChange,
    type,
    label,
    fullWidth,
    ...props
  }: Record<string, unknown> & {
    value?: string
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
    type?: string
    label?: string
    fullWidth?: boolean
  }) => (
    <div data-testid="digi-textfield" data-label={label}>
      <input type={type ?? 'text'} value={value} onChange={(e) => onChange?.(e)} data-full-width={fullWidth} {...props} />
    </div>
  ),
}))

const renderWithI18n = (component: React.ReactElement) => {
  return render(<I18nextProvider i18n={i18n}>{component}</I18nextProvider>)
}

describe('TextFilter', () => {
  const mockOnChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders text input', () => {
    renderWithI18n(<TextFilter value="" onChange={mockOnChange} />)

    const textField = screen.getByTestId('digi-textfield')
    const input = textField.querySelector('input')

    expect(input).toBeInTheDocument()
    expect(input?.type).toBe('text')
  })

  it('displays current value', () => {
    renderWithI18n(<TextFilter value="test value" onChange={mockOnChange} />)

    const textField = screen.getByTestId('digi-textfield')
    const input = textField.querySelector('input')

    expect(input?.value).toBe('test value')
  })

  it('handles value change', () => {
    renderWithI18n(<TextFilter value="" onChange={mockOnChange} />)

    const textField = screen.getByTestId('digi-textfield')
    const input = textField.querySelector('input')

    fireEvent.change(input!, { target: { value: 'new value' } })

    expect(mockOnChange).toHaveBeenCalledWith('new value')
  })

  it('handles null value', () => {
    renderWithI18n(<TextFilter value={null} onChange={mockOnChange} />)

    const textField = screen.getByTestId('digi-textfield')
    const input = textField.querySelector('input')

    expect(input?.value).toBe('')
  })

  it('handles undefined value', () => {
    renderWithI18n(<TextFilter value={undefined} onChange={mockOnChange} />)

    const textField = screen.getByTestId('digi-textfield')
    const input = textField.querySelector('input')

    expect(input?.value).toBe('')
  })

  it('has fullWidth prop', () => {
    renderWithI18n(<TextFilter value="" onChange={mockOnChange} />)

    const textField = screen.getByTestId('digi-textfield')
    const input = textField.querySelector('input')

    expect(input?.getAttribute('data-full-width')).toBe('true')
  })

  describe('NumberFilter', () => {
    const mockOnChange = vi.fn()

    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('renders number input', () => {
      renderWithI18n(<NumberFilter value={0} onChange={mockOnChange} />)

      const textField = screen.getByTestId('digi-textfield')
      const input = textField.querySelector('input')

      expect(input).toBeInTheDocument()
      expect(input?.type).toBe('number')
    })

    it('displays current numeric value', () => {
      renderWithI18n(<NumberFilter value={42} onChange={mockOnChange} />)

      const textField = screen.getByTestId('digi-textfield')
      const input = textField.querySelector('input')

      expect(input?.value).toBe('42')
    })

    it('handles numeric value change', () => {
      renderWithI18n(<NumberFilter value={0} onChange={mockOnChange} />)

      const textField = screen.getByTestId('digi-textfield')
      const input = textField.querySelector('input')

      fireEvent.change(input!, { target: { value: '123' } })

      expect(mockOnChange).toHaveBeenCalledWith('123')
    })

    it('handles null value', () => {
      renderWithI18n(<NumberFilter value={null} onChange={mockOnChange} />)

      const textField = screen.getByTestId('digi-textfield')
      const input = textField.querySelector('input')

      expect(input?.value).toBe('')
    })

    it('handles decimal values', () => {
      renderWithI18n(<NumberFilter value={3.14} onChange={mockOnChange} />)

      const textField = screen.getByTestId('digi-textfield')
      const input = textField.querySelector('input')

      expect(input?.value).toBe('3.14')
    })

    it('handles negative values', () => {
      renderWithI18n(<NumberFilter value={-10} onChange={mockOnChange} />)

      const textField = screen.getByTestId('digi-textfield')
      const input = textField.querySelector('input')

      expect(input?.value).toBe('-10')
    })

    describe('DateFilter', () => {
      const mockOnChange = vi.fn()

      beforeEach(() => {
        vi.clearAllMocks()
      })

      it('renders date input', () => {
        renderWithI18n(<DateFilter value="" onChange={mockOnChange} />)

        const textField = screen.getByTestId('digi-textfield')
        const input = textField.querySelector('input')

        expect(input).toBeInTheDocument()
        expect(input?.type).toBe('date')
      })

      it('displays current date value', () => {
        renderWithI18n(<DateFilter value="2023-01-01" onChange={mockOnChange} />)

        const textField = screen.getByTestId('digi-textfield')
        const input = textField.querySelector('input')

        expect(input?.value).toBe('2023-01-01')
      })

      it('handles date value change', () => {
        renderWithI18n(<DateFilter value="" onChange={mockOnChange} />)

        const textField = screen.getByTestId('digi-textfield')
        const input = textField.querySelector('input')

        fireEvent.change(input!, { target: { value: '2023-12-25' } })

        expect(mockOnChange).toHaveBeenCalledWith('2023-12-25')
      })

      it('handles null value', () => {
        renderWithI18n(<DateFilter value={null} onChange={mockOnChange} />)

        const textField = screen.getByTestId('digi-textfield')
        const input = textField.querySelector('input')

        expect(input?.value).toBe('')
      })

      it('handles empty string value', () => {
        renderWithI18n(<DateFilter value="" onChange={mockOnChange} />)

        const textField = screen.getByTestId('digi-textfield')
        const input = textField.querySelector('input')

        expect(input?.value).toBe('')
      })

      describe('DateRangeFilter', () => {
        const mockOnChange = vi.fn()

        beforeEach(() => {
          vi.clearAllMocks()
        })

        it('renders two date inputs', () => {
          const value: FilterValueRange = { start: '', end: '' }
          renderWithI18n(<DateRangeFilter value={value} onChange={mockOnChange} />)

          const textFields = screen.getAllByTestId('digi-textfield')

          expect(textFields).toHaveLength(2)

          const startInput = textFields[0].querySelector('input')
          const endInput = textFields[1].querySelector('input')

          expect(startInput?.type).toBe('date')
          expect(endInput?.type).toBe('date')
        })

        it('displays current range values', () => {
          const value: FilterValueRange = { start: '2023-01-01', end: '2023-12-31' }
          renderWithI18n(<DateRangeFilter value={value} onChange={mockOnChange} />)

          const textFields = screen.getAllByTestId('digi-textfield')
          const startInput = textFields[0].querySelector('input')
          const endInput = textFields[1].querySelector('input')

          expect(startInput?.value).toBe('2023-01-01')
          expect(endInput?.value).toBe('2023-12-31')
        })

        it('handles start date change', () => {
          const value: FilterValueRange = { start: '', end: '2023-12-31' }
          renderWithI18n(<DateRangeFilter value={value} onChange={mockOnChange} />)

          const textFields = screen.getAllByTestId('digi-textfield')
          const startInput = textFields[0].querySelector('input')

          fireEvent.change(startInput!, { target: { value: '2023-01-01' } })

          expect(mockOnChange).toHaveBeenCalledWith({ start: '2023-01-01', end: '2023-12-31' })
        })

        it('handles end date change', () => {
          const value: FilterValueRange = { start: '2023-01-01', end: '' }
          renderWithI18n(<DateRangeFilter value={value} onChange={mockOnChange} />)

          const textFields = screen.getAllByTestId('digi-textfield')
          const endInput = textFields[1].querySelector('input')

          fireEvent.change(endInput!, { target: { value: '2023-12-31' } })

          expect(mockOnChange).toHaveBeenCalledWith({ start: '2023-01-01', end: '2023-12-31' })
        })

        it('handles null value', () => {
          renderWithI18n(<DateRangeFilter value={null} onChange={mockOnChange} />)

          const textFields = screen.getAllByTestId('digi-textfield')
          const startInput = textFields[0].querySelector('input')
          const endInput = textFields[1].querySelector('input')

          expect(startInput?.value).toBe('')
          expect(endInput?.value).toBe('')
        })

        it('handles undefined value', () => {
          renderWithI18n(<DateRangeFilter value={undefined} onChange={mockOnChange} />)

          const textFields = screen.getAllByTestId('digi-textfield')
          const startInput = textFields[0].querySelector('input')
          const endInput = textFields[1].querySelector('input')

          expect(startInput?.value).toBe('')
          expect(endInput?.value).toBe('')
        })

        it('has proper labels', () => {
          const value: FilterValueRange = { start: '', end: '' }
          renderWithI18n(<DateRangeFilter value={value} onChange={mockOnChange} />)

          const textFields = screen.getAllByTestId('digi-textfield')

          expect(textFields[0]).toHaveAttribute('data-label', 'startDate')
          expect(textFields[1]).toHaveAttribute('data-label', 'endDate')
        })

        it('preserves existing values when updating one field', () => {
          const value: FilterValueRange = { start: '2023-01-01', end: '2023-06-30' }
          renderWithI18n(<DateRangeFilter value={value} onChange={mockOnChange} />)

          const textFields = screen.getAllByTestId('digi-textfield')
          const endInput = textFields[1].querySelector('input')

          fireEvent.change(endInput!, { target: { value: '2023-12-31' } })

          expect(mockOnChange).toHaveBeenCalledWith({ start: '2023-01-01', end: '2023-12-31' })
        })

        describe('MultiSelectFilter', () => {
          const mockOnChange = vi.fn()
          const mockColumnConfig = {
            uniqueValues: ['Apple', 'Banana', 'Cherry', 'Date'],
          }

          beforeEach(() => {
            vi.clearAllMocks()
          })

          it('renders select box with multiple option', () => {
            renderWithI18n(<MultiSelectFilter value={[]} onChange={mockOnChange} columnConfig={mockColumnConfig} />)

            const selectBox = screen.getByTestId('select-box')
            const select = selectBox.querySelector('select')

            expect(select).toBeInTheDocument()
            expect(select?.multiple).toBe(true)
          })

          it('generates options from columnConfig uniqueValues', () => {
            renderWithI18n(<MultiSelectFilter value={[]} onChange={mockOnChange} columnConfig={mockColumnConfig} />)

            const selectBox = screen.getByTestId('select-box')
            const options = selectBox.querySelectorAll('option')

            expect(options).toHaveLength(4)
            expect(options[0].value).toBe('Apple')
            expect(options[0].textContent).toBe('Apple')
            expect(options[1].value).toBe('Banana')
            expect(options[1].textContent).toBe('Banana')
            expect(options[2].value).toBe('Cherry')
            expect(options[2].textContent).toBe('Cherry')
            expect(options[3].value).toBe('Date')
            expect(options[3].textContent).toBe('Date')
          })

          it('handles value selection', () => {
            renderWithI18n(<MultiSelectFilter value={[]} onChange={mockOnChange} columnConfig={mockColumnConfig} />)

            const selectBox = screen.getByTestId('select-box')
            const select = selectBox.querySelector('select')

            // Simulate selecting multiple options
            fireEvent.change(select!, {
              target: {
                selectedOptions: [{ value: 'Apple' }, { value: 'Cherry' }],
              },
            })

            expect(mockOnChange).toHaveBeenCalledWith([
              { value: 'Apple', label: 'Apple', labelEn: 'Apple' },
              { value: 'Cherry', label: 'Cherry', labelEn: 'Cherry' },
            ])
          })

          it('handles current value display', () => {
            const currentValue = [
              { value: 'Apple', label: 'Apple', labelEn: 'Apple' },
              { value: 'Banana', label: 'Banana', labelEn: 'Banana' },
            ]

            renderWithI18n(<MultiSelectFilter value={currentValue} onChange={mockOnChange} columnConfig={mockColumnConfig} />)

            // The value is handled by the SelectBox component
            const selectBox = screen.getByTestId('select-box')
            expect(selectBox).toBeInTheDocument()
          })

          it('handles null value', () => {
            renderWithI18n(<MultiSelectFilter value={null} onChange={mockOnChange} columnConfig={mockColumnConfig} />)

            const selectBox = screen.getByTestId('select-box')
            const select = selectBox.querySelector('select')

            expect(select).toBeInTheDocument()
          })

          it('handles undefined value', () => {
            renderWithI18n(<MultiSelectFilter value={undefined} onChange={mockOnChange} columnConfig={mockColumnConfig} />)

            const selectBox = screen.getByTestId('select-box')
            const select = selectBox.querySelector('select')

            expect(select).toBeInTheDocument()
          })

          it('handles empty columnConfig', () => {
            const emptyColumnConfig = { uniqueValues: [] }

            renderWithI18n(<MultiSelectFilter value={[]} onChange={mockOnChange} columnConfig={emptyColumnConfig} />)

            const selectBox = screen.getByTestId('select-box')
            const options = selectBox.querySelectorAll('option')

            expect(options).toHaveLength(0)
          })

          it('converts values to strings for labels', () => {
            const numericColumnConfig = {
              uniqueValues: [1, 2, 3, 4.5],
            }

            renderWithI18n(<MultiSelectFilter value={[]} onChange={mockOnChange} columnConfig={numericColumnConfig} />)

            const selectBox = screen.getByTestId('select-box')
            const options = selectBox.querySelectorAll('option')

            expect(options[0].textContent).toBe('1')
            expect(options[1].textContent).toBe('2')
            expect(options[2].textContent).toBe('3')
            expect(options[3].textContent).toBe('4.5')
          })

          it('handles mixed data types in uniqueValues', () => {
            const mixedColumnConfig = {
              uniqueValues: ['string', 123, true, null, undefined],
            }

            renderWithI18n(<MultiSelectFilter value={[]} onChange={mockOnChange} columnConfig={mixedColumnConfig} />)

            const selectBox = screen.getByTestId('select-box')
            const options = selectBox.querySelectorAll('option')

            expect(options[0].textContent).toBe('string')
            expect(options[1].textContent).toBe('123')
            expect(options[2].textContent).toBe('true')
            expect(options[3].textContent).toBe('null')
            expect(options[4].textContent).toBe('undefined')
          })

          it('has fullWidth prop', () => {
            renderWithI18n(<MultiSelectFilter value={[]} onChange={mockOnChange} columnConfig={mockColumnConfig} />)

            const selectBox = screen.getByTestId('select-box')
            // The fullWidth prop is passed to SelectBox, we can verify it exists
            expect(selectBox).toBeInTheDocument()
          })

          it('has empty label', () => {
            renderWithI18n(<MultiSelectFilter value={[]} onChange={mockOnChange} columnConfig={mockColumnConfig} />)

            const selectBox = screen.getByTestId('select-box')
            expect(selectBox).toHaveAttribute('data-label', '')
          })

          it('handles large number of options', () => {
            const largeColumnConfig = {
              uniqueValues: Array.from({ length: 1000 }, (_, i) => `Option ${i + 1}`),
            }

            renderWithI18n(<MultiSelectFilter value={[]} onChange={mockOnChange} columnConfig={largeColumnConfig} />)

            const selectBox = screen.getByTestId('select-box')
            const options = selectBox.querySelectorAll('option')

            expect(options).toHaveLength(1000)
            expect(options[0].textContent).toBe('Option 1')
            expect(options[999].textContent).toBe('Option 1000')
          })

          describe('Option mapping', () => {
            it('creates correct IOption structure', () => {
              const testValues = ['test1', 'test2']
              const columnConfig = { uniqueValues: testValues }

              renderWithI18n(<MultiSelectFilter value={[]} onChange={mockOnChange} columnConfig={columnConfig} />)

              // The mapping happens internally, we verify through the rendered options
              const selectBox = screen.getByTestId('select-box')
              const options = selectBox.querySelectorAll('option')

              expect(options[0].value).toBe('test1')
              expect(options[0].textContent).toBe('test1')
              expect(options[1].value).toBe('test2')
              expect(options[1].textContent).toBe('test2')
            })

            it('handles special characters in values', () => {
              const specialValues = ['<script>', '&amp;', '"quotes"', "'single'"]
              const columnConfig = { uniqueValues: specialValues }

              renderWithI18n(<MultiSelectFilter value={[]} onChange={mockOnChange} columnConfig={columnConfig} />)

              const selectBox = screen.getByTestId('select-box')
              const options = selectBox.querySelectorAll('option')

              expect(options).toHaveLength(4)
              // Values should be properly handled by React
            })
          })
        })
      })
    })
  })
})
