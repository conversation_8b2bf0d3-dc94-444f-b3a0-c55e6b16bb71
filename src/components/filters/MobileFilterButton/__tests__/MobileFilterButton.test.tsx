import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { I18nextProvider } from 'react-i18next'
import { MobileFilterButton, FilterState } from '../MobileFilterButton'
import { Column } from '@/types/WFaceTypes'
import i18n from '@/i18n'

// Mock the wface components
vi.mock('wface', () => ({
  WBox: ({ children, ...props }: Record<string, unknown> & { children?: React.ReactNode }) => (
    <div data-testid="wbox" {...props}>
      {children}
    </div>
  ),
  WButton: ({ children, onClick, ...props }: Record<string, unknown> & { children?: React.ReactNode; onClick?: () => void }) => (
    <button data-testid="wbutton" onClick={onClick} {...props}>
      {children}
    </button>
  ),
  WDialog: ({ children, open, onClose, ...props }: Record<string, unknown> & { children?: React.ReactNode; open?: boolean; onClose?: () => void }) =>
    open ? (
      <div data-testid="wdialog" {...props}>
        <div onClick={onClose} data-testid="dialog-backdrop"></div>
        {children}
      </div>
    ) : null,
  WTypography: ({ children, variant, ...props }: Record<string, unknown> & { children?: React.ReactNode; variant?: string }) => (
    <div data-testid={`wtypography-${variant ?? 'body1'}`} {...props}>
      {children}
    </div>
  ),
}))

// Mock the form components
vi.mock('@/components/formElements', () => ({
  SelectBox: ({
    value,
    onChange,
    options,
    multiple,
    label,
    ...props
  }: Record<string, unknown> & {
    value?: { value?: string } | { value?: string }[]
    // eslint-disable-next-line no-unused-vars
    onChange?: (value: unknown) => void
    options?: { value: string; label: string }[]
    multiple?: boolean
    label?: string
  }) => (
    <div data-testid="select-box" data-label={label}>
      <select
        value={multiple ? undefined : ((value as { value?: string })?.value ?? '')}
        onChange={(e) => {
          if (multiple) {
            const selectedOptions = Array.from(e.target.selectedOptions, (option: HTMLOptionElement) =>
              options?.find((opt) => opt.value === option.value),
            ).filter(Boolean)
            onChange?.(selectedOptions)
          } else {
            const selectedOption = options?.find((opt) => opt.value === e.target.value)
            onChange?.(selectedOption)
          }
        }}
        multiple={multiple}
        {...props}
      >
        {options?.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  ),
}))

vi.mock('@/components/formElements/DigiTextField/DigiTextField', () => ({
  default: ({
    value,
    onChange,
    type,
    placeholder,
    label,
    InputProps,
    ...props
  }: Record<string, unknown> & {
    value?: string
    // eslint-disable-next-line no-unused-vars
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
    type?: string
    placeholder?: string
    label?: string
    InputProps?: { startAdornment?: React.ReactNode }
  }) => (
    <div data-testid="digi-textfield" data-label={label}>
      {InputProps?.startAdornment && <span data-testid="input-adornment">{InputProps.startAdornment}</span>}
      <input type={type ?? 'text'} value={value} onChange={(e) => onChange?.(e)} placeholder={placeholder} {...props} />
    </div>
  ),
}))

// Mock MUI icons
vi.mock('@mui/icons-material/FilterList', () => ({
  default: (props: Record<string, unknown>) => (
    <div data-testid="filter-list-icon" {...props}>
      FilterList
    </div>
  ),
}))

vi.mock('@mui/icons-material/Close', () => ({
  default: (props: Record<string, unknown> & { onClick?: () => void }) => (
    <div data-testid="close-icon" onClick={props.onClick} {...props}>
      Close
    </div>
  ),
}))

vi.mock('@mui/icons-material/Search', () => ({
  default: (props: Record<string, unknown>) => (
    <div data-testid="search-icon" {...props}>
      Search
    </div>
  ),
}))

// Mock lodash
vi.mock('lodash/uniq', () => ({
  default: (arr: unknown[]) => [...new Set(arr)],
}))

const mockColumns: Column<any>[] = [
  {
    field: 'name',
    title: 'Name',
    hidden: false,
  },
  {
    field: 'age',
    title: 'Age',
    hidden: false,
    dateSetting: { type: 'number' },
  },
  {
    field: 'birthDate',
    title: 'Birth Date',
    hidden: false,
    dateSetting: { type: 'date' },
  },
  {
    field: 'actions',
    title: 'Actions',
    hidden: false,
  },
  {
    field: 'hiddenField',
    title: 'Hidden',
    hidden: true,
  },
]

const mockData = [
  { name: 'John Doe', age: 30, birthDate: '1993-01-01' },
  { name: 'Jane Smith', age: 25, birthDate: '1998-05-15' },
  { name: 'Bob Johnson', age: 35, birthDate: '1988-12-20' },
]

const defaultProps = {
  columns: mockColumns,
  data: mockData,
  onApplyFilters: vi.fn(),
  onSearch: vi.fn(),
  onSort: vi.fn(),
  title: 'Test Filters',
  currentFilters: [],
}

const renderWithI18n = (component: React.ReactElement) => {
  return render(<I18nextProvider i18n={i18n}>{component}</I18nextProvider>)
}

describe('MobileFilterButton', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('Rendering', () => {
    it('renders filter button', () => {
      renderWithI18n(<MobileFilterButton {...defaultProps} />)

      void expect(screen.getByTestId('filter-list-icon')).toBeInTheDocument()
      void expect(screen.getByTestId('wbutton')).toBeInTheDocument()
    })

    it('shows filter count when filters are active', () => {
      const activeFilters: FilterState[] = [
        { columnId: 'name', type: 'contains', value: 'John' },
        { columnId: 'age', type: 'equals', value: 30 },
      ]

      renderWithI18n(<MobileFilterButton {...defaultProps} currentFilters={activeFilters} />)

      void expect(screen.getByText('(2)')).toBeInTheDocument()
    })

    it('opens dialog when button is clicked', async () => {
      renderWithI18n(<MobileFilterButton {...defaultProps} />)

      const filterButton = screen.getByTestId('wbutton')
      void fireEvent.click(filterButton)

      await waitFor(() => {
        void expect(screen.getByTestId('wdialog')).toBeInTheDocument()
      })

      it('displays custom title', async () => {
        renderWithI18n(<MobileFilterButton {...defaultProps} title="Custom Title" />)

        const filterButton = screen.getByTestId('wbutton')
        void fireEvent.click(filterButton)

        await waitFor(() => {
          expect(screen.getByText('Custom Title')).toBeInTheDocument()
        })

        it('closes dialog when close button is clicked', async () => {
          renderWithI18n(<MobileFilterButton {...defaultProps} />)

          const filterButton = screen.getByTestId('wbutton')
          void fireEvent.click(filterButton)

          await waitFor(() => {
            void expect(screen.getByTestId('wdialog')).toBeInTheDocument()
          })

          const closeButton = screen.getByTestId('close-icon')
          void fireEvent.click(closeButton)

          // Dialog should close (component state change)
        })

        describe('Column processing', () => {
          it('filters out hidden columns and action columns', async () => {
            renderWithI18n(<MobileFilterButton {...defaultProps} />)

            const filterButton = screen.getByTestId('wbutton')
            void fireEvent.click(filterButton)

            await waitFor(() => {
              const columnSelect = screen.getByTestId('select-box')
              const options = columnSelect.querySelectorAll('option')

              // Should not include hidden field or actions
              const optionValues = Array.from(options).map((_option) => _option.getAttribute('value'))
              void expect(optionValues).not.toContain('hiddenField')
              void expect(optionValues).not.toContain('actions')
              void expect(optionValues).toContain('name')
              void expect(optionValues).toContain('age')
              void expect(optionValues).toContain('birthDate')
            })

            it('handles React element column titles', async () => {
              const columnsWithElements: Column<any>[] = [
                {
                  field: 'name',
                  title: <span>Name Element</span>,
                  hidden: false,
                },
                {
                  field: 'complex',
                  title: (
                    <div>
                      <span>Complex</span>
                      <span>Title</span>
                    </div>
                  ),
                  hidden: false,
                },
              ]

              renderWithI18n(<MobileFilterButton {...defaultProps} columns={columnsWithElements} />)

              const filterButton = screen.getByTestId('wbutton')
              void fireEvent.click(filterButton)

              // Should handle React element titles gracefully
              await waitFor(() => {
                void expect(screen.getByTestId('wdialog')).toBeInTheDocument()
              })

              describe('Search functionality', () => {
                it('handles search input', async () => {
                  renderWithI18n(<MobileFilterButton {...defaultProps} />)

                  const filterButton = screen.getByTestId('wbutton')
                  void fireEvent.click(filterButton)

                  await waitFor(() => {
                    const searchField = screen.getByTestId('digi-textfield')
                    const input = searchField.querySelector('input')

                    void fireEvent.change(input!, { target: { value: 'search term' } })
                  })

                  void expect(defaultProps.onSearch).toHaveBeenCalledWith('search term')
                })

                it('shows search icon', async () => {
                  renderWithI18n(<MobileFilterButton {...defaultProps} />)

                  const filterButton = screen.getByTestId('wbutton')
                  void fireEvent.click(filterButton)

                  await waitFor(() => {
                    void expect(screen.getByTestId('search-icon')).toBeInTheDocument()
                  })

                  describe('Filter management', () => {
                    it('displays active filters', async () => {
                      const activeFilters: FilterState[] = [{ columnId: 'name', type: 'contains', value: 'John' }]

                      renderWithI18n(<MobileFilterButton {...defaultProps} currentFilters={activeFilters} />)

                      const filterButton = screen.getByTestId('wbutton')
                      void fireEvent.click(filterButton)

                      await waitFor(() => {
                        void expect(screen.getByText('activeFilters')).toBeInTheDocument()
                        void expect(screen.getByText('John')).toBeInTheDocument()
                      })

                      it('removes active filter when close is clicked', async () => {
                        const activeFilters: FilterState[] = [{ columnId: 'name', type: 'contains', value: 'John' }]

                        renderWithI18n(<MobileFilterButton {...defaultProps} currentFilters={activeFilters} />)

                        const filterButton = screen.getByTestId('wbutton')
                        void fireEvent.click(filterButton)

                        await waitFor(() => {
                          const closeIcons = screen.getAllByTestId('close-icon')
                          const filterCloseIcon = closeIcons.find((icon) => icon.getAttribute('style')?.includes('cursor: pointer'))
                          if (filterCloseIcon) {
                            void fireEvent.click(filterCloseIcon)
                          }
                        })

                        void expect(defaultProps.onApplyFilters).toHaveBeenCalledWith([])
                      })

                      it('creates new filter', async () => {
                        renderWithI18n(<MobileFilterButton {...defaultProps} />)

                        const filterButton = screen.getByTestId('wbutton')
                        void fireEvent.click(filterButton)

                        await waitFor(() => {
                          // Select column
                          const selectBoxes = screen.getAllByTestId('select-box')
                          const columnSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'selectColumn')
                          const columnSelectElement = columnSelect?.querySelector('select')
                          void fireEvent.change(columnSelectElement!, { target: { value: 'name' } })
                        })

                        await waitFor(() => {
                          // Select filter type
                          const selectBoxes = screen.getAllByTestId('select-box')
                          const typeSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'filterType')
                          const typeSelectElement = typeSelect?.querySelector('select')
                          void fireEvent.change(typeSelectElement!, { target: { value: 'contains' } })
                        })

                        await waitFor(() => {
                          // Enter filter value
                          const valueFields = screen.getAllByTestId('digi-textfield')
                          const valueField = valueFields.find((field) => field.getAttribute('data-label') === 'value')
                          const input = valueField?.querySelector('input')
                          void fireEvent.change(input!, { target: { value: 'test value' } })
                        })

                        // Apply filter
                        const applyButton = screen.getByText('apply')
                        void fireEvent.click(applyButton)

                        void expect(defaultProps.onApplyFilters).toHaveBeenCalledWith([{ columnId: 'name', type: 'contains', value: 'test value' }])
                      })

                      describe('Sort functionality', () => {
                        it('handles sort configuration', async () => {
                          renderWithI18n(<MobileFilterButton {...defaultProps} />)

                          const filterButton = screen.getByTestId('wbutton')
                          void fireEvent.click(filterButton)

                          await waitFor(() => {
                            // Select sort column
                            const selectBoxes = screen.getAllByTestId('select-box')
                            const sortColumnSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'selectSortColumn')
                            const sortColumnElement = sortColumnSelect?.querySelector('select')
                            void fireEvent.change(sortColumnElement!, { target: { value: 'name' } })
                          })

                          await waitFor(() => {
                            // Select sort direction
                            const selectBoxes = screen.getAllByTestId('select-box')
                            const sortDirectionSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'sortDirection')
                            const sortDirectionElement = sortDirectionSelect?.querySelector('select')
                            void fireEvent.change(sortDirectionElement!, { target: { value: 'asc' } })
                          })

                          // Apply sort
                          const applyButton = screen.getByText('apply')
                          void fireEvent.click(applyButton)

                          void expect(defaultProps.onSort).toHaveBeenCalledWith({
                            columnId: 'name',
                            direction: 'asc',
                          })

                          it('clears sort when no column is selected', async () => {
                            renderWithI18n(<MobileFilterButton {...defaultProps} />)

                            const filterButton = screen.getByTestId('wbutton')
                            void fireEvent.click(filterButton)

                            await waitFor(() => {
                              const applyButton = screen.getByText('apply')
                              void fireEvent.click(applyButton)
                            })

                            void expect(defaultProps.onSort).toHaveBeenCalledWith({
                              columnId: '',
                              direction: null,
                            })

                            describe('Filter types by column type', () => {
                              it('shows string filter types for string columns', async () => {
                                renderWithI18n(<MobileFilterButton {...defaultProps} />)

                                const filterButton = screen.getByTestId('wbutton')
                                void fireEvent.click(filterButton)

                                await waitFor(() => {
                                  // Select string column
                                  const selectBoxes = screen.getAllByTestId('select-box')
                                  const columnSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'selectColumn')
                                  const columnSelectElement = columnSelect?.querySelector('select')
                                  void fireEvent.change(columnSelectElement!, { target: { value: 'name' } })
                                })

                                await waitFor(() => {
                                  // Check filter types
                                  const selectBoxes = screen.getAllByTestId('select-box')
                                  const typeSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'filterType')
                                  const options = typeSelect?.querySelectorAll('option')
                                  const optionValues = Array.from(options ?? []).map((_option) => _option.getAttribute('value'))

                                  void expect(optionValues).toContain('contains')
                                  void expect(optionValues).toContain('equals')
                                  void expect(optionValues).toContain('startsWith')
                                  void expect(optionValues).toContain('endsWith')
                                })

                                it('shows number filter types for number columns', async () => {
                                  renderWithI18n(<MobileFilterButton {...defaultProps} />)

                                  const filterButton = screen.getByTestId('wbutton')
                                  void fireEvent.click(filterButton)

                                  await waitFor(() => {
                                    // Select number column
                                    const selectBoxes = screen.getAllByTestId('select-box')
                                    const columnSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'selectColumn')
                                    const columnSelectElement = columnSelect?.querySelector('select')
                                    void fireEvent.change(columnSelectElement!, { target: { value: 'age' } })
                                  })

                                  await waitFor(() => {
                                    // Check filter types
                                    const selectBoxes = screen.getAllByTestId('select-box')
                                    const typeSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'filterType')
                                    const options = typeSelect?.querySelectorAll('option')
                                    const optionValues = Array.from(options ?? []).map((_option) => _option.getAttribute('value'))

                                    void expect(optionValues).toContain('equals')
                                    void expect(optionValues).toContain('isLessThan')
                                    void expect(optionValues).toContain('isGreaterThan')
                                  })

                                  it('shows date filter types for date columns', async () => {
                                    renderWithI18n(<MobileFilterButton {...defaultProps} />)

                                    const filterButton = screen.getByTestId('wbutton')
                                    void fireEvent.click(filterButton)

                                    await waitFor(() => {
                                      // Select date column
                                      const selectBoxes = screen.getAllByTestId('select-box')
                                      const columnSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'selectColumn')
                                      const columnSelectElement = columnSelect?.querySelector('select')
                                      void fireEvent.change(columnSelectElement!, { target: { value: 'birthDate' } })
                                    })

                                    await waitFor(() => {
                                      // Check filter types
                                      const selectBoxes = screen.getAllByTestId('select-box')
                                      const typeSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'filterType')
                                      const options = typeSelect?.querySelectorAll('option')
                                      const optionValues = Array.from(options ?? []).map((_option) => _option.getAttribute('value'))

                                      void expect(optionValues).toContain('on')
                                      void expect(optionValues).toContain('before')
                                      void expect(optionValues).toContain('after')
                                      void expect(optionValues).toContain('between')
                                    })

                                    describe('MultiSelect filter', () => {
                                      it('handles multiselect filter type', async () => {
                                        renderWithI18n(<MobileFilterButton {...defaultProps} />)

                                        const filterButton = screen.getByTestId('wbutton')
                                        void fireEvent.click(filterButton)

                                        await waitFor(() => {
                                          // Select column
                                          const selectBoxes = screen.getAllByTestId('select-box')
                                          const columnSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'selectColumn')
                                          const columnSelectElement = columnSelect?.querySelector('select')
                                          void fireEvent.change(columnSelectElement!, { target: { value: 'name' } })
                                        })

                                        await waitFor(() => {
                                          // Select multiselect filter type
                                          const selectBoxes = screen.getAllByTestId('select-box')
                                          const typeSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'filterType')
                                          const typeSelectElement = typeSelect?.querySelector('select')
                                          void fireEvent.change(typeSelectElement!, { target: { value: 'selectMultiple' } })
                                        })

                                        await waitFor(() => {
                                          // Should show multiselect options
                                          const selectBoxes = screen.getAllByTestId('select-box')
                                          const valueSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'selectValues')
                                          void expect(valueSelect).toBeInTheDocument()
                                        })

                                        describe('Date filters', () => {
                                          it('handles date between filter', async () => {
                                            renderWithI18n(<MobileFilterButton {...defaultProps} />)

                                            const filterButton = screen.getByTestId('wbutton')
                                            void fireEvent.click(filterButton)

                                            await waitFor(() => {
                                              // Select date column
                                              const selectBoxes = screen.getAllByTestId('select-box')
                                              const columnSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'selectColumn')
                                              const columnSelectElement = columnSelect?.querySelector('select')
                                              void fireEvent.change(columnSelectElement!, { target: { value: 'birthDate' } })
                                            })

                                            await waitFor(() => {
                                              // Select between filter type
                                              const selectBoxes = screen.getAllByTestId('select-box')
                                              const typeSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'filterType')
                                              const typeSelectElement = typeSelect?.querySelector('select')
                                              void fireEvent.change(typeSelectElement!, { target: { value: 'between' } })
                                            })

                                            await waitFor(() => {
                                              // Should show start and end date fields
                                              const dateFields = screen.getAllByTestId('digi-textfield')
                                              const startDateField = dateFields.find((field) => field.getAttribute('data-label') === 'startDate')
                                              const endDateField = dateFields.find((field) => field.getAttribute('data-label') === 'endDate')

                                              void expect(startDateField).toBeInTheDocument()
                                              void expect(endDateField).toBeInTheDocument()
                                            })

                                            it('handles single date filter', async () => {
                                              renderWithI18n(<MobileFilterButton {...defaultProps} />)

                                              const filterButton = screen.getByTestId('wbutton')
                                              void fireEvent.click(filterButton)

                                              await waitFor(() => {
                                                // Select date column
                                                const selectBoxes = screen.getAllByTestId('select-box')
                                                const columnSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'selectColumn')
                                                const columnSelectElement = columnSelect?.querySelector('select')
                                                void fireEvent.change(columnSelectElement!, { target: { value: 'birthDate' } })
                                              })

                                              await waitFor(() => {
                                                // Select single date filter type
                                                const selectBoxes = screen.getAllByTestId('select-box')
                                                const typeSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'filterType')
                                                const typeSelectElement = typeSelect?.querySelector('select')
                                                void fireEvent.change(typeSelectElement!, { target: { value: 'on' } })
                                              })

                                              await waitFor(() => {
                                                // Should show single date field
                                                const dateFields = screen.getAllByTestId('digi-textfield')
                                                const valueField = dateFields.find((field) => field.getAttribute('data-label') === 'value')
                                                const input = valueField?.querySelector('input')

                                                void expect(input?.type).toBe('date')
                                              })

                                              describe('Validation', () => {
                                                it('disables apply button when no changes', async () => {
                                                  renderWithI18n(<MobileFilterButton {...defaultProps} />)

                                                  const filterButton = screen.getByTestId('wbutton')
                                                  void fireEvent.click(filterButton)

                                                  await waitFor(() => {
                                                    const applyButton = screen.getByText('apply')
                                                    void expect(applyButton).toHaveProperty('disabled', true)
                                                  })

                                                  it('enables apply button when valid filter is configured', async () => {
                                                    renderWithI18n(<MobileFilterButton {...defaultProps} />)

                                                    const filterButton = screen.getByTestId('wbutton')
                                                    void fireEvent.click(filterButton)

                                                    await waitFor(() => {
                                                      // Configure valid filter
                                                      const selectBoxes = screen.getAllByTestId('select-box')
                                                      const columnSelect = selectBoxes.find(
                                                        (box) => box.getAttribute('data-label') === 'selectColumn',
                                                      )
                                                      const columnSelectElement = columnSelect?.querySelector('select')
                                                      void fireEvent.change(columnSelectElement!, { target: { value: 'name' } })
                                                    })

                                                    await waitFor(() => {
                                                      const selectBoxes = screen.getAllByTestId('select-box')
                                                      const typeSelect = selectBoxes.find((box) => box.getAttribute('data-label') === 'filterType')
                                                      const typeSelectElement = typeSelect?.querySelector('select')
                                                      void fireEvent.change(typeSelectElement!, { target: { value: 'contains' } })
                                                    })

                                                    await waitFor(() => {
                                                      const valueFields = screen.getAllByTestId('digi-textfield')
                                                      const valueField = valueFields.find((field) => field.getAttribute('data-label') === 'value')
                                                      const input = valueField?.querySelector('input')
                                                      void fireEvent.change(input!, { target: { value: 'test' } })
                                                    })

                                                    await waitFor(() => {
                                                      const applyButton = screen.getByText('apply')
                                                      void expect(applyButton).toHaveProperty('disabled', false)
                                                    })

                                                    describe('Clear functionality', () => {
                                                      it('clears all filters and settings', async () => {
                                                        const activeFilters: FilterState[] = [{ columnId: 'name', type: 'contains', value: 'John' }]

                                                        renderWithI18n(<MobileFilterButton {...defaultProps} currentFilters={activeFilters} />)

                                                        const filterButton = screen.getByTestId('wbutton')
                                                        void fireEvent.click(filterButton)

                                                        await waitFor(() => {
                                                          const clearButton = screen.getByText('clearAll')
                                                          void fireEvent.click(clearButton)
                                                        })

                                                        void expect(defaultProps.onApplyFilters).toHaveBeenCalledWith([])
                                                        void expect(defaultProps.onSearch).toHaveBeenCalledWith('')
                                                        void expect(defaultProps.onSort).toHaveBeenCalledWith({ columnId: '', direction: null })
                                                      })

                                                      describe('Edge cases', () => {
                                                        it('handles empty columns array', () => {
                                                          renderWithI18n(<MobileFilterButton {...defaultProps} columns={[]} />)

                                                          // Should not crash with empty columns
                                                        })

                                                        it('handles empty data array', () => {
                                                          renderWithI18n(<MobileFilterButton {...defaultProps} data={[]} />)

                                                          // Should not crash with empty data
                                                        })

                                                        it('handles columns with no field property', () => {
                                                          const invalidColumns = [{ title: 'Invalid Column' } as any]

                                                          renderWithI18n(<MobileFilterButton {...defaultProps} columns={invalidColumns} />)

                                                          // Should handle invalid columns gracefully
                                                        })
                                                      })
                                                    })
                                                  })
                                                })
                                              })
                                            })
                                          })
                                        })
                                      })
                                    })
                                  })
                                })
                              })
                            })
                          })
                        })
                      })
                    })
                  })
                })
              })
            })
          })
        })
      })
    })
  })
})
