import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { I18nextProvider } from 'react-i18next'
import FilterButton from '../FilterButton'
import i18n from '@/i18n'

// Mock the wface components
vi.mock('wface', () => ({
  WBox: ({ children, ...props }: Record<string, unknown> & { children?: React.ReactNode }) => (
    <div data-testid="wbox" {...props}>
      {children}
    </div>
  ),
  WButton: ({ children, onClick, ...props }: Record<string, unknown> & { children?: React.ReactNode; onClick?: () => void }) => (
    <button data-testid="wbutton" onClick={onClick} {...props}>
      {children}
    </button>
  ),
  WPopover: ({ children, open, onClose, ...props }: Record<string, unknown> & { children?: React.ReactNode; open?: boolean; onClose?: () => void }) =>
    open ? (
      <div data-testid="wpopover" {...props}>
        {children}
      </div>
    ) : null,
  WTextField: ({ value, onChange, ...props }: Record<string, unknown> & { value?: string; onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void }) => 
    <input data-testid="wtextfield" value={value} onChange={(e) => onChange?.(e)} {...props} />,
  WTypography: ({ children, ...props }: Record<string, unknown> & { children?: React.ReactNode }) => (
    <div data-testid="wtypography" {...props}>
      {children}
    </div>
  )
          }))

// Mock the SelectBox component
vi.mock('../../formElements/SelectBox/SelectBox', () => ({
  default: ({ value, onChange, options, multiple, ...props }: Record<string, unknown> & {
    value?: { value?: string } | { value?: string }[];
    onChange?: (value: unknown) => void;
    options?: { value: string; label: string }[];
    multiple?: boolean;
  }) => (
    <select
      data-testid="select-box"
      value={multiple ? undefined : ((value as { value?: string })?.value ?? '')}
      onChange={(e) => {
        if (multiple) {
          const selectedOptions = Array.from(e.target.selectedOptions, (option: HTMLOptionElement) =>
            options?.find((opt) => opt.value === option.value)
          ).filter(Boolean)
          onChange?.(selectedOptions)
        } else {
          const selectedOption = options?.find((opt) => opt.value === e.target.value)
          onChange?.(selectedOption)
        }
      }}
      multiple={multiple}
      {...props}
    >
      {options?.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}))

// Mock MUI icons
vi.mock('@mui/icons-material/FilterList', () => ({
  default: (props: Record<string, unknown>) => (
    <div data-testid="filter-icon" {...props}>
      FilterList
    </div>
  ),
}))

vi.mock('@mui/icons-material/ArrowUpward', () => ({
  default: (props: Record<string, unknown>) => (
    <div data-testid="arrow-up-icon" {...props}>
      ArrowUpward
    </div>
  ),
}))

vi.mock('@mui/icons-material/ArrowDownward', () => ({
  default: (props: Record<string, unknown>) => (
    <div data-testid="arrow-down-icon" {...props}>
      ArrowDownward
    </div>
  ),
}))

vi.mock('@mui/icons-material/SortRounded', () => ({
  default: (props: Record<string, unknown>) => (
    <div data-testid="sort-icon" {...props}>
      SortRounded
    </div>
  ),
}))

// Mock lodash functions
vi.mock('lodash/uniq', () => ({
  default: (arr: unknown[]) => [...new Set(arr)],
}))

vi.mock('lodash/uniqBy', () => ({
  default: (arr: unknown[], key: string) => {
    const seen = new Set()
    return arr.filter((item) => {
      const val = (item as Record<string, unknown>)[key]
      if (seen.has(val)) return false
      seen.add(val)
      return true
    })
  },
}))

// Mock dayjs
vi.mock('dayjs', () => ({
  default: (date?: unknown) => ({
    format: (format: string) => {
      if (!date) return ''
      const d = new Date(date as string)
      if (format === 'DD-MM-YYYY') {
        return `${d.getDate().toString().padStart(2, '0')}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getFullYear()}`
      }
      return String(date)
    },
    isValid: () => (date ? !isNaN(new Date(date as string).getTime()) : false),
    valueOf: () => (date ? new Date(date as string).getTime() : 0),
  }),
}))

const defaultProps = {
  columnId: 'testColumn',
  onApply: vi.fn(),
  onClear: vi.fn(),
  onSort: vi.fn(),
  columnType: 'string',
  currentFilter: null,
  currentSortDirection: null,
  showFilter: true,
  sorting: false,
  data: [],
}

const renderWithI18n = (component: React.ReactElement) => {
  return render(<I18nextProvider i18n={i18n}>{component}</I18nextProvider>)
}

describe('FilterButton', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Rendering', () => {
    it('renders filter button when showFilter is true', () => {
      renderWithI18n(<FilterButton {...defaultProps} />)

      expect(screen.getByTestId('filter-icon')).toBeInTheDocument()
    })

    it('does not render filter button when showFilter is false', () => {
      renderWithI18n(<FilterButton {...defaultProps} showFilter={false} />)

      expect(screen.queryByTestId('filter-icon')).not.toBeInTheDocument()
    })

    it('renders sort button when sorting is enabled', () => {
      renderWithI18n(<FilterButton {...defaultProps} sorting={true} />)

      expect(screen.getByTestId('sort-icon')).toBeInTheDocument()
    })

    it('does not render sort button when sorting is disabled', () => {
      renderWithI18n(<FilterButton {...defaultProps} sorting={false} />)

      expect(screen.queryByTestId('sort-icon')).not.toBeInTheDocument()
    })

    it('shows active filter icon when currentFilter exists', () => {
      const currentFilter = { type: 'contains', value: 'test' }
      renderWithI18n(<FilterButton {...defaultProps} currentFilter={currentFilter} />)

      const filterIcon = screen.getByTestId('filter-icon')
      expect(filterIcon).toBeInTheDocument()
    })

    it('shows appropriate sort icon based on currentSortDirection', () => {
      const { rerender } = renderWithI18n(<FilterButton {...defaultProps} sorting={true} currentSortDirection="asc" />)

      expect(screen.getByTestId('arrow-up-icon')).toBeInTheDocument()

      rerender(
        <I18nextProvider i18n={i18n}>
          <FilterButton {...defaultProps} sorting={true} currentSortDirection="desc" />
        </I18nextProvider>,
      )

      expect(screen.getByTestId('arrow-down-icon')).toBeInTheDocument()

      rerender(
        <I18nextProvider i18n={i18n}>
          <FilterButton {...defaultProps} sorting={true} currentSortDirection={null} />
        </I18nextProvider>,
      )

      expect(screen.getByTestId('sort-icon')).toBeInTheDocument()
    })

  describe('Filter interactions', () => {
    it('opens popover when filter button is clicked', async () => {
      renderWithI18n(<FilterButton {...defaultProps} />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton)

      await waitFor(() => {
        expect(screen.getByTestId('wpopover')).toBeInTheDocument()
      })
    })

    it('closes popover when close is triggered', async () => {
      renderWithI18n(<FilterButton {...defaultProps} />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton)

      await waitFor(() => {
        expect(screen.getByTestId('wpopover')).toBeInTheDocument()
      })

      // Click outside to close (simulate onClose)
      const popover = screen.getByTestId('wpopover')
      fireEvent.click(window.document.body)

      // The popover closing is handled by the component's state
    })

    it('handles filter type change', async () => {
      renderWithI18n(<FilterButton {...defaultProps} />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton)

      await waitFor(() => {
        const selectBox = screen.getByTestId('select-box')
        expect(selectBox).toBeInTheDocument()

        fireEvent.change(selectBox, { target: { value: 'contains' } })
      })
    })

    it('handles apply filter', async () => {
      renderWithI18n(<FilterButton {...defaultProps} />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton)

      await waitFor(() => {
        const selectBox = screen.getByTestId('select-box')
        fireEvent.change(selectBox, { target: { value: 'contains' } })
      })

      await waitFor(() => {
        const textField = screen.getByTestId('wtextfield')
        fireEvent.change(textField, { target: { value: 'test value' } })
      })

      const applyButton = screen.getByText('apply')
      fireEvent.click(applyButton)

      expect(defaultProps.onApply).toHaveBeenCalledWith({
        type: 'contains',
        value: 'test value',
      })
    })

    it('handles clear filter', async () => {
      renderWithI18n(<FilterButton {...defaultProps} />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton)

      await waitFor(() => {
        const clearButton = screen.getByText('clear')
        fireEvent.click(clearButton)
      })

      expect(defaultProps.onClear).toHaveBeenCalled()
    })

  describe('Sort interactions', () => {
    it('handles sort button click progression', () => {
      renderWithI18n(<FilterButton {...defaultProps} sorting={true} />)

      const sortButton = screen.getAllByTestId('wbutton')[1]

      // First click: null -> asc
      fireEvent.click(sortButton)
      expect(defaultProps.onSort).toHaveBeenCalledWith('asc')

      // Second click: asc -> desc (simulated by changing props)
      vi.clearAllMocks()
      fireEvent.click(sortButton)
      expect(defaultProps.onSort).toHaveBeenCalledWith('asc') // Will be 'desc' based on current state
    })
  })

  describe('Column types', () => {
    it('generates correct filter types for string columns', async () => {
      renderWithI18n(<FilterButton {...defaultProps} columnType="string" />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton)

      await waitFor(() => {
        const selectBox = screen.getByTestId('select-box')
        const options = selectBox.querySelectorAll('option')

        // Should have string-specific options
        const optionValues = Array.from(options).map((option) => option.getAttribute('value'))
        expect(optionValues).toContain('contains')
        expect(optionValues).toContain('equals')
        expect(optionValues).toContain('startsWith')
        expect(optionValues).toContain('endsWith')
      })
    })

    it('generates correct filter types for number columns', async () => {
      renderWithI18n(<FilterButton {...defaultProps} columnType="number" />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton)

      await waitFor(() => {
        const selectBox = screen.getByTestId('select-box')
        const options = selectBox.querySelectorAll('option')

        // Should have number-specific options
        const optionValues = Array.from(options).map((option) => option.getAttribute('value'))
        expect(optionValues).toContain('equals')
        expect(optionValues).toContain('isLessThan')
        expect(optionValues).toContain('isGreaterThan')
      })
    })

    it('generates correct filter types for date columns', async () => {
      renderWithI18n(<FilterButton {...defaultProps} columnType="date" />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton)

      await waitFor(() => {
        const selectBox = screen.getByTestId('select-box')
        const options = selectBox.querySelectorAll('option')

        // Should have date-specific options
        const optionValues = Array.from(options).map((option) => option.getAttribute('value'))
        expect(optionValues).toContain('on')
        expect(optionValues).toContain('before')
        expect(optionValues).toContain('after')
        expect(optionValues).toContain('between')
      })
    })
  })

  describe('Data processing', () => {
    it('generates unique values from data for string columns', () => {
      const testData = [
        { testColumn: 'value1' },
        { testColumn: 'value2' },
        { testColumn: 'value1' }, // duplicate
        { testColumn: 'value3' },
      ]

      renderWithI18n(<FilterButton {...defaultProps} data={testData} columnType="string" />)

      // The component should process unique values internally
      // This is tested indirectly through the multiselect functionality
    })

    it('handles date data processing', () => {
      const testData = [
        { testColumn: '2023-01-01' },
        { testColumn: '2023-01-02' },
        { testColumn: '2023-01-01' }, // duplicate
        { testColumn: 'invalid-date' },
      ]

      renderWithI18n(<FilterButton {...defaultProps} data={testData} columnType="date" />)

      // The component should filter out invalid dates and process unique values
    })

    it('handles null and undefined values in data', () => {
      const testData = [{ testColumn: 'value1' }, { testColumn: null }, { testColumn: undefined }, { testColumn: 'value2' }]

      renderWithI18n(<FilterButton {...defaultProps} data={testData} columnType="string" />)

      // The component should filter out null/undefined values
    })
  })

  describe('Current filter synchronization', () => {
    it('syncs with external filter changes', () => {
      const currentFilter = { type: 'contains', value: 'test' }
      const { rerender } = renderWithI18n(<FilterButton {...defaultProps} currentFilter={currentFilter} />)

      // Change the current filter
      const newFilter = { type: 'equals', value: 'new test' }
      rerender(
        <I18nextProvider i18n={i18n}>
          <FilterButton {...defaultProps} currentFilter={newFilter} />
        </I18nextProvider>,
      )

      // The component should sync its internal state
    })

    it('handles selectMultiple filter type', () => {
      const currentFilter = { type: 'selectMultiple', value: ['value1', 'value2'] }
      renderWithI18n(<FilterButton {...defaultProps} currentFilter={currentFilter} />)

      // The component should handle multi-select values correctly
    })

    it('handles null current filter', () => {
      renderWithI18n(<FilterButton {...defaultProps} currentFilter={null} />)

      // The component should handle null filter gracefully
    })
  })

  describe('Edge cases', () => {
    it('handles empty data array', () => {
      renderWithI18n(<FilterButton {...defaultProps} data={[]} />)

      // Should not crash with empty data
    })

    it('handles missing columnId in data', () => {
      const testData = [{ otherColumn: 'value1' }, { otherColumn: 'value2' }]

      renderWithI18n(<FilterButton {...defaultProps} data={testData} columnId="missingColumn" />)

      // Should handle missing column gracefully
    })

    it('handles date between filter', async () => {
      renderWithI18n(<FilterButton {...defaultProps} columnType="date" />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton)

      await waitFor(() => {
        const selectBox = screen.getByTestId('select-box')
        fireEvent.change(selectBox, { target: { value: 'between' } })
      })

      // Should show two date inputs for between filter
      await waitFor(() => {
        const textFields = screen.getAllByTestId('wtextfield')
        expect(textFields.length).toBeGreaterThanOrEqual(2)
      })
    })

    it('disables apply button when filter is incomplete', async () => {
      renderWithI18n(<FilterButton {...defaultProps} />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton)

      await waitFor(() => {
        const applyButton = screen.getByText('apply')
        expect(applyButton).toHaveProperty('disabled', true)
      })
    })
  })

  describe('Event handling', () => {
    it('prevents event propagation on popover clicks', async () => {
      const stopPropagation = vi.fn()
      renderWithI18n(<FilterButton {...defaultProps} />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton)

      await waitFor(() => {
        const popover = screen.getByTestId('wpopover')
        fireEvent.click(popover, { stopPropagation })
      })
    })

    it('handles filter button click with event propagation prevention', () => {
      const mockEvent = {
        stopPropagation: vi.fn(),
        currentTarget: window.document.createElement('button'),
      }

      renderWithI18n(<FilterButton {...defaultProps} />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton, mockEvent)

      // Event propagation should be stopped
    })
  })

  describe('Accessibility', () => {
    it('has proper button roles', () => {
      renderWithI18n(<FilterButton {...defaultProps} sorting={true} />)

      const buttons = screen.getAllByTestId('wbutton')
      expect(buttons).toHaveLength(2) // Filter and sort buttons
    })

    it('has proper form labels in popover', async () => {
      renderWithI18n(<FilterButton {...defaultProps} />)

      const filterButton = screen.getAllByTestId('wbutton')[0]
      fireEvent.click(filterButton)

      await waitFor(() => {
        expect(screen.getByTestId('wpopover')).toBeInTheDocument()
        // Form should have proper structure
      })
    })
  })
})