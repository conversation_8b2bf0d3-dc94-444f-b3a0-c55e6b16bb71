import React, { useState, useMemo, useEffect } from 'react'
import FilterListIcon from '@mui/icons-material/FilterList'
import <PERSON>UpwardIcon from '@mui/icons-material/ArrowUpward'
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward'
import { useTranslation } from 'react-i18next'
import { WBox, WButton, WPopover, WTextField, WTypography } from 'wface'
import SelectBox from '../../formElements/SelectBox/SelectBox'
import { IOption } from '@/types'
import SortRounded from '@mui/icons-material/SortRounded'
import uniq from 'lodash/uniq'
import uniqBy from 'lodash/uniqBy'
import dayjs from 'dayjs'
import '../FilterComponents.css'

interface FilterButtonProps {
  columnId: string // Unique identifier for the column,
  onApply: (filter: { type: string; value: unknown }) => void // Callback when filter is applied,
  onClear: () => void // Callback when filter is cleared,
  onSort: (direction: 'asc' | 'desc' | null) => void // Callback for sorting,
  columnType: string // Type of column (string, number, date),
  currentFilter: { type: string; value: unknown } | null // Current active filter,
  currentSortDirection: 'asc' | 'desc' | null // Current sort direction,
  showFilter: boolean // Whether to show filter button,
  sorting?: boolean // Whether to enable sorting,
  data?: unknown[] // Data array for generating unique values,
}

const FilterButton: React.FC<FilterButtonProps> = ({
  columnId,
  onApply,
  onClear,
  onSort,
  columnType,
  currentFilter,
  currentSortDirection,
  showFilter,
  sorting,
  data = [],
}) => {
  const { t } = useTranslation('tableFilters')
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [localFilterType, setLocalFilterType] = useState<IOption | null>({ value: '0', label: 'Filtre Türü Seçiniz', labelEn: 'Select Filter Type' })
  const [localFilterValue, setLocalFilterValue] = useState<unknown>(null)
  const [selectedValues, setSelectedValues] = useState<IOption[]>([])

  // Format date function
  const formatDate = (date: unknown): string => {
    if (!date) return ''
    return dayjs(date).format('DD-MM-YYYY')
  }

  // Generate unique values for the multiselect option
  const uniqueValues = useMemo(() => {
    if (!data.length) return []

    if (columnType === 'date') {
      // First, convert all dates to a consistent format and filter out invalid dates
      const validDates = data
        .map((item) => (item as Record<string, unknown>)[columnId])
        .filter((date) => date && dayjs(date).isValid())
        .map((date) => ({
          originalValue: date,
          formattedValue: formatDate(date),
          timestamp: dayjs(date).valueOf(),
        }))

      // Group by formatted date to remove duplicates
      const uniqueDates = uniqBy(validDates, 'formattedValue')
        .sort((a, b) => a.timestamp - b.timestamp)
        .map((date) => ({
          value: date.originalValue, // Keep original date for filtering,
          label: date.formattedValue,
          labelEn: date.formattedValue,
        }))

      return uniqueDates
    }

    // Handle non-date columns as before
    return uniq(data.map((item) => (item as Record<string, unknown>)[columnId]))
      .filter((value) => value != null)
      .sort((a, b) => {
        if (typeof a === 'string' && typeof b === 'string') {
          return a.localeCompare(b)
        }
        return (a as number) - (b as number)
      })
      .map((value) => ({
        value: value,
        label: String(value),
        labelEn: String(value),
      }))
  }, [data, columnId, columnType])

  // Define filter types based on column type
  const filterTypes: IOption[] = useMemo(() => {
    const baseTypes = [
      { value: '0', label: 'Filtre Türü Seçiniz', labelEn: 'Select Filter Type' },
      { value: 'selectMultiple', label: t('selectMultiple'), labelEn: 'Select Multiple Values' },
    ]

    if (columnType === 'number') {
      return [
        ...baseTypes,
        { value: 'equals', label: t('equals'), labelEn: 'Equals' },
        { value: 'doesntEqual', label: t('doesntEqual'), labelEn: "Doesn't Equal" },
        { value: 'isLessThan', label: t('isLessThan'), labelEn: 'Is Less Than' },
        { value: 'isLessThanOrEqualTo', label: t('isLessThanOrEqualTo'), labelEn: 'Is Less Than or Equal To' },
        { value: 'isGreaterThan', label: t('isGreaterThan'), labelEn: 'Is Greater Than' },
        { value: 'isGreaterThanOrEqualTo', label: t('isGreaterThanOrEqualTo'), labelEn: 'Is Greater Than or Equal To' },
      ]
    } else if (columnType === 'date') {
      return [
        ...baseTypes,
        { value: 'on', label: t('on'), labelEn: 'On' },
        { value: 'before', label: t('before'), labelEn: 'Before' },
        { value: 'after', label: t('after'), labelEn: 'After' },
        { value: 'between', label: t('between'), labelEn: 'Between' },
      ]
    }

    return [
      ...baseTypes,
      { value: 'contains', label: t('contains'), labelEn: 'Contains' },
      { value: 'doesntContain', label: t('doesntContain'), labelEn: "Doesn't Contain" },
      { value: 'equals', label: t('equals'), labelEn: 'Equals' },
      { value: 'doesntEqual', label: t('doesntEqual'), labelEn: "Doesn't Equal" },
      { value: 'startsWith', label: t('startsWith'), labelEn: 'Starts With' },
      { value: 'endsWith', label: t('endsWith'), labelEn: 'Ends With' },
    ]
  }, [columnType, t])

  // Sync with external filter changes
  useEffect(() => {
    if (currentFilter) {
      const matchingType = filterTypes.find((type) => type.value === currentFilter.type)
      setLocalFilterType(matchingType ?? null)

      if (currentFilter.type === 'selectMultiple') {
        if (columnType === 'date') {
          // Match based on formatted date strings to avoid duplicate selections
          const selectedOpts = uniqueValues.filter((opt) =>
            Array.isArray(currentFilter.value) && currentFilter.value.some((filterDate: unknown) => formatDate(filterDate) === formatDate(opt.value)),
          )
          setSelectedValues(selectedOpts)
        } else {
          const selectedOpts = uniqueValues.filter((opt) => Array.isArray(currentFilter.value) && currentFilter.value.includes(opt.value))
          setSelectedValues(selectedOpts)
        }
        setLocalFilterValue(null)
      } else {
        setLocalFilterValue(currentFilter.value)
        setSelectedValues([])
      }
    } else {
      setLocalFilterType(null)
      setLocalFilterValue(null)
      setSelectedValues([])
    }
  }, [currentFilter, filterTypes, uniqueValues, columnType])

  // Event Handlers
  const handleFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    void event.stopPropagation()
    setAnchorEl(event.currentTarget)
  }

  const handleClose = (event?: React.MouseEvent<HTMLElement>) => {
    void event?.stopPropagation()
    setAnchorEl(null)
  }

  const handleFilterTypeChange = (option: IOption | IOption[] | null) => {
    const newType = option as IOption
    setLocalFilterType(newType)
    // Only reset values if changing between single/multiple select modes
    if (newType?.value === 'selectMultiple') {
      setLocalFilterValue(null)
    } else {
      setSelectedValues([])
    }
  }

  const handleApply = (event: React.MouseEvent<HTMLElement>) => {
    void event.stopPropagation()
    if (localFilterType) {
      let filterValue

      if (localFilterType.value === 'selectMultiple') {
        if (columnType === 'date') {
          // Use original date values for filtering
          filterValue = selectedValues.map((v) => v.value)
        } else {
          filterValue = selectedValues.map((v) => v.value)
        }
      } else if (columnType === 'date' && localFilterType.value === 'between') {
        filterValue = {
          start: (localFilterValue as { start?: string })?.start,
          end: (localFilterValue as { end?: string })?.end,
        }
      } else if (columnType === 'date') {
        filterValue = localFilterValue
      } else {
        filterValue = localFilterValue
      }

      const filterToApply = {
        type: localFilterType.value,
        value: filterValue,
      }
      onApply(filterToApply)
    }
    handleClose(event)
  }

  const handleClear = (event: React.MouseEvent<HTMLElement>) => {
    void event.stopPropagation()
    onClear()
    setLocalFilterType(null)
    setLocalFilterValue(null)
    setSelectedValues([])
    handleClose(event)
  }

  const handleSortClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    void event.stopPropagation()
    const newSortDirection = currentSortDirection === 'asc' ? 'desc' : currentSortDirection === 'desc' ? null : 'asc'
    onSort(newSortDirection)
  }

  // UI states
  const open = Boolean(anchorEl)
  const isApplyDisabled = !localFilterType ?? 
    localFilterType?.value === '0' ?? 
    (localFilterType?.value === 'selectMultiple' ? selectedValues.length === 0 : !localFilterValue)
  // Styles
  const styles = {
    popoverContent: {
      padding: '24px',
      width: '320px',
      borderRadius: '12px',
      background: 'var(--digi-surface)',
      border: '1px solid var(--digi-border)',
      boxShadow: '0 10px 30px rgba(92, 45, 145, 0.15)',
    },
    filterTypeSection: {
      marginBottom: '24px',
    },
    valueSection: {
      marginBottom: '24px',
    },
    buttonContainer: {
      display: 'flex',
      justifyContent: 'space-between',
      marginTop: '24px',
      gap: '12px',
    },
    clearButton: {
      color: '#64748b',
      padding: '8px 16px',
      borderRadius: '6px',
      fontSize: '14px',
      fontWeight: 500,
      '&:hover': {
        backgroundColor: '#f1f5f9',
        color: '#475569',
      },
    },
    buttonGroup: {
      display: 'flex',
      gap: '8px',
    },
    applyButton: {
      backgroundColor: '#5c2d91',
      color: '#ffffff',
      padding: '8px 20px',
      borderRadius: '6px',
      fontSize: '14px',
      fontWeight: 600,
      boxShadow: '0 2px 4px rgba(92, 45, 145, 0.2)',
      '&:hover': {
        backgroundColor: '#45226d',
        boxShadow: '0 4px 12px rgba(92, 45, 145, 0.3)',
      },
      '&:disabled': {
        backgroundColor: '#e2e8f0',
        color: '#94a3b8',
        boxShadow: 'none',
      },
    },
    cancelButton: {
      color: '#64748b',
      padding: '8px 16px',
      borderRadius: '6px',
      fontSize: '14px',
      fontWeight: 500,
      border: '1px solid #e2e8f0',
      '&:hover': {
        backgroundColor: '#f8fafc',
        borderColor: '#cbd5e1',
      },
    },
    dateInputContainer: {
      display: 'flex',
      justifyContent: 'space-between',
      gap: '12px',
      '& > *': {
        flex: 1,
      },
      '& .MuiOutlinedInput-root': {
        borderRadius: '6px',
        '&:hover .MuiOutlinedInput-notchedOutline': {
          borderColor: '#5c2d91',
        },
        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
          borderColor: '#5c2d91',
          borderWidth: '2px',
        },
      },
    },
    filterButton: {
      padding: '4px',
      minWidth: 'auto',
      borderRadius: '4px',
      backgroundColor: 'transparent',
      '&:hover': {
        backgroundColor: '#f3eef8',
      },
    },
    sortButton: {
      padding: '4px',
      minWidth: 'auto',
      width: 'auto',
      marginLeft: '2px',
      borderRadius: '4px',
      backgroundColor: 'transparent',
      '&:hover': {
        backgroundColor: '#f3eef8',
      },
    },
    titleStyle: {
      fontSize: '16px',
      fontWeight: 600,
      color: '#0f172a',
      marginBottom: '16px',
    },
    labelStyle: {
      fontSize: '14px',
      fontWeight: 500,
      color: '#374151',
      marginBottom: '8px',
    },
  }
  return (
    <WBox display="flex" alignItems="center">
      {' '}
      {showFilter && (
        <WButton size="small" onClick={handleFilterClick} style={styles.filterButton}>
          {currentFilter?.type ? (
            <FilterListIcon
              sx={{
                fontSize: '16px',
                color: '#7c3aed',
                transition: 'all 0.2s ease',
                '&:hover': {
                  color: '#6d28d9',
                  transform: 'scale(1.1)',
                },
              }}
            />
          ) : (
            <FilterListIcon
              sx={{
                fontSize: '16px',
                color: '#94a3b8',
                transition: 'all 0.2s ease',
                '&:hover': {
                  color: '#64748b',
                  transform: 'scale(1.05)',
                },
              }}
            />
          )}
        </WButton>
      )}
      {sorting && (
        <WButton size="small" onClick={handleSortClick} style={styles.sortButton}>
          {currentSortDirection === 'asc' && (
            <ArrowUpwardIcon
              sx={{
                fontSize: '13px',
                color: '#f8fafc',
                fontWeight: 'bold',
                filter: 'drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4)) brightness(1.2)',
                textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
                background: 'linear-gradient(135deg, #5c2d91 0%, #7c3aed 100%)',
                borderRadius: '3px',
                padding: '2px',
                boxShadow: '0 2px 4px rgba(92, 45, 145, 0.3)',
              }}
            />
          )}
          {currentSortDirection === 'desc' && (
            <ArrowDownwardIcon
              sx={{
                fontSize: '13px',
                color: '#f8fafc',
                fontWeight: 'bold',
                filter: 'drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4)) brightness(1.2)',
                textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
                background: 'linear-gradient(135deg, #5c2d91 0%, #7c3aed 100%)',
                borderRadius: '3px',
                padding: '2px',
                boxShadow: '0 2px 4px rgba(92, 45, 145, 0.3)',
              }}
            />
          )}
          {currentSortDirection === null && (
            <SortRounded
              sx={{
                fontSize: '13px',
                color: '#cbd5e1',
                opacity: 0.8,
                filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) brightness(1.1)',
                '&:hover': {
                  color: '#f8fafc',
                  opacity: 1,
                  filter: 'drop-shadow(0 1px 3px rgba(0, 0, 0, 0.3)) brightness(1.3)',
                  transform: 'scale(1.05)',
                  transition: 'all 0.2s ease',
                },
              }}
            />
          )}
        </WButton>
      )}{' '}
      <WPopover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        onClick={(event: Event) => event.stopPropagation()}
        className="filter-popover"
        PaperProps={{
          elevation: 8,
          className: 'filter-popover',
          sx: {
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            boxShadow: '0 10px 30px rgba(92, 45, 145, 0.15)',
            overflow: 'visible',
            '&::before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              left: 14,
              width: 12,
              height: 12,
              backgroundColor: 'white',
              border: '1px solid #e2e8f0',
              borderRight: 'none',
              borderBottom: 'none',
              transform: 'translate(-50%, -50%) rotate(45deg)',
              zIndex: 0,
            },
        }}
      >
        <WBox style={styles.popoverContent} className="filter-popover-content">
          <WTypography variant="subtitle1" style={styles.titleStyle} className="filter-popover-title">
            {t('title')}
          </WTypography>
          <WBox style={styles.filterTypeSection}>
            <WTypography variant="body2" style={styles.labelStyle} className="filter-popover-label" gutterBottom>
              {t('filterType')}
            </WTypography>{' '}
            <div className="filter-form-field">
              <SelectBox label="" value={localFilterType} onChange={handleFilterTypeChange} options={filterTypes} fullWidth />
            </div>
          </WBox>{' '}
          {localFilterType && localFilterType.value !== '0' && (
            <WBox style={styles.valueSection}>
              <WTypography variant="body2" style={styles.labelStyle} className="filter-popover-label" gutterBottom>
                {t('value')}
              </WTypography>{' '}
              {localFilterType?.value === 'selectMultiple' ? (
                <SelectBox
                  label=""
                  value={selectedValues}
                  onChange={(option: IOption | IOption[] | null) => {
                    const newValues = Array.isArray(option) ? option : []
                    setSelectedValues(newValues)
                  }}
                  options={uniqueValues}
                  multiple
                  fullWidth
                  defaultText={t('selectValues')}
                />
              ) : columnType === 'date' && localFilterType?.value === 'between' ? (
                <WBox style={styles.dateInputContainer}>
                  <WTextField
                    type="date"
                    variant="outlined"
                    size="small"
                    value={(localFilterValue as { start?: string })?.start ?? ''}
                    onChange={(e) =>
                      setLocalFilterValue({
                        ...(localFilterValue as { start?: string; end?: string }),
                        start: e.target.value,
                      })
                    }
                    InputLabelProps={{ shrink: true }}
                    sx={{
                      className: 'filter-form-field',
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '6px',
                        fontSize: '14px',
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#5c2d91',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#5c2d91',
                          borderWidth: '2px',
                        },
                    }}
                  />
                  <WTextField
                    type="date"
                    variant="outlined"
                    size="small"
                    value={(localFilterValue as { end?: string })?.end ?? ''}
                    onChange={(e) =>
                      setLocalFilterValue({
                        ...(localFilterValue as { start?: string; end?: string }),
                        end: e.target.value,
                      })
                    }
                    InputLabelProps={{ shrink: true }}
                    sx={{
                      className: 'filter-form-field',
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '6px',
                        fontSize: '14px',
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#5c2d91',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#5c2d91',
                          borderWidth: '2px',
                        },
                    }}
                  />
                </WBox>
              ) : columnType === 'date' ? (
                <WTextField
                  type="date"
                  variant="outlined"
                  size="small"
                  value={String(localFilterValue ?? '')}
                  onChange={(e) => setLocalFilterValue(e.target.value)}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    className: 'filter-form-field',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '6px',
                      fontSize: '14px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#5c2d91',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#5c2d91',
                        borderWidth: '2px',
                      },
                  }}
                />
              ) : (
                <WTextField
                  variant="outlined"
                  size="small"
                  value={String(localFilterValue ?? '')}
                  onChange={(e) => setLocalFilterValue(e.target.value)}
                  fullWidth
                  type={columnType === 'number' ? 'number' : 'text'}
                  sx={{
                    className: 'filter-form-field',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '6px',
                      fontSize: '14px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#5c2d91',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#5c2d91',
                        borderWidth: '2px',
                      },
                  }}
                />
              )}
            </WBox>
          )}{' '}
          <WBox style={styles.buttonContainer}>
            <WButton onClick={handleClear} className="filter-clear-button" style={styles.clearButton}>
              {t('clear')}
            </WButton>

            <WBox style={styles.buttonGroup}>
              <WButton onClick={handleClose} className="filter-cancel-button" style={styles.cancelButton}>
                {t('cancel')}
              </WButton>
              <WButton
                onClick={handleApply}
                variant="contained"
                className="filter-apply-button"
                style={styles.applyButton}
                disabled={isApplyDisabled}
                sx={{
                  '&:hover': {
                    backgroundColor: '#45226d',
                    boxShadow: '0 4px 12px rgba(92, 45, 145, 0.3)',
                  },
                  '&:disabled': {
                    backgroundColor: '#e2e8f0',
                    color: '#94a3b8',
                    boxShadow: 'none',
                  },
                }}
              >
                {t('apply')}
              </WButton>
            </WBox>
          </WBox>
        </WBox>
      </WPopover>
    </WBox>
  )
}

export default FilterButton
              </SelectBox>
            </div>
          </WBox>
        </WBox>
    </WBox>
  </HTMLButtonElement>
  </HTMLElement>
  </HTMLElement>
  </HTMLElement>
  </HTMLButtonElement>
  </unknown>
  </IOption>
  </null>
</FilterButtonProps>
