import React, { useState, useCallback, useMemo, JSXElementConstructor, ReactElement } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { excludedColumns } from './DigiTable'
import { Column } from '@/types/WFaceTypes'
import { WBox, WTypography } from 'wface'
import { useTranslation } from 'react-i18next'

import MobileFilterButton, { FilterState } from '@/components/filters/MobileFilterButton/MobileFilterButton'
import CustomPagination from './CustomPagination'
import dayjs from 'dayjs'
import StatusBadge from './StatusBadge'
import './DigiTable.enhanced.css'
interface MobileTableConfig {
  titleFields?: string[]
  subtitleFields?: string[]
  rightFields?: string[]
}

interface MobileTableProps {
  data: unknown[]
  columns: Column<any>[]
  // eslint-disable-next-line no-unused-vars
  onRowClick?: (row: any) => void
  actions?: unknown[]
  languageFile?: any
  paging?: boolean
  filterTitle?: string
  title?: string | ReactElement<any, string | JSXElementConstructor<any>> | undefined
  mobileConfig?: MobileTableConfig
  filtering?: boolean
}

const MobileTable = React.memo<MobileTableProps>(
  ({ data, columns, title, actions, languageFile, filterTitle, mobileConfig, paging, filtering = true }) => {
    const { t } = useTranslation('mobileTable')
    const [expandedRows, setExpandedRows] = useState<{ [key: string]: boolean }>({})
    const [filters, setFilters] = useState<FilterState[]>([])
    const [searchTerm, setSearchTerm] = useState('')
    const [page, setPage] = useState(0)
    const [pageSize, setPageSize] = useState(10)
    const [sortConfig, setSortConfig] = useState<{ columnId: string; direction: 'asc' | 'desc' | null }>({ columnId: '', direction: null })

    const defaultConfig = useMemo(
      () => ({
        titleFields: [],
        subtitleFields: [],
        rightFields: [],
      }),
      [],
    )

    // Merge provided config with defaults
    const config = useMemo(
      () => ({
        titleFields: mobileConfig?.titleFields ?? defaultConfig.titleFields,
        subtitleFields: mobileConfig?.subtitleFields ?? defaultConfig.subtitleFields,
        rightFields: mobileConfig?.rightFields ?? defaultConfig.rightFields,
      }),
      [mobileConfig, defaultConfig],
    )

    const mapFields = (fields: string[], row: any) => {
      return fields.map((field) => {
        const column = columns.find((col) => col.field === field)
        if (!column) return null
        const value = row[field]
        if (column.dateSetting?.type === 'date') {
          return dayjs(value).format('DD.MM.YYYY HH:mm:ss')
        }
        return value
      })
    }

    // Add getRightContent function after getSubtitleContent
    const getRightContent = useCallback(
      (row: any) => {
        return mapFields(config.rightFields, row).join(' - ')
      },
      [columns, config.rightFields],
    )

    // Update getTitleContent for date formatting
    const getTitleContent = useCallback(
      (row: any) => {
        return mapFields(config.titleFields, row).join(' - ')
      },
      [columns, config.titleFields],
    )

    // Update getSubtitleContent for date formatting
    const getSubtitleContent = useCallback(
      (row: any) => {
        return mapFields(config.subtitleFields, row).join(' - ')
      },
      [columns, config.subtitleFields],
    )

    // Memoized filtered data
    const filteredData = useMemo(() => {
      let result = [...data]

      // Apply search term
      if (searchTerm) {
        result = result.filter((row: any) =>
          Object.entries(row).some(([key, value]) => {
            if (excludedColumns.includes(key) || !columns.find((col) => col.field === key)) return false
            return String(value).toLowerCase().includes(searchTerm.toLowerCase())
          }),
        )
      }

      // Apply filters
      if (filters.length > 0) {
        result = result.filter((row: any) => {
          return filters.every((filter) => {
            const value = row[filter.columnId]
            if (value == null) return false

            switch (filter.type) {
              case 'equals':
                return String(value) === String(filter.value)
              case 'contains':
                return String(value).toLowerCase().includes(String(filter.value).toLowerCase())
              case 'startsWith':
                return String(value).toLowerCase().startsWith(String(filter.value).toLowerCase())
              case 'endsWith':
                return String(value).toLowerCase().endsWith(String(filter.value).toLowerCase())
              case 'isLessThan':
                return Number(value) < Number(filter.value)
              case 'isGreaterThan':
                return Number(value) > Number(filter.value)
              case 'on':
                return new Date(value).toDateString() === new Date(filter.value).toDateString()
              case 'before':
                return new Date(value) < new Date(filter.value)
              case 'after':
                return new Date(value) > new Date(filter.value)
              case 'between': {
                const date = new Date(value)
                return date >= new Date(filter.value.start) && date <= new Date(filter.value.end)
              }
              case 'selectMultiple':
                return filter.value.includes(value)
              default:
                return true
            }
          })
        })
      }

      // Apply sorting
      if (sortConfig.columnId && sortConfig.direction) {
        const columnType = columns.find((col) => col.field === sortConfig.columnId)?.dateSetting?.type ?? 'string'

        result = [...result].sort((a, b) => {
          let aValue = a[sortConfig.columnId]
          let bValue = b[sortConfig.columnId]

          if (aValue == null) return 1
          if (bValue == null) return -1

          if (columnType === 'date') {
            aValue = new Date(aValue)
            bValue = new Date(bValue)
          }

          if (aValue instanceof Date && bValue instanceof Date) {
            return sortConfig.direction === 'asc' ? aValue.getTime() - bValue.getTime() : bValue.getTime() - aValue.getTime()
          }

          if (typeof aValue === 'number' && typeof bValue === 'number') {
            return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue
          }

          const aString = String(aValue).toLowerCase()
          const bString = String(bValue).toLowerCase()
          return sortConfig.direction === 'asc' ? aString.localeCompare(bString) : bString.localeCompare(aString)
        })
      }

      return result
    }, [data, searchTerm, filters, sortConfig])

    const paginatedData = useMemo(() => {
      const start = page * pageSize
      return filteredData.slice(start, start + pageSize)
    }, [filteredData, page, pageSize])

    const handlePageChange = (newPage: number) => {
      setPage(newPage)
    }

    const handlePageSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newPageSize = parseInt(event.target.value, 10)
      setPageSize(newPageSize)
      setPage(0)
    }

    const handleFiltersChange = useCallback((newFilters: FilterState[]) => {
      setFilters(newFilters)
    }, [])

    const handleSearch = useCallback((term: string) => {
      setSearchTerm(term)
    }, [])

    const handleSortChange = useCallback((sortConfig: { columnId: string; direction: 'asc' | 'desc' | null }) => {
      setSortConfig(sortConfig)
    }, [])

    const toggleRow = useCallback((rowId: string) => {
      setExpandedRows((prev) => ({
        ...prev,
        [rowId]: !prev[rowId],
      }))
    }, [])

    return (
      <div className="digi-mobile-container" data-theme="light">
        <div className="digi-mobile-header">
          <WTypography variant="h6" className="digi-mobile-title">
            {title ?? ''}
          </WTypography>
          {filtering && (
            <WBox display="flex" gap={2}>
              <MobileFilterButton
                columns={columns}
                data={data}
                onApplyFilters={handleFiltersChange}
                onSearch={handleSearch}
                onSort={handleSortChange}
                title={filterTitle ?? t('filters')}
                currentFilters={filters}
              />
            </WBox>
          )}
        </div>

        {(paging ? paginatedData : filteredData).length === 0 ? (
          <div className="digi-mobile-no-data">{searchTerm || filters.length > 0 ? t('noResultsFound') : t('noDataAvailable')}</div>
        ) : (
          (paging ? paginatedData : filteredData).map((row, index) => {
            const rowId = row.wfinstanceid ?? index
            const isExpanded = expandedRows[rowId]
            const title = getTitleContent(row)
            const subtitle = getSubtitleContent(row)
            const rightText = getRightContent(row)

            return (
              <div key={rowId} className="digi-mobile-card">
                <div
                  className="digi-mobile-card-header"
                  onClick={(e) => {
                    e.stopPropagation()
                    toggleRow(rowId)
                  }}
                >
                  <div className="digi-mobile-main-info">
                    <div className="digi-mobile-card-title">
                      <span>{title}</span>
                    </div>
                    <div className="digi-mobile-card-subtitle">
                      <span>{subtitle}</span>
                    </div>
                  </div>
                  <div className="digi-mobile-card-right">
                    <span className="digi-mobile-card-right-text">{rightText}</span>
                    <button className="digi-mobile-expand-button">{isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}</button>
                  </div>
                </div>

                {isExpanded && (
                  <>
                    <div className="digi-mobile-card-content">
                      {columns
                        .filter((col) => !col.hidden)
                        .map((col: any, idx) => {
                          const value = col.render ? col.render(row) : row[col.field as string]
                          if (!value) return null

                          return (
                            <div key={idx} className="digi-mobile-field">
                              <span className="digi-mobile-field-label">
                                {typeof col.title === 'string'
                                  ? languageFile
                                    ? languageFile(col.title)
                                    : col.title
                                  : (col.title?.props?.children?.[0]?.props?.children ?? '')}
                              </span>
                              <span className="digi-mobile-field-value">
                                {/* Check if this is a status field and render with StatusBadge */}
                                {(col.field === 'status' || col.field === 'durum' || col.field === 'priority') && typeof value === 'string' ? (
                                  <StatusBadge status={value} variant={col.field === 'priority' ? 'priority' : 'status'} size="small" />
                                ) : (
                                  value
                                )}
                              </span>
                            </div>
                          )
                        })}
                    </div>

                    {actions && actions.length > 0 && (
                      <div className="digi-mobile-actions">
                        {actions.map((action: any, idx) => (
                          <button
                            key={idx}
                            className="digi-mobile-action-button"
                            onClick={(e) => {
                              e.stopPropagation()
                              action.onClick(row)
                            }}
                          >
                            {typeof action.title === 'function' ? action.title(row) : languageFile ? languageFile(action.title) : action.title}
                          </button>
                        ))}
                      </div>
                    )}
                  </>
                )}
              </div>
            )
          })
        )}

        {paging && (
          <div className="digi-mobile-pagination">
            <CustomPagination
              count={filteredData.length}
              page={page}
              rowsPerPage={pageSize}
              onPageChange={(_unknown, newPage: number) => handlePageChange(newPage)}
              onRowsPerPageChange={handlePageSizeChange}
              rowsPerPageOptions={[5, 10, 25, 50]}
              showFirstLastPageButtons
            />
          </div>
        )}
      </div>
    )
  },
)

export default MobileTable
