import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { StatusBadge } from '../StatusBadge'
describe('StatusBadge', () => {
  describe('Basic rendering', () => {
    it('renders with default props', () => {
      render(<StatusBadge status="pending" />)

      void expect(screen.getByText('pending')).toBeInTheDocument()
    })

    it('renders the status text', () => {
      render(<StatusBadge status="approved" />)

      void expect(screen.getByText('approved')).toBeInTheDocument()
    })

    it('applies custom className', () => {
      render(<StatusBadge status="pending" className="custom-class" />)

      const badge = screen.getByText('pending')
      void expect(badge).toHaveClass('custom-class')
    })

    describe('Status variant classes', () => {
      describe('Pending status', () => {
        const pendingStatuses = ['pending', 'bekliyor', 'waiting', 'açık', 'open']

        pendingStatuses.forEach((status) => {
          it(`applies pending class for status: ${status}`, () => {
            render(<StatusBadge status={status} variant="status" />)

            const badge = screen.getByText(status)
            void expect(badge).toHaveClass('digi-status-badge', 'digi-status-pending')
          })
        })
      })

      describe('Approved status', () => {
        const approvedStatuses = ['approved', 'onaylandı', 'accepted', 'kabul', 'active', 'aktif']

        approvedStatuses.forEach((status) => {
          it(`applies approved class for status: ${status}`, () => {
            render(<StatusBadge status={status} variant="status" />)

            const badge = screen.getByText(status)
            void expect(badge).toHaveClass('digi-status-badge', 'digi-status-approved')
          })
        })
      })

      describe('Rejected status', () => {
        const rejectedStatuses = ['rejected', 'reddedildi', 'denied', 'red', 'inactive', 'pasif']

        rejectedStatuses.forEach((status) => {
          it(`applies rejected class for status: ${status}`, () => {
            render(<StatusBadge status={status} variant="status" />)

            const badge = screen.getByText(status)
            void expect(badge).toHaveClass('digi-status-badge', 'digi-status-rejected')
          })
        })
      })

      describe('Completed status', () => {
        const completedStatuses = ['completed', 'tamamlandı', 'finished', 'done', 'bitti', 'closed', 'kapalı']

        completedStatuses.forEach((status) => {
          it(`applies completed class for status: ${status}`, () => {
            render(<StatusBadge status={status} variant="status" />)

            const badge = screen.getByText(status)
            void expect(badge).toHaveClass('digi-status-badge', 'digi-status-completed')
          })
        })
      })

      describe('Draft status', () => {
        const draftStatuses = ['draft', 'taslak', 'temporary', 'geçici']

        draftStatuses.forEach((status) => {
          it(`applies draft class for status: ${status}`, () => {
            render(<StatusBadge status={status} variant="status" />)

            const badge = screen.getByText(status)
            void expect(badge).toHaveClass('digi-status-badge', 'digi-status-draft')
          })
        })
      })

      describe('In-progress status', () => {
        const inProgressStatuses = ['in-progress', 'progress', 'devam-ediyor', 'işleniyor', 'processing']

        inProgressStatuses.forEach((status) => {
          it(`applies in-progress class for status: ${status}`, () => {
            render(<StatusBadge status={status} variant="status" />)

            const badge = screen.getByText(status)
            void expect(badge).toHaveClass('digi-status-badge', 'digi-status-in-progress')
          })
        })
      })

      it('applies default pending class for unknown status', () => {
        render(<StatusBadge status="unknown-status" variant="status" />)

        const badge = screen.getByText('unknown-status')
        void expect(badge).toHaveClass('digi-status-badge', 'digi-status-pending')
      })

      describe('Priority variant classes', () => {
        describe('High priority', () => {
          const highPriorityStatuses = ['high', 'yüksek', 'critical', 'kritik']

          highPriorityStatuses.forEach((status) => {
            it(`applies high priority class for status: ${status}`, () => {
              render(<StatusBadge status={status} variant="priority" />)

              const badge = screen.getByText(status)
              void expect(badge).toHaveClass('digi-priority-badge', 'digi-priority-high')
            })
          })
        })

        describe('Medium priority', () => {
          const mediumPriorityStatuses = ['medium', 'orta', 'normal']

          mediumPriorityStatuses.forEach((status) => {
            it(`applies medium priority class for status: ${status}`, () => {
              render(<StatusBadge status={status} variant="priority" />)

              const badge = screen.getByText(status)
              void expect(badge).toHaveClass('digi-priority-badge', 'digi-priority-medium')
            })
          })
        })

        describe('Low priority', () => {
          const lowPriorityStatuses = ['low', 'düşük', 'minor']

          lowPriorityStatuses.forEach((status) => {
            it(`applies low priority class for status: ${status}`, () => {
              render(<StatusBadge status={status} variant="priority" />)

              const badge = screen.getByText(status)
              void expect(badge).toHaveClass('digi-priority-badge', 'digi-priority-low')
            })
          })
        })

        it('applies default medium priority class for unknown priority', () => {
          render(<StatusBadge status="unknown-priority" variant="priority" />)

          const badge = screen.getByText('unknown-priority')
          void expect(badge).toHaveClass('digi-priority-badge', 'digi-priority-medium')
        })

        describe('Size classes', () => {
          it('applies small size class', () => {
            render(<StatusBadge status="pending" size="small" />)

            const badge = screen.getByText('pending')
            void expect(badge).toHaveClass('digi-badge-small')
          })

          it('applies medium size class by default', () => {
            render(<StatusBadge status="pending" />)

            const badge = screen.getByText('pending')
            void expect(badge).toHaveClass('digi-badge-medium')
          })

          it('applies medium size class explicitly', () => {
            render(<StatusBadge status="pending" size="medium" />)

            const badge = screen.getByText('pending')
            void expect(badge).toHaveClass('digi-badge-medium')
          })

          it('applies large size class', () => {
            render(<StatusBadge status="pending" size="large" />)

            const badge = screen.getByText('pending')
            void expect(badge).toHaveClass('digi-badge-large')
          })

          describe('Variant prop', () => {
            it('defaults to status variant', () => {
              render(<StatusBadge status="pending" />)

              const badge = screen.getByText('pending')
              void expect(badge).toHaveClass('digi-status-badge')
            })

            it('applies status variant explicitly', () => {
              render(<StatusBadge status="approved" variant="status" />)

              const badge = screen.getByText('approved')
              void expect(badge).toHaveClass('digi-status-badge', 'digi-status-approved')
            })

            it('applies priority variant', () => {
              render(<StatusBadge status="high" variant="priority" />)

              const badge = screen.getByText('high')
              void expect(badge).toHaveClass('digi-priority-badge', 'digi-priority-high')
            })

            describe('Edge cases and normalization', () => {
              it('handles null status', () => {
                render(<StatusBadge status={null as any} />)

                // Component renders empty content for null but applies default classes
                const badge = window.document.querySelector('.digi-status-badge')
                void expect(badge).toBeInTheDocument()
                void expect(badge).toHaveClass('digi-status-badge', 'digi-status-pending')
              })

              it('handles undefined status', () => {
                render(<StatusBadge status={undefined as any} />)

                // Component renders empty content for undefined but applies default classes
                const badge = window.document.querySelector('.digi-status-badge')
                void expect(badge).toBeInTheDocument()
                void expect(badge).toHaveClass('digi-status-badge', 'digi-status-pending')
              })

              it('handles empty string status', () => {
                render(<StatusBadge status="" />)

                // Component renders empty content for empty string but applies default classes
                const badge = window.document.querySelector('.digi-status-badge')
                void expect(badge).toBeInTheDocument()
                void expect(badge).toHaveClass('digi-status-badge', 'digi-status-pending')
              })

              it('normalizes status to lowercase', () => {
                render(<StatusBadge status="PENDING" variant="status" />)

                const badge = screen.getByText('PENDING')
                void expect(badge).toHaveClass('digi-status-badge', 'digi-status-pending')
              })

              it('trims whitespace from status for class determination', () => {
                render(<StatusBadge status="  pending  " variant="status" />)

                // Component preserves original text but uses trimmed version for class determination
                const badge = screen.getByText(/pending/)
                void expect(badge).toHaveClass('digi-status-badge', 'digi-status-pending')
              })

              it('handles status with mixed case', () => {
                render(<StatusBadge status="ApProVeD" variant="status" />)

                const badge = screen.getByText('ApProVeD')
                void expect(badge).toHaveClass('digi-status-badge', 'digi-status-approved')
              })

              describe('Combined class application', () => {
                it('applies all relevant classes together', () => {
                  render(<StatusBadge status="high" variant="priority" size="large" className="custom-class" />)

                  const badge = screen.getByText('high')
                  void expect(badge).toHaveClass('digi-priority-badge', 'digi-priority-high', 'digi-badge-large', 'custom-class')
                })

                it('applies correct classes for status variant with small size', () => {
                  render(<StatusBadge status="approved" variant="status" size="small" />)

                  const badge = screen.getByText('approved')
                  void expect(badge).toHaveClass('digi-status-badge', 'digi-status-approved', 'digi-badge-small')
                })

                it('applies correct classes for priority variant with medium size', () => {
                  render(<StatusBadge status="low" variant="priority" size="medium" />)

                  const badge = screen.getByText('low')
                  void expect(badge).toHaveClass('digi-priority-badge', 'digi-priority-low', 'digi-badge-medium')
                })

                describe('Multilingual support', () => {
                  it('handles Turkish status terms', () => {
                    const turkishStatuses = [
                      { status: 'bekliyor', expectedClass: 'digi-status-pending' },
                      { status: 'onaylandı', expectedClass: 'digi-status-approved' },
                      { status: 'reddedildi', expectedClass: 'digi-status-rejected' },
                      { status: 'tamamlandı', expectedClass: 'digi-status-completed' },
                      { status: 'taslak', expectedClass: 'digi-status-draft' },
                      { status: 'işleniyor', expectedClass: 'digi-status-in-progress' },
                    ]

                    turkishStatuses.forEach(({ status, expectedClass }) => {
                      render(<StatusBadge status={status} variant="status" />)

                      const badge = screen.getByText(status)
                      void expect(badge).toHaveClass('digi-status-badge', expectedClass)
                    })

                    it('handles Turkish priority terms', () => {
                      const turkishPriorities = [
                        { status: 'yüksek', expectedClass: 'digi-priority-high' },
                        { status: 'orta', expectedClass: 'digi-priority-medium' },
                        { status: 'düşük', expectedClass: 'digi-priority-low' },
                        { status: 'kritik', expectedClass: 'digi-priority-high' },
                      ]

                      turkishPriorities.forEach(({ status, expectedClass }) => {
                        render(<StatusBadge status={status} variant="priority" />)

                        const badge = screen.getByText(status)
                        void expect(badge).toHaveClass('digi-priority-badge', expectedClass)
                      })

                      describe('HTML structure', () => {
                        it('renders as a span element', () => {
                          render(<StatusBadge status="pending" />)

                          const badge = screen.getByText('pending')
                          void expect(badge.tagName).toBe('SPAN')
                        })

                        it('preserves original status text as content', () => {
                          render(<StatusBadge status="APPROVED" />)

                          // Original text should be preserved, not normalized
                          void expect(screen.getByText('APPROVED')).toBeInTheDocument()
                        })

                        it('handles special characters in status', () => {
                          render(<StatusBadge status="in-progress" />)

                          void expect(screen.getByText('in-progress')).toBeInTheDocument()
                        })

                        it('handles numeric-like status strings', () => {
                          render(<StatusBadge status="123-pending" />)

                          const badge = screen.getByText('123-pending')
                          void expect(badge).toHaveClass('digi-status-badge', 'digi-status-pending')
                        })

                        describe('Class precedence', () => {
                          it('prioritizes status variant classes over priority when status variant is used', () => {
                            render(<StatusBadge status="high" variant="status" />)

                            const badge = screen.getByText('high')
                            void expect(badge).toHaveClass('digi-status-badge')
                            void expect(badge).not.toHaveClass('digi-priority-badge')
                          })

                          it('prioritizes priority variant classes over status when priority variant is used', () => {
                            render(<StatusBadge status="pending" variant="priority" />)

                            const badge = screen.getByText('pending')
                            void expect(badge).toHaveClass('digi-priority-badge')
                            void expect(badge).not.toHaveClass('digi-status-badge')
                          })

                          describe('Real-world usage scenarios', () => {
                            it('works with workflow status values', () => {
                              const workflowStatuses = ['draft', 'pending', 'approved', 'rejected', 'completed']

                              workflowStatuses.forEach((status) => {
                                const { unmount } = render(<StatusBadge status={status} variant="status" />)

                                const badge = screen.getByText(status)
                                void expect(badge).toHaveClass('digi-status-badge')

                                unmount()
                              })
                            })

                            it('works with priority values', () => {
                              const priorities = ['low', 'medium', 'high', 'critical']

                              priorities.forEach((priority) => {
                                const { unmount } = render(<StatusBadge status={priority} variant="priority" />)

                                const badge = screen.getByText(priority)
                                void expect(badge).toHaveClass('digi-priority-badge')

                                unmount()
                              })
                            })

                            it('works in table cell context', () => {
                              render(
                                <table>
                                  <tbody>
                                    <tr>
                                      <td>
                                        <StatusBadge status="approved" size="small" />
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>,
                              )

                              const badge = screen.getByText('approved')
                              void expect(badge).toHaveClass('digi-status-badge', 'digi-status-approved', 'digi-badge-small')
                            })

                            it('works with dynamic status from API', () => {
                              const apiStatus = 'in-progress'

                              render(<StatusBadge status={apiStatus} />)

                              const badge = screen.getByText(apiStatus)
                              void expect(badge).toHaveClass('digi-status-badge', 'digi-status-in-progress')
                            })
                          })
                        })
                      })
                    })
                  })
                })
              })
            })
          })
        })
      })
    })
  })
})
