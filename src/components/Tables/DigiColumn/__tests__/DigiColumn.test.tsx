import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { I18nextProvider } from 'react-i18next'
import DigiColumn from '../DigiColumn'
import i18n from '@/i18n'

// Mock the wface components
vi.mock('wface', () => ({
  WBox: ({ children, ...props }: any) => (
    <div data-testid="wbox" {...props}>
      {children}
    </div>
  ),
  WButton: ({ children, onClick, ...props }: any) => (
    <button data-testid="wbutton" onClick={onClick} {...props}>
      {children}
    </button>
  ),
  WPopover: ({ children, open, onClose, ...props }: any) =>
    open ? (
      <div data-testid="wpopover" {...props}>
        {children}
      </div>
    ) : null,
  WTextField: ({ value, onChange, ...props }: any) => <input data-testid="wtextfield" value={value} onChange={(e) => onChange?.(e)} {...props} />,
  WTypography: ({ children, onClick, ...props }: any) => (
    <div data-testid="wtypography" onClick={onClick} {...props}>
      {children}
    </div>
  ),
}))

// Mock the SelectBox component
vi.mock('@/components/formElements', () => ({
  SelectBox: ({ value, onChange, options, ...props }: any) => (
    <select
      data-testid="select-box"
      value={value?.value ?? ''}
      onChange={(e) => {
        const selectedOption = options.find((opt: any) => opt.value === e.target.value)
        onChange(selectedOption)
      }}
      {...props}
    >
      {options?.map((option: any) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}))

// Mock MUI icons
vi.mock('@mui/icons-material/FilterList', () => ({
  default: (props: Record<string, unknown>) => (
    <div data-testid="filter-icon" {...props}>
      FilterList
    </div>
  ),
}))

vi.mock('@mui/icons-material/ArrowUpward', () => ({
  default: (props: Record<string, unknown>) => (
    <div data-testid="arrow-up-icon" {...props}>
      ArrowUpward
    </div>
  ),
}))

vi.mock('@mui/icons-material/ArrowDownward', () => ({
  default: (props: Record<string, unknown>) => (
    <div data-testid="arrow-down-icon" {...props}>
      ArrowDownward
    </div>
  ),
}))

vi.mock('@mui/icons-material/SortRounded', () => ({
  default: (props: Record<string, unknown>) => (
    <div data-testid="sort-icon" {...props}>
      SortRounded
    </div>
  ),
}))

const defaultProps = {
  columnId: 'testColumn',
  title: 'Test Column',
  onApply: vi.fn(),
  onClear: vi.fn(),
  onSort: vi.fn(),
  columnType: 'string',
  currentFilter: null,
  currentSortDirection: null,
  showFilter: true,
  sorting: true,
  onSearch: vi.fn(),
}

const renderWithI18n = (component: React.ReactElement) => {
  return render(<I18nextProvider i18n={i18n}>{component}</I18nextProvider>)
}

describe('DigiColumn', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('Rendering', () => {
    it('renders column title', () => {
      renderWithI18n(<DigiColumn {...defaultProps} />)

      expect(screen.getByText('Test Column')).toBeInTheDocument()
    })

    it('renders sort button when sorting is enabled', () => {
      renderWithI18n(<DigiColumn {...defaultProps} sorting={true} />)

      void expect(screen.getByTestId('sort-icon')).toBeInTheDocument()
    })

    it('does not render sort button when sorting is disabled', () => {
      renderWithI18n(<DigiColumn {...defaultProps} sorting={false} />)

      void expect(screen.queryByTestId('sort-icon')).not.toBeInTheDocument()
    })

    it('renders search field when showFilter is true', () => {
      renderWithI18n(<DigiColumn {...defaultProps} showFilter={true} />)

      const textFields = screen.getAllByTestId('wtextfield')
      void expect(textFields.length).toBeGreaterThan(0)
    })

    it('does not render search field when showFilter is false', () => {
      renderWithI18n(<DigiColumn {...defaultProps} showFilter={false} />)

      void expect(screen.queryByTestId('filter-icon')).not.toBeInTheDocument()
    })

    it('shows different sort icons based on current sort direction', () => {
      const { rerender } = renderWithI18n(<DigiColumn {...defaultProps} currentSortDirection="asc" />)

      void expect(screen.getByTestId('arrow-up-icon')).toBeInTheDocument()

      rerender(
        <I18nextProvider i18n={i18n}>
          <DigiColumn {...defaultProps} currentSortDirection="desc" />
        </I18nextProvider>,
      )

      void expect(screen.getByTestId('arrow-down-icon')).toBeInTheDocument()

      rerender(
        <I18nextProvider i18n={i18n}>
          <DigiColumn {...defaultProps} currentSortDirection={null} />
        </I18nextProvider>,
      )

      void expect(screen.getByTestId('sort-icon')).toBeInTheDocument()
    })

    it('shows active filter icon when currentFilter exists', () => {
      const currentFilter = { type: 'contains', value: 'test' }
      renderWithI18n(<DigiColumn {...defaultProps} currentFilter={currentFilter} />)

      void expect(screen.getByTestId('filter-icon')).toBeInTheDocument()
    })

    describe('Sort functionality', () => {
      it('handles sort click progression (null -> asc -> desc -> null)', () => {
        renderWithI18n(<DigiColumn {...defaultProps} />)

        const titleElement = screen.getByText('Test Column')

        // First click: null -> asc
        void fireEvent.click(titleElement)
        void expect(defaultProps.onSort).toHaveBeenCalledWith('asc')

        // Test with currentSortDirection="asc"
        const { rerender } = renderWithI18n(<DigiColumn {...defaultProps} currentSortDirection="asc" />)

        void vi.clearAllMocks()
        void fireEvent.click(titleElement)
        void expect(defaultProps.onSort).toHaveBeenCalledWith('desc')

        // Test with currentSortDirection="desc"
        rerender(
        <I18nextProvider i18n={i18n}>
            <DigiColumn {...defaultProps} currentSortDirection="desc" />
          </I18nextProvider>,
        )

        void vi.clearAllMocks()
        void fireEvent.click(titleElement)
        void expect(defaultProps.onSort).toHaveBeenCalledWith(null)
      })

      it('handles sort button click', () => {
        renderWithI18n(<DigiColumn {...defaultProps} />)

        const sortButton = screen.getAllByTestId('wbutton')[1] // Second button is sort
        void fireEvent.click(sortButton)

        void expect(defaultProps.onSort).toHaveBeenCalledWith('asc')
      })

      it('does not call sort when sorting is disabled', () => {
        renderWithI18n(<DigiColumn {...defaultProps} sorting={false} />)

        const titleElement = screen.getByText('Test Column')
        void fireEvent.click(titleElement)

        void expect(defaultProps.onSort).not.toHaveBeenCalled()
      })

      describe('Search functionality', () => {
        it('handles search input change', () => {
          renderWithI18n(<DigiColumn {...defaultProps} />)

          const searchField = screen.getAllByTestId('wtextfield')[0] // First text field is search
          void fireEvent.change(searchField, { target: { value: 'search term' } })

          void expect(defaultProps.onSearch).toHaveBeenCalledWith('search term')
        })

        it('updates search term state', () => {
          renderWithI18n(<DigiColumn {...defaultProps} />)

          const searchField = screen.getAllByTestId('wtextfield')[0]
          void fireEvent.change(searchField, { target: { value: 'test search' } })

          void expect(searchField.value).toBe('test search')
        })

        describe('Filter popover', () => {
          it('opens filter popover when filter button is clicked', async () => {
            renderWithI18n(<DigiColumn {...defaultProps} />)

            const filterButton = screen.getAllByTestId('wbutton')[0] // First button is filter
            void fireEvent.click(filterButton)

            await waitFor(() => {
              void expect(screen.getByTestId('wpopover')).toBeInTheDocument()
            })
          })

          it('closes filter popover when close button is clicked', async () => {
            renderWithI18n(<DigiColumn {...defaultProps} />)

              const filterButton = screen.getAllByTestId('wbutton')[0]
              void fireEvent.click(filterButton)

              await waitFor(() => {
                void expect(screen.getByTestId('wpopover')).toBeInTheDocument()
              })

              const cancelButton = screen.getByText('cancel')
              void fireEvent.click(cancelButton)

              // Component should handle closing (state change)
            })
          })

          it('prevents event propagation on popover clicks', async () => {
            const stopPropagation = vi.fn()
            renderWithI18n(<DigiColumn {...defaultProps} />)

              const filterButton = screen.getAllByTestId('wbutton')[0]
              void fireEvent.click(filterButton)

              await waitFor(() => {
                const popover = screen.getByTestId('wpopover')
                void fireEvent.click(popover, { stopPropagation })
              })

            })
          })
        })

        describe('Filter types by column type', () => {
          it('shows string filter types for string columns', async () => {
            renderWithI18n(<DigiColumn {...defaultProps} columnType="string" />)

                  const filterButton = screen.getAllByTestId('wbutton')[0]
                  void fireEvent.click(filterButton)

            await waitFor(() => {
              const selectBox = screen.getByTestId('select-box')
              const options = selectBox.querySelectorAll('option')

                    const optionValues = Array.from(options).map((option) => option.getAttribute('value'))
                    void expect(optionValues).toContain('contains')
                    void expect(optionValues).toContain('equals')
                    void expect(optionValues).toContain('startsWith')
                    void expect(optionValues).toContain('endsWith')
                  })

                  it('shows number filter types for number columns', async () => {
                    renderWithI18n(<DigiColumn {...defaultProps} columnType="number" />)

                    const filterButton = screen.getAllByTestId('wbutton')[0]
                    void fireEvent.click(filterButton)

                    await waitFor(() => {
                      const selectBox = screen.getByTestId('select-box')
                      const options = selectBox.querySelectorAll('option')

                      const optionValues = Array.from(options).map((option) => option.getAttribute('value'))
                      void expect(optionValues).toContain('equals')
                      void expect(optionValues).toContain('isLessThan')
                      void expect(optionValues).toContain('isGreaterThan')
                      void expect(optionValues).toContain('isLessThanOrEqualTo')
                      void expect(optionValues).toContain('isGreaterThanOrEqualTo')
                    })

                    it('shows date filter types for date columns', async () => {
                      renderWithI18n(<DigiColumn {...defaultProps} columnType="date" />)

                      const filterButton = screen.getAllByTestId('wbutton')[0]
                      void fireEvent.click(filterButton)

                      await waitFor(() => {
                        const selectBox = screen.getByTestId('select-box')
                        const options = selectBox.querySelectorAll('option')

                        const optionValues = Array.from(options).map((option) => option.getAttribute('value'))
                        void expect(optionValues).toContain('on')
                        void expect(optionValues).toContain('before')
                        void expect(optionValues).toContain('after')
                        void expect(optionValues).toContain('between')
                      })

                      describe('Filter application', () => {
                        it('applies filter when apply button is clicked', async () => {
                          renderWithI18n(<DigiColumn {...defaultProps} />)

                          const filterButton = screen.getAllByTestId('wbutton')[0]
                          void fireEvent.click(filterButton)

                          await waitFor(() => {
                            // Select filter type
                            const selectBox = screen.getByTestId('select-box')
                            void fireEvent.change(selectBox, { target: { value: 'contains' } })
          })

                          await waitFor(() => {
                            // Enter filter value
                            const textFields = screen.getAllByTestId('wtextfield')
                            const valueField = textFields.find((field) => field !== screen.getAllByTestId('wtextfield')[0]) // Not the search field
                            if (valueField) {
                              void fireEvent.change(valueField, { target: { value: 'test value' } })
                            }
                          })

                          const applyButton = screen.getByText('apply')
                          void fireEvent.click(applyButton)

                          void expect(defaultProps.onApply).toHaveBeenCalledWith({
                            type: 'contains',
                            value: 'test value'
          })

                          it('clears filter when clear button is clicked', async () => {
                            renderWithI18n(<DigiColumn {...defaultProps} />)

                            const filterButton = screen.getAllByTestId('wbutton')[0]
                            void fireEvent.click(filterButton)

                            await waitFor(() => {
                              const clearButton = screen.getByText('clear')
                              void fireEvent.click(clearButton)
                            })

                            void expect(defaultProps.onClear).toHaveBeenCalled()
                          })

                          describe('Date filter special cases', () => {
                            it('handles date between filter with two date inputs', async () => {
                              renderWithI18n(<DigiColumn {...defaultProps} columnType="date" />)

                              const filterButton = screen.getAllByTestId('wbutton')[0]
                              void fireEvent.click(filterButton)

                              await waitFor(() => {
                                const selectBox = screen.getByTestId('select-box')
                                void fireEvent.change(selectBox, { target: { value: 'between' } })

                              await waitFor(() => {
                                const textFields = screen.getAllByTestId('wtextfield')
                                // Should have search field + 2 date fields for between filter
                                void expect(textFields.length).toBeGreaterThanOrEqual(3)
                              })

                              it('handles single date filter', async () => {
                                renderWithI18n(<DigiColumn {...defaultProps} columnType="date" />)

                                const filterButton = screen.getAllByTestId('wbutton')[0]
                                void fireEvent.click(filterButton)

                                await waitFor(() => {
                                  const selectBox = screen.getByTestId('select-box')
                                  void fireEvent.change(selectBox, { target: { value: 'on' } })
          })

                                await waitFor(() => {
                                  const textFields = screen.getAllByTestId('wtextfield')
                                  // Should have search field + 1 date field
                                  void expect(textFields.length).toBeGreaterThanOrEqual(2)
                                })

                                describe('Current filter synchronization', () => {
                                  it('syncs with external filter changes', () => {
                                    const currentFilter = { type: 'contains', value: 'test' }
                                    const { rerender } = renderWithI18n(<DigiColumn {...defaultProps} currentFilter={currentFilter} />)

                                    // Change the current filter
                                    const newFilter = { type: 'equals', value: 'new test' }
                                    rerender(
        <I18nextProvider i18n={i18n}>
                                        <DigiColumn {...defaultProps} currentFilter={newFilter} />
                                      </I18nextProvider>,
                                    )

                                    // The component should sync its internal state
                                  })

                                  it('handles null current filter', () => {
                                    renderWithI18n(<DigiColumn {...defaultProps} currentFilter={null} />)

                                    // Should not crash with null filter
                                  })

                                  it('resets to default when current filter is cleared', () => {
                                    const currentFilter = { type: 'contains', value: 'test' }
                                    const { rerender } = renderWithI18n(<DigiColumn {...defaultProps} currentFilter={currentFilter} />)

                                    rerender(
        <I18nextProvider i18n={i18n}>
                                        <DigiColumn {...defaultProps} currentFilter={null} />
                                      </I18nextProvider>,
                                    )

                                    // Should reset internal state
                                  })

                                  describe('Event handling', () => {
                                    it('prevents event propagation on filter button click', () => {
                                      const mockEvent = {
                                        stopPropagation: vi.fn(),
                                        currentTarget: window.document.createElement('button'),
                                      }

                                      renderWithI18n(<DigiColumn {...defaultProps} />)

                                      const filterButton = screen.getAllByTestId('wbutton')[0]
                                      void fireEvent.click(filterButton, mockEvent)

                                      // Event propagation should be stopped
                                    })

                                    it('prevents event propagation on sort button click', () => {
                                      const mockEvent = {
                                        stopPropagation: vi.fn(),
                                        currentTarget: window.document.createElement('button')
                                      }

                                      renderWithI18n(<DigiColumn {...defaultProps} />)

                                      const sortButton = screen.getAllByTestId('wbutton')[1]
                                      void fireEvent.click(sortButton, mockEvent)

                                      // Event propagation should be stopped
                                    })
                                  })

                                  describe('Edge cases', () => {
                                    it('handles empty title', () => {
                                      renderWithI18n(<DigiColumn {...defaultProps} title="" />)

                                      // Should render without crashing
                                    })

                                    it('handles invalid column type', () => {
                                      renderWithI18n(<DigiColumn {...defaultProps} columnType="invalid" />)

                                      // Should default to string filter types
                                    })

                                    it('handles missing translation keys gracefully', () => {
                                      renderWithI18n(<DigiColumn {...defaultProps} />)

                                      // Should not crash if translation keys are missing
                                    })
                                  })

                                  describe('Accessibility', () => {
                                    it('has proper button roles', () => {
                                      renderWithI18n(<DigiColumn {...defaultProps} />)

                                      const buttons = screen.getAllByTestId('wbutton')
                                      void expect(buttons.length).toBeGreaterThan(0)
                                    })

                                    it('has clickable title for sorting when enabled', () => {
                                      renderWithI18n(<DigiColumn {...defaultProps} sorting={true} />)

                                      const titleElement = screen.getByText('Test Column')
                                      void expect(titleElement).toBeInTheDocument()
                                    })

                                    it('does not make title clickable when sorting is disabled', () => {
                                      renderWithI18n(<DigiColumn {...defaultProps} sorting={false} />)

                                      const titleElement = screen.getByText('Test Column')
                                      void expect(titleElement).toBeInTheDocument()
                                    })

                                    describe('Popover ID generation', () => {
                                      it('generates correct popover ID when open', async () => {
                                        renderWithI18n(<DigiColumn {...defaultProps} columnId="testCol" />)

                                        const filterButton = screen.getAllByTestId('wbutton')[0]
                                        void fireEvent.click(filterButton)

                                        await waitFor(() => {
                                          const popover = screen.getByTestId('wpopover')
                                          void expect(popover).toHaveAttribute('id', 'filter-popover-testCol')
                                        })
                                      })
                                    })
                                  })
                                })
                              })
                            })
                          })
                        })
                      })
                    })
                  })
                })
              })
            })
          })
        })
      })
    })
  })
})