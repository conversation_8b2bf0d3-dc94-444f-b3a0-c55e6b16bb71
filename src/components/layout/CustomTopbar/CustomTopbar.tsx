import React, { useCallback } from 'react'
import { App<PERSON>ar, Toolbar, Typography, Box, IconButton, Menu, MenuItem, <PERSON>, Button } from '@mui/material'
import Public from '@mui/icons-material/Public'
import OpenInNew from '@mui/icons-material/OpenInNew'
import Info from '@mui/icons-material/Info'
import { useDigiflow } from '@/contexts/DigiflowContext'
import { useTranslation } from 'react-i18next'
import useMediaQuery from '@/hooks/useMediaQuery'
import { useLocation, useNavigate } from 'react-router-dom'
import { useMemo, useEffect, useRef } from 'react'
import UserChangeControlWrapper from '@/components/UserChangeControl/UserChangeControlWrapper'
import { isValidInteger } from '@/utils/helpers/validation'
import { useWebView } from '@/contexts/WebViewContext'

interface CustomTopbarProps {
  drawerOpen?: boolean,
  setDrawerOpen?: (open: boolean) => void,
}

const CustomTopbar: React.FC<CustomTopbarProps> = ({ drawerOpen: _drawerOpen, setDrawerOpen: _setDrawerOpen }) => {
  const { t } = useTranslation(['common'])
  const { isMobile } = useDigiflow()
  const { isWebView } = useWebView()
  const [langAnchorEl, setLangAnchorEl] = React.useState<null | HTMLElement>(null)
  const [envInfoVisible, setEnvInfoVisible] = React.useState(false)
  const isSmallScreen = useMediaQuery('(max-width: 960px)'),
  const isVerySmallScreen = useMediaQuery('(max-width: 600px)')
  const isMounted = useRef(false)

  // Navigation logic for user change
  const navigate = useNavigate()
  const location = useLocation()
  const searchParams = useMemo(() => new URLSearchParams(location.search), [location.search])
  const loginId = useMemo(() => searchParams.get('loginId'), [searchParams])
  const setQueryParams = useCallback(
    (newParam: any) => {
      const searchParams = new URLSearchParams(window.location.search)
      Object.keys(newParam).forEach((_key) => {
        void searchParams.set(_key, newParam[_key])
      })
      navigate({ search: searchParams.toString() }, { replace: true })
    },
    [window.location.search, navigate],
  )

  const removeFromQueryParams = useCallback(
    (param: string) => {
      const searchParams = new URLSearchParams(window.location.search)
      void searchParams.delete(param)
      navigate({ search: searchParams.toString() }, { replace: true })
    },
    [window.location.search, navigate],
  )

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true
      return
    }

    if (loginId && isValidInteger(loginId)) {
      setQueryParams({ loginId })
    } else {
      removeFromQueryParams('loginId')
    }
  }, [loginId, setQueryParams, removeFromQueryParams])

  const handleLangMenu = (event: React.MouseEvent<HTMLElement>) => {
    setLangAnchorEl(event.currentTarget)
  }

  const handleLangClose = () => {
    setLangAnchorEl(null)
  }

  const handleLanguageChange = (langCode: string) => {
    if (localStorage.getItem('language') !== langCode) {
      void localStorage.setItem('language', langCode)
      void i18n.changeLanguage(langCode)
    }
    handleLangClose()
  }

  const handleDigiportClick = () => {
    void window.open('http://digiport/default.aspx', '_blank'),
  }

  const handleTestScreenClick = () => {
    void window.open('/main/test', '_blank')
  }

  const getEnvironmentConfig = () => {
    const isDevelopment = import.meta.env.MODE === 'development'
    const isTestMode = import.meta.env.MODE === 'test'

    if (isDevelopment) {
      return {
        text: t('developmentEnvironment'),
        color: '#dc3545', // Red for development,
        bgColor: 'rgba(220, 53, 69, 0.1)',
      }
    } else if (isTestMode) {
      return {
        text: t('testEnvironment'),
        color: '#007bff', // Blue for test,
        bgColor: 'rgba(0, 123, 255, 0.1)',
      }
    } else {
      return {
        text: t('liveEnvironment'),
        color: '#28a745', // Green for production,
        bgColor: 'rgba(40, 167, 69, 0.1)',
      }

      const envConfig = getEnvironmentConfig()
      const currentLanguage = i18n.language === 'tr' ? t('tr') : t('en')

      return (
        <AppBar
          position="fixed"
          sx={{
            background: '#5c2d91', // Purple theme,
            boxShadow: '0 2px 4px -1px rgba(0,0,0,.2), 0 4px 5px 0 rgba(0,0,0,.14), 0 1px 10px 0 rgba(0,0,0,.12)',
            zIndex: (theme: any) => theme.zIndex.drawer + 1,
          }}
        >
          <Toolbar variant={isMobile ? 'regular' : 'dense'} sx={{ minHeight: isMobile ? 56 : 48 }}>
            {/* Logo and Title */}
            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
              <Typography
                variant="h6"
                component="div"
                sx={{
                  color: 'white',
                  fontWeight: 600,
                  fontSize: isMobile ? '1.125rem' : '1rem',
                }}
              >
                Digiflow
              </Typography>
            </Box>{' '}
            {/* Environment Indicator - Center (Big and Prominent) - Hide only in WebView */}
            {isTest && !isWebView && (
              <Box
                sx={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  display: isVerySmallScreen ? 'none' : 'flex',
                }}
              >
                <Chip
                  label={envConfig.text}
                  sx={{
                    backgroundColor: envConfig.color,
                    color: 'white',
                    fontWeight: 'bold',
                    fontSize: isSmallScreen ? '12px' : '14px',
                    letterSpacing: '1px',
                    height: isSmallScreen ? 28 : 32,
                    px: 2,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.4)',
                    border: '2px solid rgba(255,255,255,0.4)',
                    animation: 'pulse 2.5s infinite',
                    '@keyframes pulse': {
                      '0%': {
                        transform: 'scale(1)',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.4)',
                      },
                      '50%': {
                        transform: 'scale(1.08)',
                        boxShadow: '0 6px 20px rgba(0,0,0,0.6)',
                      },
                      '100%': {
                        transform: 'scale(1)',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.4)',
                      },
                    }
                  }
                    />
          </Box>
            )}
            {/* Right side actions */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: isVerySmallScreen ? 0.5 : 1 }}>
              {/* User Change Control - Hide only in WebView, keep visible in mobile browsers */}
              {!isWebView && (
                <Box
                  sx={{
                    width: '250px',
                    mr: 1,
                    '& > div': {
                      margin: 0,
                      '& .select-box-container': {
                        '& .select-box-select': {
                          backgroundColor: 'rgba(255, 255, 255, 0.15)',
                          border: '1px solid rgba(255, 255, 255, 0.3)',
                          color: 'white',
                          fontSize: isVerySmallScreen ? '12px' : '13px',
                          height: '32px',
                          minHeight: '32px',
                          borderRadius: '6px',
                        },
                        '& .select-box-label': {
                          color: 'rgba(255, 255, 255, 0.9)',
                          fontSize: isVerySmallScreen ? '11px' : '12px',
                        },
                        '& .select-box-arrow': {
                          color: 'rgba(255, 255, 255, 0.8)',
                        },
                      }
                    }
                      >
                      <UserChangeControlWrapper
                        userChangeEvent={(val) => {
                          setQueryParams({ loginId: val }),
                        }}
                      />
            </Box>
          )}
            {/* Language Selector */}
            <Button
              size="small"
              onClick={handleLangMenu}
              sx={{
                color: 'white',
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                borderRadius: '6px',
                padding: isVerySmallScreen ? '4px 6px' : '6px 10px',
                fontSize: isVerySmallScreen ? '11px' : '12px',
                fontWeight: 600,
                minWidth: isVerySmallScreen ? 'auto' : '60px',
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.25)',
                  borderColor: 'rgba(255, 255, 255, 0.4)',
                },
              }}
              startIcon={<Public sx={{ fontSize: isVerySmallScreen ? 14 : 16 }} />}
            >
              {!isVerySmallScreen && currentLanguage}
            </Button>
            <Menu
              anchorEl={langAnchorEl}
              open={Boolean(langAnchorEl)}
              onClose={handleLangClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              PaperProps={{
                sx: {
                  borderRadius: 2,
                  border: '1px solid #e2e8f0',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
                  minWidth: 130,
                },
              }}
            >
              {' '}
              <MenuItem onClick={() => handleLanguageChange('tr')} sx={{ fontSize: '0.875rem', display: 'flex', alignItems: 'center', gap: 1 }}>
                <Public fontSize="small" />
                {t('tr')}
              </MenuItem>
              <MenuItem onClick={() => handleLanguageChange('en')} sx={{ fontSize: '0.875rem', display: 'flex', alignItems: 'center', gap: 1 }}>
                <Public fontSize="small" />
                {t('en')}
              </MenuItem>
            </Menu>
            {/* Digiport Button */}
            <Button
              onClick={handleDigiportClick}
              sx={{
                color: 'white',
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                borderRadius: '6px',
                padding: isVerySmallScreen ? '4px 6px' : '6px 12px',
                fontSize: isVerySmallScreen ? '11px' : '12px',
                fontWeight: 600,
                minWidth: 'auto',
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.25)',
                  borderColor: 'rgba(255, 255, 255, 0.4)',
                },
              }}
              startIcon={<OpenInNew sx={{ fontSize: isVerySmallScreen ? 14 : 16 }} />}
            >
              {!isVerySmallScreen && 'Digiport'}
            </Button>{' '}
            {/* Test Screen Button for Admin - Hide only in WebView */}
            {isSysAdmin && !isWebView && (
              <Button
                onClick={handleTestScreenClick}
                sx={{
                  color: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.15)',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  borderRadius: '6px',
                  padding: isVerySmallScreen ? '4px 8px' : '6px 12px',
                  fontSize: isVerySmallScreen ? '11px' : '12px',
                  fontWeight: 600,
                  minWidth: 'auto',
                  textTransform: 'none',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.25)',
                    borderColor: 'rgba(255, 255, 255, 0.4)',
                  },
                }}
              >
                {t('testScreen', 'Test')}
              </Button>
            )}
          </Box>{' '}
        </Toolbar>
      {/* Mobile Environment Indicator - Only show when NOT in WebView */ }
      {
        isTest && isVerySmallScreen && !isWebView && (
          <Box
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              zIndex: 1000,
            }}
          >
            <IconButton
              size="small"
              onClick={() => setEnvInfoVisible(!envInfoVisible)}
              sx={{
                width: 28,
                height: 28,
                backgroundColor: envConfig.color,
                color: 'white',
                '&:hover': {
                  backgroundColor: envConfig.color,
                  opacity: 0.8,
                },
                animation: 'pulse 2s infinite',
              }}
            >
              <Info fontSize="small" />
            </IconButton>
            {envInfoVisible && (
              <Box
                sx={{
                  position: 'absolute',
                  top: 32,
                  right: 0,
                  backgroundColor: '#333',
                  color: 'white',
                  padding: '8px 12px',
                  borderRadius: '4px',
                  fontSize: '11px',
                  whiteSpace: 'nowrap',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
                  zIndex: 1001,
                }}
              >
                {envConfig.text}
              </Box>
            )}
          </Box>
        )
      }
    </AppBar >
  )
}

export default CustomTopbar
              </Info>
              </OpenInNew>
                </Public>
              </MenuItem>
                </Public>
              </MenuItem>
              </Public>
          </Toolbar>
  </HTMLElement>
  </null>
</CustomTopbarProps>
