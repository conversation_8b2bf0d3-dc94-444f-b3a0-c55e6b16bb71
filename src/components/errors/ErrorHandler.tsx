import React from 'react'
import { useTranslation } from 'react-i18next'
import AuthenticationError from './AuthenticationError'
import ServiceUnavailableError from './ServiceUnavailableError'

interface ErrorHandlerProps {
  error: Error | unknown
  onRetry?: () => void
  fallback?: React.ReactNode
}

/**
 * Smart error handler that shows the appropriate error component based on error type
 */
export const ErrorHandler: React.FC<ErrorHandlerProps> = ({ error, onRetry, fallback }) => {
  const { t } = useTranslation(['errors'])

  // Check for authentication errors
  if (
    (error as Error & { isAuthenticationError?: boolean; response?: { status?: number } })?.isAuthenticationError ||
    (error as Error & { response?: { status?: number } })?.response?.status === 401
  ) {
    return <AuthenticationError onRetry={onRetry} />
  }

  // Check for service unavailable errors
  if (
    (error as Error & { isServiceUnavailable?: boolean })?.isServiceUnavailable ||
    (!(error as Error & { response?: unknown })?.response &&
      ((error as Error & { code?: string })?.code === 'ERR_NETWORK' ||
        (error as Error & { code?: string })?.code === 'ECONNABORTED' ||
        !window.navigator.onLine)) ||
    ((error as Error & { response?: { status?: number } })?.response?.status &&
      [502, 503, 504].includes((error as Error & { response?: { status?: number } })?.response?.status ?? 0))
  ) {
    return <ServiceUnavailableError onRetry={onRetry} />
  }

  // Show fallback for other errors
  if (fallback) {
    return <>{fallback}</>
  }

  // Default error display
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="text-center max-w-md">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">{t('unexpectedError')}</h2>
        <p className="text-gray-600 mb-6">{(error as Error)?.message ?? t('unexpectedError')}</p>
        {onRetry && (
          <button onClick={onRetry} className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            {t('tryAgain')}
          </button>
        )}
      </div>
    </div>
  )
}

export default ErrorHandler
