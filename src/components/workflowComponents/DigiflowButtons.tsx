import { WButton, WTypography } from 'wface'
import { CSSProperties } from 'react'
import { WorkflowComponentType } from '@/types'
import { useTranslation } from 'react-i18next'
import useMediaQuery from '@/hooks/useMediaQuery'
import './workflow.style.css' // Import the CSS file

const WorkflowButtons = ({
  selectedWorkflow,
  activeUserId,
  wfInstanceId,
  refInstanceId,
  copyInstanceId,
}: {
  selectedWorkflow: WorkflowComponentType,
  activeUserId: any,
  wfInstanceId: any,
  refInstanceId: any,
  copyInstanceId: any,
}) => {
  const { t } = useTranslation('workflowLayout')
  const isMobile = useMediaQuery('(max-width: 901px)')

  const baseDesktopUrl = `${import.meta.env.VITE_APP_OLD_URL}/WFPages/${selectedWorkflow.flowLink}`
  const baseMobileUrl = `${import.meta.env.VITE_APP_OLD_URL}/MobilPages/${selectedWorkflow.mobilePage}`

  const queryParams = new URLSearchParams()
  if (activeUserId) queryParams.append('loginId', activeUserId)
  if (wfInstanceId != null) queryParams.append('wfInstanceId', wfInstanceId)
  if (refInstanceId != null) queryParams.append('refInstanceId', refInstanceId)
  if (copyInstanceId != null) queryParams.append('copyInstanceId', copyInstanceId)

  const desktopUrl = `${baseDesktopUrl}?${queryParams.toString()}`
  const mobileUrl = `${baseMobileUrl}?${queryParams.toString()}`

  const containerStyle: CSSProperties = {
    display: 'flex',
    marginTop: !isMobile ? '-2rem' : 0,
    flexDirection: !isMobile ? 'row' : 'column',
    alignItems: !isMobile ? 'center' : 'stretch',
    justifyContent: !isMobile ? 'space-between' : 'flex-start',
    gap: '1rem',
  }

  const buttonContainerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: isMobile ? 'row' : 'column',
    gap: '0.5rem',
    justifyContent: isMobile ? 'center' : 'flex-start',
    marginTop: selectedWorkflow.mobilePage ? '-0.5rem' : '-1rem',
    marginBottom: selectedWorkflow.mobilePage ? '2rem' : undefined,
  }

  return (
    <div style={containerStyle}>
      <div />
      <div style={buttonContainerStyle}>
        {!isMobile && (
          <WButton className="digiflow-styled-button" onClick={() => window.open(desktopUrl, '_blank')} variant="contained">
            <WTypography>{t('digiflowWorkflowLink')}</WTypography>
          </WButton>
        )}
        {selectedWorkflow.mobilePage && isMobile && (
          <WButton className="digiflow-styled-button" onClick={() => window.open(mobileUrl, '_blank')} variant="contained">
            <WTypography fontSize="0.8rem">{t('digiflowWorkflowMobileLink')}</WTypography>
          </WButton>
        )}
      </div>
  )
}

export default WorkflowButtons
      </div>
    </div>
