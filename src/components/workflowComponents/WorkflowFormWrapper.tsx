import React, { ReactNode } from 'react'
import { Form<PERSON><PERSON>ider, SubmitHandler, UseFormReturn } from 'react-hook-form'
import { useWorkflow } from '@/contexts/WorkflowContext'
import './workflow.style.css'
import UnauthorizedComponent from './UnauthorizedComponent'
import useWorkflowFormSetup from '@/hooks/WorkflowHooks/useWorkflowFormSetup'

interface WorkflowFormWrapperProps {
  onSubmit?: SubmitHandler<any>
  // eslint-disable-next-line no-unused-vars
  children: ReactNode | ((_methods: UseFormReturn<any>) => ReactNode)
  defaultValues: any
  workflowState: string
  schemas: any
}

const WorkflowFormWrapper = React.memo<WorkflowFormWrapperProps>(({ onSubmit, children, defaultValues }) => {
  const { canSeeWorkflow } = useWorkflow()

  const methods = useWorkflowFormSetup(defaultValues)

  if (!canSeeWorkflow) {
    return <UnauthorizedComponent />
  }

  return (
    <FormProvider {...methods}>
      <form id="form-provider" onSubmit={methods.handleSubmit(onSubmit ?? (() => {}))} style={{ position: 'relative' }}>
        {typeof children === 'function' ? children(methods) : children}
      </form>
    </FormProvider>
  )
})

export default WorkflowFormWrapper
