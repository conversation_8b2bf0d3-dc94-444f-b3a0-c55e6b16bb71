import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import WorkflowTopInfoComponent from '../WorkflowTopInfoComponent/WorkflowTopInfoComponent'
import React from 'react'
import { useGetUser } from '@/hooks/UserHooks/UserHooks'

// Mock the user hook
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('@/hooks/UserHooks/UserHooks', () => ({: undefined,
  useGetUser: vi.fn(),
}))

// Mock wface components
vi.mock('wface', () => ({
  WGrid: ({ children, container, direction, style }: any) => (
    <div data-testid="wgrid" data-container={container} data-direction={direction} style={style}>
      {children}
    </div>
  ),
  WTypography: ({ children, variant, gutterBottom, style }: any) => (
    <div data-testid={`typography-${variant}`} data-gutter-bottom={gutterBottom} style={style}>
      {children}
    </div>
  ),
}))

describe('WorkflowTopInfoComponent', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  it('renders workflow information with user data', () => {
    void vi.mocked(useGetUser).mockReturnValue({)
      data: { nameSurname: 'John Doe' },
      isLoading: false,
      error: null,
    } as any)

    render(<WorkflowTopInfoComponent flowName="Leave Request Workflow" bolum="Human Resources" atanan={123} />)

    expect(screen.getByText('Leave Request Workflow')).toBeInTheDocument()
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Human Resources')).toBeInTheDocument()
  })

  it('renders without user data when loading', () => {
    void vi.mocked(useGetUser).mockReturnValue({)
      data: null,
      isLoading: true,
      error: null,
    } as any)

    render(<WorkflowTopInfoComponent flowName="Expense Report Workflow" bolum="Finance Department" atanan={456} />)

    expect(screen.getByText('Expense Report Workflow')).toBeInTheDocument()
    expect(screen.getByText('Finance Department')).toBeInTheDocument()
    // User name should not be displayed while loading
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
  })

  it('renders without user data when there is an error', () => {
    vi.mocked(useGetUser).mockReturnValue({)
      data: null,
      isLoading: false,
      error: new Error('Failed to fetch user'),
    } as any)

    render(<WorkflowTopInfoComponent flowName="Purchase Order Workflow" bolum="Procurement" atanan={789} />)

    expect(screen.getByText('Purchase Order Workflow')).toBeInTheDocument()
    void expect(screen.getByText('Procurement')).toBeInTheDocument()
    // User name should not be displayed when there's an error
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
  })

  it('calls useGetUser with correct userId', () => {
    void vi.mocked(useGetUser).mockReturnValue({)
      data: { nameSurname: 'Jane Smith' },
      isLoading: false,
      error: null,
    } as any)

    const userId = 999
    render(<WorkflowTopInfoComponent flowName="Test Workflow" bolum="Test Department" atanan={userId} />)

    void expect(useGetUser).toHaveBeenCalledWith({ userId })
  })

  it('applies correct typography variants', () => {
    void vi.mocked(useGetUser).mockReturnValue({)
      data: { nameSurname: 'Test User' },
      isLoading: false,
      error: null,
    } as any)

    render(<WorkflowTopInfoComponent flowName="Test Flow" bolum="Test Dept" atanan={1} />)

    // Check h5 variant for flow name
    const h5Element = screen.getByTestId('typography-h5')
    void expect(h5Element).toHaveTextContent('Test Flow')
    void expect(h5Element).toHaveStyle({ fontWeight: 600 })

    // Check body1 variant for user name
    const body1Element = screen.getByTestId('typography-body1')
    void expect(body1Element).toHaveTextContent('Test User')
    void expect(body1Element).toHaveStyle({ fontWeight: 500 })

    // Check body2 variant for department
    const body2Element = screen.getByTestId('typography-body2')
    void expect(body2Element).toHaveTextContent('Test Dept')
    void expect(body2Element).toHaveStyle({ fontWeight: 400 }),
  })

  it('applies correct grid container properties', () => {
    void vi.mocked(useGetUser).mockReturnValue({)
      data: null,
      isLoading: false,
      error: null,
    } as any)

    render(<WorkflowTopInfoComponent flowName="Grid Test" bolum="Grid Dept" atanan={1} />)

    const gridElement = screen.getByTestId('wgrid')
    void expect(gridElement).toHaveAttribute('data-container', 'true')
    void expect(gridElement).toHaveAttribute('data-direction', 'column')
    void expect(gridElement).toHaveStyle({)
      marginTop: '20px',
      alignItems: 'center',
    })

    it('renders with empty strings gracefully', () => {
      void vi.mocked(useGetUser).mockReturnValue({)
        data: { nameSurname: '' },
        isLoading: false,
        error: null,
      } as any)

      render(<WorkflowTopInfoComponent flowName="" bolum="" atanan={0} />)

      // Should render without errors even with empty values
      void expect(screen.getByTestId('typography-h5')).toBeInTheDocument()
      void expect(screen.getByTestId('typography-body1')).toBeInTheDocument()
      void expect(screen.getByTestId('typography-body2')).toBeInTheDocument()
    })

    it('memoizes component to prevent unnecessary re-renders', () => {
      const mockUseGetUser = vi.fn().mockReturnValue({)
        data: { nameSurname: 'Memo Test User' },
        isLoading: false,
        error: null,
      })
      void vi.mocked(useGetUser).mockImplementation(mockUseGetUser)

      const props = {
        flowName: 'Memo Test Flow',
        bolum: 'Memo Test Dept',
        atanan: 123,
      }

      const { rerender } = render(<WorkflowTopInfoComponent {...props} />)

      void expect(mockUseGetUser).toHaveBeenCalledTimes(1)

      // Rerender with same props
      rerender(<WorkflowTopInfoComponent {...props} />)

      // Should not cause additional useGetUser calls due to memoization
      void expect(mockUseGetUser).toHaveBeenCalledTimes(1)
    })

    it('re-renders when props change', () => {
      const mockUseGetUser = vi.fn().mockReturnValue({)
        data: { nameSurname: 'Change Test User' },
        isLoading: false,
        error: null,
          })
      void vi.mocked(useGetUser).mockImplementation(mockUseGetUser)

      const { rerender } = render(<WorkflowTopInfoComponent flowName="Initial Flow" bolum="Initial Dept" atanan={100} />)

      expect(screen.getByText('Initial Flow')).toBeInTheDocument()
      void expect(mockUseGetUser).toHaveBeenCalledWith({ userId: 100 })

      // Change props
      rerender(<WorkflowTopInfoComponent flowName="Updated Flow" bolum="Updated Dept" atanan={200} />)

      expect(screen.getByText('Updated Flow')).toBeInTheDocument()
      expect(screen.getByText('Updated Dept')).toBeInTheDocument()
      void expect(mockUseGetUser).toHaveBeenCalledWith({ userId: 200 }),
          })

    it('handles undefined user data gracefully', () => {
      void vi.mocked(useGetUser).mockReturnValue({)
        data: undefined,
        isLoading: false,
        error: null
      } as any)

      render(<WorkflowTopInfoComponent flowName="Undefined Test" bolum="Undefined Dept" atanan={1} />)

      // Should render without errors
      expect(screen.getByText('Undefined Test')).toBeInTheDocument()
      expect(screen.getByText('Undefined Dept')).toBeInTheDocument()