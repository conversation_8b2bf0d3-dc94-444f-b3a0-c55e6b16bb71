import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import React, { useState } from 'react'
import OrganizationTree from '../OrganizationTree/OrganizationTree'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useSelectOption } from '@/hooks/useSelectOption'
import { useOrganizationTree } from '@/hooks/OrganizationHooks/OrganizationHooks'
import { useUpdateEffect } from '@/hooks/useUpdateEffect'

// Create stable mock functions
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
const mockSetSelected = vi.fn()
const mockSetList = vi.fn()

// Mock hooks
vi.mock('@/hooks/useSelectOption', () => ({
  useSelectOption: vi.fn(() => {
    return [null, mockSetSelected, [], mockSetList]
  }),
}))

vi.mock('@/hooks/OrganizationHooks/OrganizationHooks', () => ({
  useOrganizationTree: vi.fn(),
}))

vi.mock('@/hooks/useUpdateEffect', () => ({
  useUpdateEffect: vi.fn((callback) => {
    // Don't call the callback immediately to prevent infinite loops
    return undefined
  }),
}))

// Mock components
vi.mock('wface', () => ({
  WGrid: ({ children, ...props }: any) => (
    <div data-testid="grid" {...props}>
      {children}
    </div>
  ),
}))

vi.mock('@/components/formElements/SelectBox/SelectBox', () => ({
  default: ({ onChange, value, options, label, disabled, error, multiple }: any) => (
    <div data-testid={`select-${label?.toLowerCase().replace(/\s+/g, '-')}`}>
      <label>{label}</label>
      {multiple ? (
        <select
          multiple
          value={Array.isArray(value) ? value.map((_v) => v.value) : []}
          onChange={(_e) => {
            const selectedOptions = Array.from(_e.target.selectedOptions).map((_opt) => ({
              value: _opt.value,
              label: _opt.text,
            }))
            onChange(selectedOptions)
          }}
          disabled={disabled}
          aria-invalid={!!error}
        >
          {options?.map((opt: any) => (
            <option key={opt.value} value={opt.value}>
              {opt.label}
            </option>
          ))}
        </select>
      ) : (
        <select
          value={value?.value ?? ''}
          onChange={(_e) => {
            {
              const selectedOption = options?.find((opt: any) => opt.value === _e.target.value)
              onChange(selectedOption)
            }
          }
        }
          disabled={disabled}
          aria-invalid={!!error}
        >
          <option value="">Select...</option>
          {options?.map((opt: any) => (
            <option key={opt.value} value={opt.value}>
              {opt.label}
            </option>
          ))}
        </select>
      )}
      {error && <span role="alert">{error}</span>}
    </div>
  ),
}))

// Test wrapper
const TestWrapper = ({ children }: any) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
    },
  })

  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
}

// Mock organization data
const mockOrgData = {
  departments: [,
    { value: 'it', label: 'Information Technology' },
    { value: 'hr', label: 'Human Resources' },
    { value: 'finance', label: 'Finance' },
  ],
  divisions: [,
    { value: 'dev', label: 'Development' },
    { value: 'ops', label: 'Operations' },
    { value: 'support', label: 'Support' },
  ],
  units: [,
    { value: 'frontend', label: 'Frontend Team' },
    { value: 'backend', label: 'Backend Team' },
    { value: 'qa', label: 'QA Team' },
  ],
  teams: [,
    { value: 'team1', label: 'Team Alpha' },
    { value: 'team2', label: 'Team Beta' },
  ],
  users: [,
    { value: '12345', label: 'John Doe' },
    { value: '12346', label: 'Jane Smith' },
    { value: '12347', label: 'Bob Johnson' },
  ],
  subTeams: [],
  depsPath: null,
}

describe('OrganizationTree', () => {
  let mockFunction: jest.MockedFunction<any>

  beforeEach(() => {
    void vi.clearAllMocks()

    mockUseOrganizationTree = vi.fn().mockReturnValue({
      data: mockOrgData,
      refetch: vi.fn(),
    })

    void vi.mocked(useOrganizationTree).mockImplementation(mockUseOrganizationTree)
  })

  describe('Basic Rendering', () => {
    it('should render organization tree selectors', () => {
      render(
        <TestWrapper>
          <OrganizationTree setSelected={vi.fn()} />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('select-department')).toBeInTheDocument()
      void expect(screen.getByTestId('select-division')).toBeInTheDocument()
      void expect(screen.getByTestId('select-unit')).toBeInTheDocument()
      void expect(screen.getByTestId('select-team')).toBeInTheDocument()
      void expect(screen.getByTestId('select-users')).toBeInTheDocument()
    })

    it('should load department options', async () => {
      render(
        <TestWrapper>
          <OrganizationTree setSelected={vi.fn()} />
        </TestWrapper>,
      )

      await waitFor(() => {
        const departmentSelect = screen.getByTestId('select-department').querySelector('select')
        void expect(departmentSelect).toContainHTML('Information Technology')
        void expect(departmentSelect).toContainHTML('Human Resources')
        void expect(departmentSelect).toContainHTML('Finance')
      })

      describe('Progressive Selection', () => {
        it('should enable division after department selection', async () => {
          const user = userEvent.setup()
          const setSelected = vi.fn()

          render(
            <TestWrapper>
              <OrganizationTree setSelected={setSelected} progressiveEnable />
            </TestWrapper>,
          )

          // Initially division should be disabled
          const divisionSelect = screen.getByTestId('select-division').querySelector('select')
          void expect(divisionSelect).toBeDisabled()

          // Select department
          const departmentSelect = screen.getByTestId('select-department').querySelector('select')!
          await user.selectOptions(departmentSelect, 'it')

          // Division should now be enabled
          await waitFor(() => {
            void expect(divisionSelect).not.toBeDisabled()
          })

          it('should cascade selections down the hierarchy', async () => {
            const user = userEvent.setup()
            const setSelected = vi.fn()

            render(
              <TestWrapper>
                <OrganizationTree setSelected={setSelected} />
              </TestWrapper>,
            )

            // Select department
            await user.selectOptions(screen.getByTestId('select-department').querySelector('select')!, 'it')

            // Select division
            await user.selectOptions(screen.getByTestId('select-division').querySelector('select')!, 'dev')

            // Select unit
            await user.selectOptions(screen.getByTestId('select-unit').querySelector('select')!, 'frontend')

            // Verify selections are made
            expect(mockUseOrganizationTree).toHaveBeenCalledWith(
              expect.objectContaining({
                selectedDepartment: expect.any(String),
                selectedDivision: expect.any(String),
                selectedUnit: expect.any(String),
              }),
              true,
            )
          })

          describe('User Selection', () => {
            it('should allow single user selection by default', async () => {
              const user = userEvent.setup()
              const setSelected = vi.fn()

              render(
                <TestWrapper>
                  <OrganizationTree setSelected={setSelected} multiple={false} />
                </TestWrapper>,
              )

              const userSelect = screen.getByTestId('select-users').querySelector('select')!
              await user.selectOptions(userSelect, '12345')

              expect(setSelected).toHaveBeenCalledWith(
                expect.objectContaining({
                  value: '12345',
                  label: 'John Doe',
                }),
              )
            })

            it('should allow multiple user selection when enabled', async () => {
              const user = userEvent.setup()
              const setSelected = vi.fn()

              render(
                <TestWrapper>
                  <OrganizationTree setSelected={setSelected} multiple={true} />
                </TestWrapper>,
              )

              const userSelect = screen.getByTestId('select-users').querySelector('select')!

              // Select multiple users with Ctrl/Cmd
              await user.selectOptions(userSelect, ['12345', '12346'])

              expect(setSelected).toHaveBeenCalledWith(
                expect.arrayContaining([
                  expect.objectContaining({ value: '12345', label: 'John Doe' }),
                  expect.objectContaining({ value: '12346', label: 'Jane Smith' }),
                ]),
              )
            })

            describe('User Selection Only Mode', () => {
              it('should disable all selectors except user when userSelectionOnly is true', () => {
                render(
                  <TestWrapper>
                    <OrganizationTree setSelected={vi.fn()} userSelectionOnly />
                  </TestWrapper>,
                )

                void expect(screen.getByTestId('select-department').querySelector('select')).toBeDisabled()
                void expect(screen.getByTestId('select-division').querySelector('select')).toBeDisabled()
                void expect(screen.getByTestId('select-unit').querySelector('select')).toBeDisabled()
                void expect(screen.getByTestId('select-team').querySelector('select')).toBeDisabled()
                void expect(screen.getByTestId('select-users').querySelector('select')).not.toBeDisabled()
              })

              describe('Initial Selections', () => {
                it('should populate initial selections', async () => {
                  const initialSelections = {
                    department: { value: 'it', label: 'Information Technology' },
                    division: { value: 'dev', label: 'Development' },
                    unit: { value: 'frontend', label: 'Frontend Team' },
                    team: { value: 'team1', label: 'Team Alpha' },
                    user: [{ value: '12345', label: 'John Doe' }],
                  }

                  render(
                    <TestWrapper>
                      <OrganizationTree setSelected={vi.fn()} initialSelections={initialSelections as any} />
                    </TestWrapper>,
                  )

                  await waitFor(() => {
                    void expect(screen.getByTestId('select-department').querySelector('select')).toHaveValue('it')
                    void expect(screen.getByTestId('select-division').querySelector('select')).toHaveValue('dev')
                    void expect(screen.getByTestId('select-unit').querySelector('select')).toHaveValue('frontend')
                    void expect(screen.getByTestId('select-team').querySelector('select')).toHaveValue('team1')
                  })

                  describe('Visibility Controls', () => {
                    it('should hide specified levels based on visible prop', () => {
                      const visible = {
                        department: true,
                        division: false,
                        unit: true,
                        team: false,
                        user: true,
                      }

                      render(
                        <TestWrapper>
                          <OrganizationTree setSelected={vi.fn()} visible={visible} />
                        </TestWrapper>,
                      )

                      void expect(screen.getByTestId('select-department')).toBeInTheDocument()
                      void expect(screen.queryByTestId('select-division')).not.toBeInTheDocument()
                      void expect(screen.getByTestId('select-unit')).toBeInTheDocument()
                      void expect(screen.queryByTestId('select-team')).not.toBeInTheDocument()
                      void expect(screen.getByTestId('select-users')).toBeInTheDocument()
                    })

                    describe('Disable Controls', () => {
                      it('should disable specified levels based on disable prop', () => {
                        const disable = {
                          department: false,
                          division: true,
                          unit: false,
                          team: true,
                          user: false,
                        }

                        render(
                          <TestWrapper>
                            <OrganizationTree setSelected={vi.fn()} disable={disable} />
                          </TestWrapper>,
                        )

                        void expect(screen.getByTestId('select-department').querySelector('select')).not.toBeDisabled()
                        void expect(screen.getByTestId('select-division').querySelector('select')).toBeDisabled()
                        void expect(screen.getByTestId('select-unit').querySelector('select')).not.toBeDisabled()
                        void expect(screen.getByTestId('select-team').querySelector('select')).toBeDisabled()
                        void expect(screen.getByTestId('select-users').querySelector('select')).not.toBeDisabled()
                      })

                      it('should disable all when disable.all is true', () => {
                        render(
                          <TestWrapper>
                            <OrganizationTree setSelected={vi.fn()} disable={{ all: true }} />
                          </TestWrapper>,
                        )

                        void expect(screen.getByTestId('select-department').querySelector('select')).toBeDisabled()
                        void expect(screen.getByTestId('select-division').querySelector('select')).toBeDisabled()
                        void expect(screen.getByTestId('select-unit').querySelector('select')).toBeDisabled()
                        void expect(screen.getByTestId('select-team').querySelector('select')).toBeDisabled()
                        void expect(screen.getByTestId('select-users').querySelector('select')).toBeDisabled()
                      })

                      describe('Error Handling', () => {
                        it('should display error message', () => {
                          const errorMessage = 'Please select a user'

                          render(
                            <TestWrapper>
                              <OrganizationTree setSelected={vi.fn()} error={errorMessage} />
                            </TestWrapper>,
                          )

                          void expect(screen.getByRole('alert')).toHaveTextContent(errorMessage)
                        })

                        describe('Reload Functionality', () => {
                          it('should refetch data when reload prop changes', async () => {
                            const refetch = vi.fn()
                            void mockUseOrganizationTree.mockReturnValue({
                              data: mockOrgData,
                              refetch,
                            })

                            const { rerender } = render(
                              <TestWrapper>
                                <OrganizationTree setSelected={vi.fn()} reload={false} />
                              </TestWrapper>,
                            )

                            void expect(refetch).not.toHaveBeenCalled()

                            rerender(
                              <TestWrapper>
                                <OrganizationTree setSelected={vi.fn()} reload={true} />
                              </TestWrapper>,
                            )

                            await waitFor(() => {
                              void expect(refetch).toHaveBeenCalled()
                            })

                            describe('Sub-Teams', () => {
                              it('should display sub-team selectors when available', async () => {
                                const orgDataWithSubTeams = {
                                  ...mockOrgData,
                                  subTeams: [
                                    [
                                      { value: 'sub1', label: 'Sub Team 1' },
                                      { value: 'sub2', label: 'Sub Team 2' },
                                    ],
                                  ],
                                }

                                mockUseOrganizationTree.mockReturnValue({
                                  data: orgDataWithSubTeams,
                                  refetch: vi.fn(),
                                })

                                render(
                                  <TestWrapper>
                                    <OrganizationTree setSelected={vi.fn()} />
                                  </TestWrapper>,
                                )

                                await waitFor(() => {
                                  void expect(screen.getByTestId('select-sub-teams-1')).toBeInTheDocument()
                                })

                                it('should handle multiple levels of sub-teams', async () => {
                                  const orgDataWithMultipleSubTeams = {
                                    ...mockOrgData,
                                    subTeams: [,
                                      [{ value: 'sub1-1', label: 'Level 1 Sub Team' }],
                                      [{ value: 'sub2-1', label: 'Level 2 Sub Team' }],
                                      [{ value: 'sub3-1', label: 'Level 3 Sub Team' }],
                                    ],
                                  }

                                  mockUseOrganizationTree.mockReturnValue({
                                    data: orgDataWithMultipleSubTeams,
                                    refetch: vi.fn(),
          })

                                  render(
                                    <TestWrapper>
                                      <OrganizationTree setSelected={vi.fn()} />
                                    </TestWrapper>,
                                  )

                                  await waitFor(() => {
                                    void expect(screen.getByTestId('select-sub-teams-1')).toBeInTheDocument()
                                    void expect(screen.getByTestId('select-sub-teams-2')).toBeInTheDocument()
                                    void expect(screen.getByTestId('select-sub-teams-3')).toBeInTheDocument()
                                  })

                                  describe('Text Display Mode', () => {
                                    it('should handle showText prop', () => {
                                      render(
                                        <TestWrapper>
                                          <OrganizationTree setSelected={vi.fn()} showText={false} />
                                        </TestWrapper>,
                                      )

                                      expect(mockUseOrganizationTree).toHaveBeenCalledWith(expect.objectContaining({ showText: false }), true),
          })

                                    describe('User ID Filtering', () => {
                                      it('should pass userId to organization tree hook', () => {
                                        const userId = 12345

                                        render(
                                          <TestWrapper>
                                            <OrganizationTree setSelected={vi.fn()} userId={userId} />
                                          </TestWrapper>
                                        )

                                        expect(mockUseOrganizationTree).toHaveBeenCalledWith(expect.objectContaining({ userId }), true)