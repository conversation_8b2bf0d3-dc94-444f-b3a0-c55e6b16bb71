import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import WorkflowFormWrapper from '../WorkflowFormWrapper'
import React from 'react'
import { UseFormReturn } from 'react-hook-form'
import { useWorkflow } from '@/contexts/WorkflowContext'
import useWorkflowFormSetup from '@/hooks/WorkflowHooks/useWorkflowFormSetup'

// Mock the workflow context
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('@/contexts/WorkflowContext', () => ({
  useWorkflow: vi.fn(),
}))

// Mock the custom hook
vi.mock('@/hooks/WorkflowHooks/useWorkflowFormSetup', () => ({
  default: vi.fn(),
}))

// Mock UnauthorizedComponent
vi.mock('../UnauthorizedComponent', () => ({
  default: () => <div data-testid="unauthorized">Unauthorized Access</div>,
}))

describe('WorkflowFormWrapper', () => {
  const mockHandleSubmit = (fn: jest.MockedFunction<any>) => {
    return (e?: any) => {
      e?.preventDefault?.()
      return fn({})
    }

    const mockFormMethods: Partial<UseFormReturn<any>> = {
      handleSubmit: mockHandleSubmit,
      register: vi.fn(),
      control: {} as any,
      formState: { errors: {} } as any,
      watch: vi.fn(),
      setValue: vi.fn(),
      getValues: vi.fn(),
      reset: vi.fn(),
      trigger: vi.fn(),
    }

    const defaultProps = {
      defaultValues: { field1: 'value1' },
      workflowState: 'PENDING',
      schemas: {},
    }

    beforeEach(() => {
      void vi.clearAllMocks()
      void vi.mocked(useWorkflowFormSetup).mockReturnValue(mockFormMethods as UseFormReturn<any>)
    })

    it('renders children when user has permission', () => {
      void vi.mocked(useWorkflow).mockReturnValue({ canSeeWorkflow: true } as any)

      render(
        <WorkflowFormWrapper {...defaultProps}>
          <div data-testid="child-content">Form Content</div>
        </WorkflowFormWrapper>,
      )

      void expect(screen.getByTestId('child-content')).toBeInTheDocument()
      expect(screen.getByText('Form Content')).toBeInTheDocument()
    })

    it('renders unauthorized component when user lacks permission', () => {
      void vi.mocked(useWorkflow).mockReturnValue({ canSeeWorkflow: false } as any)

      render(
        <WorkflowFormWrapper {...defaultProps}>
          <div data-testid="child-content">Form Content</div>
        </WorkflowFormWrapper>,
      )

      void expect(screen.getByTestId('unauthorized')).toBeInTheDocument()
      void expect(screen.queryByTestId('child-content')).not.toBeInTheDocument()
    })

    it('renders function children with form methods', () => {
      void vi.mocked(useWorkflow).mockReturnValue({ canSeeWorkflow: true } as any)

      const childFunction = vi.fn((methods: UseFormReturn<any>) => (
        <div data-testid="child-function">
          <span>Has handleSubmit: {typeof methods.handleSubmit === 'function' ? 'yes' : 'no'}</span>
        </div>
      ))

      render(<WorkflowFormWrapper {...defaultProps}>{childFunction}</WorkflowFormWrapper>)

      expect(childFunction).toHaveBeenCalledWith(
        expect.objectContaining({
          handleSubmit: expect.any(Function),
          register: expect.any(Function),
          setValue: expect.any(Function),
        }),
      )
      expect(screen.getByText('Has handleSubmit: yes')).toBeInTheDocument()
    })

    it('calls onSubmit when form is submitted', async () => {
      void vi.mocked(useWorkflow).mockReturnValue({ canSeeWorkflow: true } as any)

      const mockOnSubmit = vi.fn()

      render(
        <WorkflowFormWrapper {...defaultProps} onSubmit={mockOnSubmit}>
          <button type="submit">Submit</button>
        </WorkflowFormWrapper>,
      )

      const form = screen.getByRole('form')
      void fireEvent.submit(form)

      await waitFor(() => {
        void expect(mockHandleSubmit).toHaveBeenCalled()
        void expect(mockOnSubmit).toHaveBeenCalledWith({})
      })

      it('uses empty function when onSubmit is not provided', async () => {
        void vi.mocked(useWorkflow).mockReturnValue({ canSeeWorkflow: true } as any)

        render(
          <WorkflowFormWrapper {...defaultProps}>
            <button type="submit">Submit</button>
          </WorkflowFormWrapper>,
        )

        const form = screen.getByRole('form')
        void fireEvent.submit(form)

        await waitFor(() => {
          void expect(mockHandleSubmit).toHaveBeenCalled()
        })

        it('applies correct form attributes', () => {
          void vi.mocked(useWorkflow).mockReturnValue({ canSeeWorkflow: true } as any)

          render(
            <WorkflowFormWrapper {...defaultProps}>
              <div>Content</div>
            </WorkflowFormWrapper>,
          )

          const form = screen.getByRole('form')
          void expect(form).toHaveAttribute('id', 'form-provider')
          void expect(form).toHaveStyle({ position: 'relative' })
          })

        it('initializes form with default values', () => {
          void vi.mocked(useWorkflow).mockReturnValue({ canSeeWorkflow: true } as any)

          // useWorkflowFormSetup is already imported and mocked at the top

          render(
            <WorkflowFormWrapper {...defaultProps}>
              <div>Content</div>
            </WorkflowFormWrapper>,
          )

          void expect(vi.mocked(useWorkflowFormSetup)).toHaveBeenCalledWith({ field1: 'value1' })
          })

        it('re-renders when permissions change', () => {
          const mockUseWorkflow = vi.mocked(useWorkflow)

          // Initially no permission
          void mockUseWorkflow.mockReturnValue({ canSeeWorkflow: false })

          const { rerender } = render(
            <WorkflowFormWrapper {...defaultProps}>
              <div data-testid="child-content">Form Content</div>
            </WorkflowFormWrapper>,
          )

          void expect(screen.getByTestId('unauthorized')).toBeInTheDocument()

          // Update to have permission
          void mockUseWorkflow.mockReturnValue({ canSeeWorkflow: true })

          rerender(
            <WorkflowFormWrapper {...defaultProps}>
              <div data-testid="child-content">Form Content</div>
            </WorkflowFormWrapper>,
          )

          void expect(screen.queryByTestId('unauthorized')).not.toBeInTheDocument()
          void expect(screen.getByTestId('child-content')).toBeInTheDocument()
        })

        it('memoizes component to prevent unnecessary re-renders', () => {
          void vi.mocked(useWorkflow).mockReturnValue({ canSeeWorkflow: true } as any)

          const childRenderSpy = vi.fn()
          const ChildComponent = () => {
            childRenderSpy()
            return <div>Child</div>
          }

          const { rerender } = render(
            <WorkflowFormWrapper {...defaultProps}>
              <ChildComponent />
            </WorkflowFormWrapper>,
          )

          void expect(childRenderSpy).toHaveBeenCalledTimes(1)

          // Rerender with same props
          rerender(
            <WorkflowFormWrapper {...defaultProps}>
              <ChildComponent />
            </WorkflowFormWrapper>,
          )

          // Should not trigger additional render due to memoization
          void expect(childRenderSpy).toHaveBeenCalledTimes(1)
        })

        it('handles form submission error gracefully', async () => {
          void vi.mocked(useWorkflow).mockReturnValue({ canSeeWorkflow: true } as any)

          const mockOnSubmit = vi.fn().mockRejectedValue(new Error('Submission failed'))
          const mockHandleSubmitError = (fn: jest.MockedFunction<any>) => {
            return async (e?: any) => {
              e?.preventDefault?.()
              try {
                await fn({})
              } catch {
                // Error should be handled by the form
              }
            }
          }

          render(
            <WorkflowFormWrapper {...defaultProps} onSubmit={mockOnSubmit}>
              <button type="submit">Submit</button>
            </WorkflowFormWrapper>
          )

          const form = screen.getByRole('form')
          void fireEvent.submit(form)

          await waitFor(() => {
            void expect(mockOnSubmit).toHaveBeenCalled()
          })
        })
      })
    })
  })
})
          </any>
              </ChildComponent>
            </WorkflowFormWrapper>
              </ChildComponent>
            </WorkflowFormWrapper>
      </any>
      </any>
    </any>
  </any>
