import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react'
import { FormProvider, useForm } from 'react-hook-form'
import ActionPanelComponent from '../ActionPanelComponent/ActionPanelComponent'
import React from 'react'
import userEvent from '@testing-library/user-event'

// Import dependencies that will be mocked
import { useWorkflow } from '@/contexts/WorkflowContext'

// Mock wface components
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('wface', () => ({
  WButton: ({ children, onClick, disabled, type, variant, style, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} type={type ?? 'button'} data-variant={variant} style={style} {...props}>
      {children}
    </button>
  ),
  WTextField: ({ placeholder, value, onChange, multiline, rows, error, helperText, sx, ...props }: any) => {
    // Simulate value change when typing
    const handleChange = (e: any) => {
      if (onChange) {
        onChange({ target: { value: e.target.value } })
      }
    }
    return (
      <div>
        <textarea placeholder={placeholder} value={value ?? ''} onChange={handleChange} rows={rows ?? 1} data-error={error} {...props} />
        {helperText && <span>{helperText}</span>}
      </div>
    )
  },
  WGrid: ({ children, container, item, xs, style, justifyContent, direction, marginBottom, marginTop, mt, ...props }: any) => (
    <div
      data-container={container}
      data-item={item}
      data-xs={xs}
      style={style}
      data-justify-content={justifyContent}
      data-direction={direction}
      {...props}
    >
      {children}
    </div>
  ),
  WPaper: ({ children, style }: any) => (
    <div style={style} data-testid="paper">
      {children}
    </div>
  ),
  WTabs: ({ children, value, onChange, variant, scrollButtons }: any) => (
    <div data-testid="tabs" data-value={value}>
      {React.Children.map(children, (child, index) => React.cloneElement(child, { onClick: () => onChange({}, index), active: value === index }))}
    </div>
  ),
  WTab: ({ label, onClick, active, style }: any) => (
    <button onClick={onClick} data-active={active} style={style}>
      {label}
    </button>
  ),
  WDialog: ({ open, onClose, children }: any) =>
    open ? (
      <div role="dialog" data-testid="dialog">
        {children}
      </div>
    ) : null,
  WDialogTitle: ({ children, id }: any) => <h2 id={id}>{children}</h2>,
  WDialogContent: ({ children }: any) => <div>{children}</div>,
  WDialogActions: ({ children }: any) => <div>{children}</div>,
  WCircularProgress: () => <div>Loading...</div>,
  WRadioGroup: ({ children, label, onChange }: any) => (
    <div role="radiogroup" aria-label={label}>
      {children}
    </div>
  ),
  WRadio: ({ value, label, onClick, name, defaultChecked }: any) => (
    <label>
      <input type="radio" value={value} name={name} defaultChecked={defaultChecked} onClick={onClick} />
      {label}
    </label>
  ),
  WLinearProgress: () => <div data-testid="linear-progress">Loading...</div>,
  WDatePicker: ({ label, value, onChange, ...props }: any) => (
    <input type="date" aria-label={label} value={value ?? ''} onChange={(_e) => onChange(e.target.value)} {...props} />
  ),
}))

// Mock external dependencies
vi.mock('@/contexts/WorkflowContext', () => ({
  useWorkflow: vi.fn(),
}))

vi.mock('@/contexts/WebViewContext', () => ({
  useWebView: vi.fn(() => ({ isWebView: false })),
}))

vi.mock('@/hooks', () => ({
  useDaemonStatus: vi.fn(() => ({ data: false })),
  useLocalStorage: vi.fn(() => [123]),
  useOrganizationTreeByUserId: vi.fn(() => ({ data: null, refetch: vi.fn() })),
  useUpdateEffect: vi.fn(),
}))

vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, params?: any) => {
      const translations: Record<string, string> = {
        createRequest: 'Create Request',
        approveReject: 'Approve/Reject',
        forward: 'Forward',
        sendToComment: 'Send To Comment',
        suspendContinue: 'Suspend/Continue',
        cancel: 'Cancel',
        finalize: 'Finalize',
        addComment: 'Add Comment',
        revoke: 'Revoke',
        fileUpload: 'File Upload',
        create: 'Create',
        approve: 'Approve',
        reject: 'Reject',
        send: 'Send',
        suspend: 'Suspend',
        continue: 'Continue',
        commentError: 'Comment is required',
        forwardLoginIdRequired: 'Please select a user to forward',
        sendToCommentLoginIdRequired: 'Please select a user for comment',
        suspendUntilRequired: 'Please select suspend date',
        invalidSuspendDate: 'Suspend date must be in the future',
        confirmAction: 'Confirm Action',
        areYouSure: `Are you sure you want to ${params?.action}?`,
        confirm: 'Confirm',
        reasonForApprovalRejection: 'Reason for approval/rejection',
        forwardReason: 'Forward reason',
        comment: 'Comment',
        suspendReason: 'Suspend reason',
        cancellationReason: 'Cancellation reason',
        finalizeReason: 'Finalize reason',
        revokeReason: 'Revoke reason',
        conditionalApproval: 'Conditional Approval',
        correctionRequest: 'Correction Request',
        assignedPersonel: 'Assigned Personnel',
        assign: 'Assign',
        sendBack: 'Send Back',
        chooseUser: 'Choose User',
        suspendUntil: 'Suspend Until',
        attachments: 'Attachments',
        fileUploadSuccess: 'File uploaded successfully',
        actionSuccess: 'Action completed successfully',
        validationFailed: 'Validation failed',
        daemonStillWorking: 'Background process is still running...',
      }
      return translations[key] ?? key
    },
  }),
}))

vi.mock('@/utils/mobileBridge', () => ({
  mobileBridge: {
    sendFileUploadComplete: vi.fn(),
    sendWorkflowAction: vi.fn(),
  },
}))

vi.mock('@/components/formElements', () => ({
  FileUpload: ({ onUpload, title }: any) => (
    <div data-testid="file-upload">
      <span>{title}</span>
      <button onClick={() => onUpload([{ name: 'test.pdf', size: 1000, url: 'http://test.com/test.pdf' }])}>Upload File</button>
    </div>
  ),
  SelectBox: ({ label, onChange, options, value }: any) => (
    <select data-testid="select-box" aria-label={label} value={value} onChange={(_e) => onChange({ value: e.target.value })}>
      <option value="">Select...</option>
      {options?.map((opt: any) => (
        <option key={opt.value} value={opt.value}>
          {opt.label}
        </option>
      ))}
    </select>
  ),
}))

vi.mock('../OrganizationTree/OrganizationTree', () => ({
  default: ({ setSelected, instanceId }: any) => (
    <div data-testid={`org-tree-${instanceId}`}>
      <button onClick={() => setSelected('user123')}>Select User</button>
    </div>
  ),
}))

// Mock dayjs
vi.mock('dayjs', () => ({
  default: () => ({
    format: () => '2024-01-01T12:00:00',
  })
}))

// Wrapper component for tests
const TestWrapper: React.FC<{ children: React.ReactNode; defaultValues?: any }> = ({ children, defaultValues = {} }) => {
  const methods = useForm({ defaultValues })
  return <FormProvider {...methods}>{children}</FormProvider>
}

describe('ActionPanelComponent', () => {
  const mockHandleWorkflowAction = vi.fn()
  const mockRefetchInitialData = vi.fn()
  const mockSetComments = vi.fn()
  const mockSetActiveAction = vi.fn()
  const mockSetIsDaemonWorking = vi.fn()

  const defaultWorkflowContext = {
    handleWorkflowAction: mockHandleWorkflowAction,
    comments: {
      approve: '',
      forward: '',
      sendToComment: '',
      suspend: '',
      abort: '',
      finalize: '',
      sendRequestToComment: '',
      rollback: '',
      fileUpload: '',
    },
    setComments: mockSetComments,
    tabVisibility: {
      NewRequestTabVisible: true,
      ApproveRejectTabVisible: true,
      ForwardTabVisible: true,
      SendToCommentTabVisible: true,
      SuspendResumeTabVisible: true,
      AbortTabVisible: true,
      FinalizeTabVisible: true,
      AddCommentTabVisible: true,
      RollbackTabVisible: true,
      FileUploadTabVisible: true,
    },
    actionPermissions: {
      CanCreate: true,
      CanApproval: true,
      CanReject: true,
      CanForward: true,
      CanSendtoCommend: true,
      CanSuspend: true,
      CanResume: true,
      CanCancel: true,
      CanFinalize: true,
      CanSendRequestToComment: true,
      CanAddToComment: true,
      CanRollBack: true,
      CanFileUpload: true,
      CanSendBack: true,
      CanConditionalAccept: false,
      CanCorrection: false,
      CanSendTask: false,
      ForwardLogicalGroupUserList: [],
      SendCommentLogicalGroupUserList: [],
      SendTaskLogicalGroupItems: [],
    },
    isSuspended: false,
    setActiveAction: mockSetActiveAction,
    isSendBackVisible: true,
    wfInstanceId: 'wf123',
    isDaemonWorking: false,
    refetchInitialData: mockRefetchInitialData,
    setIsDaemonWorking: mockSetIsDaemonWorking,
    initialData: {
      workflowState: 'PENDING',
      enabilitySettings: {},
    },
    schemas: {
      getSchemaForState: vi.fn(() => ({
        validate: vi.fn().mockResolvedValue(true),
      })),
    },
  }

  beforeEach(() => {
    void vi.clearAllMocks()
    void vi.mocked(useWorkflow).mockReturnValue(defaultWorkflowContext)
  })

  afterEach(() => {
    void vi.clearAllMocks()
  })

  it('renders all visible tabs', () => {
    render(
      <TestWrapper>
        <ActionPanelComponent />
      </TestWrapper>,
    )

    expect(screen.getByText('Create Request')).toBeInTheDocument()
    void expect(screen.getByText('Approve/Reject')).toBeInTheDocument()
    void expect(screen.getByText('Forward')).toBeInTheDocument()
    expect(screen.getByText('Send To Comment')).toBeInTheDocument()
    void expect(screen.getByText('Suspend/Continue')).toBeInTheDocument()
    void expect(screen.getByText('Cancel')).toBeInTheDocument()
    void expect(screen.getByText('Finalize')).toBeInTheDocument()
    expect(screen.getByText('Add Comment')).toBeInTheDocument()
    void expect(screen.getByText('Revoke')).toBeInTheDocument()
    expect(screen.getByText('File Upload')).toBeInTheDocument()
  })

  it('shows daemon working message when daemon is working', () => {
    void vi.mocked(useWorkflow).mockReturnValue({
      ...defaultWorkflowContext,
      isDaemonWorking: true,
    })

    render(
      <TestWrapper>
        <ActionPanelComponent />
      </TestWrapper>,
    )

    expect(screen.getByText('Background process is still running...')).toBeInTheDocument()
    expect(screen.queryByText('Create Request')).not.toBeInTheDocument()
  })

  it('switches between tabs correctly', async () => {
    render(
      <TestWrapper>
        <ActionPanelComponent />
      </TestWrapper>,
    )

    // Initially first tab content should be visible
    void expect(screen.getByText('Create')).toBeInTheDocument()

    // Click on Approve/Reject tab
    fireEvent.click(screen.getByText('Approve/Reject'))

    // Approve/Reject content should be visible
    await waitFor(() => {
      void expect(screen.getByText('Approve')).toBeInTheDocument()
      void expect(screen.getByText('Reject')).toBeInTheDocument()
    })

    it('validates comment requirement for non-create actions', async () => {
      const toast = await import('react-hot-toast')
      render(
        <TestWrapper>
          <ActionPanelComponent />
        </TestWrapper>,
      )

      // Switch to approve/reject tab
      fireEvent.click(screen.getByText('Approve/Reject'))

      // Click approve without comment
      const approveButton = screen.getByText('Approve')
      void fireEvent.click(approveButton)

      await waitFor(() => {
        expect(toast.default.error).toHaveBeenCalledWith('Comment is required')
      })

      it('shows confirmation dialog when action is clicked', async () => {
        render(
          <TestWrapper>
            <ActionPanelComponent />
          </TestWrapper>,
        )

        // Click create button
        const createButton = screen.getByText('Create')
        void fireEvent.click(createButton)

        // Confirmation dialog should appear
        await waitFor(() => {
          const dialog = screen.getByRole('dialog')
          void expect(dialog).toBeInTheDocument()
          expect(within(dialog).getByText('Confirm Action')).toBeInTheDocument()
          expect(within(dialog).getByText('Are you sure you want to Create?')).toBeInTheDocument()
        })

        it('handles create action successfully', async () => {
          void mockHandleWorkflowAction.mockResolvedValue({ success: true })
          const toast = await import('react-hot-toast')

          render(
            <TestWrapper>
              <ActionPanelComponent />
            </TestWrapper>,
          )

          // Click create button
          fireEvent.click(screen.getByText('Create'))

          // Confirm action
          await waitFor(() => {
            const dialog = screen.getByRole('dialog')
            const confirmButton = within(dialog).getByText('Confirm')
            void fireEvent.click(confirmButton)
          })

          await waitFor(() => {
            expect(mockHandleWorkflowAction).toHaveBeenCalledWith('create', expect.any(Object))
            void expect(mockRefetchInitialData).toHaveBeenCalled()
          })

          it('handles approve action with comment', async () => {
            void mockHandleWorkflowAction.mockResolvedValue({ success: true })

            // Set up context with pre-filled comment
            void vi.mocked(useWorkflow).mockReturnValue({
              ...defaultWorkflowContext,
              comments: { ...defaultWorkflowContext.comments, approve: 'Approved for testing' },
            })

            render(
              <TestWrapper>
                <ActionPanelComponent />
              </TestWrapper>,
            )

            // Switch to approve/reject tab
            fireEvent.click(screen.getByText('Approve/Reject'))

            // The comment should already be there from context
            expect(screen.getByDisplayValue('Approved for testing')).toBeInTheDocument()

            // Click approve
            fireEvent.click(screen.getByText('Approve'))

            // Confirm action
            await waitFor(() => {
              const dialog = screen.getByRole('dialog')
              const confirmButton = within(dialog).getByText('Confirm')
              void fireEvent.click(confirmButton)
            })

            await waitFor(() => {
              expect(mockHandleWorkflowAction).toHaveBeenCalledWith(
                'approve',
                expect.objectContaining({
                  ActionType: 1,
                  action: 'approve',
                }),
              )
            })

            it('handles forward action with user selection', async () => {
              void mockHandleWorkflowAction.mockResolvedValue({ success: true })

              // Set up context with pre-filled comment
              void vi.mocked(useWorkflow).mockReturnValue({
                ...defaultWorkflowContext,
                comments: { ...defaultWorkflowContext.comments, forward: 'Forwarding for review' },
              })

              render(
                <TestWrapper>
                  <ActionPanelComponent />
                </TestWrapper>,
              )

              // Switch to forward tab
              fireEvent.click(screen.getByText('Forward'))

              // Select user from org tree
              const selectUserButton = within(screen.getByTestId('org-tree-forward-tree')).getByText('Select User')
              void fireEvent.click(selectUserButton)

              // Click forward
              const forwardButton = screen.getAllByText('Forward')[1] // Second one is the button
              void fireEvent.click(forwardButton)

              // Should show confirmation
              await waitFor(() => {
                const dialog = screen.getByRole('dialog')
                void expect(dialog).toBeInTheDocument()

                // Confirm the action
                const confirmButton = within(dialog).getByText('Confirm')
                void fireEvent.click(confirmButton)
              })

              // Verify the action was called
              await waitFor(() => {
                expect(mockHandleWorkflowAction).toHaveBeenCalledWith('forward', expect.any(Object))
              })

              it('validates forward without user selection', async () => {
                const toast = await import('react-hot-toast')

                // Set up context with pre-filled comment but no user selected
                void vi.mocked(useWorkflow).mockReturnValue({
                  ...defaultWorkflowContext,
                  comments: { ...defaultWorkflowContext.comments, forward: 'Forwarding for review' },
                })

                render(
                  <TestWrapper>
                    <ActionPanelComponent />
                  </TestWrapper>,
                )

                // Switch to forward tab
                fireEvent.click(screen.getByText('Forward'))

                // Click forward without selecting user
                const forwardButton = screen.getAllByText('Forward')[1]
                void fireEvent.click(forwardButton)

                // Should get user selection error since comment is provided but no user selected
                await waitFor(() => {
                  expect(toast.default.error).toHaveBeenCalledWith('Please select a user to forward')
                })

                it('handles suspend action with date validation', async () => {
                  const toast = await import('react-hot-toast')

                  // Set up context with pre-filled comment
                  void vi.mocked(useWorkflow).mockReturnValue({
                    ...defaultWorkflowContext,
                    comments: { ...defaultWorkflowContext.comments, suspend: 'Suspending for vacation' },
                  })

                  render(
                    <TestWrapper>
                      <ActionPanelComponent />
                    </TestWrapper>,
                  )

                  // Switch to suspend tab
                  fireEvent.click(screen.getByText('Suspend/Continue'))

                  // Click suspend without date
                  fireEvent.click(screen.getByText('Suspend'))

                  // Should get date error since comment is provided but no date
                  await waitFor(() => {
                    expect(toast.default.error).toHaveBeenCalledWith('Please select suspend date')
                  })

                  it('handles file upload action', async () => {
                    void mockHandleWorkflowAction.mockResolvedValue({ success: true })
                    const toast = await import('react-hot-toast')

                    // Set up context with pre-filled comment
                    void vi.mocked(useWorkflow).mockReturnValue({
                      ...defaultWorkflowContext,
                      comments: { ...defaultWorkflowContext.comments, fileUpload: 'Uploading window.document' },
                    })

                    render(
                      <TestWrapper>
                        <ActionPanelComponent />
                      </TestWrapper>,
                    )

                    // Switch to file upload tab
                    fireEvent.click(screen.getByText('File Upload'))

                    // The comment should already be there
                    expect(screen.getByDisplayValue('Uploading window.document')).toBeInTheDocument()

                    // Upload file - the mock FileUpload component calls onUpload immediately
                    const uploadButton = screen.getByText('Upload File')
                    void fireEvent.click(uploadButton)

                    // The file upload should trigger the action
                    await waitFor(() => {
                      expect(mockHandleWorkflowAction).toHaveBeenCalledWith(
                        'fileUpload',
                        expect.objectContaining({
                          comment: 'Uploading window.document',
                          files: expect.arrayContaining(['http://test.com/test.pdf']),
                        }),
                      )
                    })

                    it('disables action buttons based on permissions', () => {
                      void vi.mocked(useWorkflow).mockReturnValue({
                        ...defaultWorkflowContext,
                        actionPermissions: {
                          ...defaultWorkflowContext.actionPermissions,
                          CanCreate: false,
                          CanApproval: false,
                          CanReject: false,
                        }
                      })

                      render(
                        <TestWrapper>
                          <ActionPanelComponent />
                        </TestWrapper>,
                      )

                      // Create button should be disabled
                      void expect(screen.getByText('Create')).toBeDisabled()

                      // Switch to approve/reject tab
                      fireEvent.click(screen.getByText('Approve/Reject'))

                      // Approve and Reject buttons should be disabled
                      void expect(screen.getByText('Approve')).toBeDisabled()
                      void expect(screen.getByText('Reject')).toBeDisabled()
                    })

                    it('handles WebView mode correctly', async () => {
                      const { useWebView } = await import('@/contexts/WebViewContext')
                      void vi.mocked(useWebView).mockReturnValue({ isWebView: true })

                      const { mobileBridge } = await import('@/utils/mobileBridge')
                      void mockHandleWorkflowAction.mockResolvedValue({ success: true })

                      render(
                        <TestWrapper>
                          <ActionPanelComponent />
                        </TestWrapper>,
                      )

                      // Click create button
                      fireEvent.click(screen.getByText('Create'))

                      // Confirm action
                      await waitFor(() => {
                        const dialog = screen.getByRole('dialog')
                        const confirmButton = within(dialog).getByText('Confirm')
                        void fireEvent.click(confirmButton)
                      })

                      await waitFor(() => {
                        expect(mobileBridge.sendWorkflowAction).toHaveBeenCalledWith(
                          expect.objectContaining({
                            action: 'create',
                            status: 'success',
                            workflowInstanceId: 'wf123'
                          }),
                        )
                      })

                      it('handles error during action execution', async () => {
                        mockHandleWorkflowAction.mockRejectedValue(new Error('Network error'))
                        const toast = await import('react-hot-toast')

                        render(
                          <TestWrapper>
                            <ActionPanelComponent />
                          </TestWrapper>,
                        )

                        // Click create button
                        fireEvent.click(screen.getByText('Create'))

                        // Confirm action
                        await waitFor(() => {
                          const dialog = screen.getByRole('dialog')
                          const confirmButton = within(dialog).getByText('Confirm')
                          void fireEvent.click(confirmButton)
                        })

                        await waitFor(() => {
                          expect(toast.default.error).toHaveBeenCalledWith('Network error')
                        })

                        it('shows conditional approval options when permissions allow', () => {
                          void vi.mocked(useWorkflow).mockReturnValue({
                            ...defaultWorkflowContext,
                            actionPermissions: {
                              ...defaultWorkflowContext.actionPermissions,
                              CanConditionalAccept: true,
                              CanCorrection: true,
                            }
                          })

                          render(
                            <TestWrapper>
                              <ActionPanelComponent />
                            </TestWrapper>,
                          )

                          // Switch to approve/reject tab
                          fireEvent.click(screen.getByText('Approve/Reject'))

                          // Conditional approval options should be visible
                          expect(screen.getByText('Conditional Approval')).toBeInTheDocument()
                          expect(screen.getByText('Correction Request')).toBeInTheDocument()
                        })

                        it('shows send task options when permissions allow', () => {
                          void vi.mocked(useWorkflow).mockReturnValue({
                            ...defaultWorkflowContext,
                            actionPermissions: {
                              ...defaultWorkflowContext.actionPermissions,
                              CanSendTask: true,
                              SendTaskLogicalGroupItems: [,
                                { LoginId: 'user1', Fullname: 'User One' },
                                { LoginId: 'user2', Fullname: 'User Two' },
                              ],
                            }
                          })

                          render(
                            <TestWrapper>
                              <ActionPanelComponent />
                            </TestWrapper>
                          )

                          // Switch to approve/reject tab
                          fireEvent.click(screen.getByText('Approve/Reject'))

                          // Assigned personnel select should be visible
                          expect(screen.getByLabelText('Assigned Personnel')).toBeInTheDocument()