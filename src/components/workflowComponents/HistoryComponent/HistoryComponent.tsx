import WorkflowHistoryTable from '@/components/Tables/WorkflowHistory/WorkflowHistoryTable'
import useGetParams from '@/hooks/useGetParams'
import { IWorkflowData } from '@/types/WorkflowTypes'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { WBox, WTypography } from 'wface'
import { useTheme } from '@mui/material/styles'

const HistoryComponent = React.memo<IWorkflowData>(() => {
  const { t } = useTranslation(['common'])
  const params = useGetParams<{ wfInstanceId: number }>(['wfInstanceId'])
  const theme = useTheme()

  return (
    <WBox
      sx={{
        width: '100%',
        backgroundColor: theme.palette.background.paper,
        borderRadius: theme.shape.borderRadius,
        overflow: 'hidden',
        boxShadow: theme.shadows[1],
      }}
    >
      <WBox
        sx={{
          backgroundColor: theme.palette.primary.main,
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
          padding: { xs: theme.spacing(1.5), sm: theme.spacing(2) },
          textAlign: 'center',
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <WTypography
          variant="h6"
          sx={{
            color: theme.palette.primary.contrastText,
            fontWeight: 500,
            fontSize: '1.125rem',
          }}
        >
          {t('history')}
        </WTypography>
      </WBox>
      <WBox
        sx={{
          backgroundColor: theme.palette.background.paper,
          padding: { xs: theme.spacing(1), sm: theme.spacing(2) },
        }}
      >
        <WorkflowHistoryTable wfInstanceId={params.wfInstanceId as number} />
      </WBox>
  )
})

export default HistoryComponent
        </WorkflowHistoryTable>
</IWorkflowData>
