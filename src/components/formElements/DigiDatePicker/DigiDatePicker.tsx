import React, { useState, useRef, useCallback, forwardRef, useEffect } from 'react'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { StaticDatePicker } from '@mui/x-date-pickers/StaticDatePicker'
import { styled } from '@mui/material/styles'
import TextField from '@mui/material/TextField'
import IconButton from '@mui/material/IconButton'
import { useTranslation } from 'react-i18next'
import dayjs, { Dayjs } from 'dayjs'
import 'dayjs/locale/tr'
import { ClickAwayListener, Paper } from '@mui/material'
import { X, CalendarIcon } from 'lucide-react'
import { generateSecureComponentId } from '@/utils/crypto'
import './DigiDatePicker.css'

// Global event system for closing other date pickers
const CLOSE_OTHER_DATEPICKERS_EVENT = 'closeOtherDatePickers'

interface DigiDatePickerProps {
  id?: string
  name?: string
  label?: string
  value?: Date | null
  // eslint-disable-next-line no-unused-vars
  onChange?: (date: Date | null) => void
  error?: string
  helperText?: string
  disabled?: boolean
  readOnly?: boolean
  clearable?: boolean
  minDate?: Date
  maxDate?: Date
  fullWidth?: boolean
  required?: boolean
  disablePast?: boolean
  disableFuture?: boolean
  size?: 'small' | 'medium' | 'large'
  variant?: 'outlined' | 'filled' | 'standard'
  format?: string
  autoFocus?: boolean
  className?: string
  style?: React.CSSProperties
  placeholder?: string
  disableHighlightToday?: boolean
  // eslint-disable-next-line no-unused-vars
  shouldDisableDate?: (date: Date) => boolean
  onOpen?: () => void
  onClose?: () => void
  // eslint-disable-next-line no-unused-vars
  onError?: (error: Error | null) => void
  inputProps?: Record<string, unknown>
}

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiInputBase-root': {
    borderRadius: theme.shape.borderRadius,
  },
  '& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderColor: theme.palette.primary.main,
    borderWidth: 2,
  },
  '& .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: theme.palette.primary.main,
  },
  '& .MuiInputLabel-root.Mui-focused': {
    color: theme.palette.primary.main,
  },
  '& .MuiInputAdornment-root': {
    marginRight: -8,
  },
}))

const PopperContainer = styled(Paper)(({ theme }) => ({
  zIndex: 1300,
  background: 'transparent',
  borderRadius: '4px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
  animation: 'datepickerFadeIn 0.2s ease-out',
  width: 'auto',
  position: 'relative',

  // Hide the toolbar in StaticDatePicker
  '& .MuiPickersLayout-toolbar': {
    display: 'none',
  },

  // Style the calendar container
  '& .MuiPickersLayout-contentWrapper': {
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: 'white',
    borderRadius: '4px',
    minWidth: '300px',
    maxWidth: '400px',
    width: 'auto',
  },

  // Ensure the calendar itself has proper sizing
  '& .MuiDateCalendar-root': {
    width: 'auto',
    maxWidth: '100%',
  },

  '& .MuiPickersDay-root': {
    fontSize: '0.875rem',

    '&.Mui-selected': {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,

      '&:hover': {
        backgroundColor: theme.palette.primary.dark,
      },
    },

    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.04)',
    },
  },
}))

const DigiDatePicker = forwardRef<HTMLDivElement, DigiDatePickerProps>((props, ref) => {
  const {
    id,
    name,
    label,
    value,
    onChange,
    error,
    helperText,
    disabled,
    readOnly,
    clearable,
    minDate,
    maxDate,
    fullWidth,
    required,
    disablePast,
    disableFuture,
    size,
    variant,
    format = 'DD/MM/YYYY',
    autoFocus,
    className,
    style,
    placeholder,
    shouldDisableDate,
    onOpen,
    onClose,
    onError,
    inputProps,
  } = props

  const { i18n } = useTranslation()
  const [isOpen, setIsOpen] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [internalError, setInternalError] = useState<string | null>(null)
  const [dropdownPlacement, setDropdownPlacement] = useState<'bottom' | 'top'>('bottom')
  const inputRef = useRef<HTMLDivElement>(null)
  const isInternalChange = useRef(false)
  const componentId = useRef(generateSecureComponentId('datepicker'))

  // Listen for globalThis close events from other date pickers
  useEffect(() => {
    const handleCloseOthers = (event: CustomEvent) => {
      if (event.detail.exceptId !== componentId.current && isOpen) {
        setIsOpen(false)
        onClose?.()
      }
    }

    window.addEventListener(CLOSE_OTHER_DATEPICKERS_EVENT, handleCloseOthers as EventListener)
    return () => {
      window.removeEventListener(CLOSE_OTHER_DATEPICKERS_EVENT, handleCloseOthers as EventListener)
    }
  }, [isOpen, onClose])

  const handleDateChange = useCallback(
    (date: Dayjs | null) => {
      if (onChange) {
        // Only convert to Date if date is valid
        const dateValue = date?.isValid() ? date.toDate() : null
        onChange(dateValue)
      }
      setIsOpen(false)
      onClose?.()
    },
    [onChange, onClose],
  )

  const handleInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value
      setInputValue(newValue)

      if (newValue && !dayjs(newValue, format).isValid()) {
        setInternalError('Invalid date format')
        onError?.(new Error('Invalid date format'))
      } else {
        setInternalError(null)
        onError?.(null)
      }
    },
    [format, onError],
  )

  const handleClear = useCallback(
    (event: React.MouseEvent) => {
      event.stopPropagation()
      event.preventDefault()

      isInternalChange.current = true
      setInputValue('')
      setInternalError(null)
      onError?.(null)

      // Explicitly pass null to onChange
      if (onChange) {
        onChange(null)
      }

      setIsOpen(false)
      onClose?.()
    },
    [onChange, onClose, onError],
  )

  const handleClickAway = useCallback(() => {
    setIsOpen(false)
    onClose?.()
  }, [onClose])

  const handleOpen = useCallback(
    (event: React.MouseEvent) => {
      event.stopPropagation()
      isInternalChange.current = false
      if (!disabled && !readOnly) {
        // Close other date pickers
        window.dispatchEvent(
          new CustomEvent(CLOSE_OTHER_DATEPICKERS_EVENT, {
            detail: { exceptId: componentId.current },
          }),
        )

        // Calculate dropdown placement
        if (inputRef.current) {
          const inputRect = inputRef.current.getBoundingClientRect()
          const viewportHeight = window.innerHeight
          const inputCenterY = inputRect.top + inputRect.height / 2
          const viewportCenterY = viewportHeight / 2

          // If input is below middle of screen, open above
          setDropdownPlacement(inputCenterY > viewportCenterY ? 'top' : 'bottom')
        }

        setIsOpen(true)
        onOpen?.()
      }
    },
    [disabled, readOnly, onOpen],
  )

  const formatDate = useCallback(
    (date: Date | null): string => {
      if (!date) return ''
      const dayjsDate = dayjs(date)
      return dayjsDate.isValid() ? dayjsDate.format(format) : ''
    },
    [format],
  )

  React.useEffect(() => {
    if (!isInternalChange.current) {
      setInputValue(formatDate(value as Date))
    }
    isInternalChange.current = false
  }, [value, formatDate])

  const containerClasses = ['digi-date-picker', size, className, (error ?? internalError) ? 'error' : '', disabled ? 'disabled' : '']
    .filter(Boolean)
    .join(' ')

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale={i18n.language}>
      <ClickAwayListener onClickAway={handleClickAway}>
        <div className={containerClasses} ref={ref} style={{ ...style, position: 'relative' }}>
          <div className="digi-date-picker-input" ref={inputRef}>
            <StyledTextField
              id={id}
              name={name}
              label={label}
              value={inputValue}
              onChange={handleInputChange}
              error={Boolean(error ?? internalError)}
              helperText={error ?? internalError ?? helperText}
              disabled={disabled}
              required={required}
              fullWidth={fullWidth}
              size={size === 'large' ? 'medium' : size}
              variant={variant}
              autoFocus={autoFocus}
              placeholder={placeholder}
              InputProps={{
                ...inputProps,
                onClick: (e: React.MouseEvent<HTMLInputElement>) => {
                  e.preventDefault()
                  handleOpen(e)
                },
                readOnly: true,
                endAdornment: (
                  <>
                    {clearable && inputValue && !disabled && !readOnly && (
                      <IconButton size="small" onClick={handleClear} sx={{ padding: '4px' }}>
                        <X size={16} />
                      </IconButton>
                    )}
                    <IconButton size="small" sx={{ padding: '4px' }} disabled={disabled} onClick={handleOpen}>
                      <CalendarIcon size={16} />
                    </IconButton>
                  </>
                ),
              }}
            />
          </div>
          {isOpen && (
            <div
              className={`digi-date-picker-popper digi-date-picker-popper-${dropdownPlacement}`}
              style={{
                position: 'absolute',
                left: 0,
                ...(dropdownPlacement === 'bottom'
                  ? {
                      top: '100%',
                      marginTop: '4px',
                    }
                  : {
                      bottom: '100%',
                      marginBottom: '4px',
                    }),
                zIndex: 1300,
              }}
            >
              <PopperContainer>
                <StaticDatePicker
                  value={value ? dayjs(value) : null}
                  onChange={handleDateChange}
                  minDate={minDate ? dayjs(minDate) : undefined}
                  maxDate={maxDate ? dayjs(maxDate) : undefined}
                  disablePast={disablePast}
                  disableFuture={disableFuture}
                  shouldDisableDate={shouldDisableDate ? (date: Dayjs) => shouldDisableDate(date.toDate()) : undefined}
                />
              </PopperContainer>
            </div>
          )}
        </div>
      </ClickAwayListener>
    </LocalizationProvider>
  )
})

DigiDatePicker.displayName = 'DigiDatePicker'

export default DigiDatePicker
