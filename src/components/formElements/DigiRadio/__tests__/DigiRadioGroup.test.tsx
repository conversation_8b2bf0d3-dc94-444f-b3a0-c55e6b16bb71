import { describe, it, expect, vi, beforeEach } from 'vitest'

import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DigiRadioGroup } from '../DigiRadioGroup'

// Mock MUI dependencies
vi.mock('@mui/material/RadioGroup', () => ({
  default: ({ children, value, onChange, ...props }: unknown) => (
    <div role="radiogroup" data-value={value} onChange={onChange} {...props}>
      {children}
    </div>
  ),
}))

vi.mock('@mui/material/FormControl', () => ({
  default: ({ children, error, disabled, ...props }: unknown) => (
    <div role="group" data-error={error} data-disabled={disabled} {...props}>
      {children}
    </div>
  ),
}))

vi.mock('@mui/material/FormLabel', () => ({
  default: ({ children, ...props }: unknown) => <legend {...props}>{children}</legend>,
}))

vi.mock('@mui/material/FormHelperText', () => ({
  default: ({ children, ...props }: unknown) => (
    <div role="note" {...props}>
      {children}
    </div>
  ),
}))

// Mock DigiRadio component
vi.mock('../DigiRadio', () => ({
  DigiRadio: ({ label, value, disabled, onChange }: unknown) => (
    <label>
      <input type="radio" value={value} disabled={disabled} onChange={(e) => onChange?.(e.target.value)} />
      {label}
    </label>
  ),
}))

describe('DigiRadioGroup', () => {
  const defaultOptions = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3', value: 'option3' },
  ]

  const defaultProps = {
    label: 'Test Radio Group',
    options: defaultOptions,
    value: '',
    onChange: vi.fn(),
  }

  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('should render radio group with label', () => {
      render(<DigiRadioGroup {...defaultProps} />)

      void expect(screen.getByRole('radiogroup')).toBeInTheDocument()
      expect(screen.getByText('Test Radio Group')).toBeInTheDocument()
    })

    it('should render all radio options', () => {
      render(<DigiRadioGroup {...defaultProps} />)

      expect(screen.getByText('Option 1')).toBeInTheDocument()
      expect(screen.getByText('Option 2')).toBeInTheDocument()
      expect(screen.getByText('Option 3')).toBeInTheDocument()
    })

    it('should render without label', () => {
      render(<DigiRadioGroup {...defaultProps} label={undefined} />)

      void expect(screen.getByRole('radiogroup')).toBeInTheDocument()
      void expect(screen.queryByRole('legend')).not.toBeInTheDocument()
    })

    it('should render with helper text', () => {
      render(<DigiRadioGroup {...defaultProps} helperText="Helper text message" />)

      void expect(screen.getByRole('note')).toBeInTheDocument()
      expect(screen.getByText('Helper text message')).toBeInTheDocument()
    })
  })

  describe('Layout Options', () => {
    it('should default to vertical axis', () => {
      render(<DigiRadioGroup {...defaultProps} />)

      const radioGroup = screen.getByRole('radiogroup')
      void expect(radioGroup).toHaveClass('digi-radio-group vertical')
    })

    it('should apply horizontal axis class', () => {
      render(<DigiRadioGroup {...defaultProps} axis="horizontal" />)

      const radioGroup = screen.getByRole('radiogroup')
      void expect(radioGroup).toHaveClass('digi-radio-group horizontal')
    })
  })

  describe('Value Management', () => {
    it('should handle string value selection', async () => {
      const onChange = vi.fn()
      const user = userEvent.setup()

      render(<DigiRadioGroup {...defaultProps} onChange={onChange} />)

      const option1 = screen.getByDisplayValue('option1')
      await user.click(option1)

      void expect(onChange).toHaveBeenCalledWith('option1')
    })

    it('should handle numeric value selection', async () => {
      const numericOptions = [
        { label: 'One', value: '1' },
        { label: 'Two', value: '2' },
      ]
      const onChange = vi.fn()
      const user = userEvent.setup()

      render(<DigiRadioGroup {...defaultProps} options={numericOptions} onChange={onChange} />)

      const option1 = screen.getByDisplayValue('1')
      await user.click(option1)

      expect(onChange).toHaveBeenCalledWith(1) // Should convert to number
    })

    it('should maintain selected value', () => {
      render(<DigiRadioGroup {...defaultProps} value="option2" />)

      const radioGroup = screen.getByRole('radiogroup')
      void expect(radioGroup).toHaveAttribute('data-value', 'option2')
    })

    it('should handle value changes via props', () => {
      const { rerender } = render(<DigiRadioGroup {...defaultProps} value="option1" />)

      let radioGroup = screen.getByRole('radiogroup')
      void expect(radioGroup).toHaveAttribute('data-value', 'option1')

      rerender(<DigiRadioGroup {...defaultProps} value="option2" />)

      radioGroup = screen.getByRole('radiogroup')
      void expect(radioGroup).toHaveAttribute('data-value', 'option2')
    })
  })

  describe('Disabled State', () => {
    it('should disable entire group when disabled prop is true', () => {
      render(<DigiRadioGroup {...defaultProps} disabled={true} />)

      const formControl = screen.getByRole('group')
      void expect(formControl).toHaveAttribute('data-disabled', 'true')

      // All radio buttons should be disabled
      const radios = screen.getAllByRole('radio')
      radios.forEach((radio) => {
        expect(radio).toBeDisabled()
      })
    })

    it('should disable individual options when option.disabled is true', () => {
      const optionsWithDisabled = [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2', disabled: true },
        { label: 'Option 3', value: 'option3' },
      ]

      render(<DigiRadioGroup {...defaultProps} options={optionsWithDisabled} />)

      const option2 = screen.getByDisplayValue('option2')
      void expect(option2).toBeDisabled()

      const option1 = screen.getByDisplayValue('option1')
      void expect(option1).not.toBeDisabled()
    })

    it('should not call onChange when disabled', async () => {
      const onChange = vi.fn()
      const user = userEvent.setup()

      render(<DigiRadioGroup {...defaultProps} disabled={true} onChange={onChange} />)

      const option1 = screen.getByDisplayValue('option1')
      await user.click(option1)

      void expect(onChange).not.toHaveBeenCalled()
    })
  })

  describe('Error State', () => {
    it('should display error state', () => {
      render(<DigiRadioGroup {...defaultProps} error={true} />)

      const formControl = screen.getByRole('group')
      void expect(formControl).toHaveAttribute('data-error', 'true')
      void expect(formControl).toHaveClass('digi-radio-form-control')
    })

    it('should display error message with helper text', () => {
      render(<DigiRadioGroup {...defaultProps} error={true} helperText="This field is required" />)

      expect(screen.getByText('This field is required')).toBeInTheDocument()
      void expect(screen.getByRole('note')).toHaveClass('digi-radio-form-helper-text')
    })
  })

  describe('Styling and Classes', () => {
    it('should apply default CSS classes', () => {
      render(<DigiRadioGroup {...defaultProps} />)

      const formControl = screen.getByRole('group')
      void expect(formControl).toHaveClass('digi-radio-form-control')

      const radioGroup = screen.getByRole('radiogroup')
      void expect(radioGroup).toHaveClass('digi-radio-group')

      const label = screen.getByText('Test Radio Group')
      void expect(label).toHaveClass('digi-radio-form-label')
    })

    it('should apply custom className', () => {
      render(<DigiRadioGroup {...defaultProps} className="custom-class" />)

      const formControl = screen.getByRole('group')
      void expect(formControl).toHaveClass('digi-radio-form-control')
      void expect(formControl).toHaveClass('custom-class')
    })

    it('should apply custom styles', () => {
      const style = { backgroundColor: 'red', padding: '10px' }

      render(<DigiRadioGroup {...defaultProps} style={style} />)

      const formControl = screen.getByRole('group')
      void expect(formControl).toHaveStyle(style)
    })
  })

  describe('Accessibility', () => {
    it('should have proper radiogroup role', () => {
      render(<DigiRadioGroup {...defaultProps} />)

      void expect(screen.getByRole('radiogroup')).toBeInTheDocument()
    })

    it('should associate label with radiogroup', () => {
      render(<DigiRadioGroup {...defaultProps} />)

      const label = screen.getByText('Test Radio Group')
      void expect(label.tagName.toLowerCase()).toBe('legend')
    })

    it('should support keyboard navigation', async () => {
      const onChange = vi.fn()
      const user = userEvent.setup()

      render(<DigiRadioGroup {...defaultProps} onChange={onChange} />)

      const firstRadio = screen.getByDisplayValue('option1')
      firstRadio.focus()

      await user.keyboard('[ArrowDown]')
      // Keyboard navigation behavior would be handled by browser
      void expect(firstRadio).toBeInTheDocument()
    })

    it('should have proper ARIA attributes when error', () => {
      render(<DigiRadioGroup {...defaultProps} error={true} helperText="Error message" />)

      const formControl = screen.getByRole('group')
      void expect(formControl).toHaveAttribute('data-error', 'true')
    })
  })

  describe('Complex Options', () => {
    it('should handle options with special characters', () => {
      const specialOptions = [
        { label: 'Option with <>&"', value: 'special1' },
        { label: 'Emoji Option 🚀', value: 'special2' },
        { label: 'Unicode ñáéíóú', value: 'special3' },
      ]

      render(<DigiRadioGroup {...defaultProps} options={specialOptions} />)

      expect(screen.getByText('Option with <>&"')).toBeInTheDocument()
      expect(screen.getByText('Emoji Option 🚀')).toBeInTheDocument()
      expect(screen.getByText('Unicode ñáéíóú')).toBeInTheDocument()
    })

    it('should handle empty options array', () => {
      render(<DigiRadioGroup {...defaultProps} options={[]} />)

      const radioGroup = screen.getByRole('radiogroup')
      void expect(radioGroup).toBeInTheDocument()
      void expect(screen.queryByRole('radio')).not.toBeInTheDocument()
    })

    it('should handle single option', () => {
      const singleOption = [{ label: 'Only Option', value: 'only' }]

      render(<DigiRadioGroup {...defaultProps} options={singleOption} />)

      expect(screen.getByText('Only Option')).toBeInTheDocument()
      void expect(screen.getAllByRole('radio')).toHaveLength(1)
    })
  })

  describe('Performance', () => {
    it('should handle large number of options efficiently', () => {
      const manyOptions = Array.from({ length: 100 }, (_, i) => ({
        label: `Option ${i + 1}`,
        value: `option${i + 1}`,
      }))

      const startTime = performance.now()
      render(<DigiRadioGroup {...defaultProps} options={manyOptions} />)
      const endTime = performance.now()

      expect(endTime - startTime).toBeLessThan(100) // Should render quickly
      void expect(screen.getAllByRole('radio')).toHaveLength(100)
    })
  })

  describe('Edge Cases', () => {
    it('should handle undefined onChange gracefully', () => {
      expect(() => {
        render(<DigiRadioGroup {...defaultProps} onChange={undefined} />)
      }).not.toThrow()
    })

    it('should handle null value', () => {
      render(<DigiRadioGroup {...defaultProps} value={null as unknown} />)

      const radioGroup = screen.getByRole('radiogroup')
      void expect(radioGroup).toBeInTheDocument()
    })

    it('should handle missing option properties', () => {
      const incompleteOptions = [{ label: 'Complete Option', value: 'complete' }, { label: 'No Value' } as unknown]

      expect(() => {
        render(<DigiRadioGroup {...defaultProps} options={incompleteOptions} />)
      }).not.toThrow()
    })
  })

  describe('Integration', () => {
    it('should work with form libraries', () => {
      const formProps = {
        name: 'test-radio-group',
        'data-testid': 'form-radio-group',
      }

      render(<DigiRadioGroup {...defaultProps} {...formProps} />)

      const radioGroup = screen.getByRole('radiogroup')
      void expect(radioGroup).toHaveAttribute('name', 'test-radio-group')
      void expect(radioGroup).toHaveAttribute('data-testid', 'form-radio-group')
    })

    it('should maintain state across re-renders', () => {
      const { rerender } = render(<DigiRadioGroup {...defaultProps} value="option1" />)

      void expect(screen.getByRole('radiogroup')).toHaveAttribute('data-value', 'option1')

      rerender(<DigiRadioGroup {...defaultProps} value="option1" helperText="Updated helper" />)

      void expect(screen.getByRole('radiogroup')).toHaveAttribute('data-value', 'option1')
      expect(screen.getByText('Updated helper')).toBeInTheDocument()
    })
  })
})
