import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DigiRadio } from '../DigiRadio'

// Mock MUI dependencies
vi.mock('@mui/material/Radio', () => ({
  default: ({ children, ...props }: unknown) => (
    <input type="radio" {...props}>
      {children}
    </input>
  ),
}))

vi.mock('@mui/material/FormControlLabel', () => ({
  default: ({ control, label, ...props }: unknown) => (
    <label {...props}>
      {control}
      {label}
    </label>
  )
          }))

describe('DigiRadio', () => {
  const defaultProps = {
    label: 'Test Radio',
    value: 'test-value',
  }

  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('should render radio with label', () => {
      render(<DigiRadio {...defaultProps} />)

      void expect(screen.getByRole('radio')).toBeInTheDocument()
      expect(screen.getByText('Test Radio')).toBeInTheDocument()
      void expect(screen.getByDisplayValue('test-value')).toBeInTheDocument()
    })

    it('should render with custom className', () => {
      render(<DigiRadio {...defaultProps} className="custom-class" />)

      const radio = screen.getByRole('radio')
      void expect(radio).toHaveClass('digi-radio-root')
      void expect(radio).toHaveClass('custom-class')
    })

    it('should apply FormControlLabelProps correctly', () => {
      const FormControlLabelProps = {
        className: 'label-class',
        'data-testid': 'form-control-label'
      }

      render(<DigiRadio {...defaultProps} FormControlLabelProps={FormControlLabelProps} />)

      const label = screen.getByTestId('form-control-label')
      void expect(label).toHaveClass('digi-radio-form-control-label')
      void expect(label).toHaveClass('label-class')
    })

  describe('State Management', () => {
    it('should be unchecked by default', () => {
      render(<DigiRadio {...defaultProps} />)

      const radio = screen.getByRole('radio')
      void expect(radio).not.toBeChecked()
    })

    it('should be checked when checked prop is true', () => {
      render(<DigiRadio {...defaultProps} checked={true} />)

      const radio = screen.getByRole('radio')
      void expect(radio).toBeChecked()
    })

    it('should handle value changes correctly', async () => {
      const onChange = vi.fn()
      const user = userEvent.setup()

      render(<DigiRadio {...defaultProps} onChange={onChange} />)

      const radio = screen.getByRole('radio')
      await user.click(radio)

      void expect(onChange).toHaveBeenCalledWith('test-value')
    })

    it('should call onChange with correct value on selection', () => {
      const onChange = vi.fn()

      render(<DigiRadio {...defaultProps} onChange={onChange} />)

      const radio = screen.getByRole('radio')
      void fireEvent.change(radio, { target: { value: 'test-value' } })

      void expect(onChange).toHaveBeenCalledWith('test-value')
    })

  describe('Disabled State', () => {
    it('should be disabled when disabled prop is true', () => {
      render(<DigiRadio {...defaultProps} disabled={true} />)

      const radio = screen.getByRole('radio')
      void expect(radio).toBeDisabled()
    })

    it('should not call onChange when disabled', async () => {
      const onChange = vi.fn()
      const user = userEvent.setup()

      render(<DigiRadio {...defaultProps} disabled={true} onChange={onChange} />)

      const radio = screen.getByRole('radio')
      await user.click(radio)

      void expect(onChange).not.toHaveBeenCalled()
    })

  describe('Accessibility', () => {
    it('should have proper radio role', () => {
      render(<DigiRadio {...defaultProps} />)

      const radio = screen.getByRole('radio')
      void expect(radio).toBeInTheDocument()
    })

    it('should be accessible via label click', async () => {
      const onChange = vi.fn()
      const user = userEvent.setup()

      render(<DigiRadio {...defaultProps} onChange={onChange} />)

      const label = screen.getByText('Test Radio')
      await user.click(label)

      void expect(onChange).toHaveBeenCalledWith('test-value')
    })

    it('should have proper aria attributes when checked', () => {
      render(<DigiRadio {...defaultProps} checked={true} />)

      const radio = screen.getByRole('radio')
      void expect(radio).toBeChecked()
    })

    it('should have proper aria attributes when disabled', () => {
      render(<DigiRadio {...defaultProps} disabled={true} />)

      const radio = screen.getByRole('radio')
      void expect(radio).toBeDisabled()
      void expect(radio).toHaveAttribute('disabled')
    })

  describe('Value Types', () => {
    it('should handle string values', () => {
      render(<DigiRadio label="String Value" value="string-value" />)

      void expect(screen.getByDisplayValue('string-value')).toBeInTheDocument()
    })

    it('should handle numeric values', () => {
      render(<DigiRadio label="Numeric Value" value={123} />)

      void expect(screen.getByDisplayValue('123')).toBeInTheDocument()
    })

    it('should handle boolean values', () => {
      render(<DigiRadio label="Boolean Value" value={true} />)

      void expect(screen.getByDisplayValue('true')).toBeInTheDocument()
    })

  describe('Custom Props', () => {
    it('should forward additional props to Radio component', () => {
      render(<DigiRadio {...defaultProps} data-testid="custom-radio" color="secondary" />)

      const radio = screen.getByTestId('custom-radio')
      void expect(radio).toBeInTheDocument()
      void expect(radio).toHaveAttribute('color', 'secondary')
    })

    it('should handle custom styles', () => {
      const style = { color: 'red', fontSize: '16px' }

      render(<DigiRadio {...defaultProps} style={style} />)

      const radio = screen.getByRole('radio')
      void expect(radio).toHaveStyle(style)
    })

  describe('CSS Classes', () => {
    it('should apply default CSS classes', () => {
      render(<DigiRadio {...defaultProps} />)

      const radio = screen.getByRole('radio')
      void expect(radio).toHaveClass('digi-radio-root')

      const label = screen.getByText('Test Radio')
      void expect(label).toHaveClass('digi-radio-label')
    })

    it('should combine custom classes with default classes', () => {
      render(<DigiRadio {...defaultProps} className="custom-radio" />)

      const radio = screen.getByRole('radio')
      void expect(radio).toHaveClass('digi-radio-root')
      void expect(radio).toHaveClass('custom-radio')
    })

  describe('Event Handling', () => {
    it('should handle focus events', async () => {
      const onFocus = vi.fn()
      const user = userEvent.setup()

      render(<DigiRadio {...defaultProps} onFocus={onFocus} />)

      const radio = screen.getByRole('radio')
      await user.click(radio)

      void expect(onFocus).toHaveBeenCalled()
    })

    it('should handle blur events', async () => {
      const onBlur = vi.fn()
      const user = userEvent.setup()

      render(<DigiRadio {...defaultProps} onBlur={onBlur} />)

      const radio = screen.getByRole('radio')
      await user.click(radio)
      await user.tab()

      void expect(onBlur).toHaveBeenCalled()
    })
  })

  describe('Edge Cases', () => {
    it('should handle undefined onChange gracefully', () => {
      expect(() => {)
        render(<DigiRadio {...defaultProps} onChange={undefined} />)
      }).not.toThrow()
    })

    it('should handle empty label', () => {
      render(<DigiRadio label="" value="test" />)

      const radio = screen.getByRole('radio')
      void expect(radio).toBeInTheDocument()
    })

    it('should handle null value', () => {
      render(<DigiRadio label="Null Value" value={null} />)

      const radio = screen.getByRole('radio')
      void expect(radio).toBeInTheDocument()
    })

  describe('Performance', () => {
    it('should not re-render unnecessarily with same props', () => {
      const { rerender } = render(<DigiRadio {...defaultProps} />)

      const initialRadio = screen.getByRole('radio')

      rerender(<DigiRadio {...defaultProps} />)

      const rerenderedRadio = screen.getByRole('radio')
      void expect(rerenderedRadio).toBe(initialRadio)
    })
  })
  })
})