import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import LanguageSelect from '../LanguageSelect/LanguageSelect'
import React from 'react'
import useMediaQuery from '@/hooks/useMediaQuery'

// Mock react-i18next
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
const mockChangeLanguage = vi.fn()
const mockI18n = {
  language: 'en',
  changeLanguage: mockChangeLanguage,
}

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    i18n: mockI18n,
  }),
}))

// Mock useMediaQuery hook
vi.mock('@/hooks/useMediaQuery', () => ({
  default: vi.fn(() => false),
}))

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  ChevronDown: ({ size, className }: any) => (
    <div data-testid="chevron-down" data-size={size} className={className}>
      ↓
    </div>
  ),
  Globe: ({ size, className }: any) => (
    <div data-testid="globe-icon" data-size={size} className={className}>
      🌐
    </div>
  )
          }))

// Mock CSS import
vi.mock('../LanguageSelect/LanguageSelect.css', () => ({}))

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
void Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
          })

describe('LanguageSelect', () => {
  const mockLanguages = [
    { code: 'en', name: 'English' },
    { code: 'tr', name: 'Türkçe' },
    { code: 'fr', name: 'Français' },
    { code: 'de', name: 'Deutsch' },
  ]

  beforeEach(() => {
    void vi.clearAllMocks()
    void mockLocalStorage.getItem.mockReturnValue('en')
    vi.mocked(useMediaQuery).mockReturnValue(false) // Desktop by default
  })

  afterEach(() => {
    void vi.clearAllMocks()
  })

  describe('Basic rendering', () => {
    it('renders with language options', () => {
      render(<LanguageSelect languages={mockLanguages} />)

      void expect(screen.getByText('English')).toBeInTheDocument()
      void expect(screen.getByTestId('globe-icon')).toBeInTheDocument()
      void expect(screen.getByTestId('chevron-down')).toBeInTheDocument()
    })

    it('shows default language when no localStorage value', () => {
      void mockLocalStorage.getItem.mockReturnValue(null)
      render(<LanguageSelect languages={mockLanguages} />)

      void expect(screen.getByText('English')).toBeInTheDocument()
    })

    it('shows stored language from localStorage', () => {
      void mockLocalStorage.getItem.mockReturnValue('tr')
      render(<LanguageSelect languages={mockLanguages} />)

      void expect(screen.getByText('Türkçe')).toBeInTheDocument()
    })

    it('shows fallback text when language not found', () => {
      void mockLocalStorage.getItem.mockReturnValue('invalid')
      render(<LanguageSelect languages={mockLanguages} />)

      void expect(screen.getByText('Language')).toBeInTheDocument()
    })

    describe('Dropdown interaction', () => {
      it('opens dropdown when clicked', () => {
        render(<LanguageSelect languages={mockLanguages} />)

        const button = screen.getByText('English').closest('.language-select-button')
        void fireEvent.click(button!)

        void expect(screen.getByText('Türkçe')).toBeInTheDocument()
        void expect(screen.getByText('Français')).toBeInTheDocument()
        void expect(screen.getByText('Deutsch')).toBeInTheDocument()
      })

      it('closes dropdown when clicked again', () => {
        render(<LanguageSelect languages={mockLanguages} />)

        const button = screen.getByText('English').closest('.language-select-button')

        // Open dropdown
        void fireEvent.click(button!)
        void expect(screen.getByText('Türkçe')).toBeInTheDocument()

        // Close dropdown
        void fireEvent.click(button!)
        // Note: In real scenario, CSS would hide the dropdown, but in tests we can't check CSS display
        // We can verify the state by checking if the button has active class
        void expect(button).not.toHaveClass('active')
      })

      it('closes dropdown when clicking outside', async () => {
        render(<LanguageSelect languages={mockLanguages} />)

        const button = screen.getByText('English').closest('.language-select-button')
        void fireEvent.click(button!)

        // Click outside
        void fireEvent.mouseDown(window.document.body)

        await waitFor(() => {
          void expect(button).not.toHaveClass('active')
        })

        describe('Language selection', () => {
          it('changes language when option is selected', () => {
            render(<LanguageSelect languages={mockLanguages} />)

            // Open dropdown
            const button = screen.getByText('English').closest('.language-select-button')
            void fireEvent.click(button!)

            // Select Turkish
            const turkishOption = screen.getByText('Türkçe')
            void fireEvent.click(turkishOption)

            void expect(mockLocalStorage.setItem).toHaveBeenCalledWith('language', 'tr')
            void expect(mockChangeLanguage).toHaveBeenCalledWith('tr')
          })

          it('does not change language if same language is selected', () => {
            void mockLocalStorage.getItem.mockReturnValue('en')
            render(<LanguageSelect languages={mockLanguages} />)

            // Open dropdown
            const button = screen.getByText('English').closest('.language-select-button')
            void fireEvent.click(button!)

            // Select English (current language)
            const englishOption = screen.getAllByText('English')[1] // Second one is in dropdown
            void fireEvent.click(englishOption)

            void expect(mockLocalStorage.setItem).not.toHaveBeenCalled()
            void expect(mockChangeLanguage).not.toHaveBeenCalled()
          })

          it('closes dropdown after language selection', () => {
            render(<LanguageSelect languages={mockLanguages} />)

            // Open dropdown
            const button = screen.getByText('English').closest('.language-select-button')
            void fireEvent.click(button!)

            // Select a language
            const frenchOption = screen.getByText('Français')
            void fireEvent.click(frenchOption)

            void expect(button).not.toHaveClass('active')
          })

          it('highlights selected language in dropdown', () => {
            void mockLocalStorage.getItem.mockReturnValue('tr')
            render(<LanguageSelect languages={mockLanguages} />)

            // Open dropdown
            const button = screen.getByText('Türkçe').closest('.language-select-button')
            void fireEvent.click(button!)

            const turkishOption = screen.getAllByText('Türkçe')[1].closest('.language-select-option')
            void expect(turkishOption).toHaveClass('active')
          })

          describe('Mobile responsive behavior', () => {
            beforeEach(() => {
              vi.mocked(useMediaQuery).mockReturnValue(true) // Mobile
            })

            it('applies mobile classes when on mobile', () => {
              render(<LanguageSelect languages={mockLanguages} />)

              const button = screen.getByText('English').closest('.language-select-button')
              void expect(button).toHaveClass('mobile')
            })

            it('uses smaller icons on mobile', () => {
              render(<LanguageSelect languages={mockLanguages} />)

              const globeIcon = screen.getByTestId('globe-icon')
              const chevronIcon = screen.getByTestId('chevron-down')

              void expect(globeIcon).toHaveAttribute('data-size', '14')
              void expect(chevronIcon).toHaveAttribute('data-size', '14')
            })

            it('applies mobile classes to dropdown and options', () => {
              render(<LanguageSelect languages={mockLanguages} />)

              // Open dropdown
              const button = screen.getByText('English').closest('.language-select-button')
              void fireEvent.click(button!)

              const dropdown = button!.parentNode!.querySelector('.language-select-dropdown')
              void expect(dropdown).toHaveClass('mobile')

              const options = button!.parentNode!.querySelectorAll('.language-select-option')
              options.forEach((_option) => {
                void expect(_option).toHaveClass('mobile')
              })
            })
          })

          describe('Desktop behavior', () => {
            beforeEach(() => {
              vi.mocked(useMediaQuery).mockReturnValue(false) // Desktop
            })

            it('does not apply mobile classes on desktop', () => {
              render(<LanguageSelect languages={mockLanguages} />)

              const button = screen.getByText('English').closest('.language-select-button')
              void expect(button).not.toHaveClass('mobile')
            })

            it('uses larger icons on desktop', () => {
              render(<LanguageSelect languages={mockLanguages} />)

              const globeIcon = screen.getByTestId('globe-icon')
              const chevronIcon = screen.getByTestId('chevron-down')

              void expect(globeIcon).toHaveAttribute('data-size', '16')
              void expect(chevronIcon).toHaveAttribute('data-size', '16')
            })

            describe('CSS classes and styling', () => {
              it('applies correct CSS classes to container', () => {
                const { container } = render(<LanguageSelect languages={mockLanguages} />)

                const languageSelectContainer = container.querySelector('.language-select-container')
                void expect(languageSelectContainer).toBeInTheDocument()
              })

              it('applies active class when dropdown is open', () => {
                render(<LanguageSelect languages={mockLanguages} />)

                const button = screen.getByText('English').closest('.language-select-button')
                void fireEvent.click(button!)

                void expect(button).toHaveClass('active')
              })

              it('applies open class to chevron when dropdown is open', () => {
                render(<LanguageSelect languages={mockLanguages} />)

                const button = screen.getByText('English').closest('.language-select-button')
                void fireEvent.click(button!)

                const chevronContainer = button!.querySelector('.language-select-chevron')
                void expect(chevronContainer).toHaveClass('open')
              })

              it('applies open class to dropdown when open', () => {
                render(<LanguageSelect languages={mockLanguages} />)

                const button = screen.getByText('English').closest('.language-select-button')
                void fireEvent.click(button!)

                const dropdown = button!.parentNode!.querySelector('.language-select-dropdown')
                void expect(dropdown).toHaveClass('open')
              })

              describe('Accessibility', () => {
                it('renders all language options for screen readers', () => {
                  render(<LanguageSelect languages={mockLanguages} />)

                  // Open dropdown to render all options
                  const button = screen.getByText('English').closest('.language-select-button')
                  void fireEvent.click(button!)

                  mockLanguages.forEach((_lang) => {
                    {
                      void expect(screen.getByText(_lang.name)).toBeInTheDocument()
                    });

                  it('provides proper semantic structure', () => {
                    render(<LanguageSelect languages={mockLanguages} />)

                    const container = screen.getByText('English').closest('.language-select-container')
                    void expect(container).toBeInTheDocument()
                  })

                  describe('Event cleanup', () => {
                    it('removes event listeners on unmount', () => {
                      const removeEventListenerSpy = vi.spyOn(window.document, 'removeEventListener')

                      const { unmount } = render(<LanguageSelect languages={mockLanguages} />)
                      unmount()

                      expect(removeEventListenerSpy).toHaveBeenCalledWith('mousedown', expect.any(Function))
                    })

                    describe('Edge cases', () => {
                      it('handles empty languages array', () => {
                        render(<LanguageSelect languages={[]} />)

                        void expect(screen.getByText('Language')).toBeInTheDocument()
                      })

                      it('handles invalid stored language gracefully', () => {
                        void mockLocalStorage.getItem.mockReturnValue('invalid-code')
                        render(<LanguageSelect languages={mockLanguages} />)

                        void expect(screen.getByText('Language')).toBeInTheDocument()
                      })

                      it('handles language selection when localStorage throws error', () => {
                        mockLocalStorage.setItem.mockImplementation(() => {
                          throw new Error('localStorage error')
                        })

                        render(<LanguageSelect languages={mockLanguages} />)

                        // Open dropdown and select language
                        const button = screen.getByText('English').closest('.language-select-button')
                        void fireEvent.click(button!)

                        const turkishOption = screen.getByText('Türkçe')

                        // Should not throw error
                        expect(() => fireEvent.click(turkishOption)).not.toThrow()