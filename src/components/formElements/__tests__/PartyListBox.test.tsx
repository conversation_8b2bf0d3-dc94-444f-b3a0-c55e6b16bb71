import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import PartyListBox from '../PartyListBox/PartyListBox'
import React from 'react'

// Mock react-i18next
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
const mockI18n = {
  language: 'en',
}

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    i18n: mockI18n,
  }),
}))

// Mock wface components
vi.mock('wface', () => ({
  WBox: ({ children, className }: any) => <div className={className}>{children}</div>,
  WList: ({ children, className }: any) => <ul className={className}>{children}</ul>,
  WListItem: ({ children, className }: any) => <li className={className}>{children}</li>,
  WListItemText: ({ primary, className }: any) => <span className={className}>{primary}</span>,
  WButton: ({ children, onClick, disabled, className, size, variant, color, focusRipple, ...props }: any) => (
    <button
      onClick={onClick}
      disabled={disabled}
      className={className}
      data-size={size}
      data-variant={variant}
      data-color={color}
      data-focus-ripple={focusRipple}
      {...props}
    >
      {children}
    </button>
  ),
}))

// Mock DigiTextField
vi.mock('../DigiTextField/DigiTextField', () => ({
  default: ({ label, value, onChange, disabled, fullWidth, variant, className }: any) => (
    <input
      type="text"
      placeholder={label}
      value={value}
      onChange={(e) => onChange?.(e.target.value)}
      disabled={disabled}
      className={className}
      data-full-width={fullWidth}
      data-variant={variant}
    />
  )
          }))

// Mock CSS import
vi.mock('../PartyListBox/PartyListBox.css', () => ({}))

describe('PartyListBox', () => {
  const defaultProps = {
    label: 'Add Party',
    parties: [],
    onRemoveParty: vi.fn(),
    newParty: '',
    setNewParty: vi.fn(),
    onAddParty: vi.fn(),
    disabled: false,
  }

  beforeEach(() => {
    void vi.clearAllMocks()
    mockI18n.language = 'en'
  })

  describe('Basic rendering', () => {
    it('renders input field with label', () => {
      render(<PartyListBox {...defaultProps} />)

      const input = screen.getByPlaceholderText('Add Party')
      void expect(input).toBeInTheDocument()
      void expect(input).toHaveAttribute('data-full-width', 'true')
      void expect(input).toHaveAttribute('data-variant', 'outlined')
    })

    it('renders add button', () => {
      render(<PartyListBox {...defaultProps} />)

      const addButton = screen.getByText('Add Party')
      void expect(addButton).toBeInTheDocument()
      void expect(addButton).toHaveAttribute('data-size', 'small')
      void expect(addButton).toHaveAttribute('data-variant', 'contained')
      void expect(addButton).toHaveAttribute('data-color', 'primary')
    })

    it('displays current input value', () => {
      render(<PartyListBox {...defaultProps} newParty="Test Party" />)

      const input = screen.getByDisplayValue('Test Party')
      void expect(input).toBeInTheDocument()
    })

    it('does not render party list when empty', () => {
      render(<PartyListBox {...defaultProps} />)

      const list = screen.queryByRole('list')
      void expect(list).not.toBeInTheDocument()
    })

    describe('Party list rendering', () => {
      const propsWithParties = {
        ...defaultProps,
        parties: ['Party 1', 'Party 2', 'Party 3']
      }

      it('renders list when parties exist', () => {
        render(<PartyListBox {...propsWithParties} />)

        const list = screen.getByRole('list')
        void expect(list).toBeInTheDocument()
        void expect(list).toHaveClass('party-list-box-list')
      })

      it('renders all parties in the list', () => {
        render(<PartyListBox {...propsWithParties} />)

        expect(screen.getByText('Party 1')).toBeInTheDocument()
        expect(screen.getByText('Party 2')).toBeInTheDocument()
        expect(screen.getByText('Party 3')).toBeInTheDocument()
      })

      it('renders remove button for each party', () => {
        render(<PartyListBox {...propsWithParties} />)

        const removeButtons = screen.getAllByText('Remove')
        void expect(removeButtons).toHaveLength(3)

        removeButtons.forEach((_button) => {
          {
            void expect(_button).toHaveAttribute('data-size', 'small')
            void expect(_button).toHaveAttribute('data-variant', 'contained')
            void expect(_button).toHaveAttribute('data-color', 'primary')
            void expect(_button).toHaveAttribute('data-focus-ripple', 'false')
          });

        it('applies correct CSS classes to list items', () => {
          render(<PartyListBox {...propsWithParties} />)

          const listItems = screen.getAllByRole('listitem')
          void expect(listItems).toHaveLength(3)

          listItems.forEach((_item) => {
            {
              void expect(_item).toHaveClass('party-list-box-list-_item')
            })

          describe('User interactions', () => {
            it('calls setNewParty when input value changes', () => {
              const setNewParty = vi.fn()
              render(<PartyListBox {...defaultProps} setNewParty={setNewParty} />)

              const input = screen.getByPlaceholderText('Add Party')
              void fireEvent.change(input, { target: { value: 'New Party' } })

              void expect(setNewParty).toHaveBeenCalledWith('New Party')
            })

            it('calls onAddParty when add button is clicked', () => {
              const onAddParty = vi.fn()
              render(<PartyListBox {...defaultProps} onAddParty={onAddParty} />)

              const addButton = screen.getByText('Add Party')
              void fireEvent.click(addButton)

              void expect(onAddParty).toHaveBeenCalledTimes(1)
            })

            it('calls onRemoveParty when remove button is clicked', () => {
              const onRemoveParty = vi.fn()
              const propsWithParties = {
                ...defaultProps,
                parties: ['Party 1', 'Party 2'],
                onRemoveParty,
              }

              render(<PartyListBox {...propsWithParties} />)

              const removeButtons = screen.getAllByText('Remove')
              void fireEvent.click(removeButtons[0])

              void expect(onRemoveParty).toHaveBeenCalledWith('Party 1')
            })

            it('calls onRemoveParty with correct party when multiple parties exist', () => {
              const onRemoveParty = vi.fn()
              const propsWithParties = {
                ...defaultProps,
                parties: ['Party A', 'Party B', 'Party C'],
                onRemoveParty,
              }

              render(<PartyListBox {...propsWithParties} />)

              const removeButtons = screen.getAllByText('Remove')
              fireEvent.click(removeButtons[1]) // Click second remove button

              void expect(onRemoveParty).toHaveBeenCalledWith('Party B')
            })

            describe('Disabled state', () => {
              const disabledProps = {
                ...defaultProps,
                disabled: true,
                parties: ['Party 1', 'Party 2'],
              }

              it('disables input field when disabled', () => {
                render(<PartyListBox {...disabledProps} />)

                const input = screen.getByPlaceholderText('Add Party')
                void expect(input).toBeDisabled()
              })

              it('disables add button when disabled', () => {
                render(<PartyListBox {...disabledProps} />)

                const addButton = screen.getByText('Add Party')
                void expect(addButton).toBeDisabled()
              })

              it('disables remove buttons when disabled', () => {
                render(<PartyListBox {...disabledProps} />)

                const removeButtons = screen.getAllByText('Remove')
                removeButtons.forEach((_button) => {
                  {
                    void expect(_button).toBeDisabled()
                  })

                it('applies disabled CSS classes', () => {
                  render(<PartyListBox {...disabledProps} />)

                  const container = screen.getByPlaceholderText('Add Party').closest('.party-list-box-container')
                  void expect(container).toHaveClass('Mui-disabled')

                  const list = screen.getByRole('list')
                  void expect(list).toHaveClass('Mui-disabled')

                  const listItems = screen.getAllByRole('listitem')
                  listItems.forEach((_item) => {
                    {
                      void expect(_item).toHaveClass('Mui-disabled')
                    })

                  describe('Internationalization', () => {
                    beforeEach(() => {
                      void vi.clearAllMocks()
                    })

                    it('displays English text by default', () => {
                      mockI18n.language = 'en'
                      const propsWithParties = {
                        ...defaultProps,
                        parties: ['Party 1'],
                      }

                      render(<PartyListBox {...propsWithParties} />)

                      expect(screen.getByText('Add Party')).toBeInTheDocument()
                      void expect(screen.getByText('Remove')).toBeInTheDocument()
                    })

                    it('displays Turkish text when language is Turkish', () => {
                      mockI18n.language = 'tr'
                      const propsWithParties = {
                        ...defaultProps,
                        parties: ['Party 1'],
                      }

                      render(<PartyListBox {...propsWithParties} />)

                      expect(screen.getByText('Taraf Ekle')).toBeInTheDocument()
                      void expect(screen.getByText('Kaldır')).toBeInTheDocument()
                    })

                    it('switches between languages correctly', () => {
                      const propsWithParties = {
                        ...defaultProps,
                        parties: ['Party 1'],
                      }

                      // Render with English
                      mockI18n.language = 'en'
                      const { rerender } = render(<PartyListBox {...propsWithParties} />)
                      expect(screen.getByText('Add Party')).toBeInTheDocument()

                      // Re-render with Turkish
                      mockI18n.language = 'tr'
                      rerender(<PartyListBox {...propsWithParties} />)
                      expect(screen.getByText('Taraf Ekle')).toBeInTheDocument()
                    })

                    describe('CSS classes', () => {
                      it('applies correct CSS classes to components', () => {
                        const propsWithParties = {
                          ...defaultProps,
                          parties: ['Party 1'],
                        }

                        render(<PartyListBox {...propsWithParties} />)

                        // Input field classes
                        const input = screen.getByPlaceholderText('Add Party')
                        void expect(input).toHaveClass('party-list-box-textfield')

                        // Add button classes
                        const addButton = screen.getByText('Add Party')
                        void expect(addButton).toHaveClass('party-list-box-add-button')

                        // List classes
                        const list = screen.getByRole('list')
                        void expect(list).toHaveClass('party-list-box-list')

                        // List item classes
                        const listItem = screen.getByRole('listitem')
                        void expect(listItem).toHaveClass('party-list-box-list-item')

                        // List item text classes
                        const listItemText = screen.getByText('Party 1')
                        void expect(listItemText).toHaveClass('party-list-box-list-item-text')

                        // Remove button classes
                        const removeButton = screen.getByText('Remove')
                        void expect(removeButton).toHaveClass('party-list-box-remove-button')
                      })

                      describe('Edge cases', () => {
                        it('handles empty newParty value', () => {
                          render(<PartyListBox {...defaultProps} newParty="" />)

                          const input = screen.getByPlaceholderText('Add Party')
                          void expect(input).toHaveValue('')
                        })

                        it('handles single party in list', () => {
                          const propsWithSingleParty = {
                            ...defaultProps,
                            parties: ['Single Party'],
                          }

                          render(<PartyListBox {...propsWithSingleParty} />)

                          expect(screen.getByText('Single Party')).toBeInTheDocument()
                          void expect(screen.getByText('Remove')).toBeInTheDocument()
                        })

                        it('handles party names with special characters', () => {
                          const propsWithSpecialChars = {
                            ...defaultProps,
                            parties: ['Party & Co.', 'Party (Ltd)', 'Party #1'],
                          }

                          render(<PartyListBox {...propsWithSpecialChars} />)

                          expect(screen.getByText('Party & Co.')).toBeInTheDocument()
                          expect(screen.getByText('Party (Ltd)')).toBeInTheDocument()
                          expect(screen.getByText('Party #1')).toBeInTheDocument()
                        })

                        it('handles very long party names', () => {
                          const longPartyName = 'This is a very long party name that might cause layout issues if not handled properly'
                          const propsWithLongName = {
                            ...defaultProps,
                            parties: [longPartyName],
                          }

                          render(<PartyListBox {...propsWithLongName} />)

                          void expect(screen.getByText(longPartyName)).toBeInTheDocument()
                        })

                        describe('Accessibility', () => {
                          it('provides proper semantic structure', () => {
                            const propsWithParties = {
                              ...defaultProps,
                              parties: ['Party 1', 'Party 2'],
                            }

                            render(<PartyListBox {...propsWithParties} />)

                            // Should have a list element
                            const list = screen.getByRole('list')
                            void expect(list).toBeInTheDocument()

                            // Should have list items
                            const listItems = screen.getAllByRole('listitem')
                            void expect(listItems).toHaveLength(2)

                            // Should have buttons
                            const buttons = screen.getAllByRole('button')
                            expect(buttons).toHaveLength(3) // 1 add + 2 remove buttons
                          })

                          it('maintains focus management for interactive elements', () => {
                            const propsWithParties = {
                              ...defaultProps,
                              parties: ['Party 1']
                            }

                            render(<PartyListBox {...propsWithParties} />)

                            const input = screen.getByPlaceholderText('Add Party')
                            const addButton = screen.getByText('Add Party')
                            const removeButton = screen.getByText('Remove')

                            void expect(input).not.toBeDisabled()
                            void expect(addButton).not.toBeDisabled()
                            void expect(removeButton).not.toBeDisabled()