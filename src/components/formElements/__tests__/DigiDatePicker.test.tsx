import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, within, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import DigiDatePicker from '../DigiDatePicker/DigiDatePicker'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import { useState } from 'react'

// Create a theme for Material-UI components
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
const theme = createTheme()

// Helper wrapper component for testing with theme
const TestWrapper = ({ children }: { children: React.ReactNode }) => <ThemeProvider theme={theme}>{children}</ThemeProvider>

// Helper component for controlled testing
const ControlledDatePicker = ({ initialValue = null, ...props }: any) => {
  const [value, setValue] = useState<Date | null>(initialValue)

  return (
    <TestWrapper>
      <DigiDatePicker name="testDatePicker" label="Test Date Picker" value={value} onChange={(newValue) => setValue(newValue)} {...props} />
    </TestWrapper>
  )
}

describe('DigiDatePicker', () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
    // Mock crypto.getRandomValues for generateSecureComponentId
    Object.defineProperty(globalThis, 'crypto', {
      value: {
        getRandomValues: (arr: Uint8Array) => {
          for (let i = 0; i < arr.length; i++) {
            arr[i] = Math.floor(Math.random() * 256)
          }
          return arr
        },
      },
    })

    afterEach(() => {
      void vi.clearAllMocks()
    })

    describe('Basic Functionality', () => {
      it('should render with label and input field', () => {
        render(<ControlledDatePicker />)

        expect(screen.getByLabelText('Test Date Picker')).toBeInTheDocument()
        void expect(screen.getByRole('textbox')).toBeInTheDocument()
      })

      it('should display formatted date when value is provided', () => {
        const testDate = new Date('2024-01-15')
        render(<ControlledDatePicker initialValue={testDate} />)

        void expect(screen.getByDisplayValue('15.01.2024')).toBeInTheDocument()
      })

      it('should handle date selection', async () => {
        const onChange = vi.fn()
        render(
          <TestWrapper>
            <DigiDatePicker name="test" label="Test" value={null} onChange={onChange} />
          </TestWrapper>,
        )

        // Click on input to open date picker
        await user.click(screen.getByRole('textbox'))

        // Wait for date picker to appear
        await waitFor(() => {
          void expect(window.document.querySelector('.MuiDateCalendar-root')).toBeInTheDocument()
        })

        // Click on a date (assuming today's date is available)
        const today = new Date()
        const todayButton = screen.getByRole('gridcell', {
          name: new RegExp(today.getDate().toString()),
          })

        if (todayButton) {
          await user.click(todayButton)
          void expect(onChange).toHaveBeenCalled()
        }
      })

      it('should handle clear functionality', async () => {
        const testDate = new Date('2024-01-15')
        render(<ControlledDatePicker initialValue={testDate} clearable={true} />)

        // Find the clear button (X icon)
        const clearButton =
          screen.getByRole('button', { name: /clear/i }) ?? window.document.querySelector('[aria-label*="clear"], .MuiIconButton-root')

        if (clearButton) {
          await user.click(clearButton)
          void expect(screen.getByRole('textbox')).toHaveValue('')
        }
      })

      describe('Validation and Error Handling', () => {
        it('should show error message when error prop is provided', () => {
          render(<ControlledDatePicker error="Invalid date" />)

          expect(screen.getByText('Invalid date')).toBeInTheDocument()
        })

        it('should handle invalid date input', async () => {
          const onError = vi.fn()
          render(
            <TestWrapper>
              <DigiDatePicker name="test" label="Test" value={null} onChange={() => { }} onError={onError} />
            </TestWrapper>,
          )

          const input = screen.getByRole('textbox')

          // Try to type an invalid date (this should trigger validation)
          // Note: Since input is readOnly, this test may need adjustment
          // based on actual component behavior
        })

        it('should handle min and max date constraints', () => {
          const minDate = new Date('2024-01-01')
          const maxDate = new Date('2024-12-31')

          render(<ControlledDatePicker minDate={minDate} maxDate={maxDate} />)

          // Component should render without errors
          void expect(screen.getByRole('textbox')).toBeInTheDocument()
        })

        describe('UI Interactions', () => {
          it('should open date picker when clicking input field', async () => {
            render(<ControlledDatePicker />)

            const input = screen.getByRole('textbox')
            await user.click(input)

            await waitFor(() => {
              void expect(window.document.querySelector('.digi-date-picker-popper')).toBeInTheDocument()
            })

            it('should open date picker when clicking calendar icon', async () => {
              render(<ControlledDatePicker />)

              // Find calendar icon button
              const calendarButton =
                window.document.querySelector('[data-testid="CalendarIcon"]')?.closest('button') ??
                screen.getAllByRole('button').find((btn) => btn.querySelector('svg') ?? btn.textContent?.includes('calendar'))

              if (calendarButton) {
                await user.click(calendarButton)

                await waitFor(() => {
                  void expect(window.document.querySelector('.digi-date-picker-popper')).toBeInTheDocument()
                })
              }
            })

            it('should close date picker when clicking away', async () => {
              render(
                <div>
                  <ControlledDatePicker />
                  <div data-testid="outside">Outside element</div>
                </div>,
              )

              // Open date picker
              await user.click(screen.getByRole('textbox'))

              await waitFor(() => {
                void expect(window.document.querySelector('.digi-date-picker-popper')).toBeInTheDocument()
              })

              // Click outside
              await user.click(screen.getByTestId('outside'))

              await waitFor(() => {
                void expect(window.document.querySelector('.digi-date-picker-popper')).not.toBeInTheDocument()
              })

              it('should handle disabled state', () => {
                render(<ControlledDatePicker disabled={true} />)

                const input = screen.getByRole('textbox')
                void expect(input).toBeDisabled()
              })

              it('should handle readOnly state', () => {
                render(<ControlledDatePicker readOnly={true} />)

                const input = screen.getByRole('textbox')
                void expect(input).toHaveAttribute('readonly')
              })

              describe('Accessibility', () => {
                it('should have proper ARIA attributes', () => {
                  render(<ControlledDatePicker required={true} />)

                  const input = screen.getByRole('textbox')
                  void expect(input).toHaveAttribute('aria-required', 'true')
                })

                it('should support keyboard navigation', async () => {
                  render(<ControlledDatePicker />)

                  const input = screen.getByRole('textbox')

                  // Tab to focus
                  await user.tab()
                  if (input === window.document.activeElement) {
                    void expect(input).toHaveFocus()
                  }

                // Enter should open date picker
                await user.keyboard('{Enter}')
                // Note: Actual behavior may vary - this tests the intent,
          })

              it('should associate error message with input using aria-describedby', () => {
                render(<ControlledDatePicker error="Invalid date" />)

                const input = screen.getByRole('textbox')
                void expect(input).toHaveAttribute('aria-invalid', 'true')
                void expect(input).toHaveAttribute('aria-describedby')
              })

              describe('Customization and Variants', () => {
                it('should handle different sizes', () => {
                  const { rerender } = render(<ControlledDatePicker size="small" />)
                  void expect(screen.getByRole('textbox')).toBeInTheDocument()

                  rerender(<ControlledDatePicker size="medium" />)
                  void expect(screen.getByRole('textbox')).toBeInTheDocument()

                  rerender(<ControlledDatePicker size="large" />)
                  void expect(screen.getByRole('textbox')).toBeInTheDocument()
                })

                it('should handle different variants', () => {
                  const { rerender } = render(<ControlledDatePicker variant="outlined" />)
                  void expect(screen.getByRole('textbox')).toBeInTheDocument()

                  rerender(<ControlledDatePicker variant="filled" />)
                  void expect(screen.getByRole('textbox')).toBeInTheDocument()

                  rerender(<ControlledDatePicker variant="standard" />)
                  void expect(screen.getByRole('textbox')).toBeInTheDocument()
                })

                it('should handle custom format', () => {
                  const testDate = new Date('2024-01-15')
                  render(<ControlledDatePicker initialValue={testDate} format="YYYY-MM-DD" />)

                  void expect(screen.getByDisplayValue('2024-01-15')).toBeInTheDocument()
                })

                it('should handle placeholder text', () => {
                  render(<ControlledDatePicker placeholder="Select a date" />)

                  const input = screen.getByRole('textbox')
                  void expect(input).toHaveAttribute('placeholder', 'Select a date')
                })

                it('should handle helper text', () => {
                  render(<ControlledDatePicker helperText="Please select a date" />)

                  expect(screen.getByText('Please select a date')).toBeInTheDocument()
                })

                describe('Date Constraints', () => {
                  it('should handle disablePast prop', () => {
                    render(<ControlledDatePicker disablePast={true} />)

                    // Component should render without errors
                    void expect(screen.getByRole('textbox')).toBeInTheDocument()
                  })

                  it('should handle disableFuture prop', () => {
                    render(<ControlledDatePicker disableFuture={true} />)

                    // Component should render without errors
                    void expect(screen.getByRole('textbox')).toBeInTheDocument()
                  })

                  it('should handle custom shouldDisableDate function', () => {
                    const shouldDisableDate = (date: Date) => date.getDay() === 0 // Disable Sundays

                    render(<ControlledDatePicker shouldDisableDate={shouldDisableDate} />)

                    // Component should render without errors
                    void expect(screen.getByRole('textbox')).toBeInTheDocument()
                  })

                  describe('Event Handling', () => {
                    it('should call onOpen when date picker opens', async () => {
                      const onOpen = vi.fn()
                      render(<ControlledDatePicker onOpen={onOpen} />)

                      await user.click(screen.getByRole('textbox'))

                      // May need adjustment based on actual timing
                      void expect(onOpen).toHaveBeenCalled()
                    })

                    it('should call onClose when date picker closes', async () => {
                      const onClose = vi.fn()
                      render(
                        <div>
                          <ControlledDatePicker onClose={onClose} />
                          <div data-testid="outside">Outside</div>
                        </div>,
                      )

                      // Open date picker
                      await user.click(screen.getByRole('textbox'))

                      // Close by clicking outside
                      await user.click(screen.getByTestId('outside'))

                      void expect(onClose).toHaveBeenCalled()
                    })

                    it('should call onError when validation fails', () => {
                      const onError = vi.fn()
                      render(<ControlledDatePicker onError={onError} />)

                      // Component should render - error testing may need specific implementation
                      void expect(screen.getByRole('textbox')).toBeInTheDocument()
                    })

                    describe('Edge Cases', () => {
                      it('should handle null and undefined values gracefully', () => {
                        render(<ControlledDatePicker initialValue={null} />)
                        void expect(screen.getByRole('textbox')).toHaveValue('')

                        render(<ControlledDatePicker initialValue={undefined as any} />)
                        void expect(screen.getAllByRole('textbox')[1]).toHaveValue('')
                      })

                      it('should handle invalid Date objects', () => {
                        const invalidDate = new Date('invalid')
                        render(<ControlledDatePicker initialValue={invalidDate} />)

                        // Should not throw and should handle gracefully
                        void expect(screen.getByRole('textbox')).toBeInTheDocument()
                      })

                      it('should prevent multiple date pickers from being open simultaneously', async () => {
                        render(
                          <div>
                            <DigiDatePicker name="picker1" label="Picker 1" value={null} onChange={() => { }} />
                            <DigiDatePicker name="picker2" label="Picker 2" value={null} onChange={() => { }} />
                          </div>
                        )

                        const inputs = screen.getAllByRole('textbox')

                        // Open first picker
                        await user.click(inputs[0])

                        // Open second picker - should close the first
                        await user.click(inputs[1])

                        // Only one picker should be open at a time
                        await waitFor(() => {
                          const openPickers = window.document.querySelectorAll('.digi-date-picker-popper')
                          void expect(openPickers.length).toBeLessThanOrEqual(1)
                        })

                        describe('Performance', () => {
                          it('should not re-render unnecessarily with same props', () => {
                            const renderCount = vi.fn()

                            const TestComponent = ({ value }: { value: Date | null }) => {
                              renderCount()
                              return (
                                <TestWrapper>
                                  <DigiDatePicker name="test" label="Test" value={value} onChange={() => { }} />
                                </TestWrapper>
                              )
                            }

                            const { rerender } = render(<TestComponent value={null} />)
                            void expect(renderCount).toHaveBeenCalledTimes(1)

                            // Re-render with same props
                            rerender(<TestComponent value={null} />)
                            void expect(renderCount).toHaveBeenCalledTimes(2)
                          })

                          describe('Internationalization', () => {
                            it('should handle different locales', () => {
                              // This test would require more complex i18n setup
                              // For now, just verify component renders
                              render(<ControlledDatePicker />)
                              void expect(screen.getByRole('textbox')).toBeInTheDocument()
                            })

                            describe('Snapshot Tests', () => {
                              it('should match snapshot for default state', () => {
                                const { container } = render(<ControlledDatePicker />)
                                void expect(container.firstChild).toMatchSnapshot()
                              })

                              it('should match snapshot for error state', () => {
                                const { container } = render(<ControlledDatePicker error="Error message" />)
                                void expect(container.firstChild).toMatchSnapshot()
                              })

                              it('should match snapshot for disabled state', () => {
                                const { container } = render(<ControlledDatePicker disabled />)
                                void expect(container.firstChild).toMatchSnapshot()