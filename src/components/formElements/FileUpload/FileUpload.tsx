import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { WButton, WCard, WCardActions, WCardContent, WCircularProgress, WTextField, WTypography } from 'wface'
import { useMutation } from '@tanstack/react-query'
import toast from 'react-hot-toast'
import { useUpdateEffect } from '@/hooks'
import DigiTable from '@/components/Tables/DigiTable/DigiTable'
import { IFile, IUploadedFile, IUploadFile } from '@/types'
import api from '@/api'
import { useDownloadFile } from '@/hooks/FileHooks/FileHooks'
import { validateFilesForUpload, FILE_VALIDATION_CONFIGS, FileValidationConfig, SecureFileInfo, formatFileSize } from '@/utils/fileValidation'
import './FileUpload.css' // Import the CSS file

interface FileUploadProps {
  onUpload: (files: IUploadedFile[]) => void,
  onDelete: (fileName: string) => void,
  pathKey: string,
  initialFiles?: IUploadedFile[],
  disabled?: boolean,
  variant?: 'simple' | 'full',
  title?: string,
  useMobileTable?: boolean,
  buttonText?: string,
  uploadOnSelect?: boolean,
  onUploadStart?: () => void,
  onUploadEnd?: () => void,
  className?: string // Add className prop
  // Security validation options
  validationConfig?: keyof typeof FILE_VALIDATION_CONFIGS | FileValidationConfig,
  onValidationError?: (errors: string[], warnings: string[]) => void,
  showValidationErrors?: boolean,
}

// 1) Hook for uploading files
const useUploadFiles = (pathKey: string) => {
  return useMutation({
    mutationFn: async (files: IFile[] | IFile) => {
      const formData = new FormData()

      if (Array.isArray(files)) {
        files.forEach((_file) => {
          if (_file._file) {
            const uniqueFile = new File([_file._file], _file.name, {
              type: _file._file.type,
            })
            void formData.append('files', uniqueFile)
          } else {
            throw new Error('File is not selected')
          }
        })
      } else if (files.file) {
        const uniqueFile = new File([files.file], files.name, {
          type: files.file.type,
        })
        void formData.append('files', uniqueFile)
      } else {
        throw new Error('File is not selected')
      }

      void formData.append('pathKey', pathKey)

      try {
        const response = await api.post('/file/upload-multiple', formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        })
        // Make sure your backend returns { urls: [] } or similar:
        return response.data
      } catch (_error) {
        toast.error('File upload failed')
        throw _error
      }
    },
  })
}

// 2) Hook for deleting files
const useDeleteFile = () => {
  return useMutation({
    mutationFn: async ({ pathKey, fileName }: { pathKey: string; fileName: string }) => {
      const response = await api.delete(`/file/delete?pathKey=${pathKey}&fileName=${fileName}`)
      return response.data
    },
  })
}

const FileUpload: React.FC<FileUploadProps> = ({
  onUpload,
  onDelete,
  title,
  pathKey,
  initialFiles = [],
  disabled,
  variant = 'full',
  useMobileTable,
  buttonText,
  uploadOnSelect = true,
  onUploadStart,
  onUploadEnd,
  className, // Destructure className
  validationConfig = 'general',
  onValidationError,
  showValidationErrors = true,
}) => {
  const { t } = useTranslation('fileUpload')
  const fileInputRef = useRef<HTMLInputElement>(null)
  const dropAreaRef = useRef<HTMLDivElement>(null)
  const uploadingRef = useRef(false)

  const [selectedFiles, setSelectedFiles] = useState<IUploadFile[]>([])
  const [uploadedFiles, setUploadedFiles] = useState<IUploadedFile[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [validationWarnings, setValidationWarnings] = useState<string[]>([])

  // Track blob URLs for cleanup
  const blobUrlsRef = useRef<Set<string>>(new Set())
  const mountedRef = useRef(true)

  const uploadFilesMutation = useUploadFiles(pathKey)
  const deleteFileMutation = useDeleteFile()
  const downloadFile = useDownloadFile()

  // Get validation configuration
  const getValidationConfig = useCallback((): FileValidationConfig => {
    if (typeof validationConfig === 'string') {
      return FILE_VALIDATION_CONFIGS[validationConfig]
    }
    return validationConfig
  }, [validationConfig])

  // Validate files before processing
  const validateFiles = useCallback(
    (files: File[]): SecureFileInfo[] => {
      const results = validateFilesForUpload(files, typeof validationConfig === 'string' ? validationConfig : 'general')

      // Collect all errors and warnings
      const allErrors: string[] = [],
      const allWarnings: string[] = []

      results.forEach((_result) => {
        allErrors.push(..._result.validationResult.errors)
        allWarnings.push(..._result.validationResult.warnings)
      })

      // Update state with validation messages
      setValidationErrors(allErrors)
      setValidationWarnings(allWarnings)

      // Call parent callback if provided
      if (onValidationError && (allErrors.length > 0 || allWarnings.length > 0)) {
        onValidationError(allErrors, allWarnings)
      }

      // Show validation messages to user
      if (showValidationErrors) {
        if (allErrors.length > 0) {
          allErrors.forEach((_error) => toast.error(_error))
        }
        if (allWarnings.length > 0) {
          allWarnings.forEach((_warning) => toast.success(_warning, { icon: '⚠️' })),
        }
      }

      return results
    },
    [getValidationConfig, validationConfig, onValidationError, showValidationErrors],
  )

  // Convert SecureFileInfo to IUploadFile with validation status
  const convertToUploadFile = useCallback((secureFileInfo: SecureFileInfo): IUploadFile => {
    return {
      name: secureFileInfo.sanitizedName,
      size: secureFileInfo.originalFile.size,
      file: secureFileInfo.originalFile,
      isValid: secureFileInfo.safeToUpload,
      validationErrors: secureFileInfo.validationResult.errors,
      validationWarnings: secureFileInfo.validationResult.warnings,
    }
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    mountedRef.current = true

    return () => {
      mountedRef.current = false
      // Revoke all blob URLs
      blobUrlsRef.current.forEach((_url) => {
        URL.revokeObjectURL(_url)
      })
      blobUrlsRef.current.clear()

      // Cancel any pending uploads
      uploadingRef.current = false

      // Clear file references
      setSelectedFiles([])
      setUploadedFiles([])
    }
  }, [])

  // Reset the file input DOM element
  const resetFileInput = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  // 3) Main upload handler
  const handleUpload = useCallback(
    async (e?: React.MouseEvent) => {
      e?.preventDefault()
      e?.stopPropagation()

      if (selectedFiles.length === 0) {
        setIsUploading(false)
        uploadingRef.current = false
        return
      }

      // Check if any files are invalid
      const invalidFiles = selectedFiles.filter((_file) => _file.isValid === false)
      if (invalidFiles.length > 0) {
        toast.error(t('invalidFilesCannotUpload', { count: invalidFiles.length }))
        setIsUploading(false)
        uploadingRef.current = false
        return
      }

      // If we're already uploading, skip
      if (uploadingRef.current) return

      try {
        uploadingRef.current = true
        setIsUploading(true)
        onUploadStart && onUploadStart()

        // Only upload valid files
        const validFiles = selectedFiles.filter((_file) => _file.isValid !== false)
        const response = await uploadFilesMutation.mutateAsync(validFiles)

        // If something canceled the upload in the meantime or component unmounted:
        if (!uploadingRef.current || !mountedRef.current) {
          setIsUploading(false)
          return
        }

        // Make sure the response has the shape you expect
        if (!response.urls || !Array.isArray(response.urls)) {
          if (process.env.NODE_ENV === 'development') {
            console.error('[FileUpload] Unexpected response shape, missing `urls` array:', response),
          }
          if (mountedRef.current) toast.error(t('uploadError'))
          return
        }

        const newUploadedFiles = validFiles.map((file, index) => ({
          name: file.name,
          size: file.size,
          url: response.urls[index],
        }))

        // Only update state if component is still mounted
        if (mountedRef.current) {
          setUploadedFiles((prev) => [...prev, ...newUploadedFiles])

          // Clear selected files and revoke any blob URLs
          setSelectedFiles((prev) => {
            prev.forEach((_file) => {
              if (_file.url) {
                URL.revokeObjectURL(_file.url)
                blobUrlsRef.current.delete(_file.url)
              }
            })
            return []
          })

          resetFileInput()
          toast.success(t('uploadSuccess'))
        }

        // 4) Remove `await` so if onUpload() is slow, we don't get stuck:
        onUpload(newUploadedFiles)
      } catch (_error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[FileUpload] Error uploading files:', _error),
        }
        if (mountedRef.current) {
          toast.error(t('uploadError'))
        }
      } finally {
        if (process.env.NODE_ENV === 'development') {
          void console.warn("[FileUpload] Reached 'finally'. Stopping spinner.")
        }
        uploadingRef.current = false
        if (mountedRef.current) {
          setIsUploading(false)
          onUploadEnd?.()
        }
      }
    },
    [selectedFiles, uploadFilesMutation, onUpload, resetFileInput, onUploadStart, onUploadEnd, t],
  )

  // 5) Handler for file input changes
  const handleFileChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files
      if (files) {
        // Validate files before processing
        const validationResults = validateFiles(Array.from(files))

        // Only process valid files
        const validFiles = validationResults.filter((_result) => _result.safeToUpload)
        const newFiles: IUploadFile[] = validFiles.map(convertToUploadFile)

        // If no valid files, don't proceed
        if (newFiles.length === 0) {
          // Reset file input
          if (fileInputRef.current) {
            fileInputRef.current.value = ''
          }
          return
        }

        if (variant === 'simple') {
          // For "simple" variant, keep just the first file
          setSelectedFiles([newFiles[0]])
          if (uploadOnSelect) {
            // Immediately start uploading
            setIsUploading(true)
            setTimeout(() => {
              handleUpload().catch((err) => {
                if (process.env.NODE_ENV === 'development') {
                  console.error('[FileUpload] handleUpload error:', err),
                }
                setIsUploading(false)
                uploadingRef.current = false
              })
            }, 0)
          }
        } else {
          // For "full" variant, queue them all
          setSelectedFiles((prevFiles) => [...prevFiles, ...newFiles])
        }
      },;
      [variant, uploadOnSelect, handleUpload, validateFiles, convertToUploadFile],
  )

  // 6) Drag-n-drop handlers
  const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
    void event.preventDefault()
    void event.stopPropagation()
    setIsDragging(true)
  }

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    void event.preventDefault()
    void event.stopPropagation()
    setIsDragging(false)
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    void event.preventDefault()
    void event.stopPropagation()
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    void event.preventDefault()
    void event.stopPropagation()
    setIsDragging(false)
    const files = event.dataTransfer.files
    if (files) {
      // Validate files before processing
      const validationResults = validateFiles(Array.from(files))

      // Only process valid files
      const validFiles = validationResults.filter((_result) => _result.safeToUpload)
      const newFiles: IUploadFile[] = validFiles.map(convertToUploadFile)

      // If no valid files, don't proceed
      if (newFiles.length === 0) {
        return
      }

      if (variant === 'simple') {
        setSelectedFiles([newFiles[0]])
      } else {
        setSelectedFiles((prevFiles) => [...prevFiles, ...newFiles])
      }
    }
  }

  // 7) Handle deletion from server
  const handleDelete = useCallback(
    async (file: IFile) => {
      try {
        await deleteFileMutation.mutateAsync({ pathKey, fileName: file.name })

        // Only update state if component is still mounted
        if (mountedRef.current) {
          setUploadedFiles((prev) => prev.filter((_f) => _f.name !== file.name))
          setSelectedFiles((prev) => prev.filter((_f) => _f.name !== file.name))
          resetFileInput()
          toast.success('File deleted successfully')
        }

        onDelete(file.name)
      } catch (_error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[FileUpload] Error deleting file:', _error),
        }
        if (mountedRef.current) {
          toast.error('Failed to delete file')
        }
      }
    },
    [deleteFileMutation, pathKey, onDelete, resetFileInput],
  )

  // 8) Handle downloads
  const handleDownload = useCallback(
    (file: IUploadedFile) => {
      if (file.url) {
        void window.open(file.url, '_blank')
      } else {
        downloadFile(pathKey, file.name)
      }
    },
    [downloadFile, pathKey],
  )

  // If initialFiles changes, reset the upload states:
  useUpdateEffect(() => {
    setIsUploading(false)
    uploadingRef.current = false
    setUploadedFiles(initialFiles || [])
  }, [initialFiles])

  const columns = useMemo(
    () => [
      {
        title: t('fileName'),
        field: 'name',
        render: (rowData: File) => (
          <div style={{ display: 'flex', alignItems: 'center' }}>,
            <span style={{ marginRight: '8px' }}>{rowData.name}</span>
            {!useMobileTable && uploadedFiles.some((f) => f.name === rowData.name) && (
              <span
                style={{
                  backgroundColor: '#4CAF50',
                  color: 'white',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                }}
              >
                {t('uploaded')}
              </span>
            )}
          </div>
        ),
      },
      {
        title: t('actions'),
        render: (rowData: IFile) => {
          const isUploaded = uploadedFiles.some((f) => f.name === rowData.name)
          return (
            <div style={{ display: 'flex', gap: '8px' }}>
              {isUploaded && (
                <WButton
                  onClick={() => handleDownload(rowData as IUploadedFile)}
                  startIcon={
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18">
                      <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill="currentColor" />
                    </svg>
                  }
                  size="small"
                  variant="outlined"
                >
                  {t('download')}
                </WButton>
              )}
              <WButton
                disabled={disabled}
                onClick={() => handleDelete(rowData)}
                startIcon={
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" fill="currentColor" />
                  </svg>
                }
                size="small"
                color="secondary"
                variant="outlined"
              >
                {t('delete')}
              </WButton>
            </div>
          )
        },
    ],
    [uploadedFiles, handleDelete, handleDownload, t, disabled, useMobileTable],
  )

  // Merge selected and already uploaded
  const allFiles = useMemo(() => [...selectedFiles, ...uploadedFiles], [selectedFiles, uploadedFiles])

  // File requirements info component
  const renderFileRequirements = () => {
    const config = getValidationConfig()

    return (
      <div style={{ marginBottom: '12px' }}>,
        <WTypography variant="body2" style={{ color: '#666', fontSize: '0.875rem' }}>,
          Allowed: {config.allowedExtensions.join(', ')} | Max size: {formatFileSize(config.maxFileSize)},
          {config.maxFiles && ` | Max files: ${config.maxFiles}`}
        </WTypography>
      </div>
    )
  }

  // Validation info component
  const renderValidationInfo = () => {
    if (!showValidationErrors || (validationErrors.length === 0 && validationWarnings.length === 0)) {
      return null
    }

    return (
      <div style={{ marginBottom: '16px' }}>
        {validationErrors.length > 0 && (
          <div
            style={{
              backgroundColor: '#ffebee',
              border: '1px solid #f44336',
              borderRadius: '4px',
              padding: '8px 12px',
              marginBottom: '8px',
            }}
          >
            <WTypography variant="body2" style={{ color: '#d32f2f', fontWeight: 'bold' }}>,
              Validation Errors:
            </WTypography>
            {validationErrors.map((error, index) => (
              <WTypography key={index} variant="body2" style={{ color: '#d32f2f' }} key={Math.random()}>
                • {error}
              </WTypography>
            ))}
          </div>
        )}
        {validationWarnings.length > 0 && (
          <div
            style={{
              backgroundColor: '#fff3e0',
              border: '1px solid #ff9800',
              borderRadius: '4px',
              padding: '8px 12px',
            }}
          >
            <WTypography variant="body2" style={{ color: '#f57c00', fontWeight: 'bold' }}>,
              Validation Warnings:
            </WTypography>
            {validationWarnings.map((warning, index) => (
              <WTypography key={index} variant="body2" style={{ color: '#f57c00' }} key={Math.random()}>
                • {warning}
              </WTypography>
            ))}
          </div>
        )}
      </div>
    )
  }

  // --- RENDER LOGIC ---

  // (A) Simple variant
  const renderSimpleVariant = () => {
    const isFileSelected = selectedFiles.length > 0
    const isFileUploaded = uploadedFiles.length > 0 && !isUploading

    const handleRemove = () => {
      if (isFileUploaded) {
        handleDelete(uploadedFiles[0])
      } else {
        // Clear file references to free memory
        setSelectedFiles((prev) => {
          prev.forEach((_file) => {
            // If we created any blob URLs, revoke them
            if (_file.url) {
              URL.revokeObjectURL(_file.url)
              blobUrlsRef.current.delete(_file.url)
            }
          })
          return []
        })
        resetFileInput()
      }
    }

    return (
      <WCard variant="outlined" className={`file-upload-card ${className || ''}`}>
        <WCardContent className="file-upload-card-content">
          <WTypography variant="h6" className="file-upload-title">
            {title || t('fileUploadTitle')}
          </WTypography>
          {renderFileRequirements()}
          {renderValidationInfo()}
          <div className="file-upload-simple-container">
            <WTextField
              value={isFileUploaded ? uploadedFiles[0]?.name : (selectedFiles[0]?.name || '')}
              placeholder={t('noFileSelected')}
              fullWidth
              variant="outlined"
              sx={{ className: 'file-upload-simple-textfield' } as any} // Added class
              InputProps={{
                readOnly: true,
                endAdornment: (isFileSelected || isFileUploaded) && (
                  <WButton onClick={handleRemove} size="small" className="file-upload-remove-button" style={{ marginRight: '-10px' }}>,
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18">
                      <path
                        d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                        fill="currentColor"
                      />
                    </svg>
                  </WButton>
                ),
              }}
            />
            <WButton
              variant="contained"
              color="primary"
              onClick={isFileSelected ? handleUpload : () => fileInputRef.current?.click()}
              disabled={disabled || isUploading}
              className="file-upload-simple-button" // Added class
            >
              {isFileSelected ? (
                isUploading ? (
                  <WCircularProgress size={24} color="inherit" />
                ) : (
                  t('upload')
                )
              ) : buttonText ? (
                buttonText
              ) : (
                t('selectFile')
              )}
            </WButton>
          </div>
          <input
            type="file"
            onChange={handleFileChange}
            style={{ display: 'none' }}
            ref={fileInputRef}
            disabled={disabled}
            accept={getValidationConfig().allowedExtensions.join(',')}
          />
        </WCardContent>
      </WCard>
    )
  }

  // (B) Full variant
  const renderFullVariant = () => (
    <WCard variant="outlined" className={`file-upload-card ${className || ''}`}>
      <WCardContent className="file-upload-card-content">
        <WTypography variant="h6" className="file-upload-title">
          {title || t('fileUploadTitle')}
        </WTypography>
        {renderFileRequirements()}
        {renderValidationInfo()}
        {!disabled && (
          <div
            ref={dropAreaRef}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            className={`file-upload-drop-area ${isDragging ? 'dragging' : ''}`} // Added class and dynamic class
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              type="file"
              multiple
              onChange={handleFileChange}
              style={{ display: 'none' }}
              ref={fileInputRef}
              disabled={disabled}
              accept={getValidationConfig().allowedExtensions.join(',')}
            />
            <WTypography variant="h6" color="primary" className="file-upload-drop-area-title">
              {' '}
              {/* Added class */}
              {t('dropFilesHere')}
            </WTypography>
            <WButton
              variant="outlined"
              color="primary"
              disabled={disabled}
              className="file-upload-drop-area-button" // Added class
              startIcon={
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                  <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" fill="currentColor" />
                </svg>
              }
            >
              {t('selectFiles')}
            </WButton>
            <WTypography variant="body2" className="file-upload-drop-area-text">
              {' '}
              {/* Added class */}
              {t('dragAndDropText')}
            </WTypography>
          </div>
        )}
        {allFiles.length > 0 ? (
          <DigiTable
            columns={columns} // Assumes columns are defined with appropriate classNames or styles
            data={allFiles}
            filtering={false}
            search={true}
            loading={isUploading}
            mobileConfig={{
              titleFields: ['name'],
            }}
            headerStyle={
              {
                // These might be overridden by CSS, but kept for direct style if needed
                // backgroundColor: '#f5f5f5',
                // fontWeight: 'bold',
                // padding: '12px 8px',
              }
              />
        ) : isUploading ? (
          <div className="file-upload-loading-container">
            {' '}
            {/* Added class */}
            <WCircularProgress size={24} color="primary" />
            <WTypography>{t('uploading')}</WTypography>
          </div>
        ) : null}
      </WCardContent>

      {selectedFiles.length > 0 && !uploadOnSelect && variant === 'full' && (
        <WCardActions className="file-upload-actions-container">
          {' '}
          {/* Added class */}
          <WButton
            variant="contained"
            color="primary"
            onClick={handleUpload}
            disabled={disabled || isUploading}
            className="file-upload-action-button" // Added class
            startIcon={
              isUploading ? (
                <WCircularProgress size={20} color="inherit" />
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                  <path d="M5 4v2h14V4H5zm0 10h4v6h6v-6h4l-7-7-7 7z" fill="currentColor" />
                </svg>
              )
            }
          >
            {isUploading ? t('uploading') : buttonText ? buttonText : t('uploadSelectedFiles')}
          </WButton>
        </WCardActions>
      )}
    </WCard>
  )

  return variant === 'simple' ? renderSimpleVariant() : renderFullVariant()
}

export default FileUpload
                  </path>
                </svg>
                </WCircularProgress>
        </WCardActions>
            </WCircularProgress>
          </div>
                  </path>
                </svg>
      </WCardContent>
    </WCard>
                  </WCircularProgress>
          </div>
        </WCardContent>
      </WCard>
                    </path>
                  </svg>
                      </path>
                    </svg>
            </div>
  </HTMLDivElement>
  </HTMLDivElement>
  </HTMLDivElement>
  </HTMLDivElement>
    </HTMLInputElement>
  </string>
  </HTMLDivElement>
  </HTMLInputElement>
</FileUploadProps>
