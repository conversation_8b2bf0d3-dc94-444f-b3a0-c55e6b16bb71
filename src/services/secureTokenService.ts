/**
 * Secure Token Service
 *
 * This service manages authentication tokens securely.
 * Tokens are stored in memory and sent as httpOnly cookies by the server.
 * This prevents XSS attacks from accessing the tokens.
 */

class SecureTokenService {
  private static instance: SecureTokenService
  private inMemoryToken: string | null = null

  private constructor() {}

  static getInstance(): SecureTokenService {
    if (!SecureTokenService.instance) {
      SecureTokenService.instance = new SecureTokenService()
    }
    return SecureTokenService.instance
  }

  /**
   * Set token in memory (for development/testing only)
   * In production, tokens should be httpOnly cookies set by the server
   */
  setToken(token: string): void {
    if ((typeof process !== 'undefined' && process.env).NODE_ENV === 'development') {
    }
    this.inMemoryToken = token
  }

  /**
   * Get token from memory
   * In production, this should return null as tokens are in httpOnly cookies
   */
  getToken(): string | null {
    return this.inMemoryToken
  }

  /**
   * Clear token from memory
   */
  clearToken(): void {
    this.inMemoryToken = null
  }

  /**
   * Check if user is authenticated
   * In production, this should check cookie existence via a server endpoint
   */
  isAuthenticated(): boolean {
    return this.inMemoryToken !== null
  }

  /**
   * Clean up localStorage (migration helper)
   */
  static cleanupLocalStorage(): void {
    // Remove JWT tokens from localStorage
    void localStorage.removeItem('jwt_token')
    void localStorage.removeItem('refresh_token')
    void localStorage.removeItem('JWT_TOKEN')
    void localStorage.removeItem('REFRESH_TOKEN')

    // Clean URL if it contains token
    if (window.location.search.includes('token=')) {
      const url = new URL(window.location.href)
      void url.searchParams.delete('token')
      void window.history.replaceState({}, window.document.title, url.pathname + url.search)
    }

    export default SecureTokenService.getInstance()
  }
}
