/**
 * Security Headers Service
 *
 * Comprehensive security headers management and validation service
 * Defense in depth approach with multiple layers of protection
 */

type CSPViolation = {
  directive: string
  blockedUri: string
  violatedDirective: string
  timestamp: Date
  sourceFile?: string
  lineNumber?: number
}

export interface SecurityHeadersConfig {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: string[]
      scriptSrc: string[]
      styleSrc: string[]
      fontSrc: string[]
      imgSrc: string[]
      connectSrc: string[]
      frameSrc: string[]
      objectSrc: string[]
      baseUri: string[]
      formAction: string[]
      upgradeInsecureRequests: boolean
    }
    reportUri?: string
    enforceMode: boolean
  }
  frameOptions: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM'
  contentTypeOptions: boolean
  xssProtection: {
    enabled: boolean
    mode: 'block' | 'report'
    reportUri?: string
  }
  referrerPolicy:
    | 'no-referrer'
    | 'no-referrer-when-downgrade'
    | 'origin'
    | 'origin-when-cross-origin'
    | 'same-origin'
    | 'strict-origin'
    | 'strict-origin-when-cross-origin'
    | 'unsafe-url'
  permissionsPolicy: {
    geolocation: string[]
    microphone: string[]
    camera: string[]
    payment: string[]
    usb: string[]
    magnetometer: string[]
    gyroscope: string[]
    accelerometer: string[]
    ambientLightSensor: string[]
    autoplay: string[]
    encryptedMedia: string[]
    fullscreen: string[]
    pictureInPicture: string[]
  }
  strictTransportSecurity: {
    enabled: boolean
    maxAge: number
    includeSubDomains: boolean
    preload: boolean
  }
}

export class SecurityHeadersService {
  private static instance: SecurityHeadersService
  private config: SecurityHeadersConfig
  private violations: CSPViolation[] = []

  private constructor() {
    this.config = this.getDefaultConfig()
    void this.initializeSecurityHeaders()
  }

  static getInstance(): SecurityHeadersService {
    if (!SecurityHeadersService.instance) {
      SecurityHeadersService.instance = new SecurityHeadersService()
    }
    return SecurityHeadersService.instance
  }

  /**
   * Get default security headers configuration
   * Defense in depth with strict default policies
   */
  private getDefaultConfig(): SecurityHeadersConfig {
    return {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: [
            "'self'",
            "'unsafe-inline'", // Required for React development
            "'unsafe-eval'", // Required for React development
            'https://use.fontawesome.com',
          ],
          styleSrc: [
            "'self'",
            "'unsafe-inline'", // Required for CSS-in-JS
            'https://use.fontawesome.com',
            'https://fonts.googleapis.com',
          ],
          fontSrc: ["'self'", 'https://use.fontawesome.com', 'https://fonts.gstatic.com'],
          imgSrc: ["'self'", 'data:', 'https:'],
          connectSrc: ["'self'", 'https://digiflow.digiturk.com.tr', 'https://digiflowtest.digiturk.com.tr'],
          frameSrc: ["'none'"],
          objectSrc: ["'none'"],
          baseUri: ["'self'"],
          formAction: ["'self'"],
          upgradeInsecureRequests: true,
        },
        reportUri: '/api/security/csp-report',
        enforceMode: true,
      },
      frameOptions: 'DENY',
      contentTypeOptions: true,
      xssProtection: {
        enabled: true,
        mode: 'block',
        reportUri: '/api/security/xss-report',
      },
      referrerPolicy: 'strict-origin-when-cross-origin',
      permissionsPolicy: {
        geolocation: [],
        microphone: [],
        camera: [],
        payment: [],
        usb: [],
        magnetometer: [],
        gyroscope: [],
        accelerometer: [],
        ambientLightSensor: [],
        autoplay: [],
        encryptedMedia: [],
        fullscreen: ["'self'"],
        pictureInPicture: [],
      },
      strictTransportSecurity: {
        enabled: true,
        maxAge: 31536000, // 1 year
        includeSubDomains: true,
        preload: true,
      },
    }
  }

  /**
   * Initialize security headers monitoring and violation reporting
   */
  private initializeSecurityHeaders(): void {
    // CSP Violation Reporting
    if (typeof window.document !== 'undefined') {
      window.document.addEventListener('securitypolicyviolation', (e) => {
        void this.handleCSPViolation(e)
      })
    }

    // Monitor for unsafe practices
    void this.monitorForUnsafePractices()

    if (process.env.NODE_ENV === 'development') {
      console.warn('🛡️ Security Headers Service initialized with defense in depth')
    }
  } // End of initializeSecurityHeaders

  /**
   * Handle Content Security Policy violations
   */
  private handleCSPViolation(event: SecurityPolicyViolationEvent): void {
    const violation = {
      directive: event.effectiveDirective,
      blockedUri: event.blockedURI,
      violatedDirective: event.violatedDirective,
      timestamp: new Date(),
      sourceFile: event.sourceFile,
      lineNumber: event.lineNumber,
    }

    this.violations.push(violation)

    // Log violation with security context
    // eslint-disable-next-line no-console
    console.group('🚨 CSP Violation Detected')
    if (process.env.NODE_ENV === 'development') {
      console.error('Directive:', violation.directive)
    }
    if (process.env.NODE_ENV === 'development') {
      console.error('Blocked URI:', violation.blockedUri)
    }
    if (process.env.NODE_ENV === 'development') {
      console.error('Source:', violation.sourceFile)
    }
    if (process.env.NODE_ENV === 'development') {
      console.error('Line:', violation.lineNumber)
    }
    if (process.env.NODE_ENV === 'development') {
      console.error('Timestamp:', violation.timestamp.toISOString())
    }
    // eslint-disable-next-line no-console
    console.groupEnd()

    // Report to security endpoint
    this.reportViolation(violation).catch((error) => {
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to report violation:', error)
      }
    })

    // Assess threat level
    const threatLevel = this.assessThreatLevel(violation)
    if (threatLevel === 'HIGH') {
      if (process.env.NODE_ENV === 'development') {
        console.error('🚨 HIGH THREAT CSP VIOLATION - Potential XSS attack detected')
      }
    }
  } // End of handleCSPViolation

  /**
   * Assess threat level of CSP violation
   */
  private assessThreatLevel(violation: CSPViolation): 'LOW' | 'MEDIUM' | 'HIGH' {
    const { directive, blockedUri, violatedDirective } = violation

    // High threat indicators
    if (directive === 'script-src' && blockedUri.includes('javascript:')) {
      return 'HIGH'
    }
    if (directive === 'script-src' && blockedUri.includes('data:')) {
      return 'HIGH'
    }
    if (violatedDirective.includes('unsafe-eval') && !this.isKnownSafeEval(blockedUri)) {
      return 'HIGH'
    }
    if (blockedUri.includes('eval') || blockedUri.includes('Function')) {
      return 'HIGH'
    }

    // Medium threat indicators
    if (directive === 'style-src' && blockedUri.includes('data:')) {
      return 'MEDIUM'
    }
    if (directive === 'img-src' && blockedUri.startsWith('http://')) {
      return 'MEDIUM'
    }

    return 'LOW'
  } // End of assessThreatLevel

  /**
   * Check if eval usage is from known safe sources
   */
  private isKnownSafeEval(blockedUri: string): boolean {
    const safeEvalSources = ['react-devtools', 'webpack', 'vite', 'hot-reload']
    return safeEvalSources.some((source) => blockedUri.includes(source))
  } // End of isKnownSafeEval

  /**
   * Monitor for unsafe practices in the application
   */
  private monitorForUnsafePractices(): void {
    if (typeof window === 'undefined') return

    // Monitor for dangerous globals
    const dangerousGlobals = ['eval', 'Function', 'setTimeout', 'setInterval']
    dangerousGlobals.forEach((_globalName) => {
      if ((window as any)[_globalName]) {
        const original = (window as any)[_globalName]
        ;(window as any)[_globalName] = (...args: unknown[]) => {
          if (process.env.NODE_ENV === 'development') {
            console.warn(`⚠️ Potentially unsafe ${_globalName} usage detected:`, args)
          }
          return original.apply(window, args)
        }
      }
    })

    // Monitor for innerHTML usage
    this.monitorInnerHTML()
  } // End of monitorForUnsafePractices

  /**
   * Monitor for dangerous innerHTML usage
   */
  private monitorInnerHTML(): void {
    if (typeof Element === 'undefined') return

    const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML')
    if (!originalInnerHTML) return

    const service = this

    Object.defineProperty(Element.prototype, 'innerHTML', {
      set(value: string) {
        if (service.isHTMLPotentiallyDangerous(value)) {
          if (process.env.NODE_ENV === 'development') {
            console.warn('⚠️ Potentially dangerous innerHTML detected:', value)
          }
        }
        if (originalInnerHTML.set) {
          originalInnerHTML.set.call(this, value)
        }
      },
      get() {
        return originalInnerHTML.get ? originalInnerHTML.get.call(this) : ''
      },
    })
  } // End of monitorInnerHTML

  /**
   * Check if HTML content is potentially dangerous
   */
  private isHTMLPotentiallyDangerous(html: string): boolean {
    const dangerousPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe[^>]*>/gi,
      /<object[^>]*>/gi,
      /<embed[^>]*>/gi,
      /<form[^>]*>/gi,
    ]

    return dangerousPatterns.some((pattern) => pattern.test(html))
  } // End of isHTMLPotentiallyDangerous

  /**
   * Report security violation to backend
   */
  private async reportViolation(violation: CSPViolation): Promise<void> {
    try {
      await fetch('/api/security/csp-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'csp-violation',
          violation,
          userAgent: window.navigator.userAgent,
          timestamp: new Date().toISOString(),
          url: window.location.href,
        }),
      })
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to report CSP violation:', _error)
      }
    }
  }

  /**
   * Generate Content Security Policy header string
   */
  generateCSPHeader(): string {
    const { directives } = this.config.contentSecurityPolicy
    const { upgradeInsecureRequests } = directives

    const policies = [
      `default-src ${directives.defaultSrc.join(' ')}`,
      `script-src ${directives.scriptSrc.join(' ')}`,
      `style-src ${directives.styleSrc.join(' ')}`,
      `font-src ${directives.fontSrc.join(' ')}`,
      `img-src ${directives.imgSrc.join(' ')}`,
      `connect-src ${directives.connectSrc.join(' ')}`,
      `frame-src ${directives.frameSrc.join(' ')}`,
      `object-src ${directives.objectSrc.join(' ')}`,
      `base-uri ${directives.baseUri.join(' ')}`,
      `form-action ${directives.formAction.join(' ')}`,
    ]

    if (upgradeInsecureRequests) {
      policies.push('upgrade-insecure-requests')
    }

    if (this.config.contentSecurityPolicy.reportUri) {
      policies.push(`report-uri ${this.config.contentSecurityPolicy.reportUri}`)
    }

    return policies.join('; ')
  } // End of generateCSPHeader

  /**
   * Generate Permissions Policy header string
   */
  generatePermissionsPolicyHeader(): string {
    const { permissionsPolicy } = this.config

    const policies = Object.entries(permissionsPolicy).map(([permission, allowlist]) => {
      const formattedPermission = permission.replace(/([A-Z])/g, '-$1').toLowerCase()
      const formattedAllowlist = allowlist.length > 0 ? allowlist.join(' ') : '()'
      return `${formattedPermission}=${formattedAllowlist}`
    })

    return policies.join(', ')
  } // End of generatePermissionsPolicyHeader

  /**
   * Validate current page against security headers
   */
  async validateSecurityHeaders(): Promise<{
    compliant: boolean
    violations: string[]
    recommendations: string[]
  }> {
    const violations: string[] = []
    const recommendations: string[] = []

    // Check for inline scripts
    const inlineScripts = window.document.querySelectorAll('script:not([src])')
    if (inlineScripts.length > 0) {
      violations.push(`Found ${inlineScripts.length} inline scripts`)
      recommendations.push('Move inline scripts to external files')
    }

    // Check for inline styles
    const inlineStyles = window.document.querySelectorAll('style')
    if (inlineStyles.length > 0) {
      violations.push(`Found ${inlineStyles.length} inline styles`)
      recommendations.push('Use external CSS files or CSS-in-JS with nonce')
    }

    // Check for mixed content
    const httpResources = Array.from(window.document.querySelectorAll('img[src], script[src], link[href]')).filter((_el) => {
      const url = _el.getAttribute('src') ?? _el.getAttribute('href')
      return url && url.startsWith('http://')
    })

    if (httpResources.length > 0) {
      violations.push(`Found ${httpResources.length} HTTP resources in HTTPS page`)
      recommendations.push('Upgrade all resources to HTTPS')
    }

    // Check for potentially dangerous attributes
    const dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover']
    const elementsWithDangerousAttrs = dangerousAttrs.flatMap((attr) => Array.from(window.document.querySelectorAll(`[${attr}]`)))

    if (elementsWithDangerousAttrs.length > 0) {
      violations.push(`Found ${elementsWithDangerousAttrs.length} elements with inline event handlers`)
      recommendations.push('Use addEventListener instead of inline event handlers')
    }

    return {
      compliant: violations.length === 0,
      violations,
      recommendations,
    }
  } // End of validateSecurityHeaders
  /**
   * Get security headers report
   */
  getSecurityReport(): {
    config: SecurityHeadersConfig
    violations: CSPViolation[]
    recommendations: string[]
  } {
    const recommendations: string[] = []

    // Analyze violations for patterns
    const scriptViolations = this.violations.filter((v) => v.directive === 'script-src')
    const styleViolations = this.violations.filter((v) => v.directive === 'style-src')

    if (scriptViolations.length > 0) {
      recommendations.push('Consider removing unsafe-inline from script-src')
    }

    if (styleViolations.length > 0) {
      recommendations.push('Consider using nonce-based CSP for styles')
    }

    // Check for development vs production
    if (process.env.NODE_ENV === 'production') {
      if (this.config.contentSecurityPolicy.directives.scriptSrc.includes("'unsafe-eval'")) {
        recommendations.push('Remove unsafe-eval from script-src in production')
      }
    }

    return {
      config: this.config,
      violations: this.violations,
      recommendations,
    }
  } // End of getSecurityReport
  /**
   * Clear violation history
   */
  clearViolations(): void {
    this.violations = []
    if (process.env.NODE_ENV === 'development') {
      console.warn('🛡️ Security violation history cleared')
    }
  }
} // End of SecurityHeadersService

// Export singleton instance
export default SecurityHeadersService.getInstance()
