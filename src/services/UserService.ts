import api from '@/api.tsx'
import { IActiveUser, IOption, IUser } from '@/types'

export const getUserId = async (): Promise<number> => {
  const response = await api.get('users/id')
  return response.data
}

export const getUser = async (userId: number, instanceId: number = 0, wfDefId: number = 0): Promise<IUser> => {
  if (userId) {
    const response = await api.get(`users/by/${userId}?instanceId=${instanceId}&wfDefId=${wfDefId}`)
    return response.data
  } else return {} as IUser
}

export const getActiveUser = async (): Promise<IActiveUser> => {
  const response = await api.get('users/by')
  return { data: response.data, isSysAdmin: response?.headers['x-is-sysadmin'] === 'True' }
}

export const listUsers = async (): Promise<IUser[]> => {
  const response = await api.get('/users')
  return response.data
}

export const listAllUsers = async (): Promise<IOption[]> => {
  try {
    const response = await api.get('/users/select-options')
    return response.data
  } catch (error: Error | unknown) {
    throw new Error(error.response?.data?.message ?? 'Failed to fetch users.')
  }
}
