import { useNavigationHistory } from '@/hooks/useNavigationHistory'
import { useNavigate } from 'react-router-dom'
import { useAppContext, useUserContext } from 'wface'
import { safeNavigate } from '@/utils/helpers/navigation/navigationHelpers'

const AppHelper = () => {
  const navigate = useNavigate()
  const { data: user } = useUserContext()
  const { setValue, cache, openScreenById, setQueryParams } = useAppContext()
  const { addToHistory } = useNavigationHistory(openScreenById)
  const loading = (value: boolean) => void setValue('loading', value)
  const showMessage = (value: string) => void setValue('message', value)
  const openScreen = (screen: string, parameters?: any) => {
    const urlPath = '/main/' + screen
    let searchParams = ''

    // Handle URLSearchParams or plain object
    if (parameters instanceof URLSearchParams) {
      searchParams = parameters.toString()
    } else if (parameters && typeof parameters === 'object') {
      searchParams = new URLSearchParams(parameters).toString()
    } else if (parameters) {
      searchParams = parameters.toString()
    }

    // Add ? prefix if there are params
    const searchString = searchParams ? `?${searchParams}` : ''

    // Set query params in app context before navigation
    if (searchParams) {
      const paramsObject: Record<string, string> = {}
      const urlSearchParams = new URLSearchParams(searchParams)
      urlSearchParams.forEach((value, key) => {
        paramsObject[key] = value
      })
      setQueryParams(paramsObject)
    }

    // Navigate directly with query params instead of using openScreenById
    // since wface's openScreen doesn't support query params
    safeNavigate(navigate, urlPath + searchString)

    addToHistory(urlPath, searchString)
  }

  const openScreenUrl = (screen: string, urlParams?: Record<string, string>) => {
    openScreenById(screen, urlParams)
  }
  const getInitialScreenValue = (screen: string): unknown => {
    const value = localStorage.getItem(screen)
    return value ? JSON.parse(value) : null
  }
  const setInitialScreenValue = (screen: string, value: unknown): void => {
    localStorage.setItem(screen, JSON.stringify(value))
  }
  const clearInitialScreenValue = (screen: string): void => {
    localStorage.removeItem(screen)
  }
  return {
    user,
    cache,
    setValue,
    loading,
    showMessage,
    setQueryParams,
    openScreenById,
    openScreen,
    openScreenUrl,
    getInitialScreenValue,
    setInitialScreenValue,
    clearInitialScreenValue,
  }
}
const useAppHelper = AppHelper
export default useAppHelper
