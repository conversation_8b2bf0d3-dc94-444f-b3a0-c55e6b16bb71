import { getUser, getUserId } from '..'
import useAppHelper from './appHelper'

function setUserId(user: any) {
  // eslint-disable-next-line no-unused-vars
  const { setValue } = useAppHelper() as { setValue: (key: string, value: string | number | boolean | object) => void }
  setValue('UserId', user.id)
}

export default function AppHooks() {
  const onAppDidMount = () => {
    void getUserId().then((userId) => {
      void getUser(userId).then((result) => {
        setUserId(result)
      })
    })
  }
  return { onAppDidMount }
} // End of AppHooks
