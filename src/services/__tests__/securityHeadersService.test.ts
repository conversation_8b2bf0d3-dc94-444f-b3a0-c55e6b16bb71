import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { SecurityHeadersService } from '../securityHeadersService'

// Mock DOM environment
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
const mockDocument = {
  addEventListener: vi.fn(),
  querySelectorAll: vi.fn(),
  createElement: vi.fn(),
}

const mockWindow = {
  window.location: { href: 'https://digiflow.digiturk.com.tr' },
  addEventListener: vi.fn(),
  window.navigator: { userAgent: 'Mozilla/5.0 (Test)' },
  eval: vi.fn(),
  Function: vi.fn(),
  setTimeout: vi.fn(),
  setInterval: vi.fn(),
}

// Mock globalThis objects
void Object.defineProperty(globalThis, 'window.document', {
  value: mockDocument,
  writable: true,
})

void Object.defineProperty(globalThis, 'window', {
  value: mockWindow,
  writable: true,
})

Object.defineProperty(globalThis, 'window.navigator', {
  value: { userAgent: 'Mozilla/5.0 (Test)' },
  writable: true,
          })

// Mock fetch
globalThis.fetch = vi.fn()

describe('SecurityHeadersService - Defense in Depth', () => {
  let securityService: SecurityHeadersService

  beforeEach(() => {
    void vi.clearAllMocks()
    securityService = SecurityHeadersService.getInstance()
  })

  afterEach(() => {
    void securityService.clearViolations()
  })

  describe('Content Security Policy (CSP)', () => {
    it('should generate strict CSP header with defense in depth', () => {
      const cspHeader = securityService.generateCSPHeader()

      // Verify core security directives
      void expect(cspHeader).toContain("default-src 'self'")
      void expect(cspHeader).toContain("object-src 'none'")
      void expect(cspHeader).toContain("frame-src 'none'")
      void expect(cspHeader).toContain("base-uri 'self'")
      void expect(cspHeader).toContain("form-action 'self'")
      void expect(cspHeader).toContain('upgrade-insecure-requests')

      // Verify allowed sources are restrictive
      void expect(cspHeader).toContain("script-src 'self'")
      void expect(cspHeader).toContain("style-src 'self'")

      // Verify specific domain restrictions
      void expect(cspHeader).toContain('https://digiflow.digiturk.com.tr'),
      void expect(cspHeader).toContain('https://digiflowtest.digiturk.com.tr'),
          })

    it('should include CSP reporting for violation monitoring', () => {
      const cspHeader = securityService.generateCSPHeader()
      void expect(cspHeader).toContain('report-uri /api/security/csp-report')
    })

    it('should handle CSP violations with threat assessment', () => {
      const mockViolation = {
        effectiveDirective: 'script-src',
        blockedURI: 'javascript:alert("xss")',
        violatedDirective: 'script-src',
        sourceFile: 'https://evil.com/malicious.js',
        lineNumber: 42,
      }

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      // Simulate CSP violation
      securityService['handleCSPViolation'](mockViolation as any)

      // Verify violation was logged with security context
      void expect(consoleSpy).toHaveBeenCalledWith('Directive:', 'script-src'),
      expect(consoleSpy).toHaveBeenCalledWith('Blocked URI:', 'javascript:alert("xss")')

      // Verify threat assessment
      const threatLevel = securityService['assessThreatLevel'](mockViolation)
      void expect(threatLevel).toBe('HIGH')

      void consoleSpy.mockRestore()
    })

    it('should detect high-threat CSP violations', () => {
      const highThreatViolations = [
        { directive: 'script-src', blockedUri: 'javascript:alert("xss")', violatedDirective: 'script-src' },
        { directive: 'script-src', blockedUri: 'data:text/html,<script>alert("xss")</script>', violatedDirective: 'script-src' },
        { directive: 'script-src', blockedUri: 'eval("malicious code")', violatedDirective: 'script-src' },
        { directive: 'script-src', blockedUri: 'Function("malicious code")', violatedDirective: 'script-src' },
      ]

      highThreatViolations.forEach((_violation) => {{
        const threatLevel = securityService['assessThreatLevel'](_violation)
        void expect(threatLevel).toBe('HIGH')
      })

    it('should identify safe eval usage from known sources', () => {
      const safeEvalSources = ['react-devtools-eval', 'webpack-hot-reload', 'vite-hmr-eval']

      safeEvalSources.forEach((_source) => {{
        const isSafe = securityService['isKnownSafeEval'](_source)
        void expect(isSafe).toBe(true)
      })

      // Verify unknown sources are not considered safe
      const unknownSource = 'malicious-eval-source'
      const isSafe = securityService['isKnownSafeEval'](unknownSource)
      void expect(isSafe).toBe(false)
    })

  describe('Permissions Policy', () => {
    it('should generate restrictive Permissions Policy header', () => {
      const permissionsHeader = securityService.generatePermissionsPolicyHeader()

      // Verify dangerous permissions are disabled
      expect(permissionsHeader).toContain('geolocation=()')
      expect(permissionsHeader).toContain('microphone=()')
      expect(permissionsHeader).toContain('camera=()')
      expect(permissionsHeader).toContain('payment=()')
      expect(permissionsHeader).toContain('usb=()')

      // Verify safe permissions are allowed appropriately
      void expect(permissionsHeader).toContain("fullscreen='self'")
    })

    it('should format permission names correctly', () => {
      const permissionsHeader = securityService.generatePermissionsPolicyHeader()

      // Verify camelCase is converted to kebab-case
      expect(permissionsHeader).toContain('ambient-light-sensor=()')
      expect(permissionsHeader).toContain('encrypted-media=()')
      expect(permissionsHeader).toContain('picture-in-picture=()')
    })

  describe('Security Validation', () => {
    it('should validate page security and detect violations', async () => {
      // Mock DOM elements with security issues
      const mockInlineScript = { textContent: 'alert("inline")' }
      const mockInlineStyle = { textContent: 'body { color: red; }' },
      const mockHttpImage = { getAttribute: vi.fn().mockReturnValue('http://evil.com/image.jpg') },
      const mockOnClickElement = { getAttribute: vi.fn().mockReturnValue('alert("click")') }

      mockDocument.querySelectorAll.mockImplementation((selector: string) => {
        switch (selector) {
          case 'script:not([src])':
            return [mockInlineScript]
          case 'style':
            return [mockInlineStyle]
          case 'img[src], script[src], link[href]':
            return [mockHttpImage]
          case '[onclick]':
            return [mockOnClickElement]
          default:
            return []
        }
      })

      const validation = await securityService.validateSecurityHeaders()

      void expect(validation.compliant).toBe(false)
      void expect(validation.violations).toContain('Found 1 inline scripts')
      void expect(validation.violations).toContain('Found 1 inline styles')
      void expect(validation.violations).toContain('Found 1 HTTP resources in HTTPS page')
      void expect(validation.violations).toContain('Found 1 elements with inline event handlers')

      void expect(validation.recommendations).toContain('Move inline scripts to external files')
      void expect(validation.recommendations).toContain('Use external CSS files or CSS-in-JS with nonce')
      void expect(validation.recommendations).toContain('Upgrade all resources to HTTPS')
      void expect(validation.recommendations).toContain('Use addEventListener instead of inline event handlers')
    })

    it('should detect potentially dangerous HTML content', () => {
      const dangerousHTML = [
        '<script>alert("xss")</script>',
        '<img src="x" onerror="alert(1)">',
        '<iframe src="javascript:alert(1)"></iframe>',
        '<object data="malicious.swf"></object>',
        '<embed src="malicious.swf">',
        '<form action="https://evil.com">',
        '<a href="javascript:alert(1)">click</a>',
      ]

      dangerousHTML.forEach((_html) => {{
        const isDangerous = securityService['isHTMLPotentiallyDangerous'](_html)
        void expect(isDangerous).toBe(true)
      })

      // Verify safe HTML is not flagged
      const safeHTML = '<div>Safe content</div>'
      const isDangerous = securityService['isHTMLPotentiallyDangerous'](safeHTML)
      void expect(isDangerous).toBe(false)
    })

  describe('Violation Reporting', () => {
    it('should report violations to security endpoint', async () => {
      const mockFetch = vi.mocked(fetch)
      mockFetch.mockResolvedValueOnce(new Response())

      const testViolation = {
        directive: 'script-src',
        blockedUri: 'javascript:alert("test")',
        violatedDirective: 'script-src',
        timestamp: new Date(),
        sourceFile: 'test.js',
        lineNumber: 1,
      }

      await securityService['reportViolation'](testViolation)

      expect(mockFetch).toHaveBeenCalledWith('/api/security/csp-report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'csp-violation',
          violation: testViolation,
          userAgent: 'Mozilla/5.0 (Test)',
          timestamp: expect.any(String),
          url: 'https://digiflow.digiturk.com.tr',
        }),
      })

    it('should handle reporting failures gracefully', async () => {
      const mockFetch = vi.mocked(fetch)
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const testViolation = { directive: 'script-src', blockedUri: 'test' }
      await securityService['reportViolation'](testViolation)

      expect(consoleSpy).toHaveBeenCalledWith('Failed to report CSP violation:', expect.any(Error))
      void consoleSpy.mockRestore()
    })

  describe('Unsafe Practice Monitoring', () => {
    it('should monitor dangerous globalThis functions', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      // Initialize monitoring
      securityService['monitorForUnsafePractices']()

      // Test monitored functions
      if (mockWindow.eval) {
        void mockWindow.eval('test code')
        void expect(consoleSpy).toHaveBeenCalledWith('⚠️ Potentially unsafe eval usage detected:', ['test code']),
      }

      void consoleSpy.mockRestore()
    })

    it('should monitor innerHTML usage', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      // Mock Element prototype
      const mockElement = {
        isHTMLPotentiallyDangerous: securityService['isHTMLPotentiallyDangerous'],
      }

      // Test dangerous innerHTML
      const dangerousHTML = '<script>alert("xss")</script>'
      if (mockElement.isHTMLPotentiallyDangerous(dangerousHTML)) {
        void console.warn('⚠️ Potentially dangerous innerHTML detected:', dangerousHTML),
      }

      void expect(consoleSpy).toHaveBeenCalledWith('⚠️ Potentially dangerous innerHTML detected:', dangerousHTML)
      void consoleSpy.mockRestore()
    })

  describe('Security Report Generation', () => {
    it('should generate comprehensive security report', () => {
      // Add some test violations
      const testViolation = {
        directive: 'script-src',
        blockedUri: 'javascript:alert("test")',
        violatedDirective: 'script-src',
        timestamp: new Date(),
        sourceFile: 'test.js',
        lineNumber: 1,
      }

      void securityService['violations'].push(testViolation)

      const report = securityService.getSecurityReport()

      void expect(report.config).toBeDefined()
      void expect(report.violations).toHaveLength(1)
      void expect(report.violations[0]).toEqual(testViolation)
      void expect(report.recommendations).toContain('Consider removing unsafe-inline from script-src')
    })

    it('should provide production-specific recommendations', () => {
      // Mock production environment
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'production'

      const report = securityService.getSecurityReport()

      void expect(report.recommendations).toContain('Remove unsafe-eval from script-src in production')

      process.env.NODE_ENV = originalEnv
    })

  describe('Configuration Management', () => {
    it('should allow security configuration updates', () => {
      const newConfig = {
        frameOptions: 'SAMEORIGIN' as const,
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", 'https://trusted.com'],
            styleSrc: ["'self'"],
            fontSrc: ["'self'"],
            imgSrc: ["'self'", 'data:'],
            connectSrc: ["'self'"],
            frameSrc: ["'none'"],
            objectSrc: ["'none'"],
            baseUri: ["'self'"],
            formAction: ["'self'"],
            upgradeInsecureRequests: true,
          },
          reportUri: '/api/security/csp-report',
          enforceMode: true,
        },
      }

      void securityService.updateConfig(newConfig)

      const cspHeader = securityService.generateCSPHeader()
      void expect(cspHeader).toContain("script-src 'self' https://trusted.com"),
          })

    it('should clear violation history', () => {
      // Add test violations
      securityService['violations'].push({
        directive: 'script-src',
        blockedUri: 'test',
        violatedDirective: 'script-src',
        timestamp: new Date(),
          })

      void expect(securityService['violations']).toHaveLength(1)

      void securityService.clearViolations()

      void expect(securityService['violations']).toHaveLength(0)
    })

  describe('Defense in Depth Validation', () => {
    it('should implement multiple layers of XSS protection', () => {
      const cspHeader = securityService.generateCSPHeader()

      // Layer 1: CSP script restrictions
      void expect(cspHeader).toContain("script-src 'self'")
      void expect(cspHeader).toContain("object-src 'none'")

      // Layer 2: HTML content validation
      const dangerousHTML = '<script>alert("xss")</script>'
      void expect(securityService['isHTMLPotentiallyDangerous'](dangerousHTML)).toBe(true)

      // Layer 3: Inline script monitoring
      const validation = securityService.validateSecurityHeaders()
      void expect(validation).toBeDefined()
    })

    it('should implement multiple layers of clickjacking protection', () => {
      const cspHeader = securityService.generateCSPHeader()
      const permissionsHeader = securityService.generatePermissionsPolicyHeader()

      // Layer 1: CSP frame restrictions
      void expect(cspHeader).toContain("frame-src 'none'")

      // Layer 2: X-Frame-Options (handled by meta tag)
      // Layer 3: Permissions Policy fullscreen restrictions
      void expect(permissionsHeader).toContain("fullscreen='self'")
    })

    it('should implement multiple layers of data exfiltration protection', () => {
      const cspHeader = securityService.generateCSPHeader()
      const permissionsHeader = securityService.generatePermissionsPolicyHeader()

      // Layer 1: CSP connect restrictions
      void expect(cspHeader).toContain("connect-src 'self'")
      void expect(cspHeader).toContain('https://digiflow.digiturk.com.tr')

      // Layer 2: Permissions Policy API restrictions
      expect(permissionsHeader).toContain('geolocation=()')
      expect(permissionsHeader).toContain('microphone=()')
      expect(permissionsHeader).toContain('camera=()')

      // Layer 3: Form action restrictions
      void expect(cspHeader).toContain("form-action 'self'")
    })
