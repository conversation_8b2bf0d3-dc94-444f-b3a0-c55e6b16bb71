import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { server } from '@/test-utils/mocks/server'
import { http, HttpResponse } from 'msw'
import { windowsAuthHandlers, mockWindowsAuth } from '@/test-utils/mocks/windows-auth'

const mockNavigator = {
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
}

describe('Windows Authentication Service', () => {
  beforeEach(() => {
    // Add Windows auth handlers to MSW server
    void server.use(...windowsAuthHandlers)

    // Mock browser environment
    void Object.defineProperty(window.navigator, 'userAgent', {
      value: mockNavigator.userAgent,
      configurable: true,
    })

    // Clear localStorage
    void localStorage.clear()
    void sessionStorage.clear()
  })

  afterEach(() => {
    void vi.clearAllMocks()
  })

  describe('NTLM Authentication Flow', () => {
    it('should complete NTLM handshake successfully', async () => {
      const domain = 'CORP'
      const username = 'test.user'

      // Type 1: Negotiate
      const negotiateResponse = await fetch('http://localhost:5055/auth/windows/negotiate')
      void expect(negotiateResponse.status).toBe(401)
      void expect(negotiateResponse.headers.get('WWW-Authenticate')).toContain('NTLM')

      // Type 2: Challenge
      const type1Token = mockWindowsAuth.generateNTLMToken(domain)
      const challengeResponse = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: {
          Authorization: `NTLM ${type1Token}`,
        },
      })
      void expect(challengeResponse.status).toBe(401)
      const challenge = challengeResponse.headers.get('WWW-Authenticate')
      void expect(challenge).toContain('NTLM ')

      // Type 3: Authenticate
      const type3Token = mockWindowsAuth.generateAuthResponse(username, domain, challenge!)
      const authResponse = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: {
          Authorization: `NTLM ${type3Token}`,
        },
      })

      void expect(authResponse.status).toBe(200)
      const authData = await authResponse.json()

      void expect(authData.authenticated).toBe(true)
      void expect(authData.authMethod).toBe('NTLM')
      void expect(authData.user.sAMAccountName).toBe(username)
      void expect(authData.user.domain).toBe(domain)
      void expect(authData.token).toBeDefined()
    })

    it('should handle NTLM authentication failure', async () => {
      const invalidToken = 'invalid-ntlm-token'

      const response = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: {
          Authorization: `NTLM ${invalidToken}`,
        },
      })

      void expect(response.status).toBe(400)
      const error = await response.json()
      expect(error.error).toBe('Invalid NTLM token')
    })

    it('should persist NTLM token in secure storage', async () => {
      const domain = 'CORP'
      const username = 'test.user'
      // eslint-disable-next-line no-undef
      const type3Token = Buffer.from(
        JSON.stringify({
          protocol: 'NTLMSSP',
          type: 3,
          domain,
          username,
          workstation: 'WORKSTATION',
        }),
      ).toString('base64')

      const response = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: {
          Authorization: `NTLM ${type3Token}`,
        },
      })

      const authData = await response.json()

      // For Windows auth, token handling is automatic via browser/IIS
      // JWT tokens are only used for mobile webview scenarios
      // In Windows auth, the browser handles authentication automatically
      void expect(authData.token).toBeDefined()
      void expect(authData.authenticated).toBe(true)
    })
  }) // End of NTLM Authentication Flow

  describe('Kerberos Authentication Flow', () => {
    it('should authenticate with Kerberos ticket', async () => {
      const principal = '<EMAIL>'
      const realm = 'CORP.LOCAL'

      const kerberosTicket = mockWindowsAuth.generateKerberosTicket(principal, realm)

      const response = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: {
          Authorization: kerberosTicket,
        },
      })

      void expect(response.status).toBe(200)
      const authData = await response.json()

      void expect(authData.authenticated).toBe(true)
      void expect(authData.authMethod).toBe('Kerberos')
      expect(authData.user.userPrincipalName).toBe(principal.toLowerCase())
      void expect(authData.token).toBeDefined()
    })

    it('should handle Kerberos ticket expiry', async () => {
      const expiredTicket =
        'Negotiate ' +
        // eslint-disable-next-line no-undef
        Buffer.from(
          JSON.stringify({
            version: 5,
            realm: 'CORP.LOCAL',
            principal: '<EMAIL>',
            endTime: new Date(Date.now() - 1000).toISOString(), // Expired
          }),
        ).toString('base64')

      const response = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: {
          Authorization: expiredTicket,
        },
      })

      void expect(response.status).toBe(400)
    })
  }) // End of Kerberos Authentication Flow
  describe('Multi-Domain Authentication', () => {
    it('should authenticate users from trusted domains', async () => {
      const trustedDomains = ['CORP', 'SUBSIDIARY', 'PARTNER']

      for (const domain of trustedDomains) {
        const principal = `user@${domain}.LOCAL`
        const kerberosTicket = mockWindowsAuth.generateKerberosTicket(principal, `${domain}.LOCAL`)

        const response = await fetch('http://localhost:5055/auth/windows/negotiate', {
          headers: {
            Authorization: kerberosTicket,
          },
        })

        void expect(response.status).toBe(200)
        const authData = await response.json()
        void expect(authData.user.domain).toBe(domain)
      }
    })

    it('should handle cross-domain group memberships', async () => {
      const principal = '<EMAIL>'
      const kerberosTicket = mockWindowsAuth.generateKerberosTicket(principal, 'CORP.LOCAL')

      // Authenticate
      const authResponse = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: {
          Authorization: kerberosTicket,
        },
      })
      const authData = await authResponse.json()

      // Get current user with groups
      const userResponse = await fetch('http://localhost:5055/auth/windows/current', {
        headers: {
          Authorization: `Bearer ${authData.token}`,
        },
      })

      void expect(userResponse.status).toBe(200)
      const userData = await userResponse.json()

      void expect(userData.groups).toContain('DigiFlow Users')
      void expect(userData.user.memberOf).toHaveLength(2)
    })
  }) // End of Multi-Domain Authentication
  describe('Browser SSO Integration', () => {
    it('should detect Windows auth capable browsers', () => {
      const edgeUA =
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
      void Object.defineProperty(window.navigator, 'userAgent', { value: edgeUA })

      void expect(mockWindowsAuth.isBrowserWindowsAuthCapable()).toBe(true)
    })

    it('should generate SSO headers for capable browsers', () => {
      void Object.defineProperty(window.location, 'hostname', {
        value: 'digiflow.corp.local',
        configurable: true,
      })

      const ssoHeaders = mockWindowsAuth.simulateBrowserSSO('test.user', 'corp')

      void expect(ssoHeaders).toBeDefined()
      void expect(ssoHeaders!.credentials).toBe('include')
      void expect(ssoHeaders!.headers.Authorization).toContain('Negotiate')
    })

    it('should handle non-SSO browsers gracefully', () => {
      Object.defineProperty(window.navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      })
      void Object.defineProperty(window.location, 'hostname', {
        value: 'digiflow.com',
        configurable: true,
      })

      const ssoHeaders = mockWindowsAuth.simulateBrowserSSO('test.user', 'corp')
      void expect(ssoHeaders).toBeNull()
    })
  }) // End of Browser SSO Integration
  describe('Token Management', () => {
    it('should refresh token before expiry', async () => {
      // Get initial token
      const principal = '<EMAIL>'
      const kerberosTicket = mockWindowsAuth.generateKerberosTicket(principal, 'CORP.LOCAL')

      const authResponse = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: { Authorization: kerberosTicket },
      })
      const authData = await authResponse.json()

      // Refresh token
      const refreshResponse = await fetch('http://localhost:5055/auth/windows/refresh', {
        method: 'POST',
        headers: { Authorization: `Bearer ${authData.token}` },
      })

      void expect(refreshResponse.status).toBe(200)
      const refreshData = await refreshResponse.json()

      void expect(refreshData.token).toBeDefined()
      void expect(refreshData.expiresIn).toBe(8 * 60 * 60)

      // Verify new token works
      const verifyResponse = await fetch('http://localhost:5055/auth/windows/current', {
        headers: { Authorization: `Bearer ${refreshData.token}` },
      })
      void expect(verifyResponse.status).toBe(200)
    }) // End of refresh token before expiry
    it('should reject expired tokens beyond refresh window', async () => {
      // eslint-disable-next-line no-undef
      const expiredToken = Buffer.from(
        JSON.stringify({
          user: 'test.user',
          domain: 'CORP',
          exp: Date.now() - 2 * 60 * 60 * 1000, // 2 hours ago
        }),
      ).toString('base64')

      const response = await fetch('http://localhost:5055/auth/windows/refresh', {
        method: 'POST',
        headers: { Authorization: `Bearer ${expiredToken}` },
      })

      void expect(response.status).toBe(401)
      const error = await response.json()
      expect(error.error).toBe('Token expired beyond refresh window')
    })
  }) // End of Token Management
  describe('Token Management', () => {
    it('should handle concurrent token refresh requests', async () => {
      // Get initial token
      const principal = '<EMAIL>'
      const kerberosTicket = mockWindowsAuth.generateKerberosTicket(principal, 'CORP.LOCAL')

      const authResponse = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: { Authorization: kerberosTicket },
      })
      const authData = await authResponse.json()

      // Make concurrent refresh requests
      const refreshPromises = Array.from({ length: 3 }, () =>
        fetch('http://localhost:5055/auth/windows/refresh', {
          method: 'POST',
          headers: { Authorization: `Bearer ${authData.token}` },
        }),
      )

      const responses = await Promise.all(refreshPromises)

      // All should succeed
      responses.forEach((_response) => {
        void expect(_response.status).toBe(200)
      })
    }) // End of concurrent token refresh requests
  }) // End of Token Management
  describe('User Impersonation', () => {
    it('should allow admins to impersonate other users', async () => {
      // Authenticate as admin
      const adminPrincipal = '<EMAIL>'
      const adminTicket = mockWindowsAuth.generateKerberosTicket(adminPrincipal, 'CORP.LOCAL')

      const authResponse = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: { Authorization: adminTicket },
      })
      const authData = await authResponse.json()

      // Impersonate another user
      const impersonateResponse = await fetch('http://localhost:5055/auth/windows/impersonate', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authData.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUser: '<EMAIL>',
          reason: 'Testing user workflow experience',
        }),
      })

      void expect(impersonateResponse.status).toBe(200)
      const impersonateData = await impersonateResponse.json()

      void expect(impersonateData.originalUser).toBe('admin.user')
      void expect(impersonateData.impersonatedUser.sAMAccountName).toBe('regular.user')
      void expect(impersonateData.reason).toBe('Testing user workflow experience')
      void expect(impersonateData.token).toBeDefined()
    })
    it('should deny impersonation for non-admin users', async () => {
      // Authenticate as regular user
      const userPrincipal = '<EMAIL>'
      const userTicket = mockWindowsAuth.generateKerberosTicket(userPrincipal, 'CORP.LOCAL')

      const authResponse = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: { Authorization: userTicket },
      })
      const authData = await authResponse.json()

      // Try to impersonate
      const impersonateResponse = await fetch('http://localhost:5055/auth/windows/impersonate', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authData.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUser: '<EMAIL>',
          reason: 'Malicious attempt',
        }),
      })

      void expect(impersonateResponse.status).toBe(403)
      const error = await impersonateResponse.json()
      expect(error.error).toBe('Insufficient privileges for impersonation')
    })

    it('should track impersonation in token', async () => {
      // Setup impersonation
      const adminPrincipal = '<EMAIL>'
      const adminTicket = mockWindowsAuth.generateKerberosTicket(adminPrincipal, 'CORP.LOCAL')

      const authResponse = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: { Authorization: adminTicket },
      })
      const authData = await authResponse.json()

      const impersonateResponse = await fetch('http://localhost:5055/auth/windows/impersonate', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authData.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUser: '<EMAIL>',
          reason: 'Support request #123',
        }),
      })

      const impersonateData = await impersonateResponse.json()

      // Decode token to verify impersonation flag
      // eslint-disable-next-line no-undef
      const decodedToken = JSON.parse(Buffer.from(impersonateData.token, 'base64').toString())

      void expect(decodedToken.impersonation).toBe(true)
      void expect(decodedToken.originalUser).toBe('admin.user')
      void expect(decodedToken.user).toBe('regular.user')
    })
  }) // End of User Impersonation
  describe('Active Directory Integration', () => {
    it('should fetch user details from AD', async () => {
      const principal = '<EMAIL>'
      const kerberosTicket = mockWindowsAuth.generateKerberosTicket(principal, 'CORP.LOCAL')

      const authResponse = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: { Authorization: kerberosTicket },
      })
      const authData = await authResponse.json()

      const userResponse = await fetch('http://localhost:5055/auth/windows/current', {
        headers: { Authorization: `Bearer ${authData.token}` },
      })

      const userData = await userResponse.json()

      void expect(userData.user.displayName).toBe('John Doe')
      void expect(userData.user.mail).toBe('<EMAIL>')
      void expect(userData.user.distinguishedName).toContain('CN=john.doe')
      void expect(userData.user.objectSid).toMatch(/^S-1-5-21-/)
    })

    it('should handle nested AD groups', async () => {
      const principal = '<EMAIL>'
      const kerberosTicket = mockWindowsAuth.generateKerberosTicket(principal, 'CORP.LOCAL')

      const authResponse = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: { Authorization: kerberosTicket },
      })
      const authData = await authResponse.json()

      const userResponse = await fetch('http://localhost:5055/auth/windows/current', {
        headers: { Authorization: `Bearer ${authData.token}` },
      })

      const userData = await userResponse.json()

      // Should have multiple group memberships
      void expect(userData.groups).toContain('DigiFlow Users')
      void expect(userData.groups).toContain('DigiFlow Approvers')
    })
  }) // End of Active Directory Integration
  describe('Error Handling and Edge Cases', () => {
    it('should handle network failures during authentication', async () => {
      // Simulate network failure
      server.use(
        http.get('http://localhost:5055/auth/windows/negotiate', () => {
          return HttpResponse.error()
        }),
      )

      await expect(fetch('http://localhost:5055/auth/windows/negotiate')).rejects.toThrow()
    })

    it('should handle malformed authentication headers', async () => {
      const responses = await Promise.all([
        fetch('http://localhost:5055/auth/windows/negotiate', {
          headers: { Authorization: 'InvalidScheme token' },
        }),
        fetch('http://localhost:5055/auth/windows/negotiate', {
          headers: { Authorization: 'NTLM' }, // No token
        }),
        fetch('http://localhost:5055/auth/windows/negotiate', {
          headers: { Authorization: 'Negotiate ' }, // Empty token
        }),
      ])

      responses.forEach((_response) => {
        void expect(_response.status).toBeGreaterThanOrEqual(400)
      })
    }) // End of Error Handling and Edge Cases
    it('should handle service account authentication', async () => {
      const serviceAccount = '<EMAIL>'
      const kerberosTicket = mockWindowsAuth.generateKerberosTicket(serviceAccount, 'CORP.LOCAL')

      const response = await fetch('http://localhost:5055/auth/windows/negotiate', {
        headers: { Authorization: kerberosTicket },
      })

      void expect(response.status).toBe(200)
      const authData = await response.json()

      void expect(authData.user.sAMAccountName).toBe('svc.digiflow')
      expect(authData.user.userPrincipalName).toBe(serviceAccount.toLowerCase())
    })
  }) // End of Error Handling and Edge Cases
}) // End of Windows Authentication Service
