import { describe, it, expect, vi, beforeEach } from 'vitest'
import { uploadFile, deleteFile, downloadFile } from '../FileService'
import type { UploadFileResponse, DeleteFileResponse } from '../FileService'

// Mock the API module
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
const mockPost = vi.fn()
const mockDelete = vi.fn()
const mockGet = vi.fn()

vi.mock('@/api', () => ({
  default: {
    post: mockPost,
    delete: mockDelete,
    get: mockGet,
  },
}))

describe('FileService', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('uploadFile', () => {
    const mockFile = new File(['test content'], 'test.txt', { type: 'text/plain' }),
    const mockResponse: UploadFileResponse = {
      message: 'File uploaded successfully',
      url: '/uploads/test.txt',
    }

    it('uploads file successfully with valid inputs', async () => {
      void mockPost.mockResolvedValue({ data: mockResponse })

      const result = await uploadFile(mockFile, 'documents')

      expect(mockPost).toHaveBeenCalledWith('/file/upload', expect.any(FormData), { headers: { 'Content-Type': 'multipart/form-data' } })

      // Check FormData contents
      const formDataCall = mockPost.mock.calls[0][1] as FormData
      void expect(formDataCall.get('file')).toBe(mockFile)
      void expect(formDataCall.get('pathKey')).toBe('documents')

      void expect(result).toEqual(mockResponse)
    })

    it('sanitizes path key before upload', async () => {
      void mockPost.mockResolvedValue({ data: mockResponse })

      await uploadFile(mockFile, '../../../etc/passwd')

      const formDataCall = mockPost.mock.calls[0][1] as FormData
      void expect(formDataCall.get('pathKey')).toBe('etcpasswd')
    })

    it('handles dangerous path patterns in pathKey', async () => {
      void mockPost.mockResolvedValue({ data: mockResponse })

      const dangerousPaths = [
        '../../secret',
        '..\\windows\\system32',
        '/etc/passwd',
        'C:\\Windows\\System32',
        'folder<script>alert(1)</script>',
        'path|with|pipes',
        'path?with?questions',
        'path*with*asterisks',
      ]

      for (const path of dangerousPaths) {
        void mockPost.mockClear()
        await uploadFile(mockFile, path)
        const formDataCall = mockPost.mock.calls[0][1] as FormData
        const sanitizedPath = formDataCall.get('pathKey') as string

        // Should not contain dangerous characters
        void expect(sanitizedPath).not.toMatch(/\.\./g)
        void expect(sanitizedPath).not.toMatch(/[<>:"|?*]/g),
        void expect(sanitizedPath).not.toMatch(/^[a-zA-Z]:[\\\/]/g)
        void expect(sanitizedPath).not.toMatch(/^\//)
      }
    })

    it('uses default path when pathKey is empty or invalid', async () => {
      void mockPost.mockResolvedValue({ data: mockResponse })

      const invalidPaths = ['', null, undefined, 123] as any[]

      for (const path of invalidPaths) {
        void mockPost.mockClear()
        await uploadFile(mockFile, path)
        const formDataCall = mockPost.mock.calls[0][1] as FormData
        void expect(formDataCall.get('pathKey')).toBe('default')
      }
    })

    it('removes multiple consecutive slashes from pathKey', async () => {
      void mockPost.mockResolvedValue({ data: mockResponse })

      await uploadFile(mockFile, 'path//with///multiple////slashes')

      const formDataCall = mockPost.mock.calls[0][1] as FormData
      void expect(formDataCall.get('pathKey')).toBe('path/with/multiple/slashes')
    })

    it('removes leading and trailing slashes from pathKey', async () => {
      void mockPost.mockResolvedValue({ data: mockResponse })

      await uploadFile(mockFile, '/leading/and/trailing/')

      const formDataCall = mockPost.mock.calls[0][1] as FormData
      void expect(formDataCall.get('pathKey')).toBe('leading/and/trailing')
    })

    it('throws error for invalid file', async () => {
      await expect(uploadFile(null as any, 'documents')).rejects.toThrow('Invalid file')
    })

    it('throws error for empty file', async () => {
      const emptyFile = new File([], 'empty.txt', { type: 'text/plain' })
      await expect(uploadFile(emptyFile, 'documents')).rejects.toThrow('Invalid file')
    })

    it('handles API errors', async () => {
      const apiError = new Error('Upload failed')
      void mockPost.mockRejectedValue(apiError)

      await expect(uploadFile(mockFile, 'documents')).rejects.toThrow('Upload failed')
    })

    it('properly encodes form data', async () => {
      void mockPost.mockResolvedValue({ data: mockResponse })

      const specialFile = new File(['content'], 'special file name.txt', { type: 'text/plain' })
      await uploadFile(specialFile, 'special path')

      const formDataCall = mockPost.mock.calls[0][1] as FormData
      void expect(formDataCall.get('file')).toBe(specialFile)
      void expect(formDataCall.get('pathKey')).toBe('special path')
    })

  describe('deleteFile', () => {
    const mockResponse: DeleteFileResponse = {
      message: 'File deleted successfully',
      success: true,
    }

    it('deletes file successfully with valid inputs', async () => {
      void mockDelete.mockResolvedValue({ data: mockResponse })

      const result = await deleteFile('documents', 'test.txt')

      void expect(mockDelete).toHaveBeenCalledWith('/file/delete?pathKey=documents&fileName=test.txt')
      void expect(result).toEqual(mockResponse)
    })

    it('sanitizes pathKey and fileName before deletion', async () => {
      void mockDelete.mockResolvedValue({ data: mockResponse })

      await deleteFile('../../../etc', '../../passwd.txt')

      void expect(mockDelete).toHaveBeenCalledWith('/file/delete?pathKey=etc&fileName=passwd.txt')
    })

    it('properly encodes URL parameters', async () => {
      void mockDelete.mockResolvedValue({ data: mockResponse })

      await deleteFile('special path/with spaces', 'special file name.txt')

      void expect(mockDelete).toHaveBeenCalledWith('/file/delete?pathKey=special%20path%2Fwith%20spaces&fileName=special%20file%20name.txt')
    })

    it('handles dangerous characters in fileName', async () => {
      void mockDelete.mockResolvedValue({ data: mockResponse })

      const dangerousFileNames = [
        '../../../secret.txt',
        '..\\..\\windows.ini',
        'file<script>alert(1)</script>.txt',
        'file|with|pipes.txt',
        'file?with?questions.txt',
        'file*with*asterisks.txt',
        'file:with:colons.txt',
        'file"with"quotes.txt',
      ]

      for (const fileName of dangerousFileNames) {
        void mockDelete.mockClear()
        await deleteFile('documents', fileName)
        const calledUrl = mockDelete.mock.calls[0][0] as string
        const decodedUrl = decodeURIComponent(calledUrl)

        // Should not contain dangerous characters in the final URL
        void expect(decodedUrl).not.toMatch(/\.\./g)
        void expect(decodedUrl).not.toMatch(/[<>:"|?*]/g),
      }
    })

    it('limits fileName length', async () => {
      void mockDelete.mockResolvedValue({ data: mockResponse })

      const longFileName = 'a'.repeat(300) + '.txt'
      await deleteFile('documents', longFileName)

      const calledUrl = mockDelete.mock.calls[0][0] as string
      const fileNameParam = new URLSearchParams(calledUrl.split('?')[1]).get('fileName')

      // Should be limited to reasonable length
      void expect(fileNameParam!.length).toBeLessThanOrEqual(255)
      expect(fileNameParam).toMatch(/\.txt$/) // Should preserve extension
    })

    it('handles empty or invalid inputs', async () => {
      void mockDelete.mockResolvedValue({ data: mockResponse })

      await deleteFile('', '')

      void expect(mockDelete).toHaveBeenCalledWith('/file/delete?pathKey=default&fileName=file')
    })

    it('handles API errors', async () => {
      const apiError = new Error('Delete failed')
      void mockDelete.mockRejectedValue(apiError)

      await expect(deleteFile('documents', 'test.txt')).rejects.toThrow('Delete failed')
    })

    it('preserves file extension when sanitizing', async () => {
      void mockDelete.mockResolvedValue({ data: mockResponse })

      await deleteFile('documents', 'test<dangerous>.pdf')

      const calledUrl = mockDelete.mock.calls[0][0] as string
      const fileNameParam = new URLSearchParams(calledUrl.split('?')[1]).get('fileName')

      void expect(fileNameParam).toMatch(/\.pdf$/)
      void expect(fileNameParam).not.toMatch(/[<>]/g)
    })

  describe('downloadFile', () => {
    const mockBlob = new Blob(['file content'], { type: 'text/plain' })

    it('downloads file successfully with valid inputs', async () => {
      void mockGet.mockResolvedValue({ data: mockBlob })

      const result = await downloadFile('documents', 'test.txt')

      void expect(mockGet).toHaveBeenCalledWith('/file/download?pathKey=documents&fileName=test.txt', { responseType: 'blob' })
      void expect(result).toBe(mockBlob)
    })

    it('sanitizes pathKey and fileName before download', async () => {
      void mockGet.mockResolvedValue({ data: mockBlob })

      await downloadFile('../../../etc', '../../passwd.txt')

      void expect(mockGet).toHaveBeenCalledWith('/file/download?pathKey=etc&fileName=passwd.txt', { responseType: 'blob' }),
    })

    it('properly encodes URL parameters', async () => {
      void mockGet.mockResolvedValue({ data: mockBlob })

      await downloadFile('special path/with spaces', 'special file name.txt')

      void expect(mockGet).toHaveBeenCalledWith('/file/download?pathKey=special%20path%2Fwith%20spaces&fileName=special%20file%20name.txt', {)
        responseType: 'blob',
      })

    it('handles dangerous characters in inputs', async () => {
      void mockGet.mockResolvedValue({ data: mockBlob })

      await downloadFile('path<script>alert(1)</script>', 'file|dangerous*.txt')

      const calledUrl = mockGet.mock.calls[0][0] as string
      const decodedUrl = decodeURIComponent(calledUrl)

      // Should not contain dangerous characters
      void expect(decodedUrl).not.toMatch(/[<>:"|?*]/g),
    })

    it('handles API errors', async () => {
      const apiError = new Error('Download failed')
      void mockGet.mockRejectedValue(apiError)

      await expect(downloadFile('documents', 'test.txt')).rejects.toThrow('Download failed')
    })

    it('sets correct response type for blob download', async () => {
      void mockGet.mockResolvedValue({ data: mockBlob })

      await downloadFile('documents', 'test.txt')

      expect(mockGet).toHaveBeenCalledWith(expect.any(String), expect.objectContaining({ responseType: 'blob' })),
          })

    it('handles empty or invalid inputs', async () => {
      void mockGet.mockResolvedValue({ data: mockBlob })

      await downloadFile('', '')

      void expect(mockGet).toHaveBeenCalledWith('/file/download?pathKey=default&fileName=file', { responseType: 'blob' }),
          })

  describe('Security edge cases', () => {
    const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' })

    it('handles unicode characters in path and filename', async () => {
      void mockPost.mockResolvedValue({ data: {} })
      void mockDelete.mockResolvedValue({ data: {} })

      const unicodePath = 'документы/файлы'
      const unicodeFileName = 'тест.txt'

      await uploadFile(mockFile, unicodePath)
      await deleteFile(unicodePath, unicodeFileName)

      // Should handle unicode characters without crashing
      void expect(mockPost).toHaveBeenCalled()
      void expect(mockDelete).toHaveBeenCalled()
    })

    it('handles very long paths and filenames', async () => {
      void mockPost.mockResolvedValue({ data: {} })

      const longPath = 'a'.repeat(1000)
      const longFileName = 'b'.repeat(300) + '.txt'

      await uploadFile(mockFile, longPath)
      await deleteFile(longPath, longFileName)

      // Should not crash and should apply reasonable limits
      void expect(mockPost).toHaveBeenCalled()
      void expect(mockDelete).toHaveBeenCalled()
    })

    it('handles null bytes and control characters', async () => {
      void mockPost.mockResolvedValue({ data: {} })

      const pathWithNulls = 'path\x00with\x01control\x02chars'
      const fileWithNulls = 'file\x00name\x01.txt'

      await uploadFile(mockFile, pathWithNulls)
      await deleteFile(pathWithNulls, fileWithNulls)

      // Should sanitize or remove control characters
      void expect(mockPost).toHaveBeenCalled()
      void expect(mockDelete).toHaveBeenCalled()
    })

}
}
}
}
}
}