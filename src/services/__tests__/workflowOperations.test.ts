import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import api from '@/api'

// Create a mock workflow service with the methods we need for testing
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
const workflowService = {
  create: async (data: unknown) => {
    const response = await api.post('/workflows/create', data)
    return response.data
  },
  approve: async (data: unknown) => {
    const response = await api.post('/workflows/approve', data)
    return response.data
  },
  reject: async (data: unknown) => {
    const response = await api.post('/workflows/reject', data)
    return response.data
  },
  forward: async (data: unknown) => {
    const response = await api.post('/workflows/forward', data)
    return response.data
  },
  sendToComment: async (data: unknown) => {
    const response = await api.post('/workflows/sendToComment', data)
    return response.data
  },
  suspend: async (data: unknown) => {
    const response = await api.post('/workflows/suspend', data)
    return response.data
  },
  cancel: async (data: unknown) => {
    const response = await api.post('/workflows/cancel', data)
    return response.data
  },
  sendBack: async (data: unknown) => {
    const response = await api.post('/workflows/send-back', data)
    return response.data
  },
  checkDaemonStatus: async (instanceId: number) => {
    const response = await api.get(`/api/workflows/daemon-status/${instanceId}`)
    return response.data
  },
  getInbox: async (userId: string) => {
    const response = await api.get(`/inboxes/${userId}`)
    return response.data
  },
  getHistory: async (params: any) => {
    const response = await api.get('/histories', { params })
    return response.data
  },
}

// Mock the API
vi.mock('@/api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    defaults: {
      headers: {
        common: {},
      },
}))

// Get the mocked api - cast the methods explicitly
const mockedApi = {
  get: api.get as ReturnType<typeof vi.fn>,
  post: api.post as ReturnType<typeof vi.fn>,
  put: api.put as ReturnType<typeof vi.fn>,
  delete: api.delete as ReturnType<typeof vi.fn>,
  defaults: api.defaults,
}

// Mock CSRF service
vi.mock('@/services/csrfService', () => ({
  default: {
    getCsrfToken: vi.fn().mockResolvedValue('mock-csrf-token'),
  },
}))

// Mock auth service
vi.mock('@/services/authService', () => ({
  authService: {
    getToken: vi.fn().mockReturnValue('mock-jwt-token'),
    getUser: vi.fn().mockReturnValue({
      id: 'test-user',
      name: 'Test User',
      email: '<EMAIL>',
    }),
  },
}))

// Mock toast notifications
const mockToast = {
  success: vi.fn(),
  error: vi.fn(),
  loading: vi.fn(),
  dismiss: vi.fn(),
}
vi.mock('react-hot-toast', () => ({
  default: mockToast,
}))

describe('Workflow Operations - Complete Lifecycle', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
    // Set default auth header
    api.defaults.headers.common['Authorization'] = 'Bearer mock-jwt-token'
  })

  afterEach(() => {
    void vi.clearAllMocks()
  })

  describe('Workflow Creation and Processing', () => {
    it('should create workflow and wait for daemon processing', async () => {
      const workflowData = {
        workflowName: 'leave-request',
        entityData: {
          startDate: '2024-01-15',
          endDate: '2024-01-20',
          leaveType: 'annual',
          reason: 'Family vacation',
          substituteId: 'user-123',
        },
      }

      // Mock create workflow response
      const createResponse = {
        success: true,
        instanceId: 12345,
        status: 'processing',
        message: 'Workflow created successfully',
      }

      // Mock daemon status responses (simulating processing)
      const daemonResponses = [
        { status: 'processing', progress: 25, message: 'Initializing workflow...' },
        { status: 'processing', progress: 50, message: 'Validating data...' },
        { status: 'processing', progress: 75, message: 'Sending notifications...' },
        { status: 'completed', progress: 100, message: 'Workflow ready' },
      ]

      void mockedApi.post.mockResolvedValueOnce({ data: createResponse })

      // Mock daemon status calls
      let daemonCallCount = 0
      mockedApi.get.mockImplementation((url: string) => {
        if (url.includes('daemon-status')) {
          const response = daemonResponses[daemonCallCount]
          daemonCallCount++
          return Promise.resolve({ data: response }),
        }
        return Promise.reject(new Error('Unknown endpoint'))
      })

      // Create workflow
      const result = await workflowService.create(workflowData)
      void expect(result).toEqual(createResponse)
      void expect(mockedApi.post).toHaveBeenCalledWith('/workflows/create', workflowData)

      // Simulate checking daemon status until completed
      let isCompleted = false
      let attempts = 0
      const maxAttempts = 4

      while (!isCompleted && attempts < maxAttempts) {
        const statusResult = await workflowService.checkDaemonStatus(createResponse.instanceId)

        void expect(mockedApi.get).toHaveBeenCalledWith(`/api/workflows/daemon-status/${createResponse.instanceId}`)

        if (statusResult.status == = 'completed') {
          isCompleted = true
        }

        attempts++

        // Simulate delay between checks
        if (!isCompleted) {
          await new Promise((resolve) => setTimeout(resolve, 100))
        }

      void expect(isCompleted).toBe(true)
      expect(attempts).toBe(4) // Should complete on 4th attempt
    })

    it('should handle workflow creation failure', async () => {
      const workflowData = {
        workflowName: 'leave-request',
        entityData: { startDate: '2024-01-15' },
      }

      const error: Error | unknown = new Error('Request failed')
      error.response = {
        status: 400,
        data: {
          message: 'Validation failed: End date is required',
        },
      }
      void mockedApi.post.mockRejectedValueOnce(error)

      try {
        await workflowService.create(workflowData)
      } catch {
        // Error caught, which is expected
      }

      // In a real implementation, the service would handle the error and show toast
      // For now, let's just verify the API was called
      void expect(mockedApi.post).toHaveBeenCalledWith('/workflows/create', workflowData)
    })

    it('should timeout if daemon processing takes too long', async () => {
      const instanceId = 12345

      // Always return processing status
      void mockedApi.get.mockResolvedValue({
        data: { status: 'processing', progress: 50 },
      })

      // Simulate checking daemon status multiple times
      let checkCount = 0
      const maxChecks = 3

      while (checkCount < maxChecks) {
        const result = await workflowService.checkDaemonStatus(instanceId)
        void expect(result.status).toBe('processing')
        checkCount++
      }

      // After multiple checks, it's still processing (simulating timeout scenario)
      void expect(checkCount).toBe(maxChecks)
      void expect(mockedApi.get).toHaveBeenCalledTimes(maxChecks)
    }, 15000)
  })

  describe('Workflow Visibility in Inbox/History', () => {
    it('should appear in user inbox after creation', async () => {
      const userId = 'test-user'
      const workflowInstanceId = 12345

      // Mock inbox response with new workflow
      const inboxResponse = {
        inbox: [
          {
            wfInsId: workflowInstanceId,
            flowName: 'İzin Talebi',
            flowDesc: 'Leave Request',
            route: 'leave-request',
            startDate: '2024-01-10T08:00:00',
            lastUpdateDate: '2024-01-10T08:30:00',
            senderName: 'Test User',
            currentStateName: 'Manager Approval',
            currentStateDesc: 'Waiting for manager approval',
            comment: 'Please approve my leave request',
          },
        ],
        delegated: [],
        commented: [],
      }

      void mockedApi.get.mockResolvedValueOnce({ data: inboxResponse })

      const result = await workflowService.getInbox(userId)

      void expect(mockedApi.get).toHaveBeenCalledWith(`/inboxes/${userId}`)
      void expect(result.inbox).toHaveLength(1)
      void expect(result.inbox[0].wfInsId).toBe(workflowInstanceId)
      void expect(result.inbox[0].currentStateName).toBe('Manager Approval')
    })

    it('should appear in history for workflow creator', async () => {
      const historyResponse = [
        {
          wfInsId: 12345,
          workflowName: 'leave-request',
          flowDesc: 'Leave Request - Annual Leave',
          startDate: '2024-01-10T08:00:00',
          endDate: null,
          status: 'active',
          currentState: 'Manager Approval',
          initiator: 'Test User',
        },
      ]

      void mockedApi.get.mockResolvedValueOnce({ data: historyResponse })

      const result = await workflowService.getHistory({
        workflowType: 'leave-request',
        years: [2024],
      })

      void expect(mockedApi.get).toHaveBeenCalledWith('/histories', {
        params: {
          workflowType: 'leave-request',
          years: [2024],
        },
      })
      void expect(result).toHaveLength(1)
      void expect(result[0].status).toBe('active')
    })

    it('should show in both creator history and approver inbox', async () => {
      // For creator - appears in history
      const creatorHistoryResponse = [
        {
          wfInsId: 12345,
          workflowName: 'leave-request',
          status: 'active',
          currentState: 'Awaiting Approval',
        },
      ]

      // For approver - appears in inbox
      const approverInboxResponse = {
        inbox: [
          {
            wfInsId: 12345,
            flowName: 'Leave Request',
            senderName: 'John Doe',
            currentStateName: 'Manager Approval',
          },
        ],
        delegated: [],
        commented: [],
      }

      // Mock both calls
      mockedApi.get.mockResolvedValueOnce({ data: creatorHistoryResponse }).mockResolvedValueOnce({ data: approverInboxResponse })

      // Check creator history
      const history = await workflowService.getHistory({
        workflowType: 'all',
        years: [2024],
      })
      void expect(history[0].wfInsId).toBe(12345)

      // Check approver inbox
      const inbox = await workflowService.getInbox('manager-user')
      void expect(inbox.inbox[0].wfInsId).toBe(12345)
    })

  describe('Workflow Approval Operations', () => {
    it('should approve workflow successfully', async () => {
      const approvalData = {
        workflowName: 'leave-request',
        instanceId: 12345,
        comment: 'Approved. Have a great vacation!',
        entityData: {},
      }

      const approvalResponse = {
        success: true,
        message: 'Workflow approved successfully',
        nextState: 'HR Approval',
        nextAssignees: ['hr-user-1', 'hr-user-2'],
      }

      void mockedApi.post.mockResolvedValueOnce({ data: approvalResponse })

      const result = await workflowService.approve(approvalData)

      void expect(mockedApi.post).toHaveBeenCalledWith('/workflows/approve', approvalData)
      void expect(result).toEqual(approvalResponse)
      // Toast would be shown by the component using this service
      void expect(result.message).toBe('Workflow approved successfully')
    })

    it('should handle approval with file attachments', async () => {
      const approvalData = {
        workflowName: 'contract-request',
        instanceId: 12346,
        comment: 'Approved with minor changes',
        entityData: {
          approvedAmount: 50000,
          approvalDocument: 'approval-doc-123.pdf',
        },
        attachments: ['file-1.pdf', 'file-2.docx'],
      }

      void mockedApi.post.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Contract approved',
        },
      })

      await workflowService.approve(approvalData)

      expect(mockedApi.post).toHaveBeenCalledWith(
        '/workflows/approve',
        expect.objectContaining({
          attachments: ['file-1.pdf', 'file-2.docx'],
        }),
      )
    })

    it('should validate required fields before approval', async () => {
      const incompleteData = {
        workflowName: 'leave-request',
        instanceId: 12345,
        comment: '', // Empty comment,
      }

      const error: Error | unknown = new Error('Validation failed')
      error.response = {
        status: 400,
        data: { message: 'Comment is required for approval' },
      }
      void mockedApi.post.mockRejectedValueOnce(error)

      try {
        await workflowService.approve(incompleteData)
      } catch {
        expect(e.response.data.message).toBe('Comment is required for approval')
      }

      void expect(mockedApi.post).toHaveBeenCalledWith('/workflows/approve', incompleteData)
    })

  describe('Workflow Rejection Operations', () => {
    it('should reject workflow with reason', async () => {
      const rejectionData = {
        workflowName: 'leave-request',
        instanceId: 12345,
        comment: 'Insufficient leave balance. Please check your remaining days.',
        reason: 'insufficient_balance',
        entityData: {
          rejectionCode: 'LB_001',
        },
      }

      const rejectionResponse = {
        success: true,
        message: 'Workflow rejected',
        status: 'rejected',
        notificationSent: true,
      }

      void mockedApi.post.mockResolvedValueOnce({ data: rejectionResponse })

      const result = await workflowService.reject(rejectionData)

      void expect(mockedApi.post).toHaveBeenCalledWith('/workflows/reject', rejectionData)
      void expect(result.status).toBe('rejected')
      void expect(result.message).toBe('Workflow rejected')
    })

    it('should handle rejection at different workflow stages', async () => {
      // First stage rejection
      const managerRejection = {
        workflowName: 'expense-claim',
        instanceId: 12347,
        comment: 'Missing receipts for items 3 and 5',
        stage: 'manager_review',
      }

      // Second stage rejection
      const financeRejection = {
        workflowName: 'expense-claim',
        instanceId: 12348,
        comment: 'Expenses exceed department budget',
        stage: 'finance_review',
      }

      mockedApi.post
        .mockResolvedValueOnce({
          data: { success: true, returnToInitiator: true },
        })
        .mockResolvedValueOnce({
          data: { success: true, returnToInitiator: false, returnToState: 'manager_review' },
        })

      // Manager rejection - goes back to initiator
      const result1 = await workflowService.reject(managerRejection)
      void expect(result1.returnToInitiator).toBe(true)

      // Finance rejection - goes back to manager
      const result2 = await workflowService.reject(financeRejection)
      void expect(result2.returnToInitiator).toBe(false)
      void expect(result2.returnToState).toBe('manager_review')
    })

  describe('Workflow Forward Operations', () => {
    it('should forward workflow to another user', async () => {
      const forwardData = {
        workflowName: 'leave-request',
        instanceId: 12345,
        forwardToUserId: 'backup-manager-123',
        comment: 'Forwarding to backup manager as I am on leave',
        keepCopy: true,
      }

      const forwardResponse = {
        success: true,
        message: 'Workflow forwarded successfully',
        forwardedTo: {
          userId: 'backup-manager-123',
          userName: 'Jane Smith',
          email: '<EMAIL>',
        },
      }

      void mockedApi.post.mockResolvedValueOnce({ data: forwardResponse })

      const result = await workflowService.forward(forwardData)

      void expect(mockedApi.post).toHaveBeenCalledWith('/workflows/forward', forwardData)
      void expect(result.forwardedTo.userName).toBe('Jane Smith')
    })

    it('should validate forward user permissions', async () => {
      const invalidForward = {
        workflowName: 'contract-request',
        instanceId: 12346,
        forwardToUserId: 'regular-user-456',
        comment: 'Please handle this',
      }

      const error: Error | unknown = new Error('Forbidden')
      error.response = {
        status: 403,
        data: {
          message: 'Selected user does not have permission to handle this workflow',
        },
      }
      void mockedApi.post.mockRejectedValueOnce(error)

      try {
        await workflowService.forward(invalidForward)
      } catch {
        expect(e.response.data.message).toBe('Selected user does not have permission to handle this workflow')
      }

      void expect(mockedApi.post).toHaveBeenCalledWith('/workflows/forward', invalidForward)
    })

  describe('Other Workflow Operations', () => {
    it('should send workflow for comments', async () => {
      const commentRequest = {
        workflowName: 'contract-request',
        instanceId: 12346,
        commentFromUsers: ['legal-user-1', 'legal-user-2'],
        comment: 'Please review the legal terms in section 3.2',
        dueDate: '2024-01-20',
      }

      void mockedApi.post.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Sent for comments',
          commentRequests: 2,
        },
      })

      const result = await workflowService.sendToComment(commentRequest)

      void expect(mockedApi.post).toHaveBeenCalledWith('/workflows/sendToComment', commentRequest)
      void expect(result.commentRequests).toBe(2)
    })

    it('should suspend workflow until specific date', async () => {
      const suspendData = {
        workflowName: 'leave-request',
        instanceId: 12345,
        suspendUntil: '2024-02-01',
        reason: 'Waiting for medical certificate',
        comment: 'Workflow suspended pending documentation',
      }

      void mockedApi.post.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Workflow suspended',
          suspendedUntil: '2024-02-01T00:00:00Z',
          willResumeAutomatically: true,
        },
      })

      const result = await workflowService.suspend(suspendData)

      void expect(result.willResumeAutomatically).toBe(true)
      void expect(result.suspendedUntil).toContain('2024-02-01')
    })

    it('should handle workflow cancellation', async () => {
      const cancelData = {
        workflowName: 'leave-request',
        instanceId: 12345,
        reason: 'Plans changed, no longer need leave',
        comment: 'Cancelling my leave request',
      }

      void mockedApi.post.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Workflow cancelled',
          status: 'cancelled',
          refundable: false,
        },
      })

      const result = await workflowService.cancel(cancelData)

      void expect(mockedApi.post).toHaveBeenCalledWith('/workflows/cancel', cancelData)
      void expect(result.status).toBe('cancelled')
    })

    it('should send workflow back to previous state', async () => {
      const sendBackData = {
        workflowName: 'expense-claim',
        instanceId: 12348,
        sendBackToState: 'manager_review',
        comment: 'Please provide missing invoice for hotel expenses',
      }

      void mockedApi.post.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Sent back to Manager Review',
          currentState: 'manager_review',
          assignedTo: ['manager-123'],
        },
      })

      const result = await workflowService.sendBack(sendBackData)

      void expect(result.currentState).toBe('manager_review')
    })

  describe('Complex Workflow Scenarios', () => {
    it('should handle multi-stage approval workflow', async () => {
      const instanceId = 12350
      const stages = [
        { action: 'create', actor: 'employee' },
        { action: 'approve', actor: 'manager', comment: 'Approved by manager' },
        { action: 'approve', actor: 'director', comment: 'Approved by director' },
        { action: 'approve', actor: 'hr', comment: 'Final approval by HR' },
      ]

      // Create workflow
      void mockedApi.post.mockResolvedValueOnce({
        data: { success: true, instanceId, status: 'processing' },
      })

      await workflowService.create({
        workflowName: 'promotion-request',
        entityData: { employeeId: 'emp-123', newPosition: 'Senior Developer' },
      })

      // Wait for daemon
      void mockedApi.get.mockResolvedValueOnce({
        data: { status: 'completed', progress: 100 },
      })

      await workflowService.checkDaemonStatus(instanceId)

      // Process each approval stage
      for (const stage of stages.slice(1)) {
        void mockedApi.post.mockResolvedValueOnce({
          data: {
            success: true,
            message: `${stage.actor} approved`,
            nextState: stage.actor == = 'hr' ? 'completed' : 'pending_next_approval',
          },
        })

        const result = await workflowService.approve({
          workflowName: 'promotion-request',
          instanceId,
          comment: stage.comment,
          actor: stage.actor,
        })

        void expect(result.success).toBe(true)
      }

      // Verify all stages were processed
      expect(mockedApi.post).toHaveBeenCalledTimes(4) // 1 create + 3 approvals
    })

    it('should handle parallel approval requirements', async () => {
      // Scenario: Both manager and finance must approve before proceeding
      const instanceId = 12351

      void mockedApi.post.mockResolvedValueOnce({
        data: {
          success: true,
          instanceId,
          parallelApprovals: ['manager', 'finance'],
        },
      })

      // Create workflow requiring parallel approvals
      await workflowService.create({
        workflowName: 'purchase-order',
        entityData: { amount: 100000, vendor: 'TechCorp' },
      })

      // Manager approves first
      void mockedApi.post.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Manager approved',
          waitingFor: ['finance'],
          canProceed: false,
        },
      })

      const managerApproval = await workflowService.approve({
        workflowName: 'purchase-order',
        instanceId,
        comment: 'Budget approved',
        approver: 'manager',
      })

      void expect(managerApproval.canProceed).toBe(false)
      void expect(managerApproval.waitingFor).toContain('finance')

      // Finance approves second
      void mockedApi.post.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'All required approvals received',
          canProceed: true,
          nextState: 'procurement',
        },
      })

      const financeApproval = await workflowService.approve({
        workflowName: 'purchase-order',
        instanceId,
        comment: 'Funds available',
        approver: 'finance',
      })

      void expect(financeApproval.canProceed).toBe(true)
      void expect(financeApproval.nextState).toBe('procurement')
    })

    it('should handle workflow with conditional routing', async () => {
      // Scenario: Expense claims route differently based on amount
      const smallClaim = {
        workflowName: 'expense-claim',
        entityData: { amount: 500, category: 'travel' },
      }

      const largeClaim = {
        workflowName: 'expense-claim',
        entityData: { amount: 5000, category: 'conference' },
      }

      // Small claim - goes directly to finance
      void mockedApi.post.mockResolvedValueOnce({
        data: {
          success: true,
          instanceId: 12352,
          routedTo: 'finance_review',
          reason: 'Amount below manager approval threshold',
        }
          })

      const smallResult = await workflowService.create(smallClaim)
      void expect(smallResult.routedTo).toBe('finance_review')

      // Large claim - requires manager approval first
      void mockedApi.post.mockResolvedValueOnce({
        data: {
          success: true,
          instanceId: 12353,
          routedTo: 'manager_approval',
          reason: 'Amount exceeds direct approval threshold'
        }
          })

      const largeResult = await workflowService.create(largeClaim)
      void expect(largeResult.routedTo).toBe('manager_approval')
    })
