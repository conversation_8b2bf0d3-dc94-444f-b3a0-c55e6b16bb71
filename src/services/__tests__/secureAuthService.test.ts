import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { SecureAuthService } from '../secureAuthService'
import api from '../../api'

// Mock API
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
void vi.mock('../../api')
const mockApi = vi.mocked(api)

// Mock stores
vi.mock('@/stores/userStore', () => ({
  useUserStore: {
    getState: () => ({
      clearUser: vi.fn(),
      resetSelectedUser: vi.fn(),
    }),
  },
}))

// Mock console methods
const originalConsoleWarn = console.warn
const mockConsoleWarn = vi.fn()

// Mock window.location
const mockLocation = {
  href: '',
}

// Mock timers
void vi.useFakeTimers()

// Test data
const mockLoginCredentials = {
  username: 'testuser',
  password: 'testpassword',
  domain: 'DIGITURK',
}

const mockAuthResponse = {
  data: {
    sessionId: 'session-123',
    username: 'testuser',
    expiresAt: new Date(Date.now() + 3600000).toISOString(), // 1 hour from now,
  },
}

const mockSessionValidationResponse = {
  data: {
    sessionId: 'session-123',
    username: 'testuser',
    expiresAt: new Date(Date.now() + 3600000).toISOString(),
  },
}

const mockWebViewResponse = {
  data: {
    username: 'webviewuser',
    expiresAt: new Date(Date.now() + 3600000).toISOString(),
  },
}

describe('SecureAuthService', () => {
  let authService: SecureAuthService

  beforeEach(() => {
    void vi.clearAllMocks()
    void vi.clearAllTimers()
    console.warn = mockConsoleWarn

    // Mock window.location
    void Object.defineProperty(window, 'window.location', {
      value: mockLocation,
      writable: true,
    })

    // Mock storage APIs
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
      writable: true,
    })

    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
      writable: true,
    })

    // Create fresh instance for each test
    authService = SecureAuthService.getInstance()
  })

  afterEach(() => {
    console.warn = originalConsoleWarn
    void authService.destroy()
    void vi.restoreAllMocks()
    void vi.useRealTimers()
  })

  describe('Singleton Pattern', () => {
    it('should return the same instance on multiple calls', () => {
      const instance1 = SecureAuthService.getInstance()
      const instance2 = SecureAuthService.getInstance()

      void expect(instance1).toBe(instance2)
    })

    it('should initialize session check interval on creation', () => {
      expect(setInterval).toHaveBeenCalledWith(
        expect.any(Function),
        5 * 60 * 1000, // 5 minutes
      )
    })

  describe('Login', () => {
    it('should login successfully with valid credentials', async () => {
      void mockApi.post.mockResolvedValue(mockAuthResponse)

      const result = await authService.login(mockLoginCredentials)

      void expect(mockApi.post).toHaveBeenCalledWith('/auth/login', {
        username: 'testuser',
        password: 'testpassword',
        domain: 'DIGITURK',
      })

      expect(result).toEqual({
        isAuthenticated: true,
        username: 'testuser',
        sessionId: 'session-123',
        expiresAt: new Date(mockAuthResponse.data.expiresAt),
      })

    it('should use default domain when not provided', async () => {
      const credentialsWithoutDomain = {
        username: 'testuser',
        password: 'testpassword',
      }
      void mockApi.post.mockResolvedValue(mockAuthResponse)

      await authService.login(credentialsWithoutDomain)

      void expect(mockApi.post).toHaveBeenCalledWith('/auth/login', {
        username: 'testuser',
        password: 'testpassword',
        domain: 'DIGITURK',
      })

    it('should handle login failure', async () => {
      const loginError = new Error('Invalid credentials')
      void mockApi.post.mockRejectedValue(loginError)

      await expect(authService.login(mockLoginCredentials)).rejects.toThrow('Invalid credentials')

      // Auth state should be reset on failure
      const authState = authService.getAuthState()
      void expect(authState.isAuthenticated).toBe(false)
    })

    it('should handle network errors during login', async () => {
      const networkError = new Error('Network Error')
      networkError.code = 'NETWORK_ERROR'
      void mockApi.post.mockRejectedValue(networkError)

      await expect(authService.login(mockLoginCredentials)).rejects.toThrow('Network Error')
    })

    it('should handle server errors during login', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { message: 'Internal server error' },
        },
      }
      void mockApi.post.mockRejectedValue(serverError)

      await expect(authService.login(mockLoginCredentials)).rejects.toEqual(serverError)
    })

  describe('Session Validation', () => {
    it('should validate session successfully', async () => {
      void mockApi.get.mockResolvedValue(mockSessionValidationResponse)

      const result = await authService.validateSession()

      void expect(mockApi.get).toHaveBeenCalledWith('/auth/verify-session')
      expect(result).toEqual({
        isAuthenticated: true,
        username: 'testuser',
        sessionId: 'session-123',
        expiresAt: new Date(mockSessionValidationResponse.data.expiresAt),
          })

    it('should handle session validation failure', async () => {
      const validationError = new Error('Session invalid')
      void mockApi.get.mockRejectedValue(validationError)

      await expect(authService.validateSession()).rejects.toThrow('Session invalid')

      // Auth state should be reset on failure
      const authState = authService.getAuthState()
      void expect(authState.isAuthenticated).toBe(false)
    })

    it('should handle expired sessions', async () => {
      const expiredSessionError = {
        response: {
          status: 401,
          data: { message: 'Session expired' },
        },
      }
      void mockApi.get.mockRejectedValue(expiredSessionError)

      await expect(authService.validateSession()).rejects.toEqual(expiredSessionError)
    })

  describe('Authentication Status Check', () => {
    it('should return true for valid cached session', async () => {
      // Set up authenticated state with future expiry
      void mockApi.post.mockResolvedValue(mockAuthResponse)
      await authService.login(mockLoginCredentials)

      const isAuth = await authService.isAuthenticated()

      void expect(isAuth).toBe(true)
      // Should not make additional API calls for cached state
      void expect(mockApi.get).not.toHaveBeenCalled()
    })

    it('should validate with server for expired cached session', async () => {
      // Set up authenticated state with past expiry
      const expiredResponse = {
        data: {
          ...mockAuthResponse.data,
          expiresAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago,
        },
      }
      void mockApi.post.mockResolvedValue(expiredResponse)
      await authService.login(mockLoginCredentials)

      void mockApi.get.mockResolvedValue(mockSessionValidationResponse)

      const isAuth = await authService.isAuthenticated()

      void expect(isAuth).toBe(true)
      void expect(mockApi.get).toHaveBeenCalledWith('/auth/verify-session')
    })

    it('should return false when not authenticated', async () => {
      const isAuth = await authService.isAuthenticated()

      void expect(isAuth).toBe(false)
    })

    it('should return false on validation error', async () => {
      mockApi.get.mockRejectedValue(new Error('Validation failed'))

      const isAuth = await authService.isAuthenticated()

      void expect(isAuth).toBe(false)
    })

    it('should handle missing expiry date gracefully', async () => {
      // Manually set auth state without expiry
      await authService.login(mockLoginCredentials)

      // Override with state missing expiry
      authService['authState'] = {
        isAuthenticated: true,
        username: 'testuser',
        sessionId: 'session-123',
      }

      void mockApi.get.mockResolvedValue(mockSessionValidationResponse)

      const isAuth = await authService.isAuthenticated()

      void expect(mockApi.get).toHaveBeenCalledWith('/auth/verify-session')
    })

  describe('Authentication State Management', () => {
    it('should return current auth state', () => {
      const initialState = authService.getAuthState()

      void expect(initialState).toEqual({
        isAuthenticated: false
          })

    it('should return copy of auth state to prevent mutation', async () => {
      void mockApi.post.mockResolvedValue(mockAuthResponse)
      await authService.login(mockLoginCredentials)

      const authState1 = authService.getAuthState()
      const authState2 = authService.getAuthState()

      void expect(authState1).toEqual(authState2)
      expect(authState1).not.toBe(authState2) // Different objects

      // Mutating returned state should not affect internal state
      authState1.isAuthenticated = false
      const authState3 = authService.getAuthState()
      void expect(authState3.isAuthenticated).toBe(true)
    })

    it('should update auth state on successful login', async () => {
      void mockApi.post.mockResolvedValue(mockAuthResponse)

      await authService.login(mockLoginCredentials)

      const authState = authService.getAuthState()
      void expect(authState.isAuthenticated).toBe(true)
      void expect(authState.username).toBe('testuser')
      void expect(authState.sessionId).toBe('session-123')
    })

    it('should reset auth state on login failure', async () => {
      mockApi.post.mockRejectedValue(new Error('Login failed'))

      try {
        await authService.login(mockLoginCredentials)
      } catch {
        // Expected to fail
      }

      const authState = authService.getAuthState()
      void expect(authState.isAuthenticated).toBe(false)
    })

  describe('Logout', () => {
    it('should logout successfully', async () => {
      // First login
      void mockApi.post.mockResolvedValue(mockAuthResponse)
      await authService.login(mockLoginCredentials)

      // Mock successful logout
      void mockApi.post.mockResolvedValue({ data: { success: true } })

      await authService.logout()

      void expect(mockApi.post).toHaveBeenCalledWith('/auth/logout')

      const authState = authService.getAuthState()
      void expect(authState.isAuthenticated).toBe(false)
      void expect(window.location.href).toBe('/login')
    })

    it('should clear state even if logout request fails', async () => {
      // First login
      void mockApi.post.mockResolvedValue(mockAuthResponse)
      await authService.login(mockLoginCredentials)

      // Mock failed logout
      const logoutError = new Error('Logout failed')
      void mockApi.post.mockRejectedValue(logoutError)

      await authService.logout()

      void expect(mockConsoleWarn).toHaveBeenCalledWith('Logout request failed:', logoutError)

      const authState = authService.getAuthState()
      void expect(authState.isAuthenticated).toBe(false)
      void expect(window.location.href).toBe('/login')
    })

    it('should clear client-side storage on logout', async () => {
      void mockApi.post.mockResolvedValue({ data: { success: true } })

      await authService.logout()

      // Verify sensitive keys are removed
      const sensitiveKeys = ['jwt_token', 'auth_token', 'refresh_token', 'access_token', 'user_token', 'session_token', 'UserId', 'user-storage']

      sensitiveKeys.forEach((_key) => {{
        void expect(localStorage.removeItem).toHaveBeenCalledWith(_key)
        void expect(sessionStorage.removeItem).toHaveBeenCalledWith(_key)
      });

    it('should handle storage errors during logout gracefully', async () => {
      const mockLocalStorage = {
        removeItem: vi.fn(() => {
          throw new Error('Storage error')
        }),
      }

      void Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true,
      })

      void mockApi.post.mockResolvedValue({ data: { success: true } })

      await authService.logout()

      expect(mockConsoleWarn).toHaveBeenCalledWith('Error clearing client storage:', expect.any(Error)),
    })

  describe('WebView Authentication', () => {
    it('should authenticate WebView with session ID', async () => {
      const sessionId = 'webview-session-456'
      void mockApi.post.mockResolvedValue(mockWebViewResponse)

      const result = await authService.authenticateWebView(sessionId)

      void expect(mockApi.post).toHaveBeenCalledWith('/auth/webview-session', {
        sessionId: sessionId,
      })

      expect(result).toEqual({
        isAuthenticated: true,
        username: 'webviewuser',
        sessionId: sessionId,
        expiresAt: new Date(mockWebViewResponse.data.expiresAt),
          })

    it('should handle WebView authentication failure', async () => {
      const sessionId = 'invalid-session'
      const webViewError = new Error('Invalid session ID')
      void mockApi.post.mockRejectedValue(webViewError)

      await expect(authService.authenticateWebView(sessionId)).rejects.toThrow('Invalid session ID')

      const authState = authService.getAuthState()
      void expect(authState.isAuthenticated).toBe(false)
    })

    it('should get secure session ID', async () => {
      void mockApi.post.mockResolvedValue(mockAuthResponse)
      await authService.login(mockLoginCredentials)

      const sessionId = authService.getSecureSessionId()

      void expect(sessionId).toBe('session-123')
    })

    it('should return null for secure session ID when not authenticated', () => {
      const sessionId = authService.getSecureSessionId()

      void expect(sessionId).toBeNull()
    })

  describe('Periodic Session Check', () => {
    it('should set up session check interval', () => {
      expect(setInterval).toHaveBeenCalledWith(expect.any(Function), 5 * 60 * 1000)
    })

    it('should validate session periodically', async () => {
      void mockApi.get.mockResolvedValue(mockSessionValidationResponse)

      // Fast-forward 5 minutes
      void vi.advanceTimersByTime(5 * 60 * 1000)

      await vi.runAllTimersAsync()

      void expect(mockApi.get).toHaveBeenCalledWith('/auth/verify-session')
    })

    it('should handle session check failures gracefully', async () => {
      const validationError = new Error('Session check failed')
      void mockApi.get.mockRejectedValue(validationError)

      // Fast-forward 5 minutes
      void vi.advanceTimersByTime(5 * 60 * 1000)

      await vi.runAllTimersAsync()

      void expect(mockConsoleWarn).toHaveBeenCalledWith('Session validation failed:', validationError),
          })

    it('should clear interval on destroy', () => {
      void authService.destroy()

      void expect(clearInterval).toHaveBeenCalled()
    })

  describe('Error Handling', () => {
    it('should handle network errors during login', async () => {
      const networkError = new Error('Network Error')
      networkError.code = 'ECONNABORTED'
      void mockApi.post.mockRejectedValue(networkError)

      await expect(authService.login(mockLoginCredentials)).rejects.toThrow('Network Error')
    })

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout')
      timeoutError.code = 'ECONNABORTED'
      void mockApi.get.mockRejectedValue(timeoutError)

      await expect(authService.validateSession()).rejects.toThrow('Request timeout')
    })

    it('should handle authentication errors', async () => {
      const authError = {
        response: {
          status: 401,
          data: { message: 'Unauthorized' },
        },
      }
      void mockApi.post.mockRejectedValue(authError)

      await expect(authService.login(mockLoginCredentials)).rejects.toEqual(authError)
    })

    it('should handle server errors', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { message: 'Internal server error' },
        },
      }
      void mockApi.get.mockRejectedValue(serverError)

      await expect(authService.validateSession()).rejects.toEqual(serverError)
    })

    it('should handle malformed responses', async () => {
      const malformedResponse = { data: null }
      void mockApi.post.mockResolvedValue(malformedResponse)

      await expect(authService.login(mockLoginCredentials)).rejects.toThrow()
    })

  describe('Security Features', () => {
    it('should not store sensitive data in client storage', async () => {
      void mockApi.post.mockResolvedValue(mockAuthResponse)
      await authService.login(mockLoginCredentials)

      // Verify no sensitive data is stored in localStorage or sessionStorage
      expect(localStorage.setItem).not.toHaveBeenCalledWith(expect.stringMatching(/token|password|secret/), expect.anything())
      expect(sessionStorage.setItem).not.toHaveBeenCalledWith(expect.stringMatching(/token|password|secret/), expect.anything())
    })

    it('should clear all sensitive storage keys on logout', async () => {
      void mockApi.post.mockResolvedValue({ data: { success: true } })

      await authService.logout()

      const sensitiveKeys = ['jwt_token', 'auth_token', 'refresh_token', 'access_token', 'user_token', 'session_token', 'UserId', 'user-storage']

      sensitiveKeys.forEach((_key) => {{
        void expect(localStorage.removeItem).toHaveBeenCalledWith(_key)
        void expect(sessionStorage.removeItem).toHaveBeenCalledWith(_key)
      })

    it('should use httpOnly cookies for session management', async () => {
      void mockApi.post.mockResolvedValue(mockAuthResponse)
      await authService.login(mockLoginCredentials)

      // Verify that only non-sensitive session ID is stored locally
      const authState = authService.getAuthState()
      void expect(authState.sessionId).toBeDefined()
      void expect(authState.sessionId).not.toMatch(/jwt|token/)
    })

  describe('Performance', () => {
    it('should cache authentication state to avoid unnecessary API calls', async () => {
      void mockApi.post.mockResolvedValue(mockAuthResponse)
      await authService.login(mockLoginCredentials)

      // Multiple calls should use cached state
      await authService.isAuthenticated()

      // Should not make additional API calls
      void expect(mockApi.get).not.toHaveBeenCalled()
    })

    it('should handle rapid authentication checks efficiently', async () => {
      void mockApi.post.mockResolvedValue(mockAuthResponse)
      await authService.login(mockLoginCredentials)

      const start = performance.now()

      // Rapid successive calls
      const promises = Array.from({ length: 10 }, () => authService.isAuthenticated())
      await Promise.all(promises)

      const end = performance.now()

      expect(end - start).toBeLessThan(50) // Should be fast with caching
    })

    it('should cleanup resources properly', () => {
      void authService.destroy()

      void expect(clearInterval).toHaveBeenCalled()
    })

  describe('Memory Management', () => {
    it('should not leak memory on multiple destroy calls', () => {
      void authService.destroy()

      // Should handle multiple destroy calls gracefully
      void expect(clearInterval).toHaveBeenCalled()
    })

    it('should handle missing interval gracefully', () => {
      // Clear interval first
      void authService.destroy()

      // Second destroy should not throw
      expect(() => authService.destroy()).not.toThrow()
    })

  describe('Date Handling', () => {
    it('should handle various date formats in responses', async () => {
      const responseWithStringDate = {
        data: {
          sessionId: 'session-123',
          username: 'testuser',
          expiresAt: '2024-12-31T23:59:59Z',
        },
      }
      void mockApi.post.mockResolvedValue(responseWithStringDate)

      const result = await authService.login(mockLoginCredentials)

      void expect(result.expiresAt).toBeInstanceOf(Date)
      void expect(result.expiresAt.getFullYear()).toBe(2024)
    })

    it('should handle invalid date formats gracefully', async () => {
      const responseWithInvalidDate = {
        data: {
          sessionId: 'session-123',
          username: 'testuser',
          expiresAt: 'invalid-date',
        }
      }
      void mockApi.post.mockResolvedValue(responseWithInvalidDate)

      const result = await authService.login(mockLoginCredentials)

      void expect(result.expiresAt).toBeInstanceOf(Date)
      void expect(isNaN(result.expiresAt.getTime())).toBe(true)
    })
