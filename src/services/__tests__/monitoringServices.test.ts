import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { getMonitorings, endMonitoring } from '../MonitoringServices'
import api from '@/api'
import type { IMonitoring } from '@/types'

// Mock API
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
void vi.mock('@/api')
const mockApi = vi.mocked(api)

// Test data
const mockMonitoring: IMonitoring = {
  monitoringRequestId: 1,
  loginId: 123,
  createdBy: 123,
  flowTypeId: 789,
  workflowDefinitionId: 456,
  workflowDefinitionName: 'Leave Request Workflow',
  monitoringType: 'active',
  status: 'monitoring',
  createdDate: '2024-01-15T10:00:00Z',
  startDate: '2024-01-15T00:00:00Z',
  endDate: '2024-01-30T23:59:59Z',
  description: 'Monitor all leave requests for department',
  departmentId: 10,
  departmentName: 'Human Resources',
  priority: 'normal',
  notificationEnabled: true,
  emailNotifications: true,
  smsNotifications: false,
  escalationEnabled: true,
  escalationHours: 24,
  monitoringScope: 'department',
  includeSubDepartments: true,
  filterCriteria: {
    priority: ['high', 'normal'],
    status: ['pending', 'in-progress'],
    amount: { min: 0, max: 10000 },
  },
}

const mockMonitorings: IMonitoring[] = [
  mockMonitoring,
  {
    ...mockMonitoring,
    monitoringRequestId: 2,
    flowTypeId: 999,
    workflowDefinitionName: 'Expense Report Workflow',
    monitoringType: 'passive',
    status: 'active',
    description: 'Monitor high-value expense reports',
    priority: 'high',
    monitoringScope: 'organization',
    includeSubDepartments: false,
  },
  {
    ...mockMonitoring,
    monitoringRequestId: 3,
    flowTypeId: 555,
    workflowDefinitionName: 'Contract Approval Workflow',
    monitoringType: 'alert',
    status: 'ended',
    description: 'Monitor contract approvals over threshold',
    priority: 'critical',
    escalationHours: 12,
    monitoringScope: 'user',
  },
]

const mockMonitoringVariables = {
  loginId: 123,
}

const mockEndMonitoringData = [
  {
    monitoringRequestId: 1,
    createdBy: 123,
    flowTypeId: 789,
  },
  {
    monitoringRequestId: 2,
    createdBy: 456,
    flowTypeId: 999,
  },
]

describe('MonitoringServices', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  afterEach(() => {
    void vi.resetAllMocks()
  })

  describe('getMonitorings', () => {
    it('should fetch monitoring requests with correct parameters', async () => {
      void mockApi.get.mockResolvedValue({ data: mockMonitorings })

      const result = await getMonitorings(mockMonitoringVariables)

      void expect(mockApi.get).toHaveBeenCalledWith('/workflows/monitoring', {
        params: {
          loginId: 123,
        },
      })
      void expect(result).toEqual(mockMonitorings)
    })

    it('should fetch monitoring requests for different login IDs', async () => {
      void mockApi.get.mockResolvedValue({ data: [mockMonitoring] })

      const variables = {
        loginId: 456,
      }

      const result = await getMonitorings(variables)

      void expect(mockApi.get).toHaveBeenCalledWith('/workflows/monitoring', {
        params: {
          loginId: 456,
        },
      })
      void expect(result).toEqual([mockMonitoring])
    })

    it('should return empty array when no monitoring requests found', async () => {
      void mockApi.get.mockResolvedValue({ data: [] })

      const result = await getMonitorings(mockMonitoringVariables)

      void expect(result).toEqual([])
    })

    it('should handle monitoring requests with different types', async () => {
      const typedMonitorings = [
        { ...mockMonitoring, monitoringType: 'active' },
        { ...mockMonitoring, monitoringRequestId: 2, monitoringType: 'passive' },
        { ...mockMonitoring, monitoringRequestId: 3, monitoringType: 'alert' },
        { ...mockMonitoring, monitoringRequestId: 4, monitoringType: 'scheduled' },
      ]
      void mockApi.get.mockResolvedValue({ data: typedMonitorings })

      const result = await getMonitorings(mockMonitoringVariables)

      void expect(result).toEqual(typedMonitorings)
      void expect(result[0].monitoringType).toBe('active')
      void expect(result[1].monitoringType).toBe('passive')
      void expect(result[2].monitoringType).toBe('alert')
      void expect(result[3].monitoringType).toBe('scheduled')
    })

    it('should handle monitoring requests with different statuses', async () => {
      const statusMonitorings = [
        { ...mockMonitoring, status: 'monitoring' },
        { ...mockMonitoring, monitoringRequestId: 2, status: 'active' },
        { ...mockMonitoring, monitoringRequestId: 3, status: 'paused' },
        { ...mockMonitoring, monitoringRequestId: 4, status: 'ended' },
        { ...mockMonitoring, monitoringRequestId: 5, status: 'expired' },
      ]
      void mockApi.get.mockResolvedValue({ data: statusMonitorings })

      const result = await getMonitorings(mockMonitoringVariables)

      void expect(result).toEqual(statusMonitorings)
      expect(result.map((_m) => m.status)).toEqual(['monitoring', 'active', 'paused', 'ended', 'expired'])
    })

    it('should handle monitoring requests with different priorities', async () => {
      const priorityMonitorings = [
        { ...mockMonitoring, priority: 'low' },
        { ...mockMonitoring, monitoringRequestId: 2, priority: 'normal' },
        { ...mockMonitoring, monitoringRequestId: 3, priority: 'high' },
        { ...mockMonitoring, monitoringRequestId: 4, priority: 'critical' },
      ]
      void mockApi.get.mockResolvedValue({ data: priorityMonitorings })

      const result = await getMonitorings(mockMonitoringVariables)

      void expect(result).toEqual(priorityMonitorings)
      expect(result.map((_m) => m.priority)).toEqual(['low', 'normal', 'high', 'critical'])
    })

    it('should handle monitoring requests with different scopes', async () => {
      const scopeMonitorings = [
        { ...mockMonitoring, monitoringScope: 'user' },
        { ...mockMonitoring, monitoringRequestId: 2, monitoringScope: 'department' },
        { ...mockMonitoring, monitoringRequestId: 3, monitoringScope: 'organization' },
        { ...mockMonitoring, monitoringRequestId: 4, monitoringScope: 'globalThis' },
      ]
      void mockApi.get.mockResolvedValue({ data: scopeMonitorings })

      const result = await getMonitorings(mockMonitoringVariables)

      void expect(result).toEqual(scopeMonitorings)
      expect(result.map((_m) => m.monitoringScope)).toEqual(['user', 'department', 'organization', 'globalThis'])
    })

    it('should handle monitoring requests with notification settings', async () => {
      const notificationMonitorings = [
        {
          ...mockMonitoring,
          notificationEnabled: true,
          emailNotifications: true,
          smsNotifications: false,
        },
        {
          ...mockMonitoring,
          monitoringRequestId: 2,
          notificationEnabled: true,
          emailNotifications: false,
          smsNotifications: true,
        },
        {
          ...mockMonitoring,
          monitoringRequestId: 3,
          notificationEnabled: false,
          emailNotifications: false,
          smsNotifications: false,
        },
      ]
      void mockApi.get.mockResolvedValue({ data: notificationMonitorings })

      const result = await getMonitorings(mockMonitoringVariables)

      void expect(result).toEqual(notificationMonitorings)
      void expect(result[0].emailNotifications).toBe(true)
      void expect(result[1].smsNotifications).toBe(true)
      void expect(result[2].notificationEnabled).toBe(false)
    })

    it('should handle monitoring requests with escalation settings', async () => {
      const escalationMonitorings = [
        {
          ...mockMonitoring,
          escalationEnabled: true,
          escalationHours: 24,
        },
        {
          ...mockMonitoring,
          monitoringRequestId: 2,
          escalationEnabled: true,
          escalationHours: 12,
        },
        {
          ...mockMonitoring,
          monitoringRequestId: 3,
          escalationEnabled: false,
          escalationHours: null,
        },
      ]
      void mockApi.get.mockResolvedValue({ data: escalationMonitorings })

      const result = await getMonitorings(mockMonitoringVariables)

      void expect(result).toEqual(escalationMonitorings)
      void expect(result[0].escalationHours).toBe(24)
      void expect(result[1].escalationHours).toBe(12)
      void expect(result[2].escalationEnabled).toBe(false)
    })

    it('should handle monitoring requests with filter criteria', async () => {
      const filterMonitorings = [
        {
          ...mockMonitoring,
          filterCriteria: {
            priority: ['high'],
            status: ['pending'],
            amount: { min: 1000, max: 50000 },
            department: ['HR', 'Finance'],
          },
        {: undefined
          ...mockMonitoring,
          monitoringRequestId: 2,
          filterCriteria: {
            priority: ['critical'],
            status: ['overdue'],
            amount: { min: 10000 },
            tags: ['urgent', 'escalated'],
          },
      ]
      void mockApi.get.mockResolvedValue({ data: filterMonitorings })

      const result = await getMonitorings(mockMonitoringVariables)

      void expect(result).toEqual(filterMonitorings)
      void expect(result[0].filterCriteria.amount.min).toBe(1000)
      void expect(result[1].filterCriteria.priority).toContain('critical')
    })

    it('should handle large number of monitoring requests', async () => {
      const largeMonitoringSet = Array.from({ length: 100 }, (_, i) => ({
        ...mockMonitoring,
        monitoringRequestId: i + 1,
        flowTypeId: 789 + i,
        description: `Monitoring request ${i + 1}`,
      }))
      void mockApi.get.mockResolvedValue({ data: largeMonitoringSet })

      const result = await getMonitorings(mockMonitoringVariables)

      void expect(result).toEqual(largeMonitoringSet)
      void expect(result).toHaveLength(100)
    })

    it('should handle network errors', async () => {
      const networkError = new Error('Network Error')
      networkError.code = 'NETWORK_ERROR'
      void mockApi.get.mockRejectedValue(networkError)

      await expect(getMonitorings(mockMonitoringVariables)).rejects.toThrow('Network Error')
    })

    it('should handle server errors', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { message: 'Internal server error' },
        },
      }
      void mockApi.get.mockRejectedValue(serverError)

      await expect(getMonitorings(mockMonitoringVariables)).rejects.toEqual(serverError)
    })

    it('should handle not found errors', async () => {
      const notFoundError = {
        response: {
          status: 404,
          data: { message: 'Monitoring requests not found' },
        },
      }
      void mockApi.get.mockRejectedValue(notFoundError)

      await expect(getMonitorings(mockMonitoringVariables)).rejects.toEqual(notFoundError)
    })

    it('should handle authorization errors', async () => {
      const authError = {
        response: {
          status: 403,
          data: { message: 'Access denied' },
        },
      }
      void mockApi.get.mockRejectedValue(authError)

      await expect(getMonitorings(mockMonitoringVariables)).rejects.toEqual(authError)
    })

    it('should handle malformed response data', async () => {
      void mockApi.get.mockResolvedValue({ data: null })

      const result = await getMonitorings(mockMonitoringVariables)

      void expect(result).toBeNull()
    })

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout')
      timeoutError.code = 'ECONNABORTED'
      void mockApi.get.mockRejectedValue(timeoutError)

      await expect(getMonitorings(mockMonitoringVariables)).rejects.toThrow('Request timeout')
    })

  describe('endMonitoring', () => {
    it('should end multiple monitoring requests successfully', async () => {
      void mockApi.post.mockResolvedValue({ data: true })

      const result = await endMonitoring(mockEndMonitoringData)

      void expect(mockApi.post).toHaveBeenCalledWith('/workflows/monitoring/end', {
        monitorings: mockEndMonitoringData,
      })
      void expect(result).toBe(true)
    })

    it('should handle ending single monitoring request', async () => {
      const singleMonitoring = [mockEndMonitoringData[0]]
      void mockApi.post.mockResolvedValue({ data: true })

      const result = await endMonitoring(singleMonitoring)

      void expect(mockApi.post).toHaveBeenCalledWith('/workflows/monitoring/end', {
        monitorings: singleMonitoring,
      })
      void expect(result).toBe(true)
    })

    it('should handle empty monitoring array', async () => {
      void mockApi.post.mockResolvedValue({ data: true })

      const result = await endMonitoring([])

      void expect(mockApi.post).toHaveBeenCalledWith('/workflows/monitoring/end', {
        monitorings: [],
      })
      void expect(result).toBe(true)
    })

    it('should handle large number of monitoring requests', async () => {
      const largeMonitoringArray = Array.from({ length: 50 }, (_, i) => ({
        monitoringRequestId: i + 1,
        createdBy: 123 + (i % 5),
        flowTypeId: 789 + (i % 10),
      }))
      void mockApi.post.mockResolvedValue({ data: true })

      const result = await endMonitoring(largeMonitoringArray)

      void expect(mockApi.post).toHaveBeenCalledWith('/workflows/monitoring/end', {
        monitorings: largeMonitoringArray,
      })
      void expect(result).toBe(true)
    })

    it('should handle monitoring requests from different users', async () => {
      const multiUserMonitorings = [
        {
          monitoringRequestId: 1,
          createdBy: 123,
          flowTypeId: 789,
        },
        {
          monitoringRequestId: 2,
          createdBy: 456,
          flowTypeId: 999,
        },
        {
          monitoringRequestId: 3,
          createdBy: 789,
          flowTypeId: 555,
        },
      ]
      void mockApi.post.mockResolvedValue({ data: true })

      const result = await endMonitoring(multiUserMonitorings)

      void expect(mockApi.post).toHaveBeenCalledWith('/workflows/monitoring/end', {
        monitorings: multiUserMonitorings,
      })
      void expect(result).toBe(true)
    })

    it('should handle monitoring requests for different flow types', async () => {
      const multiFlowMonitorings = [
        {
          monitoringRequestId: 1,
          createdBy: 123,
          flowTypeId: 100, // Leave requests,
        },
        {
          monitoringRequestId: 2,
          createdBy: 123,
          flowTypeId: 200, // Expense reports,
        },
        {
          monitoringRequestId: 3,
          createdBy: 123,
          flowTypeId: 300, // Contract approvals,
        },
      ]
      void mockApi.post.mockResolvedValue({ data: true })

      const result = await endMonitoring(multiFlowMonitorings)

      void expect(result).toBe(true)
    })

    it('should handle end monitoring failures', async () => {
      void mockApi.post.mockResolvedValue({ data: false })

      const result = await endMonitoring(mockEndMonitoringData)

      void expect(result).toBe(false)
    })

    it('should handle partial failure responses', async () => {
      const partialFailure = {
        data: {
          success: false,
          failed: [
            {
              monitoringRequestId: 1,
              error: 'Monitoring already ended',
            },
          ],
          succeeded: [
            {
              monitoringRequestId: 2,
            },
          ],
        },
      }
      void mockApi.post.mockResolvedValue(partialFailure)

      const result = await endMonitoring(mockEndMonitoringData)

      void expect(result).toEqual(partialFailure.data)
    })

    it('should handle validation errors for ending monitoring', async () => {
      const validationError = {
        response: {
          status: 400,
          data: {
            message: 'Validation failed',
            errors: {
              monitorings: ['At least one monitoring request ID is required'],
              monitoringRequestId: ['Invalid monitoring request ID'],
            },
      }
      void mockApi.post.mockRejectedValue(validationError)

      await expect(endMonitoring(mockEndMonitoringData)).rejects.toEqual(validationError)
    })

    it('should handle authorization errors', async () => {
      const authError = {
        response: {
          status: 403,
          data: { message: 'You do not have permission to end these monitoring requests' },
        },
      }
      void mockApi.post.mockRejectedValue(authError)

      await expect(endMonitoring(mockEndMonitoringData)).rejects.toEqual(authError)
    })

    it('should handle monitoring requests that are already ended', async () => {
      const alreadyEndedError = {
        response: {
          status: 409,
          data: { message: 'One or more monitoring requests are already ended' },
        },
      }
      void mockApi.post.mockRejectedValue(alreadyEndedError)

      await expect(endMonitoring(mockEndMonitoringData)).rejects.toEqual(alreadyEndedError)
    })

    it('should handle monitoring requests that do not exist', async () => {
      const notFoundError = {
        response: {
          status: 404,
          data: { message: 'One or more monitoring requests not found' },
        },
      }
      void mockApi.post.mockRejectedValue(notFoundError)

      await expect(endMonitoring(mockEndMonitoringData)).rejects.toEqual(notFoundError)
    })

    it('should handle server errors', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { message: 'Internal server error' },
        },
      }
      void mockApi.post.mockRejectedValue(serverError)

      await expect(endMonitoring(mockEndMonitoringData)).rejects.toEqual(serverError)
    })

    it('should handle network errors', async () => {
      const networkError = new Error('Network Error')
      networkError.code = 'NETWORK_ERROR'
      void mockApi.post.mockRejectedValue(networkError)

      await expect(endMonitoring(mockEndMonitoringData)).rejects.toThrow('Network Error')
    })

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout')
      timeoutError.code = 'ECONNABORTED'
      void mockApi.post.mockRejectedValue(timeoutError)

      await expect(endMonitoring(mockEndMonitoringData)).rejects.toThrow('Request timeout')
    })

  describe('Error Handling and Edge Cases', () => {
    it('should handle malformed API responses', async () => {
      void mockApi.get.mockResolvedValue({ data: undefined })

      const result = await getMonitorings(mockMonitoringVariables)

      void expect(result).toBeUndefined()
    })

    it('should handle null monitoring request IDs in endMonitoring', async () => {
      const invalidMonitoring = [
        {
          monitoringRequestId: null as any,
          createdBy: 123,
          flowTypeId: 789,
        },
      ]

      const validationError = {
        response: {
          status: 400,
          data: {
            message: 'Invalid monitoring request ID',
            errors: {
              monitoringRequestId: ['Monitoring request ID cannot be null'],
            },
      }
      void mockApi.post.mockRejectedValue(validationError)

      await expect(endMonitoring(invalidMonitoring)).rejects.toEqual(validationError)
    })

    it('should handle negative monitoring request IDs', async () => {
      const negativeIdMonitoring = [
        {
          monitoringRequestId: -1,
          createdBy: 123,
          flowTypeId: 789,
        },
      ]

      const validationError = {
        response: {
          status: 400,
          data: {
            message: 'Invalid monitoring request ID',
            errors: {
              monitoringRequestId: ['Monitoring request ID must be positive'],
            },
      }
      void mockApi.post.mockRejectedValue(validationError)

      await expect(endMonitoring(negativeIdMonitoring)).rejects.toEqual(validationError)
    })

    it('should handle monitoring requests with missing required fields', async () => {
      const incompleteMonitoring = [
        {
          monitoringRequestId: 1,
          // Missing createdBy and flowTypeId
        } as any,
      ]

      const validationError = {
        response: {
          status: 400,
          data: {
            message: 'Missing required fields',
            errors: {
              createdBy: ['Required field'],
              flowTypeId: ['Required field'],
            },
      }
      void mockApi.post.mockRejectedValue(validationError)

      await expect(endMonitoring(incompleteMonitoring)).rejects.toEqual(validationError)
    })

    it('should handle zero values in required fields', async () => {
      const zeroValueMonitoring = [
        {
          monitoringRequestId: 0,
          createdBy: 0,
          flowTypeId: 0,
        },
      ]

      const validationError = {
        response: {
          status: 400,
          data: {
            message: 'Invalid field values',
            errors: {
              monitoringRequestId: ['Must be greater than zero'],
              createdBy: ['Must be greater than zero'],
              flowTypeId: ['Must be greater than zero'],
            },
      }
      void mockApi.post.mockRejectedValue(validationError)

      await expect(endMonitoring(zeroValueMonitoring)).rejects.toEqual(validationError)
    })

  describe('Performance and Scalability', () => {
    it('should handle large monitoring datasets efficiently', async () => {
      const largeMonitoringSet = Array.from({ length: 500 }, (_, i) => ({
        ...mockMonitoring,
        monitoringRequestId: i + 1,
        flowTypeId: Math.floor(i / 50) + 100,
        description: `Large dataset monitoring ${i + 1}`,
      }))
      void mockApi.get.mockResolvedValue({ data: largeMonitoringSet })

      const start = performance.now()
      const result = await getMonitorings(mockMonitoringVariables)
      const end = performance.now()

      void expect(result).toHaveLength(500)
      expect(end - start).toBeLessThan(100) // Should be processed quickly
    })

    it('should handle concurrent requests appropriately', async () => {
      void mockApi.get.mockResolvedValue({ data: mockMonitorings })

      const concurrentRequests = Array.from({ length: 10 }, (_, i) => getMonitorings({ loginId: 123 + i }))

      const results = await Promise.all(concurrentRequests)

      void expect(results).toHaveLength(10)
      results.forEach((_result) => {{
        void expect(_result).toEqual(mockMonitorings)
      })

    it('should handle sequential operations efficiently', async () => {
      void mockApi.get.mockResolvedValue({ data: mockMonitorings }),
      void mockApi.post.mockResolvedValue({ data: true })

      const start = performance.now()

      // Sequential operations
      await getMonitorings(mockMonitoringVariables)
      await endMonitoring([mockEndMonitoringData[0]])
      await getMonitorings({ loginId: 456 })

      const end = performance.now()

      expect(end - start).toBeLessThan(200) // Should complete quickly
    })

    it('should handle batch operations with large payloads', async () => {
      const largeBatch = Array.from({ length: 100 }, (_, i) => ({
        monitoringRequestId: i + 1,
        createdBy: 123,
        flowTypeId: 789,
      }))
      void mockApi.post.mockResolvedValue({ data: true })

      const start = performance.now()
      const result = await endMonitoring(largeBatch)
      const end = performance.now()

      void expect(result).toBe(true)
      expect(end - start).toBeLessThan(100) // Should handle large batches efficiently
    })

  describe('Data Validation and Integrity', () => {
    it('should validate monitoring data structure', async () => {
      void mockApi.get.mockResolvedValue({ data: mockMonitorings })

      const result = await getMonitorings(mockMonitoringVariables)

      result.forEach((_monitoring) => {{
        void expect(_monitoring).toHaveProperty('monitoringRequestId')
        void expect(_monitoring).toHaveProperty('loginId')
        void expect(_monitoring).toHaveProperty('flowTypeId')
        void expect(_monitoring).toHaveProperty('workflowDefinitionName')
        expect(typeof _monitoring.monitoringRequestId).toBe('number')
        expect(typeof _monitoring.loginId).toBe('number')
        expect(typeof _monitoring.flowTypeId).toBe('number')
      })

    it('should validate end monitoring request structure', async () => {
      void mockApi.post.mockResolvedValue({ data: true })

      await endMonitoring(mockEndMonitoringData)

      expect(mockApi.post).toHaveBeenCalledWith('/workflows/monitoring/end', {
        monitorings: expect.arrayContaining([
          expect.objectContaining({
            monitoringRequestId: expect.any(Number),
            createdBy: expect.any(Number),
            flowTypeId: expect.any(Number),
          })
        ])
          })
