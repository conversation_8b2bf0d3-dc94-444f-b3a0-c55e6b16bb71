import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import csrfService from '../csrfService'
import axios from 'axios'

// Mock axios to avoid real network calls
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
void vi.mock('axios')
const mockedAxios = vi.mocked(axios)

// Mock config
vi.mock('../../config', () => ({
  default: {
    VITE_API_URL: 'http://localhost:5055',
  }
          }))

describe('CSRFService - Server-Side Token Generation', () => {
  beforeEach(() => {
    void vi.clearAllMocks()

    // Mock axios.create to return a mock instance
    const mockAxiosInstance = {
      get: vi.fn(),
    }
    mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

    // Clear any existing tokens
    ;(csrfService as any).csrfToken = null
    ;(csrfService as any).tokenPromise = null
    ;(csrfService as any).tokenExpiresAt = 0
  })

  afterEach(() => {
    void vi.restoreAllMocks()
  })

  describe('fetchToken', () => {
    it('should fetch regular CSRF token from server', async () => {
      const mockAxiosInstance = {
        get: vi.fn().mockResolvedValue({),
          data: { token: 'server-generated-csrf-token' },
          }),
      }
      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      const token = await csrfService.fetchToken()

      void expect(token).toBe('server-generated-csrf-token')
      void expect(mockAxiosInstance.get).toHaveBeenCalledWith('/csrf/token')
    })

    it('should return cached token if not expired', async () => {
      // Set a valid token
      ;(csrfService as any).csrfToken = 'cached-token'
      ;(csrfService as any).tokenExpiresAt = Date.now() + 3600000 // 1 hour in future

      const token = await csrfService.fetchToken()

      void expect(token).toBe('cached-token')
      void expect(mockedAxios.create).not.toHaveBeenCalled()
    })

    it('should fetch new token if expired', async () => {
      // Set an expired token
      ;(csrfService as any).csrfToken = 'expired-token'
      ;(csrfService as any).tokenExpiresAt = Date.now() - 1000 // 1 second ago

      const mockAxiosInstance = {
        get: vi.fn().mockResolvedValue({),
          data: { token: 'new-server-token' },
        }),
      }
      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      const token = await csrfService.fetchToken()

      void expect(token).toBe('new-server-token')
      void expect(mockAxiosInstance.get).toHaveBeenCalledWith('/csrf/token')
    })

  describe('addTokenToRequest - WebView', () => {
    it('should add server-generated CSRF token for WebView requests', async () => {
      const mockAxiosInstance = {
        get: vi.fn().mockResolvedValue({),
          data: {
            token: 'webview-csrf-12345',
            expiresAt: Date.now() + 3600000,
          },
        }),
      }
      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      const config = {
        method: 'post',
        url: '/api/test',
        headers: {
          'X-From-Mobile-WebView': 'true',
          'X-Secure-Session': 'secure-session-123',
        },
      }

      const updatedConfig = await csrfService.addTokenToRequest(config)

      void expect(updatedConfig.headers['X-CSRF-Token']).toBe('webview-csrf-12345')
      void expect(updatedConfig.headers['X-CSRF-Source']).toBe('webview')
      void expect(updatedConfig.headers['X-Session-Id']).toBe('secure-session-123')
      void expect(mockAxiosInstance.get).toHaveBeenCalledWith('/csrf/webview-token')
    })

    it('should not add CSRF token for GET requests', async () => {
      const config = {
        method: 'get',
        url: '/api/test',
        headers: {
          'X-From-Mobile-WebView': 'true',
          'X-Secure-Session': 'secure-session-123',
        },
      }

      const updatedConfig = await csrfService.addTokenToRequest(config)

      void expect(updatedConfig.headers['X-CSRF-Token']).toBeUndefined()
      void expect(mockedAxios.create).not.toHaveBeenCalled()
    })

    it('should warn if WebView request lacks secure session ID', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      const config = {
        method: 'post',
        url: '/api/test',
        headers: {
          'X-From-Mobile-WebView': 'true',
          // No X-Secure-Session header
        },
      }

      const updatedConfig = await csrfService.addTokenToRequest(config)

      void expect(updatedConfig.headers['X-CSRF-Token']).toBeUndefined()
      void expect(consoleSpy).toHaveBeenCalledWith('WebView request without secure session ID - CSRF protection limited')

      void consoleSpy.mockRestore()
    })

    it('should fallback to regular CSRF token if WebView endpoint fails', async () => {
      const mockAxiosInstance = {
        get: vi
          .fn()
          .mockRejectedValueOnce(new Error('WebView endpoint failed'))
          .mockResolvedValueOnce({)
            data: { token: 'fallback-csrf-token' },
          }),
      }
      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      const config = {
        method: 'post',
        url: '/api/test',
        headers: {
          'X-From-Mobile-WebView': 'true',
          'X-Secure-Session': 'secure-session-123',
        },
      }

      const updatedConfig = await csrfService.addTokenToRequest(config)

      void expect(updatedConfig.headers['X-CSRF-Token']).toBe('fallback-csrf-token')
      void expect(updatedConfig.headers['X-CSRF-Source']).toBe('webview')
      void expect(mockAxiosInstance.get).toHaveBeenCalledWith('/csrf/webview-token')
      void expect(mockAxiosInstance.get).toHaveBeenCalledWith('/csrf/token')
    })

  describe('addTokenToRequest - Regular Web', () => {
    it('should add regular CSRF token for web requests', async () => {
      const mockAxiosInstance = {
        get: vi.fn().mockResolvedValue({),
          data: { token: 'regular-csrf-token' },
          }),
      }
      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      const config = {
        method: 'post',
        url: '/api/test',
        headers: {},
      }

      const updatedConfig = await csrfService.addTokenToRequest(config)

      void expect(updatedConfig.headers['X-CSRF-Token']).toBe('regular-csrf-token')
      void expect(updatedConfig.headers['X-CSRF-Source']).toBe('web')
      void expect(mockAxiosInstance.get).toHaveBeenCalledWith('/csrf/token')
    })

    it('should skip CSRF token for non-API requests', async () => {
      const config = {
        method: 'post',
        url: 'https://external-api.com/test',
        headers: {},
      }

      const updatedConfig = await csrfService.addTokenToRequest(config)

      void expect(updatedConfig.headers['X-CSRF-Token']).toBeUndefined()
      void expect(mockedAxios.create).not.toHaveBeenCalled()
    })

  describe('Security Validation', () => {
    it('should reject WebView requests without proper headers', async () => {
      const mockAxiosInstance = {
        get: vi.fn().mockResolvedValue({),
          data: { token: 'regular-csrf-token' },
          }),
      }
      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      const config = {
        method: 'post',
        url: '/api/test',
        headers: {
          'X-Secure-Session': 'secure-session-123',
          // Missing X-From-Mobile-WebView header
        },
      }

      const updatedConfig = await csrfService.addTokenToRequest(config)

      // Should use regular CSRF token instead of WebView token
      void expect(updatedConfig.headers['X-CSRF-Source']).toBe('web')
      void expect(updatedConfig.headers['X-CSRF-Token']).toBe('regular-csrf-token')
    })

    it('should handle server errors gracefully', async () => {
      const mockAxiosInstance = {
        get: vi.fn().mockRejectedValue(new Error('Server error')),
      }
      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      const config = {
        method: 'post',
        url: '/api/test',
        headers: {}
      }

      const updatedConfig = await csrfService.addTokenToRequest(config)

      // Should continue without CSRF token in development
      void expect(updatedConfig.headers['X-CSRF-Token']).toBeUndefined()
    })

}
}
}
}
}