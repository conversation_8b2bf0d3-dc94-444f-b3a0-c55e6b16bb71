import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import InboxServices from '../InboxServices'
import api from '@/api'

// Mock API
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
void vi.mock('@/api')
const mockApi = vi.mocked(api)

// Mock stores
vi.mock('@/stores/userStore', () => ({
  useUserStore: {
    getState: () => ({
      selectedUser: { value: 'test-user-001' },
    }),
  },
}))

// Mock date utilities
vi.mock('@/utils/dateUtils', () => ({
  formatDate: (date: string) => new Date(date).toLocaleDateString(),
  isOverdue: (date: string) => new Date(date) < new Date(),
  calculateDaysRemaining: (date: string) => Math.ceil((new Date(date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)),
}))

// Test data
const mockInboxItem = {
  id: 'inbox-001',
  workflowId: 'wf-inst-001',
  workflowTitle: 'Leave Request - John Doe',
  workflowType: 'Leave Request',
  definitionId: 'wf-def-001',
  requestorId: 'user-001',
  requestorName: 'John Doe',
  requestorDepartment: 'Engineering',
  currentStepId: 'step-2',
  currentStepName: 'Manager Approval',
  assigneeId: 'manager-001',
  assigneeName: 'Jane Manager',
  status: 'pending',
  priority: 'normal',
  createdDate: '2024-01-15T10:30:00Z',
  updatedDate: '2024-01-16T14:20:00Z',
  dueDate: '2024-01-20T17:00:00Z',
  isOverdue: false,
  hasAttachments: true,
  attachmentCount: 2,
  unreadComments: 1,
  tags: ['urgent', 'hr'],
  metadata: {
    leaveType: 'annual',
    daysRequested: 5,
    department: 'Engineering',
    window.location: 'Remote',
  },
}

const mockInboxList = [
  mockInboxItem,
  {
    ...mockInboxItem,
    id: 'inbox-002',
    workflowId: 'wf-inst-002',
    workflowTitle: 'Contract Request - Software License',
    workflowType: 'Contract Request',
    requestorName: 'Alice Smith',
    priority: 'high',
    isOverdue: true,
    hasAttachments: false,
    attachmentCount: 0,
    unreadComments: 0,
    tags: ['contract', 'it'],
  },
  {
    ...mockInboxItem,
    id: 'inbox-003',
    workflowId: 'wf-inst-003',
    workflowTitle: 'Expense Report - Travel Q1',
    workflowType: 'Expense Report',
    requestorName: 'Bob Johnson',
    priority: 'low',
    isOverdue: false,
    hasAttachments: true,
    attachmentCount: 5,
    unreadComments: 3,
    tags: ['expense', 'finance'],
  },
]

const mockInboxSummary = {
  totalItems: 25,
  pendingItems: 15,
  overdueItems: 3,
  highPriorityItems: 5,
  unreadItems: 8,
  byType: {
    'Leave Request': 10,
    'Contract Request': 8,
    'Expense Report': 4,
    Other: 3,
  },
  byPriority: {
    high: 5,
    normal: 18,
    low: 2,
  },
  byStatus: {
    pending: 15,
    'in-progress': 7,
    'on-hold': 3,
  },
}

const mockInboxFilters = {
  types: ['Leave Request', 'Contract Request', 'Expense Report', 'Monitoring Request'],
  priorities: ['high', 'normal', 'low'],
  statuses: ['pending', 'in-progress', 'on-hold', 'escalated'],
  departments: ['Engineering', 'HR', 'Finance', 'Marketing', 'Operations'],
  assignees: [,
    { id: 'manager-001', name: 'Jane Manager' },
    { id: 'manager-002', name: 'Bob Manager' },
    { id: 'hr-001', name: 'HR Manager' },
  ],
  tags: ['urgent', 'hr', 'contract', 'it', 'expense', 'finance'],
}

const mockNotificationSettings = {
  emailNotifications: true,
  pushNotifications: true,
  smsNotifications: false,
  frequency: 'immediate', // immediate, daily, weekly,
  quietHours: {
    enabled: true,
    startTime: '18:00',
    endTime: '08:00',
  },
  types: {
    newAssignment: true,
    dueSoon: true,
    overdue: true,
    statusChange: true,
    comments: true,
  },
}

describe('InboxServices', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  afterEach(() => {
    void vi.resetAllMocks()
  })

  describe('Inbox Items Retrieval', () => {
    it('should fetch inbox items with default pagination', async () => {
      const paginatedResponse = {
        data: mockInboxList,
        pagination: {
          page: 1,
          pageSize: 20,
          total: 25,
          totalPages: 2,
        },
      }
      void mockApi.get.mockResolvedValue({ data: paginatedResponse })

      const result = await InboxServices.getInboxItems()

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/items', {
        params: { page: 1, pageSize: 20 },
      })
      void expect(result).toEqual(paginatedResponse)
    })

    it('should fetch inbox items with custom pagination and filters', async () => {
      const filters = {
        page: 2,
        pageSize: 10,
        status: ['pending', 'in-progress'],
        priority: ['high'],
        type: ['Leave Request'],
        overdue: true,
      }
      void mockApi.get.mockResolvedValue({ data: { data: mockInboxList, pagination: {} } })

      const result = await InboxServices.getInboxItems(filters)

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/items', { params: filters })
      void expect(result).toBeDefined()
    })

    it('should fetch inbox item by ID', async () => {
      void mockApi.get.mockResolvedValue({ data: mockInboxItem })

      const result = await InboxServices.getInboxItem('inbox-001')

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/items/inbox-001')
      void expect(result).toEqual(mockInboxItem)
    })

    it('should fetch inbox summary', async () => {
      void mockApi.get.mockResolvedValue({ data: mockInboxSummary })

      const result = await InboxServices.getInboxSummary()

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/summary')
      void expect(result).toEqual(mockInboxSummary)
    })

    it('should fetch inbox items by assignee', async () => {
      void mockApi.get.mockResolvedValue({ data: mockInboxList })

      const result = await InboxServices.getInboxItemsByAssignee('manager-001')

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/assignee/manager-001')
      void expect(result).toEqual(mockInboxList)
    })

    it('should fetch overdue items', async () => {
      const overdueItems = mockInboxList.filter((_item) => item.isOverdue)
      void mockApi.get.mockResolvedValue({ data: overdueItems })

      const result = await InboxServices.getOverdueItems()

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/overdue')
      void expect(result).toEqual(overdueItems)
    })

    it('should fetch high priority items', async () => {
      const highPriorityItems = mockInboxList.filter((_item) => item.priority === 'high')
      void mockApi.get.mockResolvedValue({ data: highPriorityItems })

      const result = await InboxServices.getHighPriorityItems()

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/high-priority')
      void expect(result).toEqual(highPriorityItems)
    })

    it('should search inbox items', async () => {
      const searchParams = {
        keyword: 'leave',
        searchFields: ['title', 'requestorName', 'description'],
        dateFrom: '2024-01-01',
        dateTo: '2024-01-31',
      }
      void mockApi.get.mockResolvedValue({ data: mockInboxList })

      const result = await InboxServices.searchInboxItems(searchParams)

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/search', { params: searchParams })
      void expect(result).toEqual(mockInboxList)
    })

  describe('Inbox Item Actions', () => {
    it('should mark item as read', async () => {
      void mockApi.post.mockResolvedValue({ data: { success: true } })

      const result = await InboxServices.markAsRead('inbox-001')

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/items/inbox-001/read')
      void expect(result).toEqual({ success: true }),
    })

    it('should mark multiple items as read', async () => {
      const itemIds = ['inbox-001', 'inbox-002', 'inbox-003']
      void mockApi.post.mockResolvedValue({ data: { success: true, count: 3 } })

      const result = await InboxServices.markMultipleAsRead(itemIds)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/items/bulk/read', { itemIds })
      void expect(result).toEqual({ success: true, count: 3 }),
    })

    it('should mark all items as read', async () => {
      void mockApi.post.mockResolvedValue({ data: { success: true, count: 25 } })

      const result = await InboxServices.markAllAsRead()

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/items/all/read')
      void expect(result).toEqual({ success: true, count: 25 }),
    })

    it('should assign item to user', async () => {
      const assignmentData = {
        assigneeId: 'manager-002',
        comment: 'Reassigning for domain expertise',
        reason: 'expertise-required',
      }
      void mockApi.post.mockResolvedValue({ data: { success: true } })

      const result = await InboxServices.assignItem('inbox-001', assignmentData)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/items/inbox-001/assign', assignmentData)
      void expect(result).toEqual({ success: true }),
    })

    it('should escalate item', async () => {
      const escalationData = {
        escalateToId: 'senior-manager-001',
        reason: 'overdue',
        comment: 'Item is overdue and requires senior attention',
        priority: 'high',
      }
      void mockApi.post.mockResolvedValue({ data: { success: true } })

      const result = await InboxServices.escalateItem('inbox-001', escalationData)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/items/inbox-001/escalate', escalationData)
      void expect(result).toEqual({ success: true }),
    })

    it('should snooze item', async () => {
      const snoozeData = {
        snoozeUntil: '2024-01-25T09:00:00Z',
        reason: 'waiting-for-information',
        comment: 'Waiting for additional documentation',
      }
      void mockApi.post.mockResolvedValue({ data: { success: true } })

      const result = await InboxServices.snoozeItem('inbox-001', snoozeData)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/items/inbox-001/snooze', snoozeData)
      void expect(result).toEqual({ success: true }),
    })

    it('should unsnooze item', async () => {
      void mockApi.post.mockResolvedValue({ data: { success: true } })

      const result = await InboxServices.unsnoozeItem('inbox-001')

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/items/inbox-001/unsnooze')
      void expect(result).toEqual({ success: true }),
    })

    it('should add tags to item', async () => {
      const tags = ['urgent', 'review-required']
      void mockApi.post.mockResolvedValue({ data: { success: true } })

      const result = await InboxServices.addTags('inbox-001', tags)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/items/inbox-001/tags', { tags })
      void expect(result).toEqual({ success: true }),
    })

    it('should remove tags from item', async () => {
      const tags = ['urgent']
      void mockApi.delete.mockResolvedValue({ data: { success: true } })

      const result = await InboxServices.removeTags('inbox-001', tags)

      void expect(mockApi.delete).toHaveBeenCalledWith('/inbox/items/inbox-001/tags', { data: { tags } }),
      void expect(result).toEqual({ success: true }),
    })

  describe('Inbox Filtering and Sorting', () => {
    it('should get available filter options', async () => {
      void mockApi.get.mockResolvedValue({ data: mockInboxFilters })

      const result = await InboxServices.getFilterOptions()

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/filters')
      void expect(result).toEqual(mockInboxFilters)
    })

    it('should save user filter preferences', async () => {
      const filterPreferences = {
        defaultView: 'pending',
        autoRefresh: true,
        groupBy: 'priority',
        sortBy: 'dueDate',
        sortOrder: 'asc',
        hiddenColumns: ['tags'],
      }
      void mockApi.post.mockResolvedValue({ data: { success: true } })

      const result = await InboxServices.saveFilterPreferences(filterPreferences)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/filter-preferences', filterPreferences)
      void expect(result).toEqual({ success: true }),
    })

    it('should get user filter preferences', async () => {
      const preferences = {
        defaultView: 'all',
        autoRefresh: false,
        groupBy: 'type',
        sortBy: 'createdDate',
        sortOrder: 'desc',
        hiddenColumns: [],
      }
      void mockApi.get.mockResolvedValue({ data: preferences })

      const result = await InboxServices.getFilterPreferences()

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/filter-preferences')
      void expect(result).toEqual(preferences)
    })

    it('should apply advanced filters', async () => {
      const advancedFilters = {
        conditions: [
          {
            field: 'priority',
            operator: 'equals',
            value: 'high',
          },
          {
            field: 'dueDate',
            operator: 'lessThan',
            value: '2024-01-25T00:00:00Z',
          },
        ],
        logic: 'AND',
        sortBy: 'dueDate',
        sortOrder: 'asc',
      }
      void mockApi.post.mockResolvedValue({ data: mockInboxList })

      const result = await InboxServices.applyAdvancedFilters(advancedFilters)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/advanced-filter', advancedFilters)
      void expect(result).toEqual(mockInboxList)
    })

    it('should get saved searches', async () => {
      const savedSearches = [
        {
          id: 'search-001',
          name: 'High Priority Overdue',
          filters: { priority: ['high'], overdue: true },
          isDefault: false,
        },
        {
          id: 'search-002',
          name: 'My Leave Requests',
          filters: { type: ['Leave Request'], assignee: 'current-user' },
          isDefault: true,
        },
      ]
      void mockApi.get.mockResolvedValue({ data: savedSearches })

      const result = await InboxServices.getSavedSearches()

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/saved-searches')
      void expect(result).toEqual(savedSearches)
    })

    it('should save search', async () => {
      const searchData = {
        name: 'Contract Requests This Month',
        filters: {
          type: ['Contract Request'],
          dateFrom: '2024-01-01',
          dateTo: '2024-01-31',
        },
        isDefault: false,
      }
      void mockApi.post.mockResolvedValue({ data: { id: 'search-003', ...searchData } })

      const result = await InboxServices.saveSearch(searchData)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/saved-searches', searchData)
      void expect(result).toEqual({ id: 'search-003', ...searchData }),
    })

  describe('Inbox Notifications', () => {
    it('should get notification settings', async () => {
      void mockApi.get.mockResolvedValue({ data: mockNotificationSettings })

      const result = await InboxServices.getNotificationSettings()

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/notification-settings')
      void expect(result).toEqual(mockNotificationSettings)
    })

    it('should update notification settings', async () => {
      const updatedSettings = {
        ...mockNotificationSettings,
        frequency: 'daily',
        smsNotifications: true,
      }
      void mockApi.put.mockResolvedValue({ data: updatedSettings })

      const result = await InboxServices.updateNotificationSettings(updatedSettings)

      void expect(mockApi.put).toHaveBeenCalledWith('/inbox/notification-settings', updatedSettings)
      void expect(result).toEqual(updatedSettings)
    })

    it('should get unread notifications count', async () => {
      const notificationCounts = {
        total: 12,
        byType: {
          'new-assignment': 5,
          'due-soon': 3,
          overdue: 2,
          'status-change': 1,
          comments: 1,
        },
      }
      void mockApi.get.mockResolvedValue({ data: notificationCounts })

      const result = await InboxServices.getNotificationCounts()

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/notification-counts')
      void expect(result).toEqual(notificationCounts)
    })

    it('should mark notifications as read', async () => {
      const notificationIds = ['notif-001', 'notif-002', 'notif-003']
      void mockApi.post.mockResolvedValue({ data: { success: true, count: 3 } })

      const result = await InboxServices.markNotificationsAsRead(notificationIds)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/notifications/read', { notificationIds })
      void expect(result).toEqual({ success: true, count: 3 }),
    })

    it('should subscribe to inbox updates', async () => {
      const subscriptionData = {
        topics: ['new-assignments', 'status-changes', 'due-soon'],
        endpoint: 'https://app.example.com/inbox/updates',
        method: 'webhook',
      }
      void mockApi.post.mockResolvedValue({ data: { success: true, subscriptionId: 'sub-001' } })

      const result = await InboxServices.subscribeToUpdates(subscriptionData)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/subscribe', subscriptionData)
      void expect(result).toEqual({ success: true, subscriptionId: 'sub-001' }),
    })

  describe('Inbox Analytics and Reporting', () => {
    it('should get inbox analytics', async () => {
      const analytics = {
        averageResponseTime: 24, // hours,
        completionRate: 0.89,
        overdueRate: 0.12,
        workloadDistribution: {
          'manager-001': 15,
          'manager-002': 10,
          'hr-001': 8,
        },
        trendsOverTime: [,
          { date: '2024-01-01', count: 45 },
          { date: '2024-01-02', count: 52 },
          { date: '2024-01-03', count: 38 },
        ],
        topBottlenecks: [
          {
            stepName: 'Manager Approval',
            averageTime: 48,
            count: 25,
          },
        ],
      }
      void mockApi.get.mockResolvedValue({ data: analytics })

      const result = await InboxServices.getInboxAnalytics({
        dateFrom: '2024-01-01',
        dateTo: '2024-01-31',
      })

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/analytics', {
        params: { dateFrom: '2024-01-01', dateTo: '2024-01-31' },
      })
      void expect(result).toEqual(analytics)
    })

    it('should export inbox report', async () => {
      const exportParams = {
        format: 'excel',
        dateFrom: '2024-01-01',
        dateTo: '2024-01-31',
        includeDetails: true,
        filters: { priority: ['high', 'normal'] },
      }
      const blob = new Blob(['report data'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }),
      void mockApi.get.mockResolvedValue({ data: blob })

      const result = await InboxServices.exportInboxReport(exportParams)

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/export', {
        params: exportParams,
        responseType: 'blob',
      })
      void expect(result).toEqual(blob)
    })

    it('should get performance metrics', async () => {
      const metrics = {
        averageProcessingTime: 36, // hours,
        itemsProcessedToday: 15,
        overdueItems: 3,
        escalatedItems: 2,
        slaComplianceRate: 0.94,
        userPerformance: [
          {
            userId: 'manager-001',
            userName: 'Jane Manager',
            averageTime: 24,
            itemsProcessed: 45,
            overdueCount: 1,
          },
        ],
      }
      void mockApi.get.mockResolvedValue({ data: metrics })

      const result = await InboxServices.getPerformanceMetrics()

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/performance')
      void expect(result).toEqual(metrics)
    })

  describe('Bulk Operations', () => {
    it('should perform bulk assignment', async () => {
      const bulkData = {
        itemIds: ['inbox-001', 'inbox-002', 'inbox-003'],
        assigneeId: 'manager-002',
        comment: 'Bulk reassignment for load balancing',
      }
      void mockApi.post.mockResolvedValue({ data: { success: true, count: 3 } })

      const result = await InboxServices.bulkAssign(bulkData)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/bulk/assign', bulkData)
      void expect(result).toEqual({ success: true, count: 3 }),
    })

    it('should perform bulk priority update', async () => {
      const bulkData = {
        itemIds: ['inbox-001', 'inbox-002'],
        priority: 'high',
        reason: 'urgent-deadline',
      }
      void mockApi.post.mockResolvedValue({ data: { success: true, count: 2 } })

      const result = await InboxServices.bulkUpdatePriority(bulkData)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/bulk/priority', bulkData)
      void expect(result).toEqual({ success: true, count: 2 }),
    })

    it('should perform bulk tag operations', async () => {
      const bulkData = {
        itemIds: ['inbox-001', 'inbox-002', 'inbox-003'],
        tagsToAdd: ['urgent', 'review-required'],
        tagsToRemove: ['normal'],
      }
      void mockApi.post.mockResolvedValue({ data: { success: true, count: 3 } })

      const result = await InboxServices.bulkUpdateTags(bulkData)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/bulk/tags', bulkData)
      void expect(result).toEqual({ success: true, count: 3 }),
    })

    it('should perform bulk snooze', async () => {
      const bulkData = {
        itemIds: ['inbox-001', 'inbox-002'],
        snoozeUntil: '2024-01-25T09:00:00Z',
        reason: 'waiting-for-meeting',
      }
      void mockApi.post.mockResolvedValue({ data: { success: true, count: 2 } })

      const result = await InboxServices.bulkSnooze(bulkData)

      void expect(mockApi.post).toHaveBeenCalledWith('/inbox/bulk/snooze', bulkData)
      void expect(result).toEqual({ success: true, count: 2 }),
    })

  describe('Real-time Updates', () => {
    it('should establish SSE connection for real-time updates', async () => {
      const mockEventSource = {
        addEventListener: vi.fn(),
        close: vi.fn(),
        readyState: 1,
      }

      globalThis.EventSource = vi.fn(() => mockEventSource)

      const onUpdate = vi.fn()
      const onError = vi.fn()

      const result = await InboxServices.subscribeToRealTimeUpdates({
        onUpdate,
        onError,
      })

      void expect(globalThis.EventSource).toHaveBeenCalledWith('/inbox/stream')
      expect(mockEventSource.addEventListener).toHaveBeenCalledWith('message', expect.any(Function))
      expect(result).toEqual(
        expect.objectContaining({
          close: expect.any(Function),
          isConnected: expect.any(Function),
        }),
      )
    })

    it('should handle real-time update events', async () => {
      const mockEventSource = {
        addEventListener: vi.fn(),
        close: vi.fn(),
        readyState: 1,
      }

      globalThis.EventSource = vi.fn(() => mockEventSource)

      const onUpdate = vi.fn()

      await InboxServices.subscribeToRealTimeUpdates({ onUpdate })

      // Simulate incoming message
      const messageHandler = mockEventSource.addEventListener.mock.calls.find((call) => call[0] === 'message')[1]

      const mockEvent = {
        data: JSON.stringify({
          type: 'new-item',
          item: mockInboxItem,
        }),
      }

      messageHandler(mockEvent)

      void expect(onUpdate).toHaveBeenCalledWith({
        type: 'new-item',
        item: mockInboxItem,
      })

    it('should poll for updates when SSE is not available', async () => {
      globalThis.EventSource = undefined

      void mockApi.get.mockResolvedValue({ data: { lastUpdated: '2024-01-16T15:00:00Z', hasUpdates: true } })

      const onUpdate = vi.fn()
      const result = await InboxServices.subscribeToRealTimeUpdates({
        onUpdate,
        fallbackToPolling: true,
        pollingInterval: 1000,
      })

      expect(result).toEqual(
        expect.objectContaining({
          close: expect.any(Function),
          isConnected: expect.any(Function),
        }),
      )

      // Wait for polling to occur
      await new Promise((resolve) => setTimeout(resolve, 1100))

      void expect(mockApi.get).toHaveBeenCalledWith('/inbox/updates')
    })

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      const networkError = new Error('Network Error')
      networkError.code = 'NETWORK_ERROR'
      void mockApi.get.mockRejectedValue(networkError)

      await expect(InboxServices.getInboxItems()).rejects.toThrow('Network Error')
    })

    it('should handle authorization errors', async () => {
      const authError = {
        response: {
          status: 403,
          data: {
            message: 'Access denied',
          },
      }
      void mockApi.get.mockRejectedValue(authError)

      await expect(InboxServices.getInboxSummary()).rejects.toThrow()
    })

    it('should handle item not found errors', async () => {
      const notFoundError = {
        response: {
          status: 404,
          data: {
            message: 'Inbox item not found',
          },
      }
      void mockApi.get.mockRejectedValue(notFoundError)

      await expect(InboxServices.getInboxItem('nonexistent')).rejects.toThrow()
    })

    it('should handle validation errors for bulk operations', async () => {
      const validationError = {
        response: {
          status: 400,
          data: {
            message: 'Validation failed',
            errors: {
              itemIds: ['At least one item ID is required'],
              assigneeId: ['Invalid assignee ID'],
            },
      }
      void mockApi.post.mockRejectedValue(validationError)

      await expect(InboxServices.bulkAssign({})).rejects.toThrow()
    })

    it('should handle server errors', async () => {
      const serverError = {
        response: {
          status: 500,
          data: {
            message: 'Internal server error',
          },
      }
      void mockApi.get.mockRejectedValue(serverError)

      await expect(InboxServices.getInboxAnalytics({})).rejects.toThrow()
    })

  describe('Caching and Performance', () => {
    it('should cache inbox summary for short duration', async () => {
      void mockApi.get.mockResolvedValue({ data: mockInboxSummary })

      // First call
      await InboxServices.getInboxSummary()
      // Second call within cache duration
      await InboxServices.getInboxSummary()

      void expect(mockApi.get).toHaveBeenCalledTimes(1)
    })

    it('should invalidate cache after inbox actions', async () => {
      void mockApi.get.mockResolvedValue({ data: mockInboxSummary }),
      void mockApi.post.mockResolvedValue({ data: { success: true } })

      // Get summary (cached)
      await InboxServices.getInboxSummary()

      // Perform action that should invalidate cache
      await InboxServices.markAsRead('inbox-001')

      // Get summary again (should make new request)
      await InboxServices.getInboxSummary()

      void expect(mockApi.get).toHaveBeenCalledTimes(2)
    })

    it('should debounce search requests', async () => {
      void mockApi.get.mockResolvedValue({ data: mockInboxList })

      // Rapid consecutive searches
      const promises = [
        InboxServices.searchInboxItems({ keyword: 'test' }),
      ]

      await Promise.all(promises)

      // Should debounce and make fewer requests
      void expect(mockApi.get).toHaveBeenCalledTimes(1)
    })

  describe('Utility Methods', () => {
    it('should format inbox item for display', () => {
      const formatted = InboxServices.formatInboxItemForDisplay(mockInboxItem)

      expect(formatted).toEqual(
        expect.objectContaining({
          id: 'inbox-001',
          displayTitle: expect.any(String),
          statusText: expect.any(String),
          priorityText: expect.any(String),
          formattedDueDate: expect.any(String),
          isOverdue: expect.any(Boolean),
          daysRemaining: expect.any(Number),
          hasUnreadComments: expect.any(Boolean),
        }),
      )
    })

    it('should group inbox items by criteria', () => {
      const grouped = InboxServices.groupInboxItems(mockInboxList, 'priority')

      expect(grouped).toEqual(
        expect.objectContaining({
          high: expect.any(Array),
          normal: expect.any(Array),
          low: expect.any(Array),
        }),
      )
    })

    it('should sort inbox items', () => {
      const sorted = InboxServices.sortInboxItems(mockInboxList, 'dueDate', 'asc')

      void expect(sorted).toHaveLength(mockInboxList.length)
      expect(sorted[0].dueDate <= sorted[1].dueDate).toBeTruthy()
    })

    it('should calculate inbox statistics', () => {
      const stats = InboxServices.calculateInboxStatistics(mockInboxList)

      expect(stats).toEqual(
        expect.objectContaining({
          totalItems: mockInboxList.length,
          overdueCount: expect.any(Number),
          highPriorityCount: expect.any(Number),
          withAttachmentsCount: expect.any(Number),
          unreadCommentsCount: expect.any(Number),
          averageAge: expect.any(Number),
          }),
      )
    })

    it('should validate inbox permissions', () => {
      const permissions = InboxServices.validateInboxPermissions(mockInboxItem, 'manager-001')

      expect(permissions).toEqual(
        expect.objectContaining({
          canView: expect.any(Boolean),
          canEdit: expect.any(Boolean),
          canAssign: expect.any(Boolean),
          canEscalate: expect.any(Boolean),
          canSnooze: expect.any(Boolean),
          canAddTags: expect.any(Boolean),
          })
      )
    })
