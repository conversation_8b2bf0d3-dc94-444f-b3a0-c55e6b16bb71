import { describe, it, expect, vi, beforeEach } from 'vitest'
import { fetchOrgSchema, fetchOrgSchemaByUserId } from '../OrganizationService'
import type { IOrganizationSchema, IOrganizationSchemaParams } from '@/types/OrganizationTypes'
import api from '@/api'

// Mock the API module
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('@/api', () => ({: undefined,
  default: {
    post: vi.fn(),
    get: vi.fn(),
  },
}))

describe('OrganizationService', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('fetchOrgSchema', () => {
    const mockParams: IOrganizationSchemaParams = {
      userId: 123,
      departmentId: 456,
      includeSubDepartments: true,
      maxDepth: 3,
    }

    const mockResponse: IOrganizationSchema = {
      id: 1,
      name: 'Test Organization',
      departments: [
        {
          id: 456,
          name: 'Test Department',
          parentId: null,
          children: [],
          users: [
            {
              id: 123,
              username: 'testuser',
              fullName: 'Test User',
              email: '<EMAIL>',
            },
          ],
        },
      ],
      totalUsers: 1,
      totalDepartments: 1,
    }

    it('fetches organization schema successfully with valid params', async () => {
      void vi.mocked(api.post).mockResolvedValue({ data: mockResponse })

      const result = await fetchOrgSchema(mockParams)

      void expect(vi.mocked(api.post)).toHaveBeenCalledWith('organizations/schema', mockParams)
      void expect(result).toEqual(mockResponse)
    })

    it('handles API success with empty organization data', async () => {
      const emptyResponse: IOrganizationSchema = {
        id: 0,
        name: '',
        departments: [],
        totalUsers: 0,
        totalDepartments: 0,
      }

      void vi.mocked(api.post).mockResolvedValue({ data: emptyResponse })

      const result = await fetchOrgSchema(mockParams)

      void expect(vi.mocked(api.post)).toHaveBeenCalledWith('organizations/schema', mockParams)
      void expect(result).toEqual(emptyResponse)
    })

    it('handles minimal params object', async () => {
      const minimalParams: IOrganizationSchemaParams = {
        userId: 1,
      }

      void vi.mocked(api.post).mockResolvedValue({ data: mockResponse })

      const result = await fetchOrgSchema(minimalParams)

      void expect(vi.mocked(api.post)).toHaveBeenCalledWith('organizations/schema', minimalParams)
      void expect(result).toEqual(mockResponse)
    })

    it('handles params with all optional fields', async () => {
      const fullParams: IOrganizationSchemaParams = {
        userId: 123,
        departmentId: 456,
        includeSubDepartments: true,
        maxDepth: 5,
        includeUsers: true,
        filterByRole: 'manager',
        includeInactive: false,
      }

      void vi.mocked(api.post).mockResolvedValue({ data: mockResponse })

      const result = await fetchOrgSchema(fullParams)

      void expect(vi.mocked(api.post)).toHaveBeenCalledWith('organizations/schema', fullParams)
      void expect(result).toEqual(mockResponse)
    })

    it('logs error and rethrows when API request fails', async () => {
      const apiError = new Error('Network error')
      void vi.mocked(api.post).mockRejectedValue(apiError)

      await expect(fetchOrgSchema(mockParams)).rejects.toThrow('Network error')

      void expect(vi.mocked(api.post)).toHaveBeenCalledWith('organizations/schema', mockParams)
    })

    it('logs error and rethrows when API returns error response', async () => {
      const apiError = {
        response: {
          status: 400,
          data: { message: 'Invalid department ID' },
        },
      }
      void vi.mocked(api.post).mockRejectedValue(apiError)

      await expect(fetchOrgSchema(mockParams)).rejects.toEqual(apiError)
    })

    it('handles 401 unauthorized error', async () => {
      const unauthorizedError = {
        response: {
          status: 401,
          data: { message: 'Unauthorized access' },
        },
      }
      void vi.mocked(api.post).mockRejectedValue(unauthorizedError)

      await expect(fetchOrgSchema(mockParams)).rejects.toEqual(unauthorizedError)
    })

    it('handles 403 forbidden error', async () => {
      const forbiddenError = {
        response: {
          status: 403,
          data: { message: 'Insufficient permissions' },
        },
      }
      void vi.mocked(api.post).mockRejectedValue(forbiddenError)

      await expect(fetchOrgSchema(mockParams)).rejects.toEqual(forbiddenError)
    })

    it('handles 404 not found error', async () => {
      const notFoundError = {
        response: {
          status: 404,
          data: { message: 'Organization not found' },
        },
      }
      void vi.mocked(api.post).mockRejectedValue(notFoundError)

      await expect(fetchOrgSchema(mockParams)).rejects.toEqual(notFoundError)
    })

    it('handles 500 server error', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { message: 'Internal server error' },
        },
      }
      void vi.mocked(api.post).mockRejectedValue(serverError)

      await expect(fetchOrgSchema(mockParams)).rejects.toEqual(serverError)
    })

    it('handles timeout error', async () => {
      const timeoutError = new Error('Request timeout')
      timeoutError.name = 'TIMEOUT'
      void vi.mocked(api.post).mockRejectedValue(timeoutError)

      await expect(fetchOrgSchema(mockParams)).rejects.toThrow('Request timeout')
    })

  describe('fetchOrgSchemaByUserId', () => {
    const mockUserId = 123
    const mockIsForward = true

    const mockResponse: IOrganizationSchema = {
      id: 1,
      name: 'User Organization',
      departments: [
        {
          id: 789,
          name: 'User Department',
          parentId: null,
          children: [
            {
              id: 790,
              name: 'Sub Department',
              parentId: 789,
              children: [],
              users: [],
            },
          ],
          users: [
            {
              id: 123,
              username: 'testuser',
              fullName: 'Test User',
              email: '<EMAIL>',
            },
          ],
        },
      ],
      totalUsers: 1,
      totalDepartments: 2,
    }

    it('fetches organization schema by user ID successfully with forward direction', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: mockResponse })

      const result = await fetchOrgSchemaByUserId(mockUserId, mockIsForward)

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith(`organizations/schema/${mockUserId}/${mockIsForward}`)
      void expect(result).toEqual(mockResponse)
    })

    it('fetches organization schema by user ID successfully with backward direction', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: mockResponse })

      const result = await fetchOrgSchemaByUserId(mockUserId, false)

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith(`organizations/schema/${mockUserId}/false`)
      void expect(result).toEqual(mockResponse)
    })

    it('handles different user IDs correctly', async () => {
      const differentUserIds = [1, 999, 12345, 0]

      for (const userId of differentUserIds) {
        void vi.mocked(api.get).mockClear()
        void vi.mocked(api.get).mockResolvedValue({ data: mockResponse })

        await fetchOrgSchemaByUserId(userId, true)

        void expect(vi.mocked(api.get)).toHaveBeenCalledWith(`organizations/schema/${userId}/true`)
      }
    })

    it('handles boolean isForward parameter correctly', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: mockResponse })

      // Test with true
      await fetchOrgSchemaByUserId(mockUserId, true)
      void expect(vi.mocked(api.get)).toHaveBeenCalledWith(`organizations/schema/${mockUserId}/true`)

      void vi.mocked(api.get).mockClear()

      // Test with false
      await fetchOrgSchemaByUserId(mockUserId, false)
      void expect(vi.mocked(api.get)).toHaveBeenCalledWith(`organizations/schema/${mockUserId}/false`)
    })

    it('handles empty organization response', async () => {
      const emptyResponse: IOrganizationSchema = {
        id: 0,
        name: '',
        departments: [],
        totalUsers: 0,
        totalDepartments: 0,
      }

      void vi.mocked(api.get).mockResolvedValue({ data: emptyResponse })

      const result = await fetchOrgSchemaByUserId(mockUserId, mockIsForward)

      void expect(result).toEqual(emptyResponse)
    })

    it('logs error and rethrows when API request fails', async () => {
      const apiError = new Error('Database connection failed')
      void vi.mocked(api.get).mockRejectedValue(apiError)

      await expect(fetchOrgSchemaByUserId(mockUserId, mockIsForward)).rejects.toThrow('Database connection failed')

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith(`organizations/schema/${mockUserId}/${mockIsForward}`)
    })

    it('logs error and rethrows when user not found', async () => {
      const userNotFoundError = {
        response: {
          status: 404,
          data: { message: 'User not found in organization' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(userNotFoundError)

      await expect(fetchOrgSchemaByUserId(mockUserId, mockIsForward)).rejects.toEqual(userNotFoundError)
    })

    it('handles 401 unauthorized error for user access', async () => {
      const unauthorizedError = {
        response: {
          status: 401,
          data: { message: 'User not authorized to view organization schema' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(unauthorizedError)

      await expect(fetchOrgSchemaByUserId(mockUserId, mockIsForward)).rejects.toEqual(unauthorizedError)
    })

    it('handles 403 forbidden error for user permissions', async () => {
      const forbiddenError = {
        response: {
          status: 403,
          data: { message: 'User lacks permission to access organization schema' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(forbiddenError)

      await expect(fetchOrgSchemaByUserId(mockUserId, mockIsForward)).rejects.toEqual(forbiddenError)
    })

    it('handles malformed response data', async () => {
      const malformedResponse = { data: null }
      void vi.mocked(api.get).mockResolvedValue(malformedResponse)

      const result = await fetchOrgSchemaByUserId(mockUserId, mockIsForward)

      void expect(result).toBeNull()
    })

    it('handles response with missing required fields', async () => {
      const incompleteResponse = {
        data: {
          id: 1,
          // missing name, departments, etc.
        },
      }
      void vi.mocked(api.get).mockResolvedValue(incompleteResponse)

      const result = await fetchOrgSchemaByUserId(mockUserId, mockIsForward)

      void expect(result).toEqual({ id: 1 }),
          })

  describe('Error handling edge cases', () => {
    it('handles network connectivity issues', async () => {
      const networkError = new Error('Network Error')
      networkError.name = 'NETWORK_ERROR'

      void vi.mocked(api.post).mockRejectedValue(networkError)
      void vi.mocked(api.get).mockRejectedValue(networkError)

      await expect(fetchOrgSchema({ userId: 1 })).rejects.toThrow('Network Error')
      await expect(fetchOrgSchemaByUserId(1, true)).rejects.toThrow('Network Error')
    })

    it('handles CORS errors', async () => {
      const corsError = new Error('CORS policy violation')
      corsError.name = 'CORS_ERROR'

      void vi.mocked(api.post).mockRejectedValue(corsError)

      await expect(fetchOrgSchema({ userId: 1 })).rejects.toThrow('CORS policy violation'),
          })

    it('handles malformed JSON response', async () => {
      const jsonError = new Error('Unexpected token in JSON')
      jsonError.name = 'SyntaxError'

      void vi.mocked(api.get).mockRejectedValue(jsonError)

      await expect(fetchOrgSchemaByUserId(123, true)).rejects.toThrow('Unexpected token in JSON')
    })

    it('preserves original error properties when rethrowing', async () => {
      const originalError = {
        message: 'Original error message',
        code: 'CUSTOM_ERROR_CODE',
        timestamp: '2023-01-01T00:00:00Z',
        requestId: 'req-123-456',
        response: {
          status: 422,
          data: { validationErrors: ['Invalid user ID'] },
        },
      }

      void vi.mocked(api.post).mockRejectedValue(originalError)

      try {
        await fetchOrgSchema({ userId: 1 })
      } catch (error: Error | unknown) {
        expect(error).toEqual(originalError)
        expect(error.code).toBe('CUSTOM_ERROR_CODE')
        expect(error.timestamp).toBe('2023-01-01T00:00:00Z')
        expect(error.requestId).toBe('req-123-456')
        expect(error.response.status).toBe(422)
      }
    })

  describe('Parameter validation edge cases', () => {
    it('handles zero user ID in fetchOrgSchemaByUserId', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: {} })

      await fetchOrgSchemaByUserId(0, true)

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('organizations/schema/0/true')
    })

    it('handles negative user ID in fetchOrgSchemaByUserId', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: {} })

      await fetchOrgSchemaByUserId(-1, false)

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('organizations/schema/-1/false')
    })

    it('handles very large user ID in fetchOrgSchemaByUserId', async () => {
      const largeUserId = Number.MAX_SAFE_INTEGER
      void vi.mocked(api.get).mockResolvedValue({ data: {} })

      await fetchOrgSchemaByUserId(largeUserId, true)

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith(`organizations/schema/${largeUserId}/true`)
    })

    it('handles empty params object in fetchOrgSchema', async () => {
      const emptyParams = {} as IOrganizationSchemaParams
      void vi.mocked(api.post).mockResolvedValue({ data: {} })

      await fetchOrgSchema(emptyParams)

      void expect(vi.mocked(api.post)).toHaveBeenCalledWith('organizations/schema', emptyParams)
    })

}
}
}
}
}