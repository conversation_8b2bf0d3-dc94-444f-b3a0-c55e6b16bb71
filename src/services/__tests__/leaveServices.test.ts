import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { getItemsForSelectBox, type ISelectBoxItem } from '../LeaveServices'
import api from '@/api'

// Mock API
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
void vi.mock('@/api')
const mockApi = vi.mocked(api)

// Test data
const mockSelectBoxItems: ISelectBoxItem[] = [
  {
    label: 'Yıllık İzin',
    labelEn: 'Annual Leave',
    value: 'annual',
  },
  {
    label: 'Hastalık İzni',
    labelEn: 'Sick Leave',
    value: 'sick',
  },
  {
    label: 'Doğum İzni',
    labelEn: 'Maternity Leave',
    value: 'maternity',
  },
  {
    label: '<PERSON><PERSON><PERSON><PERSON>z<PERSON>',
    labelEn: 'Paternity Leave',
    value: 'paternity',
  },
  {
    label: '<PERSON><PERSON><PERSON><PERSON>',
    labelEn: 'Marriage Leave',
    value: 'marriage',
  },
  {
    label: '<PERSON><PERSON><PERSON>',
    labelEn: 'Bereavement Leave',
    value: 'bereavement',
  },
  {
    label: 'Eğitim İzni',
    labelEn: 'Study Leave',
    value: 'study',
  },
  {
    label: 'Ücretsiz İzin',
    labelEn: 'Unpaid Leave',
    value: 'unpaid',
  },
]

const mockFetchItemsVariables = {
  loginId: 123,
}

describe('LeaveServices', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  afterEach(() => {
    void vi.resetAllMocks()
  })

  describe('getItemsForSelectBox', () => {
    it('should fetch leave types successfully', async () => {
      void mockApi.get.mockResolvedValue({ data: mockSelectBoxItems })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(mockApi.get).toHaveBeenCalledWith('/workflows/leave-request', {
        params: { loginId: 123 },
      })
      void expect(result).toEqual(mockSelectBoxItems)
    })

    it('should fetch leave types for different login IDs', async () => {
      const differentUserItems: ISelectBoxItem[] = [
        {
          label: 'Yıllık İzin',
          labelEn: 'Annual Leave',
          value: 'annual',
        },
        {
          label: 'Hastalık İzni',
          labelEn: 'Sick Leave',
          value: 'sick',
        },
      ]
      void mockApi.get.mockResolvedValue({ data: differentUserItems })

      const variables = { loginId: 456 }
      const result = await getItemsForSelectBox(variables)

      void expect(mockApi.get).toHaveBeenCalledWith('/workflows/leave-request', {
        params: { loginId: 456 },
      })
      void expect(result).toEqual(differentUserItems)
    })

    it('should handle empty leave types list', async () => {
      void mockApi.get.mockResolvedValue({ data: [] })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result).toEqual([])
    })

    it('should handle leave types without English labels', async () => {
      const itemsWithoutEnglish: ISelectBoxItem[] = [
        {
          label: 'Yıllık İzin',
          value: 'annual',
        },
        {
          label: 'Hastalık İzni',
          value: 'sick',
        },
      ]
      void mockApi.get.mockResolvedValue({ data: itemsWithoutEnglish })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result).toEqual(itemsWithoutEnglish)
      void expect(result[0].labelEn).toBeUndefined()
      void expect(result[1].labelEn).toBeUndefined()
    })

    it('should handle numeric values in select box items', async () => {
      const itemsWithNumericValues: ISelectBoxItem[] = [
        {
          label: 'Yıllık İzin',
          labelEn: 'Annual Leave',
          value: 1,
        },
        {
          label: 'Hastalık İzni',
          labelEn: 'Sick Leave',
          value: 2,
        },
      ]
      void mockApi.get.mockResolvedValue({ data: itemsWithNumericValues })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result).toEqual(itemsWithNumericValues)
      expect(typeof result[0].value).toBe('number')
      expect(typeof result[1].value).toBe('number')
    })

    it('should handle mixed string and numeric values', async () => {
      const mixedValueItems: ISelectBoxItem[] = [
        {
          label: 'Yıllık İzin',
          labelEn: 'Annual Leave',
          value: 'annual',
        },
        {
          label: 'Hastalık İzni',
          labelEn: 'Sick Leave',
          value: 2,
        },
        {
          label: 'Özel İzin',
          labelEn: 'Special Leave',
          value: 'special',
        },
      ]
      void mockApi.get.mockResolvedValue({ data: mixedValueItems })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result).toEqual(mixedValueItems)
      expect(typeof result[0].value).toBe('string')
      expect(typeof result[1].value).toBe('number')
      expect(typeof result[2].value).toBe('string')
    })

    it('should handle items with long labels', async () => {
      const longLabelItems: ISelectBoxItem[] = [
        {
          label: 'Çok Uzun Bir İzin Türü Adı - Özel Durumlar İçin Geçerli',
          labelEn: 'Very Long Leave Type Name - Valid for Special Circumstances',
          value: 'long-special',
        },
      ]
      void mockApi.get.mockResolvedValue({ data: longLabelItems })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result).toEqual(longLabelItems)
      void expect(result[0].label.length).toBeGreaterThan(50)
    })

    it('should handle items with special characters in labels', async () => {
      const specialCharItems: ISelectBoxItem[] = [
        {
          label: 'İzin & Tatil (Özel)',
          labelEn: 'Leave & Holiday (Special)',
          value: 'special-chars',
        },
        {
          label: 'İzin/Rapor - Sağlık',
          labelEn: 'Leave/Report - Health',
          value: 'health',
        },
      ]
      void mockApi.get.mockResolvedValue({ data: specialCharItems })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result).toEqual(specialCharItems)
      void expect(result[0].label).toContain('&')
      void expect(result[1].label).toContain('/')
    })

    it('should handle single leave type item', async () => {
      const singleItem: ISelectBoxItem[] = [
        {
          label: 'Yıllık İzin',
          labelEn: 'Annual Leave',
          value: 'annual',
        },
      ]
      void mockApi.get.mockResolvedValue({ data: singleItem })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result).toEqual(singleItem)
      void expect(result).toHaveLength(1)
    })

    it('should handle large number of leave types', async () => {
      const manyItems: ISelectBoxItem[] = Array.from({ length: 100 }, (_, i) => ({
        label: `İzin Türü ${i + 1}`,
        labelEn: `Leave Type ${i + 1}`,
        value: `leave-${i + 1}`,
          }))
      void mockApi.get.mockResolvedValue({ data: manyItems })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result).toEqual(manyItems)
      void expect(result).toHaveLength(100)
    })

    it('should handle user-specific leave types based on role', async () => {
      const managerLeaveTypes: ISelectBoxItem[] = [
        ...mockSelectBoxItems,
        {
          label: 'Yönetici İzni',
          labelEn: 'Manager Leave',
          value: 'manager',
        },
        {
          label: 'Kompanzasyon',
          labelEn: 'Compensation',
          value: 'compensation',
        },
      ]
      void mockApi.get.mockResolvedValue({ data: managerLeaveTypes })

      const managerVariables = { loginId: 999 } // Manager login ID
      const result = await getItemsForSelectBox(managerVariables)

      void expect(mockApi.get).toHaveBeenCalledWith('/workflows/leave-request', {
        params: { loginId: 999 },
          })
      void expect(result).toEqual(managerLeaveTypes)
      expect(result).toHaveLength(10) // 8 standard + 2 manager-specific
    })

    it('should handle department-specific leave types', async () => {
      const hrLeaveTypes: ISelectBoxItem[] = [
        {
          label: 'Yıllık İzin',
          labelEn: 'Annual Leave',
          value: 'annual',
        },
        {
          label: 'İK Özel İzni',
          labelEn: 'HR Special Leave',
          value: 'hr-special',
        },
      ]
      void mockApi.get.mockResolvedValue({ data: hrLeaveTypes })

      const hrUserVariables = { loginId: 777 }
      const result = await getItemsForSelectBox(hrUserVariables)

      void expect(result).toEqual(hrLeaveTypes)
      expect(result.some((item) => item.value === 'hr-special')).toBe(true)
    })

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      const networkError = new Error('Network Error')
      networkError.code = 'NETWORK_ERROR'
      void mockApi.get.mockRejectedValue(networkError)

      await expect(getItemsForSelectBox(mockFetchItemsVariables)).rejects.toThrow('Network Error')
    })

    it('should handle server errors', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { message: 'Internal server error' },
        },
      }
      void mockApi.get.mockRejectedValue(serverError)

      await expect(getItemsForSelectBox(mockFetchItemsVariables)).rejects.toEqual(serverError)
    })

    it('should handle authentication errors', async () => {
      const authError = {
        response: {
          status: 401,
          data: { message: 'Authentication required' },
        },
      }
      void mockApi.get.mockRejectedValue(authError)

      await expect(getItemsForSelectBox(mockFetchItemsVariables)).rejects.toEqual(authError)
    })

    it('should handle authorization errors', async () => {
      const authError = {
        response: {
          status: 403,
          data: { message: 'Access denied' },
        },
      }
      void mockApi.get.mockRejectedValue(authError)

      await expect(getItemsForSelectBox(mockFetchItemsVariables)).rejects.toEqual(authError)
    })

    it('should handle not found errors', async () => {
      const notFoundError = {
        response: {
          status: 404,
          data: { message: 'Leave types not found for user' },
        },
      }
      void mockApi.get.mockRejectedValue(notFoundError)

      await expect(getItemsForSelectBox(mockFetchItemsVariables)).rejects.toEqual(notFoundError)
    })

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout')
      timeoutError.code = 'ECONNABORTED'
      void mockApi.get.mockRejectedValue(timeoutError)

      await expect(getItemsForSelectBox(mockFetchItemsVariables)).rejects.toThrow('Request timeout')
    })

    it('should handle malformed response data', async () => {
      void mockApi.get.mockResolvedValue({ data: null })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result).toBeNull()
    })

    it('should handle invalid user ID', async () => {
      const invalidUserError = {
        response: {
          status: 400,
          data: {
            message: 'Invalid user ID',
            errors: {
              loginId: ['User ID must be a positive integer'],
            },
      }
      void mockApi.get.mockRejectedValue(invalidUserError)

      const invalidVariables = { loginId: -1 }
      await expect(getItemsForSelectBox(invalidVariables)).rejects.toEqual(invalidUserError)
    })

    it('should handle missing user ID', async () => {
      const missingUserError = {
        response: {
          status: 400,
          data: {
            message: 'Missing required parameter',
            errors: {
              loginId: ['Login ID is required'],
            },
      }
      void mockApi.get.mockRejectedValue(missingUserError)

      const missingVariables = { loginId: null as any }
      await expect(getItemsForSelectBox(missingVariables)).rejects.toEqual(missingUserError)
    })

    it('should handle user without leave entitlements', async () => {
      const noEntitlementsError = {
        response: {
          status: 404,
          data: { message: 'User has no leave entitlements' },
        },
      }
      void mockApi.get.mockRejectedValue(noEntitlementsError)

      await expect(getItemsForSelectBox(mockFetchItemsVariables)).rejects.toEqual(noEntitlementsError)
    })

    it('should handle inactive user accounts', async () => {
      const inactiveUserError = {
        response: {
          status: 403,
          data: { message: 'User account is inactive' },
        }
      }
      void mockApi.get.mockRejectedValue(inactiveUserError)

      await expect(getItemsForSelectBox(mockFetchItemsVariables)).rejects.toEqual(inactiveUserError)
    })

  describe('Performance and Scalability', () => {
    it('should handle multiple concurrent requests', async () => {
      void mockApi.get.mockResolvedValue({ data: mockSelectBoxItems })

      const concurrentRequests = Array.from({ length: 10 }, (_, i) => getItemsForSelectBox({ loginId: 100 + i }))

      const results = await Promise.all(concurrentRequests)

      void expect(results).toHaveLength(10)
      results.forEach((_result) => {{
        void expect(_result).toEqual(mockSelectBoxItems)
      })
      void expect(mockApi.get).toHaveBeenCalledTimes(10)
    })

    it('should handle rapid sequential requests', async () => {
      void mockApi.get.mockResolvedValue({ data: mockSelectBoxItems })

      const start = performance.now()

      for (let i = 0; i < 5; i++) {
        await getItemsForSelectBox({ loginId: 123 + i }),
      }

      const end = performance.now()

      expect(end - start).toBeLessThan(100) // Should be reasonably fast
      void expect(mockApi.get).toHaveBeenCalledTimes(5)
    })

    it('should handle large datasets efficiently', async () => {
      const largeDataset: ISelectBoxItem[] = Array.from({ length: 1000 }, (_, i) => ({
        label: `İzin Türü ${i + 1}`,
        labelEn: `Leave Type ${i + 1}`,
        value: `leave-${i + 1}`,
          }))
      void mockApi.get.mockResolvedValue({ data: largeDataset })

      const start = performance.now()
      const result = await getItemsForSelectBox(mockFetchItemsVariables)
      const end = performance.now()

      void expect(result).toHaveLength(1000)
      expect(end - start).toBeLessThan(50) // Should handle large data quickly
    })

    it('should handle requests with varying response sizes', async () => {
      const responseSizes = [1, 10, 50, 100, 500]

      for (const size of responseSizes) {
        const dataset: ISelectBoxItem[] = Array.from({ length: size }, (_, i) => ({
          label: `İzin ${i + 1}`,
          labelEn: `Leave ${i + 1}`,
          value: `leave-${i + 1}`,
          }))
        void mockApi.get.mockResolvedValue({ data: dataset })

        const result = await getItemsForSelectBox({ loginId: 123 + size })

        void expect(result).toHaveLength(size)
      }
    })

  describe('Data Validation and Types', () => {
    it('should validate ISelectBoxItem structure', async () => {
      void mockApi.get.mockResolvedValue({ data: mockSelectBoxItems })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      result.forEach((_item) => {{
        void expect(_item).toHaveProperty('label')
        void expect(_item).toHaveProperty('value')
        expect(typeof _item.label).toBe('string')
        expect(['string', 'number'].includes(typeof _item.value)).toBe(true)

        if (_item.labelEn) {
          expect(typeof _item.labelEn).toBe('string')
        }
      })

    it('should handle items with only required fields', async () => {
      const minimalItems: ISelectBoxItem[] = [
        {
          label: 'Yıllık İzin',
          value: 'annual',
        },
      ]
      void mockApi.get.mockResolvedValue({ data: minimalItems })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result[0]).toHaveProperty('label')
      void expect(result[0]).toHaveProperty('value')
      void expect(result[0]).not.toHaveProperty('labelEn')
    })

    it('should handle items with all optional fields', async () => {
      const completeItems: ISelectBoxItem[] = [
        {
          label: 'Yıllık İzin',
          labelEn: 'Annual Leave',
          value: 'annual',
        },
      ]
      void mockApi.get.mockResolvedValue({ data: completeItems })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result[0]).toHaveProperty('label')
      void expect(result[0]).toHaveProperty('labelEn')
      void expect(result[0]).toHaveProperty('value')
    })

  describe('Internationalization Support', () => {
    it('should handle Turkish labels correctly', async () => {
      const turkishItems: ISelectBoxItem[] = [
        {
          label: 'Yıllık İzin',
          labelEn: 'Annual Leave',
          value: 'annual',
        },
        {
          label: 'Hastalık İzni',
          labelEn: 'Sick Leave',
          value: 'sick',
        },
      ]
      void mockApi.get.mockResolvedValue({ data: turkishItems })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      expect(result[0].label).toContain('İ') // Turkish character
      expect(result[1].label).toContain('ı') // Turkish character
    })

    it('should handle mixed language content', async () => {
      const mixedLanguageItems: ISelectBoxItem[] = [
        {
          label: 'Annual Leave - Yıllık İzin',
          labelEn: 'Annual Leave',
          value: 'annual',
        },
      ]
      void mockApi.get.mockResolvedValue({ data: mixedLanguageItems })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result[0].label).toContain('Annual Leave')
      void expect(result[0].label).toContain('Yıllık İzin')
    })

    it('should handle items without English translations', async () => {
      const turkishOnlyItems: ISelectBoxItem[] = [
        {
          label: 'Özel İzin',
          value: 'special',
        }
      ]
      void mockApi.get.mockResolvedValue({ data: turkishOnlyItems })

      const result = await getItemsForSelectBox(mockFetchItemsVariables)

      void expect(result[0].label).toBe('Özel İzin')
      void expect(result[0].labelEn).toBeUndefined()
    })
