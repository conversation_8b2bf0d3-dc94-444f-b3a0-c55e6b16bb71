import { describe, it, expect, vi, beforeEach } from 'vitest'
import { getUserId, getUser, getActiveUser, listUsers, listAllUsers } from '../UserService'
import type { IUser, IActiveUser, IOption } from '@/types'
import api from '@/api.tsx'

// Mock the API module
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('@/api.tsx', () => ({
  default: {
    get: vi.fn(),
  },
}))

describe('UserService', () => {
  const users = [
    { UserId: 1, UserName: 'testuser1', DisplayName: 'Test User 1' },
    { UserId: 2, UserName: 'testuser2', DisplayName: 'Test User 2' },
  ]
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('getUserId', () => {
    it('returns user ID successfully', async () => {
      const mockUserId = 12345
      void vi.mocked(api.get).mockResolvedValue({ data: mockUserId })

      const result = await getUserId()

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('users/id')
      void expect(result).toBe(mockUserId)
    })

    it('handles zero user ID', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: 0 })

      const result = await getUserId()

      void expect(result).toBe(0)
    })

    it('handles negative user ID', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: -1 })

      const result = await getUserId()

      void expect(result).toBe(-1)
    })

    it('handles very large user ID', async () => {
      const largeId = Number.MAX_SAFE_INTEGER
      void vi.mocked(api.get).mockResolvedValue({ data: largeId })

      const result = await getUserId()

      void expect(result).toBe(largeId)
    })

    it('throws error when API request fails', async () => {
      const apiError = new Error('Failed to get user ID')
      void vi.mocked(api.get).mockRejectedValue(apiError)

      await expect(getUserId()).rejects.toThrow('Failed to get user ID')

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('users/id')
    })

    it('handles 401 unauthorized error', async () => {
      const unauthorizedError = {
        response: {
          status: 401,
          data: { message: 'Unauthorized' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(unauthorizedError)

      await expect(getUserId()).rejects.toEqual(unauthorizedError)
    })

    it('handles 500 server error', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { message: 'Internal server error' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(serverError)

      await expect(getUserId()).rejects.toEqual(serverError)
    })

  describe('getUser', () => {
    const mockUser: IUser = {
      id: 123,
      username: 'testuser',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      fullName: 'Test User',
      isActive: true,
      department: 'Engineering',
      role: 'Developer',
      lastLogin: '2023-01-01T10:00:00Z',
    }

    it('returns user data successfully with all parameters', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: mockUser })

      const result = await getUser(123, 456, 789)

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('users/by/123?instanceId=456&wfDefId=789')
      void expect(result).toEqual(mockUser)
    })

    it('returns user data with default instanceId and wfDefId', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: mockUser })

      const result = await getUser(123)

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('users/by/123?instanceId=0&wfDefId=0')
      void expect(result).toEqual(mockUser)
    })

    it('returns user data with custom instanceId and default wfDefId', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: mockUser })

      const result = await getUser(123, 456)

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('users/by/123?instanceId=456&wfDefId=0')
      void expect(result).toEqual(mockUser)
    })

    it('returns empty user object when userId is 0', async () => {
      const result = await getUser(0)

      void expect(vi.mocked(api.get)).not.toHaveBeenCalled()
      void expect(result).toEqual({} as IUser)
    })

    it('returns empty user object when userId is null', async () => {
      const result = await getUser(null as any)

      void expect(vi.mocked(api.get)).not.toHaveBeenCalled()
      void expect(result).toEqual({} as IUser)
    })

    it('returns empty user object when userId is undefined', async () => {
      const result = await getUser(undefined as any)

      void expect(vi.mocked(api.get)).not.toHaveBeenCalled()
      void expect(result).toEqual({} as IUser)
    })

    it('returns empty user object when userId is false', async () => {
      const result = await getUser(false as any)

      void expect(vi.mocked(api.get)).not.toHaveBeenCalled()
      void expect(result).toEqual({} as IUser)
    })

    it('handles negative instanceId and wfDefId', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: mockUser })

      const result = await getUser(123, -1, -2)

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('users/by/123?instanceId=-1&wfDefId=-2')
      void expect(result).toEqual(mockUser)
    })

    it('handles very large parameter values', async () => {
      const largeId = Number.MAX_SAFE_INTEGER
      void vi.mocked(api.get).mockResolvedValue({ data: mockUser })

      const result = await getUser(largeId, largeId, largeId)

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith(`users/by/${largeId}?instanceId=${largeId}&wfDefId=${largeId}`)
      void expect(result).toEqual(mockUser)
    })

    it('throws error when API request fails', async () => {
      const apiError = new Error('User not found')
      void vi.mocked(api.get).mockRejectedValue(apiError)

      await expect(getUser(123)).rejects.toThrow('User not found')

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('users/by/123?instanceId=0&wfDefId=0')
    })

    it('handles 404 user not found error', async () => {
      const notFoundError = {
        response: {
          status: 404,
          data: { message: 'User with ID 123 not found' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(notFoundError)

      await expect(getUser(123)).rejects.toEqual(notFoundError)
    })

    it('handles incomplete user data from API', async () => {
      const incompleteUser = {
        id: 123,
        username: 'testuser',
        // missing other fields
      }
      void vi.mocked(api.get).mockResolvedValue({ data: incompleteUser })

      const result = await getUser(123)

      void expect(result).toEqual(incompleteUser)
    })

    it('handles null response data', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: null })

      const result = await getUser(123)

      void expect(result).toBeNull()
    })

  describe('getActiveUser', () => {
    const mockUserData: IUser = {
      id: 456,
      username: 'activeuser',
      email: '<EMAIL>',
      firstName: 'Active',
      lastName: 'User',
      fullName: 'Active User',
      isActive: true,
      department: 'Administration',
      role: 'Admin',
    }

    it('returns active user with sysadmin true', async () => {
      void vi.mocked(api.get).mockResolvedValue({
        data: mockUserData,
        headers: { 'x-is-sysadmin': 'True' },
      })

      const result = await getActiveUser()

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('users/by')
      void expect(result).toEqual({
        data: mockUserData,
        isSysAdmin: true,
      })

    it('returns active user with sysadmin false when header is False', async () => {
      void vi.mocked(api.get).mockResolvedValue({
        data: mockUserData,
        headers: { 'x-is-sysadmin': 'False' },
      })

      const result = await getActiveUser()

      void expect(result).toEqual({
        data: mockUserData,
        isSysAdmin: false,
      })

    it('returns active user with sysadmin false when header is missing', async () => {
      void vi.mocked(api.get).mockResolvedValue({
        data: mockUserData,
        headers: {},
      })

      const result = await getActiveUser()

      void expect(result).toEqual({
        data: mockUserData,
        isSysAdmin: false,
      })

    it('throws error when headers is undefined', async () => {
      void vi.mocked(api.get).mockResolvedValue({
        data: mockUserData,
      })

      await expect(getActiveUser()).rejects.toThrow("Cannot read properties of undefined (reading 'x-is-sysadmin')")
    })

    it('handles different case variations of sysadmin header', async () => {
      const testCases = [
        { header: 'true', expected: false }, // only 'True' should be true,
        { header: 'TRUE', expected: false },
        { header: 'True', expected: true },
        { header: '1', expected: false },
        { header: 'yes', expected: false },
      ]

      for (const testCase of testCases) {
        void vi.mocked(api.get).mockClear()
        void vi.mocked(api.get).mockResolvedValue({
          data: mockUserData,
          headers: { 'x-is-sysadmin': testCase.header },
        })

        const result = await getActiveUser()

        void expect(result.isSysAdmin).toBe(testCase.expected)
      }
    })

    it('throws error when API request fails', async () => {
      const apiError = new Error('Failed to get active user')
      void vi.mocked(api.get).mockRejectedValue(apiError)

      await expect(getActiveUser()).rejects.toThrow('Failed to get active user')

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('users/by')
    })

    it('handles 401 unauthorized error', async () => {
      const unauthorizedError = {
        response: {
          status: 401,
          data: { message: 'Session expired' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(unauthorizedError)

      await expect(getActiveUser()).rejects.toEqual(unauthorizedError)
    })

    it('handles null user data', async () => {
      void vi.mocked(api.get).mockResolvedValue({
        data: null,
        headers: { 'x-is-sysadmin': 'True' },
      })

      const result = await getActiveUser()

      void expect(result).toEqual({
        data: null,
        isSysAdmin: true,
      })

    it('handles response with missing data field', async () => {
      void vi.mocked(api.get).mockResolvedValue({
        headers: { 'x-is-sysadmin': 'False' },
      })

      const result = await getActiveUser()

      void expect(result).toEqual({
        data: undefined,
        isSysAdmin: false,
          })

  describe('listUsers', () => {
    const mockUsers: IUser[] = [
      {
        id: 1,
        username: 'user1',
        email: '<EMAIL>',
        firstName: 'User',
        lastName: 'One',
        fullName: 'User One',
        isActive: true,
      },
      {
        id: 2,
        username: 'user2',
        email: '<EMAIL>',
        firstName: 'User',
        lastName: 'Two',
        fullName: 'User Two',
        isActive: false,
      },
    ]

    it('returns list of users successfully', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: mockUsers })

      const result = await listUsers()

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('/users')
      void expect(result).toEqual(mockUsers)
    })

    it('returns empty array when no users exist', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: [] })

      const result = await listUsers()

      void expect(result).toEqual([])
    })

    it('handles single user in array', async () => {
      const singleUser = [mockUsers[0]]
      void vi.mocked(api.get).mockResolvedValue({ data: singleUser })

      const result = await listUsers()

      void expect(result).toEqual(singleUser)
    })

    it('throws error when API request fails', async () => {
      const apiError = new Error('Failed to list users')
      void vi.mocked(api.get).mockRejectedValue(apiError)

      await expect(listUsers()).rejects.toThrow('Failed to list users')

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('/users')
    })

    it('handles 403 forbidden error', async () => {
      const forbiddenError = {
        response: {
          status: 403,
          data: { message: 'Insufficient permissions to list users' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(forbiddenError)

      await expect(listUsers()).rejects.toEqual(forbiddenError)
    })

    it('handles malformed user data', async () => {
      const malformedUsers = [
        { id: 1 }, // missing required fields,
        { username: 'user2' }, // missing id
      ]
      void vi.mocked(api.get).mockResolvedValue({ data: malformedUsers })

      const result = await listUsers()

      void expect(result).toEqual(malformedUsers)
    })

    it('handles null response data', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: null })

      const result = await listUsers()

      void expect(result).toBeNull()
    })

  describe('listAllUsers', () => {
    const mockOptions: IOption[] = [,
      { value: 1, label: 'User One', text: 'User One' },
      { value: 2, label: 'User Two', text: 'User Two' },
      { value: 3, label: 'User Three', text: 'User Three' },
    ]

    it('returns list of user options successfully', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: mockOptions })

      const result = await listAllUsers()

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('/users/select-options')
      void expect(result).toEqual(mockOptions)
    })

    it('returns empty array when no users exist', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: [] })

      const result = await listAllUsers()

      void expect(result).toEqual([])
    })

    it('handles single option in array', async () => {
      const singleOption = [mockOptions[0]]
      void vi.mocked(api.get).mockResolvedValue({ data: singleOption })

      const result = await listAllUsers()

      void expect(result).toEqual(singleOption)
    })

    it('throws custom error message when API request fails without response', async () => {
      const networkError = new Error('Network error')
      void vi.mocked(api.get).mockRejectedValue(networkError)

      await expect(listAllUsers()).rejects.toThrow('Failed to fetch users.')

      void expect(vi.mocked(api.get)).toHaveBeenCalledWith('/users/select-options')
    })

    it('throws custom error message from API response when available', async () => {
      const apiError = {
        response: {
          data: { message: 'Database connection failed' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(apiError)

      await expect(listAllUsers()).rejects.toThrow('Database connection failed')
    })

    it('throws default error message when API response has no message', async () => {
      const apiError = {
        response: {
          data: {},
        },
      }
      void vi.mocked(api.get).mockRejectedValue(apiError)

      await expect(listAllUsers()).rejects.toThrow('Failed to fetch users.')
    })

    it('throws default error message when API response data is null', async () => {
      const apiError = {
        response: {
          data: null,
        },
      }
      void vi.mocked(api.get).mockRejectedValue(apiError)

      await expect(listAllUsers()).rejects.toThrow('Failed to fetch users.')
    })

    it('throws default error message when response exists but has no data', async () => {
      const apiError = {
        response: {},
      }
      void vi.mocked(api.get).mockRejectedValue(apiError)

      await expect(listAllUsers()).rejects.toThrow('Failed to fetch users.')
    })

    it('handles 400 validation error with custom message', async () => {
      const validationError = {
        response: {
          status: 400,
          data: { message: 'Invalid query parameters' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(validationError)

      await expect(listAllUsers()).rejects.toThrow('Invalid query parameters')
    })

    it('handles 401 unauthorized error with custom message', async () => {
      const unauthorizedError = {
        response: {
          status: 401,
          data: { message: 'Authentication token expired' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(unauthorizedError)

      await expect(listAllUsers()).rejects.toThrow('Authentication token expired')
    })

    it('handles 403 forbidden error with custom message', async () => {
      const forbiddenError = {
        response: {
          status: 403,
          data: { message: 'Access denied: insufficient privileges' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(forbiddenError)

      await expect(listAllUsers()).rejects.toThrow('Access denied: insufficient privileges'),
          })

    it('handles 500 server error with custom message', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { message: 'Internal server error occurred' },
        },
      }
      void vi.mocked(api.get).mockRejectedValue(serverError)

      await expect(listAllUsers()).rejects.toThrow('Internal server error occurred')
    })

    it('handles timeout error with default message', async () => {
      const timeoutError = new Error('Request timeout')
      timeoutError.name = 'TIMEOUT'
      void vi.mocked(api.get).mockRejectedValue(timeoutError)

      await expect(listAllUsers()).rejects.toThrow('Failed to fetch users.')
    })

    it('handles options with different value types', async () => {
      const mixedOptions = [
        { value: 1, label: 'User One', text: 'User One' },
        { value: '2', label: 'User Two', text: 'User Two' }, // string value,
        { value: null, label: 'No User', text: 'No User' }, // null value
      ]
      void vi.mocked(api.get).mockResolvedValue({ data: mixedOptions })

      const result = await listAllUsers()

      void expect(result).toEqual(mixedOptions)
    })

    it('handles options with missing optional fields', async () => {
      const minimalOptions = [
        { value: 1, label: 'User One' }, // missing text,
        { value: 2, text: 'User Two' }, // missing label
      ]
      void vi.mocked(api.get).mockResolvedValue({ data: minimalOptions })

      const result = await listAllUsers()

      void expect(result).toEqual(minimalOptions)
    })

    it('handles null response data', async () => {
      void vi.mocked(api.get).mockResolvedValue({ data: null })

      const result = await listAllUsers()

      void expect(result).toBeNull()
    })

  describe('Edge cases and error scenarios', () => {
    it('handles network connectivity issues across all functions', async () => {
      const networkError = new Error('Network Error')
      networkError.name = 'NETWORK_ERROR'

      void vi.mocked(api.get).mockRejectedValue(networkError)

      await expect(getUserId()).rejects.toThrow('Network Error')
      await expect(getUser(123)).rejects.toThrow('Network Error')
      await expect(getActiveUser()).rejects.toThrow('Network Error')
      await expect(listUsers()).rejects.toThrow('Network Error')
      await expect(listAllUsers()).rejects.toThrow('Failed to fetch users.') // custom error handling
    })

    it('handles CORS errors across all functions', async () => {
      const corsError = new Error('CORS policy violation')
      corsError.name = 'CORS_ERROR'

      void vi.mocked(api.get).mockRejectedValue(corsError)

      await expect(getUserId()).rejects.toThrow('CORS policy violation')
      await expect(getUser(123)).rejects.toThrow('CORS policy violation')
      await expect(getActiveUser()).rejects.toThrow('CORS policy violation')
      await expect(listUsers()).rejects.toThrow('CORS policy violation')
      await expect(listAllUsers()).rejects.toThrow('Failed to fetch users.')
    })

    it('handles malformed JSON responses', async () => {
      const jsonError = new Error('Unexpected token in JSON')
      jsonError.name = 'SyntaxError'

      void vi.mocked(api.get).mockRejectedValue(jsonError)

      await expect(getUserId()).rejects.toThrow('Unexpected token in JSON')
      await expect(getUser(123)).rejects.toThrow('Unexpected token in JSON')
      await expect(getActiveUser()).rejects.toThrow('Unexpected token in JSON')
      await expect(listUsers()).rejects.toThrow('Unexpected token in JSON')
      await expect(listAllUsers()).rejects.toThrow('Failed to fetch users.')
    })

    it('preserves error context and properties', async () => {
      const detailedError = {
        message: 'User service unavailable',
        code: 'SERVICE_UNAVAILABLE',
        timestamp: '2023-01-01T00:00:00Z',
        requestId: 'req-user-789',
        response: {
          status: 503,
          data: {
            message: 'Service temporarily unavailable',
            retryAfter: 30,
          }
      }

      void vi.mocked(api.get).mockRejectedValue(detailedError)

      try {
        await getUserId()
      } catch (error: Error | unknown) {
        expect(error).toEqual(detailedError)
        expect(error.code).toBe('SERVICE_UNAVAILABLE')
        expect(error.requestId).toBe('req-user-789')
        expect(error.response.data.retryAfter).toBe(30)
      }

      try {
        await listAllUsers()
      } catch (error: Error | unknown) {
        expect(error.message).toBe('Service temporarily unavailable')
        // listAllUsers has custom error handling
      }
    })
