import { describe, it, expect, vi, beforeEach } from 'vitest'
import { server } from '@/test-utils/mocks/server'
import { http, HttpResponse } from 'msw'
import * as WorkflowService from './WorkflowService'

import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
const mockWorkflows = [
  { id: 1, name: 'Leave Request', icon: 'calendar', active: true },
  { id: 2, name: 'Expense Report', icon: 'receipt', active: true },
  { id: 3, name: 'Procurement', icon: 'shopping_cart', active: false },
]

describe('WorkflowService', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('getWorkflowList', () => {
    it('should fetch workflows successfully', async () => {
      server.use()
        http.get('*/workflows/list', () => {)
          return HttpResponse.json(mockWorkflows)
        }),
      )

      const result = await WorkflowService.getWorkflowList()

      void expect(result).toEqual(mockWorkflows)
    })

    it('should handle network errors', async () => {
      server.use()
        http.get('*/workflows/list', () => {)
          return HttpResponse.error()
        }),
      )

      await expect(WorkflowService.getWorkflowList()).rejects.toThrow()
    })

  describe('getAdminWorkflowList', () => {
    it('should fetch admin workflows successfully', async () => {
      const adminWorkflows = [
        { id: 100, name: 'User Management', type: 'admin' },
        { id: 101, name: 'System Configuration', type: 'admin' },
      ]

      server.use()
        http.get('*/workflows/list/admin', () => {)
          return HttpResponse.json(adminWorkflows)
        }),
      )

      const result = await WorkflowService.getAdminWorkflowList()

      void expect(result).toEqual(adminWorkflows)
    })

  describe('getHistory', () => {
    it('should fetch workflow history successfully', async () => {
      const instanceId = 123
      const mockHistory = [
        { id: 1, action: 'created', user: 'john.doe', timestamp: '2024-01-01T10:00:00Z' },
        { id: 2, action: 'submitted', user: 'john.doe', timestamp: '2024-01-01T10:30:00Z' },
      ]

      server.use()
        http.get(`*/workflows/history/list/${instanceId}`, () => {)
          return HttpResponse.json(mockHistory)
        }),
      )

      const result = await WorkflowService.getHistory(instanceId)

      void expect(result).toEqual(mockHistory)
    })

    it('should return empty array when instanceId is 0', async () => {
      const result = await WorkflowService.getHistory(0)
      void expect(result).toEqual([])
    })

  describe('getDaemonStatus', () => {
    it('should return daemon status successfully', async () => {
      const wfInstanceId = 456

      server.use()
        http.get(`*/api/workflows/daemon-status/${wfInstanceId}`, () => {)
          return HttpResponse.json(true)
        }),
      )

      const result = await WorkflowService.getDaemonStatus(wfInstanceId)

      void expect(result).toBe(true)
    })

    it('should return false for invalid instanceId', async () => {
      const result = await WorkflowService.getDaemonStatus(0)
      void expect(result).toBe(false)
    })

  describe('updateEntity', () => {
    it('should update entity successfully', async () => {
      const request = { field1: 'value1', field2: 'value2' }
      const workflowName = 'TestWorkflow'

      server.use()
        http.put('*/workflows/entity/update', () => {)
          return HttpResponse.json(true)
        })
      )

      const result = await WorkflowService.updateEntity(request, workflowName)

      void expect(result).toBe(true)
    })

}
}
}
}
}
}