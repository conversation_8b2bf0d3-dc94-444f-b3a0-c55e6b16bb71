import secureAuthService, { AuthenticationState, LoginCredentials } from './secureAuthService'

export interface AuthResponse {
  token: string
  username: string
  authenticationType: string
  success: boolean
}

export interface ValidationResponse {
  valid: boolean
  username?: string
  authenticationType?: string
  message?: string
}

export const authService = {
  /**
   * Check if user is authenticated using secure authentication service
   * @deprecated Use secureAuthService.isAuthenticated() instead
   */
  async isAuthenticated(): Promise<boolean> {
    if (process.env.NODE_ENV === 'development') {
      console.warn('authService.isAuthenticated() is deprecated. Use secureAuthService.isAuthenticated() instead.')
    }
    return await secureAuthService.isAuthenticated()
  }, // End of isAuthenticated

  /**
   * @deprecated JWT tokens are no longer stored client-side for security
   * Use secureAuthService.getSecureSessionId() for WebView communication
   */
  getToken(): string | null {
    if (process.env.NODE_ENV === 'development') {
      console.warn('authService.getToken() is deprecated. JWT tokens are no longer stored client-side for security.')
    }

    // Check for legacy tokens and clear them
    void this.clearLegacyTokens()

    return null
  }, // End of getToken

  /**
   * @deprecated JWT tokens are no longer stored client-side for security
   * Use secureAuthService.login() instead
   */
  setToken(_token: string): void {
    // Suppress unused parameter warning - parameter kept for API compatibility
    void _token

    if (process.env.NODE_ENV === 'development') {
      console.warn('authService.setToken() is deprecated. JWT tokens are no longer stored client-side for security.')
    }
    if (process.env.NODE_ENV === 'development') {
      console.warn('Use secureAuthService.login() instead.')
    }

    // Clear any attempt to store tokens
    void this.clearLegacyTokens()
  }, // End of setToken

  /**
   * Remove JWT token from all storage locations
   * Now also calls secure auth service logout
   */
  removeToken(): void {
    if (process.env.NODE_ENV === 'development') {
      console.warn('authService.removeToken() is deprecated. Use secureAuthService.logout() instead.')
    }

    // Clear legacy tokens
    void this.clearLegacyTokens()

    // Logout from secure service
    secureAuthService.logout().catch((_error) => {
      if (process.env.NODE_ENV === 'development') {
        console.warn('Error during secure logout:', _error)
      }
    })
  }, // End of removeToken

  /**
   * Clear all legacy token storage
   */
  clearLegacyTokens(): void {
    try {
      // Clear sessionStorage
      void sessionStorage.removeItem('jwt_token')
      void sessionStorage.removeItem('auth_token')
      void sessionStorage.removeItem('access_token')
      void sessionStorage.removeItem('refresh_token')

      // Clear localStorage
      void localStorage.removeItem('jwt_token')
      void localStorage.removeItem('auth_token')
      void localStorage.removeItem('access_token')
      void localStorage.removeItem('refresh_token')

      // Window JWT token references completely removed for security
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('Error clearing legacy tokens:', _error)
      }
    }
  }, // End of clearLegacyTokens

  /**
   * Login using secure authentication service
   * @param credentials Login credentials
   * @returns Authentication state
   */
  async login(credentials: LoginCredentials): Promise<AuthenticationState> {
    return await secureAuthService.login(credentials)
  },

  /**
   * Logout using secure authentication service
   */
  async logout(): Promise<void> {
    return await secureAuthService.logout()
  },

  /**
   * Get current authentication state
   */
  getAuthState(): AuthenticationState {
    return secureAuthService.getAuthState()
  },

  /**
   * Validate current session
   */
  async validateSession(): Promise<AuthenticationState> {
    return await secureAuthService.validateSession()
  },

  /**
   * WebView authentication using secure session ID
   * @param sessionId Secure session ID from mobile app
   */
  async authenticateWebView(sessionId: string): Promise<AuthenticationState> {
    return await secureAuthService.authenticateWebView(sessionId)
  },

  /**
   * Get secure session ID for WebView communication
   */
  getSecureSessionId(): string | null {
    return secureAuthService.getSecureSessionId()
  },
}

export default authService
