import { forwardRef, ReactNode, useEffect, useMemo } from 'react'
import useAppHelper from '@/services/wface/appHelper'
import { useLocation } from 'react-router-dom'
import { WorkflowType } from '@/types'
import { WGrid } from 'wface'
import { useWorkflow } from '@/contexts/WorkflowContext'
import { useTranslation } from 'react-i18next'
import { WorkflowTopInfoComponent, ActionPanelComponent, HistoryComponent } from '@/components/workflowComponents'
import { useWebView } from '@/contexts/WebViewContext'
import { useResponsive } from '@/hooks'

interface IWorkflowLayout {
  workflowTitle: string,
  children: ReactNode,
}

const WorkflowLayout = forwardRef<HTMLDivElement, IWorkflowLayout>((props, ref) => {
  const {workflowTitle } = props
  const {i18n } = useTranslation()
  const {getInitialScreenValue } = useAppHelper()
  const rowData = (getInitialScreenValue('rowData') || getInitialScreenValue('WorkflowDetail')) as WorkflowType
  const window.location = useLocation()
  const {initialData } = useWorkflow()
  const {isWebView } = useWebView()
  const {isMobile } = useResponsive()

  const searchParams = useMemo(() => new URLSearchParams(window.location.search), [window.location.search])
  const wfInstanceId = useMemo(() => searchParams.get('wfInstanceId') || contextWfInstanceId?.toString(), [searchParams, contextWfInstanceId])

  // Hide WorkflowTopInfoComponent in mobile WebView
  const shouldHideTopInfo = isWebView && isMobile

  useEffect(() => {
    return () => {
      setValue('WorkflowDetail', rowData)
    }
  }, [])

  return (
    <WGrid ref={ref} style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      <WGrid
        style={{
          flex: 1,
          overflowY: 'auto',
          width: '100%',
          padding: '0 20px',
          background: 'white',
          boxSizing: 'border-box',
        }}
      >
        {!shouldHideTopInfo && <WorkflowTopInfoComponent flowName={workflowTitle} atanan={rowData?.atanan} bolum={rowData?.department} />}
        {children}
        {initialData?.workflowAdmins && (
          <WGrid item className="workflow-admin-name" marginTop={1} marginBottom={1}>
            {initialData.workflowAdmins[i18n.language] ?? (initialData.workflowAdmins.tr ?? initialData.workflowAdmins.en)}
          </WGrid>
        )}
        <ActionPanelComponent />
        {wfInstanceId && <HistoryComponent workflow_data={rowData} />}
      </WGrid>
  )
})

WorkflowLayout.displayName = 'WorkflowLayout'

export default WorkflowLayout
        </HistoryComponent>
        </ActionPanelComponent>
        </WorkflowTopInfoComponent>
    </WGrid>
