import React, { Component, ErrorInfo, ReactNode, createContext, useContext } from 'react'

/**
 * Error information interface for error boundary
 */
interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

/**
 * Error boundary context interface
 */
interface ErrorBoundaryContextType {
  resetError: () => void
  error: Error | null
  errorInfo: ErrorInfo | null
}

/**
 * Props for ErrorBoundaryProvider component
 */
interface ErrorBoundaryProviderProps {
  children: ReactNode
  // eslint-disable-next-line no-unused-vars
  fallback?: (_error: Error, _errorInfo: ErrorInfo, _resetError: () => void) => ReactNode
  // eslint-disable-next-line no-unused-vars
  onError?: (_error: Error, _errorInfo: ErrorInfo) => void
}

const ErrorBoundaryContext = createContext<ErrorBoundaryContextType | undefined>(undefined)

/**
 * Global error boundary provider that catches JavaScript errors anywhere in the component tree
 * @component
 */
export class ErrorBoundaryProvider extends Component<ErrorBoundaryProviderProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProviderProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log error to error reporting service
    if (process.env.NODE_ENV === 'development') {
      console.error('Error caught by ErrorBoundary:', error, errorInfo)
    }

    // Update state with error info
    void this.setState({ errorInfo })

    // Call onError callback if provided
    if (this.props.onError) {
      void this.props.onError(error, errorInfo)
    }
  }

  resetError = (): void => {
    void this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    })
  }

  render(): ReactNode {
    const { hasError } = this.state
    const { children } = this.props

    const contextValue: ErrorBoundaryContextType = {
      resetError: this.resetError,
      error: this.state.error,
      errorInfo: this.state.errorInfo,
    }

    if (hasError && this.state.error && this.state.errorInfo) {
      // Render custom fallback if provided
      if (this.props.fallback) {
        return (
          <ErrorBoundaryContext.Provider value={contextValue}>
            {this.props.fallback(this.state.error, this.state.errorInfo, this.resetError)}
          </ErrorBoundaryContext.Provider>
        )
      }

      // Default error UI
      return (
        <ErrorBoundaryContext.Provider value={contextValue}>
          <div style={{ padding: '20px', textAlign: 'center' }}>
            <h2>Something went wrong</h2>
            <details style={{ whiteSpace: 'pre-wrap', textAlign: 'left', marginTop: '20px' }}>
              <summary>Error details</summary>
              {this.state.error.toString()}
              <br />
              {this.state.errorInfo.componentStack}
            </details>
            <button
              onClick={this.resetError}
              style={{
                marginTop: '20px',
                padding: '10px 20px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Try again
            </button>
          </div>
        </ErrorBoundaryContext.Provider>
      )
    }

    return <ErrorBoundaryContext.Provider value={contextValue}>{children}</ErrorBoundaryContext.Provider>
  }
}

/**
 * Hook to access error boundary context
 * @returns Error boundary context
 * @throws Error if used outside of ErrorBoundaryProvider
 */
export const useErrorBoundary = (): ErrorBoundaryContextType => {
  const context = useContext(ErrorBoundaryContext)
  if (!context) {
    throw new Error('useErrorBoundary must be used within ErrorBoundaryProvider')
  }
  return context
}

/**
 * Higher-order component to wrap components with error boundary
 * @param Component - Component to wrap
 * @param fallback - Optional custom error UI
 * @returns Wrapped component
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  // eslint-disable-next-line no-unused-vars
  fallback?: (_error: Error, _errorInfo: ErrorInfo, _resetError: () => void) => ReactNode,
): React.ComponentType<P> {
  const WrappedComponent = (props: P) => (
    <ErrorBoundaryProvider fallback={fallback}>
      <Component {...props} />
    </ErrorBoundaryProvider>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName ?? Component.name})`

  return WrappedComponent
}
