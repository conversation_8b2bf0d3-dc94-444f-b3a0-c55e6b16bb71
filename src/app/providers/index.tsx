import React, { ReactNode } from 'react'
import { ErrorBoundaryProvider } from './ErrorBoundaryProvider'
import { ThemeProvider } from './ThemeProvider'
import { QueryProvider } from './QueryProvider'
import { FeatureFlagProvider } from './FeatureFlagProvider'
import { AnalyticsProvider } from './AnalyticsProvider'

/**
 * Props for AppProviders component
 */
interface AppProvidersProps {
  children: ReactNode
}

/**
 * Main application providers component that combines all providers in the correct order
 *
 * Provider order (outer to inner):
 * 1. ErrorBoundaryProvider - Catches all errors (outermost)
 * 2. ThemeProvider - Provides theme before any UI renders
 * 3. QueryProvider - Data fetching setup
 * 4. FeatureFlagProvider - Feature toggles
 * 5. AnalyticsProvider - Analytics tracking
 *
 * Note: Authentication is handled by existing Windows Auth + JWT system for mobile WebView
 *
 * @component
 */
export const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <ErrorBoundaryProvider
      fallback={(error, _errorInfo, resetError) => (
        <div
          style={{
            minHeight: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '20px',
            textAlign: 'center',
          }}
        >
          <div>
            <h1 style={{ fontSize: '2rem', marginBottom: '1rem' }}>Oops! Something went wrong</h1>
            <p style={{ marginBottom: '2rem', color: '#666' }}>We're sorry for the inconvenience. Please try refreshing the page.</p>
            <button
              onClick={resetError}
              style={{
                padding: '12px 24px',
                fontSize: '1rem',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Refresh Page
            </button>
            {process.env.NODE_ENV === 'development' && (
              <details style={{ marginTop: '2rem', textAlign: 'left' }}>
                <summary style={{ cursor: 'pointer' }}>Error Details</summary>
                <pre
                  style={{
                    marginTop: '1rem',
                    padding: '1rem',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '4px',
                    overflow: 'auto',
                  }}
                >
                  {error.toString()}
                  {_errorInfo.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      )}
      onError={(_error, _errorInfo) => {
        // Log to error reporting service in production
        if (process.env.NODE_ENV === 'production') {
          // TODO: Send to error reporting service (e.g., Sentry)
          console.error('Application error:', _error, _errorInfo)
        }
      }}
    >
      <ThemeProvider defaultMode="system" enableSystem={true} storageKey="digiflow-theme" disableTransitionOnChange={false}>
        <QueryProvider
          enableDevtools={process.env.NODE_ENV === 'development'}
          devtoolsPosition="bottom-right"
          onError={(error) => {
            // Global query error handler
            if (process.env.NODE_ENV === 'development') {
              console.error('Query error:', error)
            }
          }}
          // eslint-disable-next-line no-unused-vars
          onSuccess={(_data) => {
            // Global query success handler
            if (process.env.NODE_ENV === 'development') {
              // Query success logging removed for production
            }
          }}
        >
          <FeatureFlagProvider
            enableStorage={true}
            storageKey="digiflow-features"
            refreshInterval={5 * 60 * 1000} // 5 minutes
            fetchFlags={async () => {
              // TODO: Implement remote feature flag fetching
              // const response = await fetch('/api/feature-flags');
              // return response.json();
              return {}
            }}
            // eslint-disable-next-line no-unused-vars
            onFlagChange={(_key, _flag) => {
              if (process.env.NODE_ENV === 'development') {
                // Feature flag change logging removed for production
              }
            }}
          >
            <AnalyticsProvider
              enabled={process.env.NODE_ENV === 'production'}
              debug={process.env.NODE_ENV === 'development'}
              trackPageViews={true}
              ignoreRoutes={['/admin', '/debug']}
              beforeTrack={(event) => {
                // Add common properties to all events
                return {
                  ...event,
                  properties: {
                    ...event.properties,
                    app_version: process.env.REACT_APP_VERSION ?? 'unknown',
                    environment: process.env.NODE_ENV,
                  },
                }
              }}
            >
              {children}
            </AnalyticsProvider>
          </FeatureFlagProvider>
        </QueryProvider>
      </ThemeProvider>
    </ErrorBoundaryProvider>
  )
}

/**
 * Re-export all providers and hooks for convenience
 */
export { ErrorBoundaryProvider, useErrorBoundary, withErrorBoundary } from './ErrorBoundaryProvider'
export { ThemeProvider, useTheme, useCSSVariable, type Theme, type ThemeMode } from './ThemeProvider'
export { QueryProvider, useQueryClient, useQuery, useMutation, useInfiniteQuery, invalidateQueries, refetchQueries } from './QueryProvider'
export {
  FeatureFlagProvider,
  useFeatureFlags,
  useFeature,
  useFeatureValue,
  useFeatureVariant,
  Feature,
  type FeatureFlag,
  type FeatureFlags,
} from './FeatureFlagProvider'
export {
  AnalyticsProvider,
  useAnalytics,
  useTrack,
  useTrackMount,
  useTrackClick,
  type AnalyticsEvent,
  type AnalyticsAdapter,
} from './AnalyticsProvider'
