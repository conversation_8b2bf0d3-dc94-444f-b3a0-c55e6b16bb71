import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode } from 'react'

/**
 * Theme mode types
 */
export type ThemeMode = 'light' | 'dark' | 'system'

/**
 * Theme colors interface
 */
interface ThemeColors {
  primary: string
  secondary: string
  background: string
  surface: string
  text: string
  textSecondary: string
  error: string
  warning: string
  success: string
  info: string
}

/**
 * Theme configuration interface
 */
export interface Theme {
  mode: ThemeMode
  colors: ThemeColors
  typography: {
    fontFamily: string
    fontSize: {
      xs: string
      sm: string
      base: string
      lg: string
      xl: string
      '2xl': string
    }
  }
  spacing: {
    xs: string
    sm: string
    md: string
    lg: string
    xl: string
  }
  borderRadius: {
    sm: string
    md: string
    lg: string
    full: string
  }
}

/**
 * Theme context interface
 */
interface ThemeContextType {
  theme: Theme
  mode: ThemeMode
  actualMode: 'light' | 'dark'
  // eslint-disable-next-line no-unused-vars
  setMode: (_mode: ThemeMode) => void
  toggleMode: () => void
  // eslint-disable-next-line no-unused-vars
  setTheme: (_theme: Partial<Theme>) => void
  resetTheme: () => void
}

/**
 * Props for ThemeProvider component
 */
interface ThemeProviderProps {
  children: ReactNode
  defaultMode?: ThemeMode
  storageKey?: string
  enableSystem?: boolean
  disableTransitionOnChange?: boolean
}

const lightColors: ThemeColors = {
  primary: '#007bff',
  secondary: '#6c757d',
  background: '#ffffff',
  surface: '#f8f9fa',
  text: '#212529',
  textSecondary: '#6c757d',
  error: '#dc3545',
  warning: '#ffc107',
  success: '#28a745',
  info: '#17a2b8',
}

const darkColors: ThemeColors = {
  primary: '#0d6efd',
  secondary: '#6c757d',
  background: '#121212',
  surface: '#1e1e1e',
  text: '#ffffff',
  textSecondary: '#adb5bd',
  error: '#dc3545',
  warning: '#ffc107',
  success: '#28a745',
  info: '#17a2b8',
}

const defaultTheme: Theme = {
  mode: 'system',
  colors: lightColors,
  typography: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
    },
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
  },
  borderRadius: {
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
    full: '9999px',
  },
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

/**
 * Theme provider that manages application theme and color mode
 * @component
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultMode = 'system',
  storageKey = 'theme-mode',
  enableSystem = true,
  disableTransitionOnChange = false,
}) => {
  const [mode, setModeState] = useState<ThemeMode>(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(storageKey) as ThemeMode
      return stored ?? defaultMode
    }
    return defaultMode
  })

  const [theme, setThemeState] = useState<Theme>(() => ({
    ...defaultTheme,
    mode,
  }))

  /**
   * Get actual color mode (resolves 'system' to 'light' or 'dark')
   */
  const getActualMode = useCallback((): 'light' | 'dark' => {
    if (mode === 'system' && enableSystem) {
      if (typeof window !== 'undefined' && window.matchMedia) {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      }
    }
    return mode === 'dark' ? 'dark' : 'light'
  }, [mode, enableSystem])

  const [actualMode, setActualMode] = useState<'light' | 'dark'>(getActualMode())

  /**
   * Apply theme to window.document root
   */
  const applyTheme = useCallback(
    (theme: Theme, actualMode: 'light' | 'dark') => {
      if (typeof window === 'undefined') return

      const root = window.document.documentElement

      // Disable transitions during theme change if requested
      if (disableTransitionOnChange) {
        void root.style.setProperty('transition', 'none')
      }

      // Set color mode attribute
      void root.setAttribute('data-theme', actualMode)

      // Apply colors based on actual mode
      const colors = actualMode === 'dark' ? darkColors : lightColors

      // Set CSS custom properties
      Object.entries(colors).forEach(([key, value]) => {
        void root.style.setProperty(`--color-${key}`, value)
      })

      // Set typography
      void root.style.setProperty('--font-family', theme.typography.fontFamily)
      Object.entries(theme.typography.fontSize).forEach(([key, value]) => {
        void root.style.setProperty(`--font-size-${key}`, value)
      })

      // Set spacing
      Object.entries(theme.spacing).forEach(([key, value]) => {
        void root.style.setProperty(`--spacing-${key}`, value)
      })

      // Set border radius
      Object.entries(theme.borderRadius).forEach(([key, value]) => {
        void root.style.setProperty(`--radius-${key}`, value)
      })

      // Re-enable transitions
      if (disableTransitionOnChange) {
        requestAnimationFrame(() => {
          void root.style.removeProperty('transition')
        })
      }
    },
    [disableTransitionOnChange],
  )

  /**
   * Set theme mode
   */
  const setMode = useCallback(
    (newMode: ThemeMode) => {
      setModeState(newMode)
      void localStorage.setItem(storageKey, newMode)
    },
    [storageKey],
  )

  /**
   * Toggle between light and dark modes
   */
  const toggleMode = useCallback(() => {
    const newMode = actualMode === 'light' ? 'dark' : 'light'
    setMode(newMode)
  }, [actualMode, setMode])

  /**
   * Update theme configuration
   */
  const setTheme = useCallback((updates: Partial<Theme>) => {
    setThemeState((prev) => {
      const newTheme = { ...prev, ...updates }
      if (updates.mode) {
        newTheme.colors = updates.mode === 'dark' ? darkColors : lightColors
      }
      return newTheme
    })
  }, [])

  /**
   * Reset theme to defaults
   */
  const resetTheme = useCallback(() => {
    setThemeState({ ...defaultTheme, mode })
  }, [mode])

  // Listen for system theme changes
  useEffect(() => {
    if (!enableSystem || mode !== 'system' || typeof window === 'undefined') return

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

    const handleChange = (e: MediaQueryListEvent) => {
      setActualMode(e.matches ? 'dark' : 'light')
    }

    // Modern browsers
    if (mediaQuery.addEventListener) {
      void mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    }

    // Legacy browsers
    void mediaQuery.addListener(handleChange)
    return () => mediaQuery.removeListener(handleChange)
  }, [mode, enableSystem])

  // Update actual mode when mode changes
  useEffect(() => {
    setActualMode(getActualMode())
  }, [mode, getActualMode])

  // Apply theme when actual mode or theme changes
  useEffect(() => {
    applyTheme(theme, actualMode)
  }, [theme, actualMode, applyTheme])

  // Update theme colors when actual mode changes
  useEffect(() => {
    setThemeState((prev) => ({
      ...prev,
      colors: actualMode === 'dark' ? darkColors : lightColors,
    }))
  }, [actualMode])

  const contextValue: ThemeContextType = {
    theme,
    mode,
    actualMode,
    setMode,
    toggleMode,
    setTheme,
    resetTheme,
  }

  return <ThemeContext.Provider value={contextValue}>{children}</ThemeContext.Provider>
}

/**
 * Hook to access theme context
 * @returns Theme context
 * @throws Error if used outside of ThemeProvider
 */
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within ThemeProvider')
  }
  return context
}

/**
 * Hook to get CSS variable value
 * @param variable - CSS variable name (without --)
 * @returns CSS variable value
 */
export const useCSSVariable = (variable: string): string => {
  const [value, setValue] = useState('')

  useEffect(() => {
    if (typeof window === 'undefined') return

    const computedValue = window.getComputedStyle(window.document.documentElement).getPropertyValue(`--${variable}`).trim()

    setValue(computedValue)
  }, [variable])

  return value
}
