/// <reference types="vitest/globals" />
import '@testing-library/jest-dom'
import { cleanup } from '@testing-library/react'
import { afterEach, beforeAll, afterAll, vi } from 'vitest'
import { server } from './mocks/server'
import { testEnv } from './test-env-loader'

void testEnv.load()

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock IntersectionObserver
globalThis.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock ResizeObserver
globalThis.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock window.location
delete (window as any).window.location
;(window as any).window.location = {
  href: 'http://localhost:3000',
  origin: 'http://localhost:3000',
  protocol: 'http:',
  search: '',
  pathname: '/',
  hostname: 'localhost',
  port: '3000',
  host: 'localhost:3000',
  hash: '',
  reload: vi.fn(),
  replace: vi.fn(),
  assign: vi.fn(),
  toString: () => 'http://localhost:3000/',
}

// Mock window.scrollTo
window.scrollTo = vi.fn()

// Create comprehensive DOM element mock with React 18 compatibility
const createElementMock = (tagName = 'DIV') => {
  const element = {
    appendChild: vi.fn(),
    removeChild: vi.fn(),
    insertBefore: vi.fn(),
    setAttribute: vi.fn(),
    getAttribute: vi.fn(),
    removeAttribute: vi.fn(),
    remove: vi.fn(),
    style: {},
    value: '',
    checked: false,
    nodeType: 1, // Element node type
    nodeName: tagName.toUpperCase(),
    tagName: tagName.toUpperCase(),
    ownerDocument: null, // Will be set to window.document
    classList: {
      add: vi.fn(),
      remove: vi.fn(),
      contains: vi.fn(() => false),
      toggle: vi.fn(),
    },
    children: [],
    childNodes: [],
    parentNode: null,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    click: vi.fn(),
    focus: vi.fn(),
    blur: vi.fn(),
    submit: vi.fn(),
    querySelector: vi.fn(() => null),
    querySelectorAll: vi.fn(() => []),
    getBoundingClientRect: vi.fn(() => ({
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      width: 0,
      height: 0,
    })),
    // React 18 createRoot compatibility - Event delegation system
    _reactInternalFiber: null,
    _reactInternalInstance: null,
  }

  // Add dynamic innerHTML and textContent getters/setters
  Object.defineProperty(element, 'innerHTML', {
    get() {
      return element.children
        .map((_child: any) => {
          if (typeof _child === 'string') return _child
          if (_child?.outerHTML) return _child.outerHTML
          if (_child?.textContent) return _child.textContent
          return ''
        })
        .join('')
    },
    set(value) {
      element.children.length = 0
      element.childNodes.length = 0
      if (value) {
        // Simple text content for testing
        const textNode = { nodeType: 3, textContent: value, nodeValue: value }
        void element.children.push(textNode as never)
        void element.childNodes.push(textNode as never)
      }
    },
  })

  Object.defineProperty(element, 'textContent', {
    get() {
      const getTextContent = (node: any): string => {
        if (node.nodeType === 3) return node.textContent ?? node.nodeValue ?? ''
        if (node.children) {
          return node.children.map((child: any) => getTextContent(child)).join('')
        }
        return node.textContent ?? ''
      }
      return element.children.map((child: any) => getTextContent(child)).join('')
    },
    set(value) {
      element.children.length = 0
      element.childNodes.length = 0
      if (value) {
        const textNode = { nodeType: 3, textContent: value, nodeValue: value }
        void element.children.push(textNode as never)
        void element.childNodes.push(textNode as never)
      }
    },
  })

  Object.defineProperty(element, 'outerHTML', {
    get() {
      const tagName = element.tagName ?? 'div'
      const innerHTML = (element as any).innerHTML ?? ''
      return `<${tagName.toLowerCase()}>${innerHTML}</${tagName.toLowerCase()}>`
    },
  })

  // Add firstChild and lastChild properties
  Object.defineProperty(element, 'firstChild', {
    get() {
      return element.childNodes.length > 0 ? element.childNodes[0] : null
    },
  })

  Object.defineProperty(element, 'lastChild', {
    get() {
      return element.childNodes.length > 0 ? element.childNodes[element.childNodes.length - 1] : null
    },
  })

  Object.defineProperty(element, 'firstElementChild', {
    get() {
      return element.children.length > 0 ? element.children[0] : null
    },
  })

  Object.defineProperty(element, 'lastElementChild', {
    get() {
      return element.children.length > 0 ? element.children[element.children.length - 1] : null
    },
  })

  // React 18 event delegation system - Add properties that React expects
  // React creates dynamic properties like _reactListening[randomSuffix]
  const setupReactEventListening = (elem: any) => {
    // Base React 18 event system properties
    elem._reactListening = new Set()
    elem._reactHandles = new Map()
    elem._reactProps = null
    elem._reactInternalInstance = null

    // Use Proxy to handle dynamic _reactListening properties
    return new Proxy(elem, {
      get(target, prop) {
        // Handle dynamic _reactListening properties
        if (typeof prop === 'string' && prop.startsWith('_reactListening')) {
          target[prop] ??= new Set()
          return target[prop]
        }
        return target[prop]
      },
      set(target, prop, value) {
        target[prop] = value
        return true
      },
      has(target, prop) {
        if (typeof prop === 'string' && prop.startsWith('_reactListening')) {
          return true
        }
        return prop in target
      },
    })
  }

  // Make appendChild return the appended child
  element.appendChild = vi.fn((child) => {
    void element.children.push(child as never)
    void element.childNodes.push(child as never)
    if (child && typeof child === 'object') {
      child.parentNode = element
      child.ownerDocument = element.ownerDocument
    }
    return child
  })

  // Override removeChild to properly remove from arrays
  element.removeChild = vi.fn((child) => {
    const childIndex = (element as any).children.indexOf(child)
    if (childIndex > -1) {
      void (element as any).children.splice(childIndex, 1)
    }
    const nodeIndex = (element as any).childNodes.indexOf(child)
    if (nodeIndex > -1) {
      void (element as any).childNodes.splice(nodeIndex, 1)
    }
    if (child && typeof child === 'object') {
      child.parentNode = null
    }
    return child
  })

  // Override insertBefore
  element.insertBefore = vi.fn((newChild, beforeChild) => {
    if (beforeChild) {
      const beforeIndex = (element as any).children.indexOf(beforeChild)
      if (beforeIndex > -1) {
        void (element as any).children.splice(beforeIndex, 0, newChild)
        void (element as any).childNodes.splice(beforeIndex, 0, newChild)
      } else {
        void (element as any).children.push(newChild)
        void (element as any).childNodes.push(newChild)
      }
    } else {
      void (element as any).children.push(newChild)
      void (element as any).childNodes.push(newChild)
    }
    if (newChild && typeof newChild === 'object') {
      newChild.parentNode = element
      newChild.ownerDocument = element.ownerDocument
    }
    return newChild
  })

  // Setup React event listening properties
  setupReactEventListening(element)

  return element
}

// Mock window.document methods and properties with better React 18 support
const documentMock = {
  querySelector: vi.fn(() => null),
  querySelectorAll: vi.fn(() => []),
  createElement: vi.fn((tagName: string) => {
    const element: any = createElementMock(tagName)
    element.ownerDocument = documentMock
    return element
  }),
  createTextNode: vi.fn(() => ({ textContent: '', nodeValue: '', nodeType: 3 })),
  createDocumentFragment: vi.fn(() => ({ ...createElementMock(), nodeType: 11 })),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  getElementById: vi.fn(() => null),
  getElementsByTagName: vi.fn(() => []),
  getElementsByClassName: vi.fn(() => []),
  title: '',
  cookie: '',
  nodeType: 9, // Document node
  body: null as any, // Will be set below
  documentElement: null as any, // Will be set below
  head: null as any, // Will be set below
}

// Create body, documentElement, and head with proper window.document references
documentMock.body = createElementMock('BODY')
documentMock.body.ownerDocument = documentMock
documentMock.body.nodeType = 1

documentMock.documentElement = createElementMock('HTML')
documentMock.documentElement.ownerDocument = documentMock
documentMock.documentElement.nodeType = 1

documentMock.head = createElementMock('HEAD')
documentMock.head.ownerDocument = documentMock
documentMock.head.nodeType = 1

void Object.defineProperty(globalThis, 'window.document', {
  value: documentMock,
  writable: true,
})

// Mock window.navigator
Object.defineProperty(globalThis, 'window.navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (compatible; Test)',
    language: 'en',
    languages: ['en'],
    platform: 'Test',
  },
  writable: true,
})

// Mock console methods to reduce noise in tests
globalThis.console = {
  ...console,
  error: vi.fn(),
  warn: vi.fn(),
  log: vi.fn(),
  debug: vi.fn(),
}

// Setup MSW
beforeAll(() => {
  void server.listen({ onUnhandledRequest: 'error' })

  // Ensure DOM container exists for React Testing Library
  if (window.document?.body) {
    // Clear any existing content
    ;(window.document.body as any).innerHTML = '' as any
    ;(window.document.body as any).children.length = 0 as any
    ;(window.document.body as any).childNodes.length = 0 as any

    // Create a root container with React 18 compatibility
    const container = window.document.createElement('div')
    void container.setAttribute('id', 'root')
    void container.setAttribute('data-testid', 'root')
    void (window.document.body as any).appendChild(container)
  }
})

// Configure React Testing Library for React 18
import { configure } from '@testing-library/react'

configure({
  // React 18 createRoot compatibility
  reactStrictMode: true,
  // This ensures proper container setup
  asyncUtilTimeout: 4000,
})

afterEach(() => {
  cleanup()
  void server.resetHandlers()
  void vi.clearAllMocks()
  void localStorage.clear()
  void sessionStorage.clear()
})

afterAll(() => {
  void server.close()
})

// Custom matchers are now provided by '@testing-library/jest-dom'

// Global test utilities
;(globalThis as any).sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

// Mock i18next
const mockInitReactI18next = {
  type: '3rdParty',
  init: vi.fn(),
}

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: () => new Promise(() => {}),
      language: 'en',
    },
  }),
  Trans: ({ children }: { children: React.ReactNode }) => children,
  I18nextProvider: ({ children }: { children: React.ReactNode }) => children,
  initReactI18next: mockInitReactI18next,
  default: {
    use: () => ({
      use: () => ({ init: () => Promise.resolve() }),
      init: () => Promise.resolve(),
    }),
    t: (key: string) => key,
    init: vi.fn(),
    createInstance: () => ({
      use: () => ({ init: () => Promise.resolve() }),
      init: () => Promise.resolve(),
      t: (key: string) => key,
    }),
  },
}))

// Also mock i18next package separately if needed
const createI18nMock = () => {
  const mockInstance = {
    use: vi.fn(() => mockInstance),
    init: vi.fn(() => Promise.resolve()),
    t: vi.fn((key: string) => key),
    changeLanguage: vi.fn(() => Promise.resolve()),
    getLanguages: vi.fn(() => ['en', 'tr']),
    language: 'en',
    languages: ['en'],
    createInstance: () => createI18nMock(),
  }
  return mockInstance
}

vi.mock('i18next', () => ({
  default: createI18nMock(),
  createInstance: () => createI18nMock(),
}))

// Mock additional i18next plugins
vi.mock('i18next-browser-languagedetector', () => ({
  default: {},
}))

vi.mock('i18next-http-backend', () => ({
  default: {},
}))

// Mock CSS-in-JS libraries
vi.mock('goober', () => ({
  css: vi.fn(() => 'mock-css-class'),
  // eslint-disable-next-line no-unused-vars
  styled: vi.fn(() => (_tag: string) => vi.fn(() => null)),
  keyframes: vi.fn(() => 'mock-keyframes'),
  glob: vi.fn(),
  extractCss: vi.fn(() => ''),
}))

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
    loading: vi.fn(),
    dismiss: vi.fn(),
    remove: vi.fn(),
  },
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    loading: vi.fn(),
    dismiss: vi.fn(),
    remove: vi.fn(),
  },
  Toaster: ({ children, ...props }: any) => (
    <div data-testid="toaster" {...props}>
      {children}
    </div>
  ),
}))

// Mock CSS object for goober
Object.defineProperty(globalThis, 'CSS', {
  value: {
    supports: vi.fn(() => true),
    escape: vi.fn((str: string) => str),
  },
  writable: true,
})

// Mock wface
vi.mock('wface', () => ({
  useAppContext: () => ({
    setValue: vi.fn(),
    cache: {},
    openScreenById: vi.fn(),
    setQueryParams: vi.fn(),
    data: { appName: 'DigiFlow', version: '1.0.0' },
    theme: 'light',
  }),
  useUserContext: () => ({
    data: { id: 1, name: 'Test User', email: '<EMAIL>' },
  }),
  WButton: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  WCard: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  WCardContent: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  WCircularProgress: () => <div>Loading...</div>,
  WTextField: (props: Record<string, unknown>) => <input {...props} />,
  WSelect: ({ children, ...props }: any) => <select {...props}>{children}</select>,
  WMenuItem: ({ children, value }: any) => <option value={value}>{children}</option>,
  WIconButton: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  WTypography: ({ children, ...props }: any) => <span {...props}>{children}</span>,
  WGrid: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  WBox: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  WLoadingButton: ({ children, loading, ...props }: any) => (
    <button {...props} disabled={loading}>
      {loading ? 'Loading...' : children}
    </button>
  ),
  WPopper: ({ children, open, ...props }: any) => (open ? <div {...props}>{children}</div> : null),
  WPaper: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  WList: ({ children, ...props }: any) => <ul {...props}>{children}</ul>,
  WListItem: ({ children, ...props }: any) => <li {...props}>{children}</li>,
  WListItemText: ({ primary, secondary, ...props }: any) => (
    <span {...props}>
      {primary} {secondary}
    </span>
  ),
  WTable: ({ children, columns, data, ...props }: any) => (
    <table {...props}>
      <thead>
        <tr>
          {columns?.map((col: any, index: number) => (
            <th key={index}>{col.headerName ?? col.field}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {data?.map((row: any, index: number) => (
          <tr key={index}>
            {columns?.map((col: any, colIndex: number) => (
              <td key={colIndex}>{row[col.field]}</td>
            ))}
          </tr>
        ))}
      </tbody>
      {children}
    </table>
  ),

  // eslint-disable-next-line no-unused-vars
  WPopover: ({ children, open, _anchorEl, ...props }: any) => (open ? <div {...props}>{children}</div> : null),
  WDialog: ({ children, open, ...props }: any) =>
    open ? (
      <div role="dialog" {...props}>
        {children}
      </div>
    ) : null,
  WDialogTitle: ({ children, ...props }: any) => <h2 {...props}>{children}</h2>,
  WDialogContent: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  WDialogActions: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  WLinearProgress: ({ value, ...props }: any) => (
    <div data-progress={value} {...props}>
      Loading...
    </div>
  ),
  WChip: ({ label, ...props }: any) => <span {...props}>{label}</span>,
  WAutocomplete: ({ value, options, onChange, renderInput, ...props }: any) => (
    <div {...props}>
      {renderInput?.({ value, onChange })}
      <select value={value} onChange={(e) => onChange?.(e, e.target.value)}>
        {options?.map((option: any, index: number) => (
          <option key={index} value={option.value ?? option}>
            {option.label ?? option}
          </option>
        ))}
      </select>
    </div>
  ),
  WFormControl: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  WInputLabel: ({ children, ...props }: any) => <label {...props}>{children}</label>,
}))
