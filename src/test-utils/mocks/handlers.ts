import { http, HttpResponse } from 'msw'
import { mockWorkflows, mockUsers, mockInbox, mockHistory } from './data'
import { windowsAuthHandlers } from './windows-auth'

const API_URL = 'http://localhost:5055'

export const handlers = [
  // Windows Authentication handlers
  ...windowsAuthHandlers,

  // Auth endpoints
  http.get(`${API_URL}/auth/user`, () => {
    return HttpResponse.json({
      id: 1,
      name: 'Test User',
      email: '<EMAIL>',
      isAdmin: false,
    })
  }),

  http.post(`${API_URL}/auth/logout`, () => {
    return HttpResponse.json({ success: true })
  }),

  // Workflow endpoints
  http.get(`${API_URL}/workflows`, ({ request }) => {
    const url = new URL(request.url)
    const status = url.searchParams.get('status')
    const workflows = status ? mockWorkflows.filter((w) => w.status === status) : mockWorkflows

    return HttpResponse.json({
      data: workflows,
      total: workflows.length,
    })
  }),

  http.get(`${API_URL}/workflows/:id`, ({ params }) => {
    const { id } = params
    const workflow = mockWorkflows.find((w) => w.id === Number(id))

    if (!workflow) {
      return HttpResponse.json({ message: 'Workflow not found' }, { status: 404 })
    }

    return HttpResponse.json(workflow)
  }),

  http.post(`${API_URL}/workflows`, async ({ request }) => {
    const data = (await request.json()) as Record<string, any>

    const newWorkflow = {
      id: mockWorkflows.length + 1,
      ...data,
      status: 'draft',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    return HttpResponse.json(newWorkflow, { status: 201 })
  }),

  http.put(`${API_URL}/workflows/:id`, async ({ request, params }) => {
    const { id } = params
    const data = (await request.json()) as Record<string, any>

    const workflow = mockWorkflows.find((w) => w.id === Number(id))
    if (!workflow) {
      return HttpResponse.json({ message: 'Workflow not found' }, { status: 404 })
    }

    const updatedWorkflow = {
      ...workflow,
      ...data,
      updatedAt: new Date().toISOString(),
    }

    return HttpResponse.json(updatedWorkflow)
  }),

  http.post(`${API_URL}/workflows/:id/submit`, ({ params }) => {
    const { id } = params

    return HttpResponse.json({
      id: Number(id),
      status: 'submitted',
      submittedAt: new Date().toISOString(),
    })
  }),

  http.post(`${API_URL}/workflows/:id/approve`, ({ params }) => {
    const { id } = params

    return HttpResponse.json({
      id: Number(id),
      status: 'approved',
      approvedAt: new Date().toISOString(),
    })
  }),

  http.post(`${API_URL}/workflows/:id/reject`, async ({ request, params }) => {
    const { id } = params
    const { reason } = (await request.json()) as { reason: string }

    return HttpResponse.json({
      id: Number(id),
      status: 'rejected',
      rejectedAt: new Date().toISOString(),
      rejectionReason: reason,
    })
  }),

  // Inbox endpoints
  http.get(`${API_URL}/inbox`, () => {
    return HttpResponse.json({
      data: mockInbox,
      total: mockInbox.length,
    })
  }),

  http.get(`${API_URL}/inboxes/:id`, ({ params }) => {
    const { id } = params
    const item = mockInbox.find((i) => i.id === Number(id))

    if (!item) {
      return HttpResponse.json({ message: 'Inbox item not found' }, { status: 404 })
    }

    return HttpResponse.json(item)
  }),

  // History endpoints
  http.get(`${API_URL}/history`, () => {
    return HttpResponse.json({
      data: mockHistory,
      total: mockHistory.length,
    })
  }),

  // User endpoints
  http.get(`${API_URL}/users`, () => {
    return HttpResponse.json({
      data: Object.values(mockUsers),
      total: 3,
    })
  }),

  http.get(`${API_URL}/users/:id`, ({ params }) => {
    const { id } = params
    const user = Object.values(mockUsers).find((u) => u.id === Number(id))

    if (!user) {
      return HttpResponse.json({ message: 'User not found' }, { status: 404 })
    }

    return HttpResponse.json(user)
  }),

  // File upload endpoint
  http.post(`${API_URL}/files/upload`, () => {
    return HttpResponse.json({
      id: 'file-123',
      filename: 'test-file.pdf',
      size: 1024,
      uploadedAt: new Date().toISOString(),
    })
  }),

  // Organization endpoints
  http.get(`${API_URL}/organization/tree`, () => {
    return HttpResponse.json({
      id: 1,
      name: 'Root Organization',
      children: [
        {
          id: 2,
          name: 'Department A',
          children: [
            { id: 4, name: 'Team A1', children: [] },
            { id: 5, name: 'Team A2', children: [] },
          ],
        },
        {
          id: 3,
          name: 'Department B',
          children: [{ id: 6, name: 'Team B1', children: [] }],
        },
      ],
    })
  }),

  // Delegation endpoints
  http.get(`${API_URL}/delegations`, () => {
    return HttpResponse.json({
      data: [
        {
          id: 1,
          fromUser: mockUsers.admin,
          toUser: mockUsers.user,
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          active: true,
        },
      ],
      total: 1,
    })
  }),

  http.post(`${API_URL}/delegations`, async ({ request }) => {
    const data = (await request.json()) as Record<string, any>

    return HttpResponse.json(
      {
        id: 2,
        ...data,
        createdAt: new Date().toISOString(),
      },
      { status: 201 },
    )
  }),

  // Survey endpoints
  http.get(`${API_URL}/surveys/:id`, ({ params }) => {
    const { id } = params

    return HttpResponse.json({
      id: Number(id),
      title: 'Test Survey',
      questions: [
        {
          id: 1,
          type: 'text',
          question: 'What is your name?',
          required: true,
        },
        {
          id: 2,
          type: 'select',
          question: 'Select your department',
          options: ['IT', 'HR', 'Finance'],
          required: true,
        },
      ],
    })
  }),

  http.post(`${API_URL}/surveys/:id/submit`, async ({ request, params }) => {
    const { id } = params
    const data = await request.json()

    return HttpResponse.json({
      id: Number(id),
      submittedAt: new Date().toISOString(),
      responses: data,
    })
  }),

  // CSRF endpoints
  http.get(`${API_URL}/csrf/token`, () => {
    return HttpResponse.json({
      token: 'mock-csrf-token-' + Date.now(),
    })
  }),

  // WebView CSRF endpoint with enhanced security
  http.get(`${API_URL}/csrf/webview-token`, ({ request }) => {
    const sessionId = request.headers.get('X-Session-Id')
    const isWebView = request.headers.get('X-From-Mobile-WebView') === 'true'

    if (!sessionId && !isWebView) {
      return HttpResponse.json({ error: 'Invalid WebView request' }, { status: 400 })
    }

    // Mock server-generated secure CSRF token
    const timestamp = Date.now()
    const tokenData = `${sessionId}:${timestamp}:${Math.random().toString(36).substring(2, 11)}`
    // eslint-disable-next-line no-undef
    const secureToken = btoa(tokenData).replace(/[+/]/g, (m) => ({ '+': '-', '/': '_' })[m] ?? m)

    return HttpResponse.json({
      token: `webview-csrf-${secureToken}`,
      expiresAt: timestamp + 3600000, // 1 hour
    })
  }),

  // Legacy CSRF endpoint (for backward compatibility)
  http.get(`${API_URL}/csrf-token`, () => {
    return HttpResponse.json({
      token: 'mock-csrf-token',
    })
  }),

  // Error simulation endpoints for testing
  http.get(`${API_URL}/test/500`, () => {
    return HttpResponse.json({ message: 'Internal server error' }, { status: 500 })
  }),

  http.get(`${API_URL}/test/network-error`, () => {
    return HttpResponse.error()
  }),

  http.get(`${API_URL}/test/timeout`, async () => {
    await new Promise((resolve) => setTimeout(resolve, 60000))
    return HttpResponse.json({ message: 'This should timeout' })
  }),
]
