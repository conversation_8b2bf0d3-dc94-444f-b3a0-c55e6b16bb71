import { config } from 'dotenv'
import { existsSync } from 'fs'
import { resolve } from 'path'

/**
 * Test Environment Loader
 * Loads environment variables in the correct order for tests
 */
export class TestEnvLoader {
  private static instance: TestEnvLoader
  private loaded = false

  private constructor() {}

  static getInstance(): TestEnvLoader {
    if (!TestEnvLoader.instance) {
      TestEnvLoader.instance = new TestEnvLoader()
    }
    return TestEnvLoader.instance
  }

  /**
   * Load test environment variables
   * Order: test-setup.env -> .env.test -> .env.test.local
   */
  load(): void {
    if (this.loaded) return

    // eslint-disable-next-line no-undef
    const rootDir = resolve(__dirname, '../..')

    // 1. Load test execution settings
    this.loadEnvFile(resolve(rootDir, 'test-setup.env'), 'Test Setup')

    // 2. Load test environment defaults
    this.loadEnvFile(resolve(rootDir, '.env.test'), 'Test Environment')

    // 3. Load local test secrets (if exists)
    this.loadEnvFile(resolve(rootDir, '.env.test.local'), 'Test Secrets', true)

    // 4. Set computed values
    this.setComputedValues()

    // 5. Validate required values
    this.validateEnvironment()

    this.loaded = true
  }

  private loadEnvFile(path: string, name: string, optional = false): void {
    if (!existsSync(path)) {
      if (!optional) {
        if (process.env.NODE_ENV === 'development') {
          console.warn(`⚠️  ${name} file not found: ${path}`)
        }
      }
      return
    }

    const result = config({ path })

    if (result.error) {
      if (process.env.NODE_ENV === 'development') {
        console.error(`❌ Failed to load ${name}: ${result.error.message}`)
      }
      if (!optional) {
        throw result.error
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log(`✅ Loaded ${name} from ${path}`)
      }
    }
  }

  private setComputedValues(): void {
    // Set NODE_ENV if not already set
    process.env.NODE_ENV = process.env.NODE_ENV ?? 'test'

    // Set test mode flag
    process.env.VITE_TEST_MODE = 'true'

    // Configure test database URL if components are provided
    if (process.env.TEST_DB_HOST && !process.env.DATABASE_URL) {
      process.env.DATABASE_URL = `postgresql://${process.env.TEST_DB_USER}:${process.env.TEST_DB_PASSWORD}@${process.env.TEST_DB_HOST}:${process.env.TEST_DB_PORT}/${process.env.TEST_DB_NAME}`
    }

    // Set API mock mode based on CI environment
    if (process.env.CI === 'true') {
      process.env.VITE_API_MOCK = 'true'
      process.env.VITE_SIGNALR_MOCK_ENABLED = 'true'
    }

    // Disable animations in tests
    process.env.VITE_DISABLE_ANIMATIONS = 'true'

    // Set coverage thresholds from individual values
    if (process.env.COVERAGE_LINES) {
      process.env.VITE_COVERAGE_THRESHOLD_LINES = process.env.COVERAGE_LINES
    }
  }

  private validateEnvironment(): void {
    const required = ['VITE_API_BASE_URL', 'VITE_AUTH_PROVIDER', 'VITE_AUTH_DOMAIN']

    const missing = required.filter((key) => !process.env[key])

    if (missing.length > 0) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ Missing required environment variables:')
      }
      missing.forEach((key) => console.error(`   - ${key}`))
      throw new Error('Missing required environment variables')
    }

    // Validate values
    if (process.env.VITE_AUTH_PROVIDER !== 'windows') {
      if (process.env.NODE_ENV === 'development') {
        console.warn('⚠️  AUTH_PROVIDER is not "windows" - tests may not work correctly')
      }
    }

    // Check for test user credentials if not mocking
    if (process.env.VITE_API_MOCK !== 'true') {
      const authRequired = ['TEST_USER_USERNAME', 'TEST_USER_PASSWORD']

      const missingAuth = authRequired.filter((key) => !process.env[key])
      if (missingAuth.length > 0) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('⚠️  Missing test user credentials - tests will use mocked auth')
        }
        process.env.VITE_AUTH_MOCK_ENABLED = 'true'
      }
    }
  }

  /**
   * Get environment variable with fallback
   */
  get(key: string, fallback?: string): string | undefined {
    return process.env[key] ?? fallback
  }

  /**
   * Get boolean environment variable
   */
  getBoolean(key: string, fallback = false): boolean {
    const value = process.env[key]
    if (value === undefined) return fallback
    return value === 'true' || value === '1' || value === 'yes'
  }

  /**
   * Get number environment variable
   */
  getNumber(key: string, fallback = 0): number {
    const value = process.env[key]
    if (value === undefined) return fallback
    const num = parseInt(value, 10)
    return isNaN(num) ? fallback : num
  }

  /**
   * Check if running in CI environment
   */
  isCI(): boolean {
    return this.getBoolean('CI', false)
  }

  /**
   * Check if running in mock mode
   */
  isMockMode(): boolean {
    return this.getBoolean('VITE_API_MOCK', true)
  }

  /**
   * Get test timeout
   */
  getTestTimeout(): number {
    return this.getNumber('TEST_TIMEOUT', 30000)
  }

  /**
   * Get coverage thresholds
   */
  getCoverageThresholds() {
    return {
      lines: this.getNumber('VITE_COVERAGE_THRESHOLD_LINES', 80),
      functions: this.getNumber('VITE_COVERAGE_THRESHOLD_FUNCTIONS', 80),
      branches: this.getNumber('VITE_COVERAGE_THRESHOLD_BRANCHES', 80),
      statements: this.getNumber('VITE_COVERAGE_THRESHOLD_STATEMENTS', 80),
    }
  }

  /**
   * Get feature flags
   */
  getFeatureFlags() {
    return {
      realTimeCollaboration: this.getBoolean('VITE_FEATURE_REAL_TIME_COLLABORATION', true),
      offlineMode: this.getBoolean('VITE_FEATURE_OFFLINE_MODE', true),
      mobileSupport: this.getBoolean('VITE_FEATURE_MOBILE_SUPPORT', true),
      bulkOperations: this.getBoolean('VITE_FEATURE_BULK_OPERATIONS', true),
      advancedSearch: this.getBoolean('VITE_FEATURE_ADVANCED_SEARCH', true),
      delegations: this.getBoolean('VITE_FEATURE_DELEGATIONS', true),
      impersonation: this.getBoolean('VITE_FEATURE_IMPERSONATION', true),
    }
  }

  /**
   * Reset environment (for testing the loader itself)
   */
  reset(): void {
    this.loaded = false
  }
}

// Export singleton instance
export const testEnv = TestEnvLoader.getInstance()

// Auto-load in test environment
if (process.env.NODE_ENV === 'test') {
  testEnv.load()
}
