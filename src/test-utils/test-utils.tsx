import { ReactElement, ReactNode } from 'react'
import {
  render,
  RenderOptions,
  RenderResult,
  screen,
  waitFor,
  waitForElementToBeRemoved,
  fireEvent,
  within,
  cleanup,
  act,
  configure,
  getByRole,
  getByText,
  getByLabelText,
  getByPlaceholderText,
  getByDisplayValue,
  getByTestId,
  getByTitle,
  getByAltText,
  queryByRole,
  queryByText,
  queryByLabelText,
  queryByPlaceholderText,
  queryByDisplayValue,
  queryByTestId,
  queryByTitle,
  queryByAltText,
  findByRole,
  findByText,
  findByLabelText,
  findByPlaceholderText,
  findByDisplayValue,
  findByTestId,
  findByTitle,
  findByAltText,
  getAllByRole,
  getAllByText,
  getAllByLabelText,
  getAllByPlaceholderText,
  getAllByDisplayValue,
  getAllByTestId,
  getAllByTitle,
  getAllByAltText,
  queryAllByRole,
  queryAllByText,
  queryAllByLabelText,
  queryAllByPlaceholderText,
  queryAllByDisplayValue,
  queryAllByTestId,
  queryAllByTitle,
  queryAllByAltText,
  findAllByRole,
  findAllByText,
  findAllByLabelText,
  findAllByPlaceholderText,
  findAllByDisplayValue,
  findAllByTestId,
  findAllByTitle,
  findAllByAltText,
  prettyDOM,
  logRoles,
  isInaccessible,
  createEvent,
} from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BrowserRouter } from 'react-router-dom'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import { I18nextProvider } from 'react-i18next'
import userEvent from '@testing-library/user-event'
import { vi, test, expect } from 'vitest'
import { DigiflowProvider } from '@/contexts/DigiflowContext'
import { WebViewProvider } from '@/contexts/WebViewContext'
import { ModalProvider } from '@/contexts/ModalContext'
import { WorkflowProvider } from '@/contexts/workflow'
import { designTokens } from '@/theme/designTokens'
import i18n from './i18n-test'

const theme = createTheme(designTokens)

// Create a custom render function that includes all providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  route?: string
  initialState?: any
  queryClient?: QueryClient
}

interface ProvidersProps {
  children: ReactNode
  queryClient?: QueryClient
}

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
        staleTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
    // logger removed - not part of QueryClientConfig in v5
  })

export const AllTheProviders = ({ children, queryClient = createTestQueryClient() }: ProvidersProps) => {
  return (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        <BrowserRouter>
          <ThemeProvider theme={theme}>
            <WebViewProvider>
              <DigiflowProvider>
                <WorkflowProvider workflowName="test-workflow" wfInstanceId={0} refInstanceId={null} copyInstanceId={null} schemas={{}}>
                  <ModalProvider>{children}</ModalProvider>
                </WorkflowProvider>
              </DigiflowProvider>
            </WebViewProvider>
          </ThemeProvider>
        </BrowserRouter>
      </I18nextProvider>
    </QueryClientProvider>
  )
}

const customRender = (ui: ReactElement, options?: CustomRenderOptions): RenderResult & { user: ReturnType<typeof userEvent.setup> } => {
  const { route, queryClient, ...renderOptions } = options ?? {}

  // Set initial route
  if (route) {
    void window.history.pushState({}, 'Test page', route)
  }

  const renderResult = render(ui, {
    wrapper: ({ children }) => <AllTheProviders queryClient={queryClient}>{children}</AllTheProviders>,
    ...renderOptions,
  })

  // Setup userEvent after render to ensure DOM is ready
  const user = userEvent.setup()

  return {
    ...renderResult,
    user,
  }
}

// Re-export specific functions from React Testing Library (excluding render to avoid conflicts)
export {
  screen,
  waitFor,
  waitForElementToBeRemoved,
  fireEvent,
  within,
  cleanup,
  act,
  configure,
  getByRole,
  getByText,
  getByLabelText,
  getByPlaceholderText,
  getByDisplayValue,
  getByTestId,
  getByTitle,
  getByAltText,
  queryByRole,
  queryByText,
  queryByLabelText,
  queryByPlaceholderText,
  queryByDisplayValue,
  queryByTestId,
  queryByTitle,
  queryByAltText,
  findByRole,
  findByText,
  findByLabelText,
  findByPlaceholderText,
  findByDisplayValue,
  findByTestId,
  findByTitle,
  findByAltText,
  getAllByRole,
  getAllByText,
  getAllByLabelText,
  getAllByPlaceholderText,
  getAllByDisplayValue,
  getAllByTestId,
  getAllByTitle,
  getAllByAltText,
  queryAllByRole,
  queryAllByText,
  queryAllByLabelText,
  queryAllByPlaceholderText,
  queryAllByDisplayValue,
  queryAllByTestId,
  queryAllByTitle,
  queryAllByAltText,
  findAllByRole,
  findAllByText,
  findAllByLabelText,
  findAllByPlaceholderText,
  findAllByDisplayValue,
  findAllByTestId,
  findAllByTitle,
  findAllByAltText,
  prettyDOM,
  logRoles,
  isInaccessible,
  createEvent,
}

// Export types separately (already imported above)
export type { RenderOptions, RenderResult }

// Export our custom render function
export { customRender as render, userEvent }

// Utility functions for common test scenarios
export const waitForLoadingToFinish = async () => {
  const { waitForElementToBeRemoved } = await import('@testing-library/react')
  await waitForElementToBeRemoved(() => window.document.querySelector('[aria-label="loading"]'), {
    timeout: 5000,
  }).catch(() => {
    // Loading element might not exist
  })
}

// Mock navigation utilities
export const mockNavigate = vi.fn()
export const mockLocation = {
  pathname: '/',
  search: '',
  hash: '',
  state: null,
}

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual<typeof import('react-router-dom')>('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => mockLocation,
  }
})

// Form testing utilities
export const fillForm = async (user: ReturnType<typeof userEvent.setup>, formData: Record<string, string | boolean | number>) => {
  for (const [name, value] of Object.entries(formData)) {
    const element = window.document.querySelector(`[name="${name}"]`)
    if (!element) {
      throw new Error(`Form element with name "${name}" not found`)
    }

    if (element instanceof HTMLInputElement) {
      if (element.type === 'checkbox') {
        if (value !== element.checked) {
          await user.click(element)
        }
      } else {
        await user.clear(element)
        await user.type(element, String(value))
      }
    } else if (element instanceof HTMLSelectElement) {
      await user.selectOptions(element, String(value))
    } else if (element instanceof HTMLTextAreaElement) {
      await user.clear(element)
      await user.type(element, String(value))
    }
  }
}

// Table testing utilities
export const getTableData = (container: HTMLElement) => {
  const rows = container.querySelectorAll('tbody tr')
  return Array.from(rows).map((row) => {
    const cells = row.querySelectorAll('td')
    return Array.from(cells).map((cell) => cell.textContent?.trim() ?? '')
  })
}

// Async utilities
export const waitForApiCall = async (apiPath: string, method = 'GET') => {
  return new Promise<void>((resolve) => {
    const checkForCall = () => {
      const calls = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
      const found = calls.some((call) => call.name.includes(apiPath) && (method ? call.initiatorType === method.toLowerCase() : true))
      if (found) {
        resolve()
      } else {
        setTimeout(checkForCall, 100)
      }
    }
    checkForCall()
  })
}

// Mock date utility
export const mockDate = (dateString: string) => {
  const RealDate = Date
  const mockDate = new Date(dateString)

  vi.spyOn(globalThis, 'Date').mockImplementation((date?: string | number | Date) => {
    if (date) {
      return new RealDate(date)
    }
    return mockDate
  })

  return () => {
    void vi.mocked(globalThis.Date).mockRestore()
  }
}
// Accessibility testing helper
export const checkAccessibility = async (container: HTMLElement) => {
  const axe = await import('axe-core')
  const results = await axe.run(container)
  return results.violations
}

// Performance testing helper
export const measureRenderTime = async (component: ReactElement, options?: CustomRenderOptions) => {
  const start = performance.now()
  const result = customRender(component, options)
  await waitForLoadingToFinish()
  const end = performance.now()

  return {
    ...result,
    renderTime: end - start,
  }
}

// Snapshot testing helper
export const createSnapshotTest = (componentName: string, component: ReactElement, options?: CustomRenderOptions) => {
  test(`${componentName} matches snapshot`, () => {
    const { container } = customRender(component, options)
    void expect(container.firstChild).toMatchSnapshot()
  })
}

// Custom matchers
export const toBeAccessible = async (container: HTMLElement) => {
  const violations = await checkAccessibility(container)

  return {
    pass: violations.length === 0,
    message: () => {
      if (violations.length === 0) {
        return 'Expected element to have accessibility violations'
      }

      const violationMessages = violations.map((_v) => `${_v.id}: ${_v.description}`).join('\n')

      return `Expected element to be accessible, but found violations:\n${violationMessages}`
    },
  }
}

// Add custom matcher to expect
void expect.extend({ toBeAccessible })
