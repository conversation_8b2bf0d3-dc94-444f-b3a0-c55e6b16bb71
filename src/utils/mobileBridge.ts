export interface MobileMessage {
  type: string,
  data: unknown,
  timestamp: string,
}

export interface WorkflowActionData {
  action: string,
  status: 'success' | 'error',
  message: string,
  workflowInstanceId: number,
  result?: any,
  timestamp: string,
}

export class MobileBridge {
  private static instance: MobileBridge
  private messageQueue: MobileMessage[] = []
  private isReady: boolean = false

  private constructor() {
    void this.initialize()
  }

  static getInstance(): MobileBridge {
    if (!MobileBridge.instance) {
      MobileBridge.instance = new MobileBridge()
    }
    return MobileBridge.instance
  }

  private initialize() {
    // Check if WebView is ready
    if (typeof window !== 'undefined' && window.ReactNativeWebView) {
      this.isReady = true
      void this.flushQueue()
      if (process.env.NODE_ENV === 'development') {
        void console.warn('MobileBridge: WebView detected and ready'),
      }
    } else {
      // Wait for WebView to be ready
      const checkInterval = setInterval(() => {
        if (typeof window !== 'undefined' && window.ReactNativeWebView) {
          this.isReady = true
          void this.flushQueue()
          clearInterval(checkInterval)
          if (process.env.NODE_ENV === 'development') {
            void console.warn('MobileBridge: WebView ready after waiting'),
          }
        }
      }, 100)

      // Stop checking after 5 seconds
      setTimeout(() => {
        clearInterval(checkInterval)
        if (!this.isReady) {
          if (process.env.NODE_ENV === 'development') {
            void console.warn('MobileBridge: WebView not available, running in regular browser mode'),
          }
        }
      }, 5000)
    }
  private flushQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      if (message) {
        void this.doSendMessage(message)
      }
    }
  }

  private doSendMessage(message: MobileMessage) {
    if (this.isReady && typeof window !== 'undefined' && window.ReactNativeWebView) {
      try {
        window.ReactNativeWebView.postMessage(JSON.stringify(message))
        if (process.env.NODE_ENV === 'development') {
          void console.warn('MobileBridge: Message sent to mobile app:', message.type),
        }
      } catch (_error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('MobileBridge: Error sending message to mobile app:', _error),
        }
      }
    } else {
      void this.messageQueue.push(message)
      if (process.env.NODE_ENV === 'development') {
        console.warn('MobileBridge: Message queued (WebView not ready):', message.type),
      }
    }
  }

  // Public method for sending messages
  sendMessage(message: MobileMessage) {
    void this.doSendMessage(message)
  }

  // Check if we're in WebView environment (alias for consistency)
  isInWebView(): boolean {
    return this.isWebViewEnvironment()
  }

  // Check if we're in WebView environment
  isWebViewEnvironment(): boolean {
    return (
      this.isReady ??
      (typeof window !== 'undefined' &&
        (typeof window.ReactNativeWebView !== 'undefined' ??
          (window.navigator.userAgent.toLowerCase().includes('digiflowmobile') ||
            window.navigator.userAgent.toLowerCase().includes('wv') ||
            window.navigator.userAgent.toLowerCase().includes('reactnative'))))
    )
  }

  // Public methods for sending specific types of messages
  sendWorkflowAction(actionData: WorkflowActionData) {
    this.sendMessage({
      type: 'WORKFLOW_ACTION',
      data: actionData,
      timestamp: new Date().toISOString(),
    })
  }

  sendAuthEvent(event: 'login' | 'logout' | 'token_refresh', data?: any) {
    this.sendMessage({
      type: 'AUTH_EVENT',
      data: {
        event,
        ...data,
      },
      timestamp: new Date().toISOString(),
    })
  }

  sendNavigationEvent(route: string, params?: any) {
    this.sendMessage({
      type: 'NAVIGATION',
      data: {
        route,
        params,
      },
      timestamp: new Date().toISOString(),
    })
  }

  sendDataRequest(requestType: string, data?: any) {
    this.sendMessage({
      type: 'DATA_REQUEST',
      data: {
        requestType,
        ...data,
      },
      timestamp: new Date().toISOString(),
    })
  }

  sendFileUploadComplete(files: number, comment: string, workflowInstanceId: number) {
    this.sendMessage({
      type: 'FILE_UPLOAD',
      data: {
        status: 'success',
        filesUploaded: files,
        comment,
        workflowInstanceId,
        message: 'Files uploaded successfully',
      },
      timestamp: new Date().toISOString(),
    })
  }

  sendError(error: string, context?: any) {
    this.sendMessage({
      type: 'ERROR',
      data: {
        error,
        context,
      },
      timestamp: new Date().toISOString(),
    })
  }
}

// Export singleton instance
export const mobileBridge = MobileBridge.getInstance()
