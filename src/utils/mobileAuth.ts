// Utility functions for mobile authentication checks
declare global {
  interface Window {
    ReactNativeWebView?: {
      // eslint-disable-next-line no-unused-vars
      postMessage: (_message: string) => void
    }
    // Secure session ID for WebView authentication
    SECURE_SESSION_ID?: string
  }
}

/**
 * Check if the user is accessing from a WebView (mobile app)
 * This function is similar to the one in api.tsx but kept separate for clarity
 */
export const isInWebView = (): boolean => {
  return (
    typeof window !== 'undefined' &&
    typeof window.ReactNativeWebView !== 'undefined' &&
    window.navigator.userAgent.toLowerCase().includes('digiflowmobile') &&
    (window.navigator.userAgent.toLowerCase().includes('wv') || window.navigator.userAgent.toLowerCase().includes('reactnative'))
  )
} // End of isInWebView

/**
 * Check if secure session ID is available (for mobile authentication)
 * @deprecated Use secureAuthService.isAuthenticated() instead
 */
export const hasValidMobileSession = (): boolean => {
  if (typeof window === 'undefined') return false

  const sessionId = window.SECURE_SESSION_ID
  if (!sessionId) return false

  // Basic validation - session ID should be a non-empty string
  return typeof sessionId === 'string' && sessionId.length > 0
}

/**
 * Check if user is coming from mobile app but doesn't have a valid session
 * @deprecated Use secureAuthService.isAuthenticated() instead
 */
export const isMobileWithoutSession = (): boolean => {
  return isInWebView() && !hasValidMobileSession()
}

/**
 * Get mobile user agent information for debugging
 */
export const getMobileDebugInfo = () => {
  if (typeof window === 'undefined') return null

  return {
    userAgent: window.navigator.userAgent,
    hasReactNativeWebView: typeof window.ReactNativeWebView !== 'undefined',
    hasSecureSessionId: !!window.SECURE_SESSION_ID,
    secureSessionIdLength: window.SECURE_SESSION_ID?.length ?? 0,
    isInWebView: isInWebView(),
    hasValidSession: hasValidMobileSession(),
    shouldShowUnauthorized: isMobileWithoutSession(),
    // Security note: JWT tokens are no longer used client-side
    deprecatedJwtTokenUsage: false,
  }
}
