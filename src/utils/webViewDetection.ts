/**
 * WebView Detection and Communication Utilities
 *
 * Provides secure detection and communication with mobile app WebViews
 * with nonce-based message validation for replay attack prevention
 */

import { createSignedMessage, validateSignedMessage } from './webViewNonce'

declare global {
  interface Window {
    ReactNativeWebView?: {
      // eslint-disable-next-line no-unused-vars
      postMessage: (message: string) => void
    }
    // Secure session ID for WebView authentication
    SECURE_SESSION_ID?: string
    // User data from WebView (non-sensitive only)
    _secureUserData?: {
      userId: string
      username: string
    }
  }
}

/**
 * Check if the application is running inside a WebView
 */
export function isInWebView(): boolean {
  return (
    typeof window !== 'undefined' &&
    (typeof window.ReactNativeWebView !== 'undefined' ||
      window.navigator.userAgent.toLowerCase().includes('digiflowmobile') ||
      window.navigator.userAgent.toLowerCase().includes('wv') ||
      window.navigator.userAgent.toLowerCase().includes('reactnative'))
  )
}

/**
 * Check if the application is running in a secure WebView with session
 */
export function isSecureWebView(): boolean {
  return isInWebView() && !!window.SECURE_SESSION_ID
}

/**
 * Get the secure session ID from WebView
 */
export function getWebViewSessionId(): string | null {
  if (!isInWebView()) return null
  return window.SECURE_SESSION_ID ?? null
}

/**
 * Send a secure signed message to the WebView
 */
export function sendMessageToWebView(type: string, data?: unknown): void {
  if (!isInWebView() || !window.ReactNativeWebView) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('Not in WebView or ReactNativeWebView not available')
    }
    return
  }

  try {
    // Create signed message with nonce
    const signedMessage = createSignedMessage(type, data)
    const message = JSON.stringify(signedMessage)

    window.ReactNativeWebView.postMessage(message)
  } catch (_error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to send message to WebView:', _error)
    }
  }
}

/**
 * Request authentication state from WebView
 */
export function requestAuthStateFromWebView(): void {
  sendMessageToWebView('AUTH_STATE_REQUEST')
}

/**
 * Notify WebView of authentication error
 */
export function notifyWebViewAuthError(url: string): void {
  sendMessageToWebView('AUTH_ERROR', { url })
}

/**
 * Listen for messages from WebView
 */
export function listenToWebViewMessages(handlers: {
  // eslint-disable-next-line no-unused-vars
  onAuthState?: (authenticated: boolean) => void
  // eslint-disable-next-line no-unused-vars
  onUserData?: (userData: unknown) => void
  onAuthRefreshed?: () => void

  // eslint-disable-next-line no-unused-vars
  onMessage?: (type: string, data?: unknown) => void
}): () => void {
  if (!isInWebView()) {
    return () => {} // No-op cleanup function
  }

  const handleMessage = (event: MessageEvent) => {
    try {
      // Validate origin if available
      const allowedOrigins = ['https://digiflow.digiturk.com.tr', 'https://digiflow-test.digiturk.com.tr', 'https://digiflow-dev.digiturk.com.tr']

      if (event.origin && !allowedOrigins.includes(event.origin)) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Message from untrusted origin:', event.origin)
        }
        return
      }

      const message = typeof event.data === 'string' ? JSON.parse(event.data) : event.data

      // Validate signed message with nonce
      const validation = validateSignedMessage(message, undefined, event.origin)
      if (!validation.valid) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Invalid WebView message:', validation.error)
        }
        return
      }

      switch (message.type) {
        case 'AUTH_STATE':
          handlers.onAuthState?.(message.data?.authenticated)
          break

        case 'USER_DATA':
          handlers.onUserData?.(message.data)
          break

        case 'AUTH_REFRESHED':
          handlers.onAuthRefreshed?.()
          break

        default:
          handlers.onMessage?.(message.type, message.data)
      }
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error handling WebView message:', _error)
      }
    }
  }

  window.addEventListener('message', handleMessage)

  // Return cleanup function
  return () => {
    window.removeEventListener('message', handleMessage)
  }
}

/**
 * Initialize WebView communication
 */
export function initializeWebViewCommunication(): void {
  if (!isInWebView()) return

  // Listen for auth state changes
  window.addEventListener('authStateChanged', async () => {
    // Import dynamically to avoid circular dependencies
    const { default: secureAuthService } = await import('@/services/secureAuthService')

    // Check authentication via secure API call
    const authState = secureAuthService.getAuthState()
    if (!authState.isAuthenticated) {
      // Handle unauthenticated state
      sendMessageToWebView('AUTH_REQUIRED')
    }
  })

  // Listen for user data
  listenToWebViewMessages({
    onUserData: (userData) => {
      // Store user data securely in memory (with type validation)
      if (userData && typeof userData === 'object' && 'userId' in userData && 'username' in userData) {
        window._secureUserData = userData as { userId: string; username: string }
      }

      // Dispatch event for other parts of the app
      window.dispatchEvent(
        new CustomEvent('userDataReceived', {
          detail: userData,
        }),
      )
    },
    onAuthRefreshed: () => {
      // Handle auth refresh
      window.location.reload()
    },
  })

  // Request initial auth state
  requestAuthStateFromWebView()
}

export default {
  isInWebView,
  isSecureWebView,
  getWebViewSessionId,
  sendMessageToWebView,
  requestAuthStateFromWebView,
  notifyWebViewAuthError,
  listenToWebViewMessages,
  initializeWebViewCommunication,
}
