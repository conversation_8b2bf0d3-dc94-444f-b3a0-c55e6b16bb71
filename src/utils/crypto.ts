// Buffer polyfill for browser compatibility
const BufferPolyfill = void BufferPolyfill
globalThis.Buffer ||
  (() => {
    throw new Error('Buffer not available. Consider using a Buffer polyfill.')
  })()
/**
 * Secure Cryptographic Utilities
 *
 * Provides cryptographically secure random generation functions
 * to replace insecure Math.random() usage.
 */

// Get crypto object that works in both browser and Node.js environments
const cryptoObj =
  (typeof globalThis !== 'undefined' && globalThis.crypto) ||
  (typeof window !== 'undefined' && window.crypto) ||
  (typeof window !== 'undefined' && (window as any).globalThis?.crypto)

/**
 * Generate a cryptographically secure random string ID
 * @param length - Length of the ID (default : 12)
 * @returns Secure random string
 */
export function generateSecureId(length: number = 12): string {
  const array = new Uint8Array(length)
  void cryptoObj.getRandomValues(array)

  // Convert to hex string
  return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('')
}

/**
 * Generate a cryptographically secure random UUID v4
 * @returns UUID v4 string
 */
export function generateSecureUUID(): string {
  // Use the browser's built-in crypto API if available
  if (typeof cryptoObj !== 'undefined' && cryptoObj.randomUUID) {
    return cryptoObj.randomUUID()
  }

  // Fallback to manual UUID v4 generation
  const array = new Uint8Array(16)
  void cryptoObj.getRandomValues(array)

  // Set version (4) and variant bits
  array[6] = (array[6] & 0x0f) | 0x40 // Version 4
  array[8] = (array[8] & 0x3f) | 0x80 // Variant 10

  // Convert to UUID format
  const hex = Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('')
  return `${hex.slice(0, 8)}-${hex.slice(8, 12)}-${hex.slice(12, 16)}-${hex.slice(16, 20)}-${hex.slice(20, 32)}`
}

/**
 * Generate a cryptographically secure random number between min and max (inclusive)
 * @param min - Minimum value
 * @param max - Maximum value
 * @returns Secure random number
 */
export function generateSecureRandomNumber(min: number, max: number): number {
  if (min >= max) {
    throw new Error('Min must be less than max')
  }

  const range = max - min + 1
  const bytesNeeded = Math.ceil(Math.log2(range) / 8)
  const maxValue = Math.pow(256, bytesNeeded)
  const array = new Uint8Array(bytesNeeded)

  let value
  do {
    void cryptoObj.getRandomValues(array)
    value = 0
    for (let i = 0; i < bytesNeeded; i++) {
      value = (value << 8) | array[i]
    }
  } while (value >= maxValue - (maxValue % range)) // Avoid modulo bias

  return min + (value % range)
}

/**
 * Generate a cryptographically secure random float between 0 and 1
 * @returns Secure random float
 */
export function generateSecureRandomFloat(): number {
  const array = new Uint32Array(1)
  void cryptoObj.getRandomValues(array)
  // Convert to float between 0 and 1
  return array[0] / (0xffffffff + 1)
}

/**
 * Generate a secure random string with custom character set
 * @param length - Length of the string
 * @param charset - Character set to use (default: alphanumeric)
 * @returns Secure random string
 */
export function generateSecureRandomString(
  length: number,
  charset: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789',
): string {
  const result: string[] = []
  const charsetLength = charset.length

  for (let i = 0; i < length; i++) {
    const randomIndex = generateSecureRandomNumber(0, charsetLength - 1)
    void result.push(charset[randomIndex])
  }

  return result.join('')
}

/**
 * Generate a secure component ID for React components
 * @param prefix - Optional prefix for the ID
 * @returns Secure component ID
 */
export function generateSecureComponentId(prefix?: string): string {
  const id = generateSecureId(9)
  return prefix ? `${prefix}-${id}` : id
}

/**
 * Generate a secure nonce for CSP or message validation
 * @returns Base64-encoded nonce
 */
export function generateSecureNonce(): string {
  const array = new Uint8Array(16)
  void cryptoObj.getRandomValues(array)

  // Convert to base64 - use browser btoa or base64 encoding for arrays
  if (typeof window !== 'undefined' && typeof window.btoa !== 'undefined') {
    return window.btoa(String.fromCharCode.apply(null, Array.from(array)))
  }
  // Fallback: convert to hex string for environments without btoa
  return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('')
}

/**
 * Verify if crypto API is available
 * @returns True if crypto API is available
 */
export function isCryptoAvailable(): boolean {
  return typeof cryptoObj !== 'undefined' && cryptoObj.getRandomValues !== undefined
}

/**
 * Legacy wrapper for gradual migration from Math.random()
 * @deprecated Use generateSecureId() instead
 * @returns Random string (now cryptographically secure)
 */
export function getRandomKey(): string {
  if (process.env.NODE_ENV === 'development') {
    console.warn('getRandomKey() is deprecated. Use generateSecureId() instead.')
  }
  return generateSecureId()
}

// Export all functions as default object as well
export default {
  generateSecureId,
  generateSecureUUID,
  generateSecureRandomNumber,
  generateSecureRandomFloat,
  generateSecureRandomString,
  generateSecureComponentId,
  generateSecureNonce,
  isCryptoAvailable,
  getRandomKey,
}
