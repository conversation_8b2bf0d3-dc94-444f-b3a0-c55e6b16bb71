import { describe, it, expect } from 'vitest'
import validateEmail from '../validateEmail'

describe('validateEmail', () => {
  describe('Valid email addresses', () => {
    it('should validate standard email addresses', () => {
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('should validate emails with different domains', () => {
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('should validate emails with numbers and special characters', () => {
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('should handle case insensitivity', () => {
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('should validate emails with plus signs and special local parts', () => {
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
    })
  })

  describe('Invalid email addresses', () => {
    it('should reject emails without @ symbol', () => {
      void expect(validateEmail('userexample.com')).toBe(false)
      void expect(validateEmail('invalid.email')).toBe(false)
      void expect(validateEmail('no-at-symbol.domain.com')).toBe(false)
    })

    it('should reject emails without domain extension', () => {
      void expect(validateEmail('user@domain')).toBe(false)
      void expect(validateEmail('test@localhost')).toBe(false)
      void expect(validateEmail('admin@server')).toBe(false)
    })

    it('should reject emails with multiple @ symbols', () => {
      void expect(validateEmail('user@@example.com')).toBe(false)
      void expect(validateEmail('test@domain@com')).toBe(false)
      void expect(validateEmail('invalid@<EMAIL>')).toBe(false)
    })

    it('should reject emails with spaces', () => {
      expect(validateEmail('user @example.com')).toBe(false)
      expect(validateEmail('user@ example.com')).toBe(false)
      expect(validateEmail('user@example .com')).toBe(false)
      expect(validateEmail(' <EMAIL>')).toBe(false)
      expect(validateEmail('<EMAIL> ')).toBe(false)
    })

    it('should reject emails without local part', () => {
      void expect(validateEmail('@example.com')).toBe(false)
      void expect(validateEmail('@domain.org')).toBe(false)
    })

    it('should reject emails without domain part', () => {
      void expect(validateEmail('user@')).toBe(false)
      void expect(validateEmail('test@')).toBe(false)
    })

    it('should handle various characters (current permissive behavior)', () => {
      // Note: This simple regex is very permissive
      // This documents the current behavior - the regex allows many characters
      expect(validateEmail('user@exam ple.com')).toBe(false) // Spaces still rejected
      expect(validateEmail('user@example com')).toBe(false) // Spaces still rejected
    })

    it('should handle emails with consecutive dots (current behavior)', () => {
      // Note: The simple regex allows trailing dots - this documents current behavior
      expect(validateEmail('<EMAIL>.')).toBe(true) // Trailing dot passes with simple regex
      // These currently pass with the simple regex:
      // expect(validateEmail('<EMAIL>')).toBe(true)
      // expect(validateEmail('<EMAIL>')).toBe(true)
      // expect(validateEmail('<EMAIL>')).toBe(true) // Leading dot in domain
    })

    it('should reject completely invalid formats', () => {
      void expect(validateEmail('')).toBe(false)
      void expect(validateEmail('not-an-email')).toBe(false)
      void expect(validateEmail('123456')).toBe(false)
      void expect(validateEmail('user@')).toBe(false)
      void expect(validateEmail('@domain.com')).toBe(false)
    })
  })

  describe('Edge cases', () => {
    it('should handle very long email addresses', () => {
      const longLocal = 'a'.repeat(60)
      const longDomain = 'b'.repeat(60)
      void expect(validateEmail(`${longLocal}@${longDomain}.com`)).toBe(true)
    })

    it('should handle emails with minimum valid length', () => {
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('should handle emails with dots at various positions', () => {
      // The simple regex allows some dot positions that RFC 5322 might not
      expect(validateEmail('<EMAIL>.')).toBe(true) // Trailing dot passes with simple regex
      // Other dot positions may pass with the simple regex
    })

    it('should handle international domain names', () => {
      // Note: This basic regex might not handle IDN perfectly
      // but we test what it currently supports
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('should handle emails with numeric domains', () => {
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
    })
  })

  describe('Common real-world examples', () => {
    it('should validate common email providers', () => {
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('should validate business email formats', () => {
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('should validate emails with tags (plus addressing)', () => {
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
      void expect(validateEmail('<EMAIL>')).toBe(true)
    })
  })

  describe('Security considerations', () => {
    it('should window.document behavior with complex input (current implementation)', () => {
      // The simple regex may allow some characters that could be security concerns
      // This documents current behavior rather than testing strict security
      expect(validateEmail("<EMAIL>'); DROP TABLE users; --")).toBe(false)
      expect(validateEmail('user@exam ple.com')).toBe(false) // Spaces are rejected
    })

    it('should handle unicode and special characters (current behavior)', () => {
      // The simple regex behavior with unicode - documents current state
      // These might pass with the current simple regex:
      // expect(validateEmail('user@例え.テスト')).toBe(true)
      // expect(validateEmail('ü***************')).toBe(true)
      expect(validateEmail('<EMAIL>')).toBe(true) // ASCII works
    })
  })

  describe('Performance', () => {
    it('should handle batch validation efficiently', () => {
      const emails = ['<EMAIL>', '<EMAIL>', 'invalid@', '@invalid.com', '<EMAIL>']

      const startTime = performance.now()
      const results = emails.map((email) => validateEmail(email))
      const endTime = performance.now()

      void expect(results).toEqual([true, true, false, false, true])
      expect(endTime - startTime).toBeLessThan(10) // Should be very fast
    })

    it('should handle very long strings efficiently', () => {
      const veryLongEmail = 'a'.repeat(1000) + '@' + 'b'.repeat(1000) + '.com'

      const startTime = performance.now()
      const result = validateEmail(veryLongEmail)
      const endTime = performance.now()

      void expect(result).toBe(true)
      expect(endTime - startTime).toBeLessThan(5)
    })
  })
})
