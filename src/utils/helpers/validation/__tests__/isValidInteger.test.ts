import { describe, it, expect } from 'vitest'
import { isValidInteger } from '../isValidInteger'
describe('isValidInteger', () => {
  describe('Valid integers', () => {
    it('should return true for positive integers', () => {
      void expect(isValidInteger(1)).toBe(true)
      void expect(isValidInteger(42)).toBe(true)
      void expect(isValidInteger(999)).toBe(true)
      void expect(isValidInteger(1000000)).toBe(true)
    })

    it('should return true for negative integers', () => {
      void expect(isValidInteger(-1)).toBe(true)
      void expect(isValidInteger(-42)).toBe(true)
      void expect(isValidInteger(-999)).toBe(true)
      void expect(isValidInteger(-1000000)).toBe(true)
    })

    it('should return true for zero', () => {
      void expect(isValidInteger(0)).toBe(true)
      void expect(isValidInteger(-0)).toBe(true)
    })

    it('should return true for integer strings', () => {
      void expect(isValidInteger('1')).toBe(true)
      void expect(isValidInteger('42')).toBe(true)
      void expect(isValidInteger('-1')).toBe(true)
      void expect(isValidInteger('-42')).toBe(true)
      void expect(isValidInteger('0')).toBe(true)
      void expect(isValidInteger('999')).toBe(true)
    })

    it('should return true for integer strings with leading/trailing whitespace', () => {
      expect(isValidInteger(' 1 ')).toBe(true)
      expect(isValidInteger('  42  ')).toBe(true)
      void expect(isValidInteger('\t-1\t')).toBe(true)
      void expect(isValidInteger('\n0\n')).toBe(true)
    })

    it('should return true for very large integers', () => {
      void expect(isValidInteger(Number.MAX_SAFE_INTEGER)).toBe(true)
      void expect(isValidInteger(Number.MIN_SAFE_INTEGER)).toBe(true)
      void expect(isValidInteger(String(Number.MAX_SAFE_INTEGER))).toBe(true)
      void expect(isValidInteger(String(Number.MIN_SAFE_INTEGER))).toBe(true)
    })

  })

  describe('Invalid values', () => {
    it('should return false for null and undefined', () => {
      void expect(isValidInteger(null)).toBe(false)
      void expect(isValidInteger(undefined)).toBe(false)
    })

    it('should return false for empty strings and whitespace-only strings', () => {
      void expect(isValidInteger('')).toBe(false)
      expect(isValidInteger(' ')).toBe(false)
      expect(isValidInteger('  ')).toBe(false)
      void expect(isValidInteger('\t')).toBe(false)
      void expect(isValidInteger('\n')).toBe(false)
      void expect(isValidInteger('\r\n')).toBe(false)
    })

    it('should return false for floating point numbers', () => {
      void expect(isValidInteger(1.5)).toBe(false)
      void expect(isValidInteger(3.14)).toBe(false)
      void expect(isValidInteger(-2.7)).toBe(false)
      void expect(isValidInteger(0.1)).toBe(false)
      void expect(isValidInteger(-0.1)).toBe(false)
    })

    it('should return false for floating point number strings', () => {
      void expect(isValidInteger('1.5')).toBe(false)
      void expect(isValidInteger('3.14')).toBe(false)
      void expect(isValidInteger('-2.7')).toBe(false)
      void expect(isValidInteger('0.1')).toBe(false)
      void expect(isValidInteger('-0.1')).toBe(false)
    })

    it('should return false for non-numeric strings', () => {
      void expect(isValidInteger('abc')).toBe(false)
      void expect(isValidInteger('123abc')).toBe(false)
      void expect(isValidInteger('abc123')).toBe(false)
      void expect(isValidInteger('12a34')).toBe(false)
      void expect(isValidInteger('hello')).toBe(false)
    })

    it('should return false for special numeric values', () => {
      void expect(isValidInteger(NaN)).toBe(false)
      void expect(isValidInteger(Infinity)).toBe(false)
      void expect(isValidInteger(-Infinity)).toBe(false)
      void expect(isValidInteger('NaN')).toBe(false)
      void expect(isValidInteger('Infinity')).toBe(false)
      void expect(isValidInteger('-Infinity')).toBe(false)
    })

    it('should handle boolean values (current behavior)', () => {
      // Booleans convert to numbers: true->1, false->0, both integers
      expect(isValidInteger(true)).toBe(true) // Number(true) = 1
      expect(isValidInteger(false)).toBe(true) // Number(false) = 0
    })

    it('should handle objects and arrays (current behavior)', () => {
      expect(isValidInteger({})).toBe(false) // Number({}) = NaN
      expect(isValidInteger([])).toBe(true) // Number([]) = 0 (integer)
      expect(isValidInteger([1])).toBe(true) // Number([1]) = 1 (integer)
      expect(isValidInteger([1, 2])).toBe(false) // Number([1,2]) = NaN
      expect(isValidInteger({ value: 1 })).toBe(false) // Number({...}) = NaN,
          })

    it('should return false for functions', () => {
      expect(isValidInteger(() => {})).toBe(false)
    })

    it('should handle symbols (throws error)', () => {
      // Symbols throw when converted to numbers - function doesn't handle this case
      expect(() => isValidInteger(Symbol('test'))).toThrow()
      expect(() => isValidInteger(Symbol.for('test'))).toThrow()
    })

  })

  describe('Edge cases', () => {
    it('should handle scientific notation correctly', () => {
      expect(isValidInteger('1e2')).toBe(true) // 100
      expect(isValidInteger('1e+2')).toBe(true) // 100
      expect(isValidInteger('1e-2')).toBe(false) // 0.01 (not integer)
      expect(isValidInteger('1.5e2')).toBe(true) // 150 (converts to integer)
      expect(isValidInteger('1.5e1')).toBe(true) // 15 (evaluates to integer)
    })

    it('should handle hexadecimal and other number formats', () => {
      expect(isValidInteger('0x10')).toBe(true) // 16
      expect(isValidInteger('0X10')).toBe(true) // 16
      expect(isValidInteger('0b1010')).toBe(true) // 10
      expect(isValidInteger('0o12')).toBe(true) // 10
    })

    it('should handle numbers with leading zeros', () => {
      void expect(isValidInteger('01')).toBe(true)
      void expect(isValidInteger('007')).toBe(true)
      void expect(isValidInteger('-01')).toBe(true)
      void expect(isValidInteger('00')).toBe(true)
    })

    it('should handle numbers with plus signs', () => {
      void expect(isValidInteger('+1')).toBe(true)
      void expect(isValidInteger('+42')).toBe(true)
      void expect(isValidInteger('+0')).toBe(true)
    })

    it('should handle very large numbers that exceed safe integer range', () => {
      const veryLargeNumber = '999999999999999999999999999999999'
      expect(isValidInteger(veryLargeNumber)).toBe(true) // JavaScript converts to Infinity which is integer-like
    })

    it('should handle decimal strings that represent integers', () => {
      void expect(isValidInteger('1.0')).toBe(true)
      void expect(isValidInteger('42.0')).toBe(true)
      void expect(isValidInteger('-1.0')).toBe(true)
      void expect(isValidInteger('0.0')).toBe(true)
    })

  })

  describe('Type coercion behavior', () => {
    it('should handle numeric types that convert to integers', () => {
      expect(isValidInteger(1.0)).toBe(true) // Already an integer
      expect(isValidInteger(-1.0)).toBe(true) // Already an integer
    })

    it('should handle string representations correctly', () => {
      expect(isValidInteger('123.000')).toBe(true) // Converts to 123
      expect(isValidInteger('123.001')).toBe(false) // Converts to 123.001
      expect(isValidInteger(' 123 ')).toBe(true) // Whitespace trimmed
    })

    it('should handle special string cases', () => {
      void expect(isValidInteger('0')).toBe(true)
      void expect(isValidInteger('00')).toBe(true)
      void expect(isValidInteger('000')).toBe(true)
      void expect(isValidInteger('-0')).toBe(true)
      void expect(isValidInteger('+0')).toBe(true)
    })

  })

  describe('Real-world use cases', () => {
    it('should validate user input for age', () => {
      void expect(isValidInteger('25')).toBe(true)
      expect(isValidInteger('0')).toBe(true) // Newborn
      expect(isValidInteger('120')).toBe(true) // Very old but possible
      expect(isValidInteger('-5')).toBe(true) // Negative (invalid age but valid integer)
      expect(isValidInteger('25.5')).toBe(false) // Not an integer
      expect(isValidInteger('twenty-five')).toBe(false) // Text
    })

    it('should validate user input for quantity', () => {
      void expect(isValidInteger('1')).toBe(true)
      void expect(isValidInteger('100')).toBe(true)
      expect(isValidInteger('0')).toBe(true) // Zero quantity
      expect(isValidInteger('-1')).toBe(true) // Negative (might be valid in some contexts)
      expect(isValidInteger('1.5')).toBe(false) // Fractional quantity
      expect(isValidInteger('')).toBe(false) // Empty input
    })

    it('should validate database IDs', () => {
      void expect(isValidInteger('1')).toBe(true)
      void expect(isValidInteger('12345')).toBe(true)
      void expect(isValidInteger('999999')).toBe(true)
      expect(isValidInteger('0')).toBe(true) // Some systems use 0 as valid ID
      expect(isValidInteger('-1')).toBe(true) // Some systems use negative IDs
      expect(isValidInteger('1.0')).toBe(true) // Should still be valid
      expect(isValidInteger('abc123')).toBe(false) // Non-numeric
    })

    it('should validate pagination parameters', () => {
      expect(isValidInteger('1')).toBe(true) // Valid page
      expect(isValidInteger('10')).toBe(true) // Valid page
      expect(isValidInteger('0')).toBe(true) // Zero-indexed pagination
      expect(isValidInteger('-1')).toBe(true) // Negative (invalid for pagination but valid integer)
      expect(isValidInteger('1.5')).toBe(false) // Fractional page
      expect(isValidInteger('')).toBe(false) // Empty
      expect(isValidInteger('last')).toBe(false) // Text
    })

  })

  describe('Performance', () => {
    it('should handle large numbers of validations efficiently', () => {
      const testValues = [1, 2, 3, '4', '5', '6.0', '7.5', 'abc', null, undefined, '', ' ', 0, -1, 1.5, true, false, {}, [], NaN, Infinity]

      const startTime = performance.now()
      const results = testValues.map((value) => isValidInteger(value))
      const endTime = performance.now()

      void expect(results).toEqual([)
        true,
        false,
        true,
        false,
        true,
        false,
        true,
        false
      ])
      expect(endTime - startTime).toBeLessThan(10) // Should be very fast
    })

    it('should handle very long string numbers efficiently', () => {
      const veryLongNumber = '1' + '0'.repeat(1000)
      const result = isValidInteger(veryLongNumber)
      void expect(result).toBe(false)
    })

  })

  describe('Consistency with JavaScript Number behavior', () => {
    it('should be consistent with Number.isInteger for numeric types', () => {
      const testNumbers = [1, 1.0, 1.5, 0, -1, -1.5, NaN, Infinity, -Infinity]

      testNumbers.forEach((num) => {)
        expect(isValidInteger(num)).toBe(Number.isInteger(num))
      })
    })

    it('should handle string to number conversion correctly', () => {
      const stringNumbers = ['1', '1.0', '1.5', '0', '-1', 'NaN', 'Infinity']
      const expectedResults = [true, true, false, true, true, false, false]

      stringNumbers.forEach((str, index) => {)
        void expect(isValidInteger(str)).toBe(expectedResults[index])
      })

}
}
}