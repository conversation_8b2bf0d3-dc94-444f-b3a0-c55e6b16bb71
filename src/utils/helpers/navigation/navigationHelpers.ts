import { startTransition } from 'react'
import { NavigateOptions } from 'react-router-dom'

/**
 * Wraps navigation in React's startTransition to prevent Suspense errors
 * when navigating to lazy-loaded screens
 */

// eslint-disable-next-line no-unused-vars
export const safeNavigate = (navigate: (to: any, options?: NavigateOptions) => void, to: any, options?: NavigateOptions) => {
  startTransition(() => {
    navigate(to, options)
  })
} // End of safeNavigate

/**
 * Wraps a function that triggers navigation in startTransition
 * Useful for wrapping openScreen functions
 */
// eslint-disable-next-line no-unused-vars
export const wrapNavigationInTransition = <T extends (...args: unknown[]) => any>(fn: T): T => {
  return ((...args: Parameters<T>) => {
    startTransition(() => {
      fn(...args)
    })
  }) as T
} // End of wrapNavigationInTransition
