// Splits an array into chunks of a specified size, useful for implementing pagination or similar.

function paginateArray<T>(array: T[], pageSize: number, pageNumber: number): T[] {
  // Correcting the pageSize to return the full array if pageSize is less than 1
  if (pageSize < 1) {
    return array
  }

  // Check if pageNumber is less than 1 and adjust it to 1 if necessary
  pageNumber = Math.max(1, pageNumber)

  return array.slice((pageNumber - 1) * pageSize, pageNumber * pageSize)
}

export default paginateArray

//// Example usage: Paginate an array.

// const items = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
// console.warn(paginateArray(items, 5, 2));  // Result => [6, 7, 8, 9, 10]
