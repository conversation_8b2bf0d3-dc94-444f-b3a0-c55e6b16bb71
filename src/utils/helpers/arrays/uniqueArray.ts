// Ensures all elements in an array are unique based on a specific key or entire object.

function uniqueArray<T>(array: T[], key?: keyof T): T[] {
  if (key) {
    return Array.from(new Map(array.map((_item) => [_item[key], _item])).values())
  }
  return Array.from(new Set(array).values())
}

export default uniqueArray

//// Example usage: Ensure an array of objects has unique values based on a specific key.

// const people = [{ id: 1, name: '<PERSON>' }, { id: 1, name: '<PERSON>' }];
// console.warn(uniqueArray(people, 'id'));
