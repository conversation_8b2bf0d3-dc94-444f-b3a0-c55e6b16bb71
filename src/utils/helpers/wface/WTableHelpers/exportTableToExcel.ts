import * as ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import { recursiveTableTitleGenerator } from './recursiveTableTitleGenerator'
import dayjs from 'dayjs'
import { tableFieldValueGetters } from '../../workflow/tableHelpers'

export async function exportTableToExcel(
  columns: unknown[],
  groupDataItems: unknown[],
  fileName: string,
  tableType?: string,
  // eslint-disable-next-line no-unused-vars
  languageFile?: (key: string) => string,
): Promise<void> {
  const workbook = new ExcelJS.Workbook()
  const sheet = workbook.addWorksheet(fileName)
  let dataLevel = 0
  let currentDataItem = (groupDataItems[0] as any)?.groups
  while (currentDataItem && currentDataItem?.length > 0) {
    dataLevel++
    currentDataItem = currentDataItem.groups
  }

  // Define header style
  const headerStyle: Partial<ExcelJS.Style> = {
    font: { bold: true, color: { argb: 'FFFFFFFF' } },
    fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF808080' } },
  }

  // Define border style
  const borderStyle: Partial<ExcelJS.Borders> = {
    top: { style: 'thin', color: { argb: 'EED3D3D3' } },
    left: { style: 'thin', color: { argb: 'EED3D3D3' } },
    bottom: { style: 'thin', color: { argb: 'EED3D3D3' } },
    right: { style: 'thin', color: { argb: 'EED3D3D3' } },
  }

  // Define title style
  const titleStyle: Partial<ExcelJS.Style> = {
    fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFD3D3D3' } },
  }

  // Function to remove HTML tags
  const removeHtmlTags = (str: string): string => {
    if (!str || typeof str !== 'string') return str
    // First replace common HTML entities
    const withoutEntities = str
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
    // Then remove all HTML tags and trim the result
    return withoutEntities.replace(/<[^>]*>/g, '').trim()
  }

  const extractTextFromReactElement = (element: any): string => {
    if (!element) return ''
    if (typeof element === 'string') return removeHtmlTags(element)
    if (element.props) {
      if (typeof element.props.children === 'string') {
        return removeHtmlTags(element.props.children)
      }
      if (Array.isArray(element.props.children)) {
        return element.props.children.map((child: any) => extractTextFromReactElement(child)).join('')
      }
      return extractTextFromReactElement(element.props.children)
    }
    return ''
  }

  const columnHeaders = columns.map((column: any) => {
    if (typeof column.title === 'object' && column.title !== null) {
      const textContent = extractTextFromReactElement(column.title)
      return languageFile ? languageFile(textContent) : textContent
    }
    return languageFile ? languageFile(column.title ?? column.field) : (column.title ?? column.field)
  })

  // Adjust columns setup
  sheet.columns = columnHeaders.map((header, index) => ({
    header,
    key: (columns[index] as any).field,
    width: 0.1,
  }))

  // Apply header style
  const headerRow = sheet.getRow(1)
  for (let i = 1; i <= columns.length - dataLevel; i++) {
    headerRow.getCell(i).style = headerStyle
    headerRow.getCell(i).border = borderStyle
  }

  let currentRowIndex = 2

  const getFieldValue = (rowData: any, field: string, tableType?: string) => {
    const isEnglish = localStorage.getItem('language') === 'en'
    tableType ??= 'default'
    const fieldValueGetter = tableFieldValueGetters[tableType] ?? tableFieldValueGetters.default
    const value = fieldValueGetter(rowData, field, isEnglish)
    return typeof value === 'string' ? removeHtmlTags(value) : value
  }

  const getRowValue = (dataItem: any, column: any) => {
    if (column?.dateSetting?.type === 'date') {
      return dayjs(dataItem[column.field]).format('DD.MM.YYYY HH:mm:ss')
    }
    return getFieldValue(dataItem, column.field, tableType)
  }

  const addDataRows = (dataItems: unknown[], sheet: ExcelJS.Worksheet, level: number) => {
    dataItems.forEach((_dataItem) => {
      const row = sheet.getRow(currentRowIndex)
      columns.forEach((column, colIndex) => {
        const cell = row.getCell(colIndex + 1 + level)
        const value = getRowValue(_dataItem, column)
        cell.value = value !== null && value !== undefined ? String(value) : ''
        cell.border = borderStyle
      })
      currentRowIndex++
    })
  }

  const addGroupedDataToSheet = (groupDataItems: unknown[], sheet: ExcelJS.Worksheet, level: number = dataLevel) => {
    groupDataItems.forEach((groupItem: any) => {
      const title = removeHtmlTags(recursiveTableTitleGenerator(groupItem, languageFile ?? ((key: string) => key)))
      const titleRow = sheet.getRow(currentRowIndex)
      titleRow.getCell(1 + level).value = ''
      const titleCell = titleRow.getCell(1 + level)
      titleCell.value = title
      void sheet.mergeCells(currentRowIndex, 1 + level, currentRowIndex, 11 + level)
      titleCell.style = titleStyle
      titleCell.alignment = { vertical: 'middle', horizontal: 'left' }
      titleRow.eachCell((cell) => (cell.border = borderStyle))

      currentRowIndex++

      if (groupItem.data && groupItem.data.length > 0) {
        addDataRows(groupItem.data, sheet, level)
      }

      if (groupItem.groups && groupItem.groups.length > 0) {
        addGroupedDataToSheet(groupItem.groups, sheet, level + 1)
      }
    })
  }

  // Initiate processing
  if (groupDataItems.length > 0 && (groupDataItems[0] as any).data === undefined && (groupDataItems[0] as any).groups === undefined) {
    addDataRows(groupDataItems, sheet, 0)
  } else {
    addGroupedDataToSheet(groupDataItems, sheet)
  }

  // Adjust column widths
  sheet.columns.forEach((column: any) => {
    let maxContentLength = 0
    if (column) {
      column.eachCell({ includeEmpty: true }, (cell: any) => {
        if (!cell.isMerged) {
          const cellLength = cell.value ? String(cell.value).length : 0
          maxContentLength = Math.max(maxContentLength, cellLength)
        }
      })
    }
    column.width = maxContentLength + 2
  })

  // Generate and save the Excel file
  const buffer = await workbook.xlsx.writeBuffer()
  const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  const fileExtension = '.xlsx'
  const blob = new Blob([buffer], { type: fileType })
  const currentDateTime = dayjs().format('YYYYMMDD_HHmmss')
  const filename = `digiflow_history_${currentDateTime}`
  saveAs(blob, filename + fileExtension)
}
