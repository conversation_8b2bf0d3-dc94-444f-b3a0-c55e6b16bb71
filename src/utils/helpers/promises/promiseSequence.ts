// Executes an array of promise-returning functions in sequence, waiting for each to resolve before executing the next.
// This is useful when each promise must be resolved in order, and the next promise execution depends on the completion of the previous one.

export default async function promiseSequence<T>(funcs: (() => Promise<T>)[]): Promise<T[]> {
  const results: T[] = []
  for (const func of funcs) {
    const result = await func()
    results.push(result)
  }
  return results
} // End of promiseSequence
