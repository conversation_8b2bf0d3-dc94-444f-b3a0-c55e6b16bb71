// Executes an array of promise-returning functions in sequence, waiting for each to resolve before executing the next.
// This is useful when each promise must be resolved in order, and the next promise execution depends on the completion of the previous one.

// Type to extract the return type from a promise-returning function
type PromiseReturnType<T> = T extends () => Promise<infer R> ? R : never

// Type to map an array of promise-returning functions to their return types
type PromiseSequenceResult<T extends readonly (() => Promise<any>)[]> = {
  [K in keyof T]: PromiseReturnType<T[K]>
}

export default async function promiseSequence<T extends readonly (() => Promise<any>)[]>(funcs: T): Promise<PromiseSequenceResult<T>> {
  const results: any[] = []
  for (const func of funcs) {
    const result = await func()
    results.push(result)
  }
  return results as PromiseSequenceResult<T>
} // End of promiseSequence
