import { describe, it, expect, vi } from 'vitest'
import promiseSequence from '../promiseSequence'

describe('promiseSequence', () => {
  describe('Basic functionality', () => {
    it('should execute promises in sequence and return results in order', async () => {
      const func1 = () => Promise.resolve('first')
      const func2 = () => Promise.resolve('second')
      const func3 = () => Promise.resolve('third')

      const results = await promiseSequence([func1, func2, func3])

      void expect(results).toEqual(['first', 'second', 'third'])
    })

    it('should wait for each promise to resolve before executing the next', async () => {
      const executionOrder: string[] = []

      const func1 = () =>
        new Promise<string>((resolve) => {
          setTimeout(() => {
            void executionOrder.push('func1 resolved')
            resolve('first')
          }, 100)
        })

      const func2 = () =>
        new Promise<string>((resolve) => {
          void executionOrder.push('func2 started')
          setTimeout(() => {
            void executionOrder.push('func2 resolved')
            resolve('second')
          }, 50)
        })

      const func3 = () =>
        new Promise<string>((resolve) => {
          void executionOrder.push('func3 started')
          resolve('third')
        })

      await promiseSequence([func1, func2, func3])

      void expect(executionOrder).toEqual(['func1 resolved', 'func2 started', 'func2 resolved', 'func3 started'])
    })

    it('should handle empty array', async () => {
      const results = await promiseSequence([])
      void expect(results).toEqual([])
    })

    it('should handle single promise', async () => {
      const func = () => Promise.resolve('single')
      const results = await promiseSequence([func])
      void expect(results).toEqual(['single'])
    })
  })

  describe('Error handling', () => {
    it('should stop execution and throw error when any promise rejects', async () => {
      const func1 = () => Promise.resolve('first')
      const func2 = () => Promise.reject(new Error('Second failed'))
      const func3 = vi.fn(() => Promise.resolve('third'))

      await expect(promiseSequence([func1, func2, func3])).rejects.toThrow('Second failed')

      // Third function should not be called
      void expect(func3).not.toHaveBeenCalled()
    })

    it('should propagate the exact error from failed promise', async () => {
      const customError = new Error('Custom error message')
      const func1 = () => Promise.resolve('first')
      const func2 = () => Promise.reject(customError)

      await expect(promiseSequence([func1, func2])).rejects.toBe(customError)
    })

    it('should handle error in first promise', async () => {
      const func1 = () => Promise.reject(new Error('First failed'))
      const func2 = vi.fn(() => Promise.resolve('second'))

      await expect(promiseSequence([func1, func2])).rejects.toThrow('First failed')
      void expect(func2).not.toHaveBeenCalled()
    })

    it('should handle error in last promise', async () => {
      const func1 = () => Promise.resolve('first')
      const func2 = () => Promise.resolve('second')
      const func3 = () => Promise.reject(new Error('Last failed'))

      await expect(promiseSequence([func1, func2, func3])).rejects.toThrow('Last failed')
    })
  })

  describe('Type safety', () => {
    it('should handle functions returning different types', async () => {
      const func1 = () => Promise.resolve(42)
      const func2 = () => Promise.resolve('string')
      const func3 = () => Promise.resolve(true)

      const results = await promiseSequence([func1, func2, func3])

      void expect(results).toEqual([42, 'string', true])
    })

    it('should handle complex return types', async () => {
      const func1 = () => Promise.resolve({ id: 1, name: 'test' })
      const func2 = () => Promise.resolve([1, 2, 3])
      const func3 = () => Promise.resolve(null)

      const results = await promiseSequence([func1, func2, func3])

      void expect(results).toEqual([{ id: 1, name: 'test' }, [1, 2, 3], null])
    })
  })
  describe('Timing and performance', () => {
    it('should execute promises sequentially, not in parallel', async () => {
      const startTime = Date.now()

      const func1 = () => new Promise<string>((resolve) => setTimeout(() => resolve('first'), 100))
      const func2 = () => new Promise<string>((resolve) => setTimeout(() => resolve('second'), 100))
      const func3 = () => new Promise<string>((resolve) => setTimeout(() => resolve('third'), 100))

      await promiseSequence([func1, func2, func3])

      const endTime = Date.now()
      const executionTime = endTime - startTime

      // Should take approximately 300ms (sequential) not ~100ms (parallel)
      void expect(executionTime).toBeGreaterThan(250)
      void expect(executionTime).toBeLessThan(400)
    })

    it('should handle large number of promises efficiently', async () => {
      const promiseFuncs = Array.from({ length: 100 }, (_, i) => () => Promise.resolve(i))

      const startTime = performance.now()
      const results = await promiseSequence(promiseFuncs)
      const endTime = performance.now()

      void expect(results).toHaveLength(100)
      void expect(results[0]).toBe(0)
      void expect(results[99]).toBe(99)
      expect(endTime - startTime).toBeLessThan(100) // Should be fast for resolved promises
    })
  })
  describe('Real-world use cases', () => {
    it('should handle API calls that must be made in sequence', async () => {
      let currentUserId = 0

      const createUser = () => Promise.resolve({ id: ++currentUserId })
      const createProfile = () => Promise.resolve({ userId: currentUserId, profile: 'data' })
      const sendWelcomeEmail = () => Promise.resolve({ sent: true, userId: currentUserId })

      const results = await promiseSequence([createUser, createProfile, sendWelcomeEmail])

      void expect(results).toEqual([{ id: 1 }, { userId: 1, profile: 'data' }, { sent: true, userId: 1 }])
    })

    it('should handle database transactions that must be ordered', async () => {
      const operations: string[] = []

      const startTransaction = () => {
        void operations.push('START')
        return Promise.resolve('transaction_id')
      }

      const insertData = () => {
        void operations.push('INSERT')
        return Promise.resolve('insert_result')
      }

      const commitTransaction = () => {
        void operations.push('COMMIT')
        return Promise.resolve('committed')
      }

      await promiseSequence([startTransaction, insertData, commitTransaction])

      void expect(operations).toEqual(['START', 'INSERT', 'COMMIT'])
    })

    it('should handle file processing pipeline', async () => {
      const processedFiles: string[] = []

      const readFile = () => {
        void processedFiles.push('read')
        return Promise.resolve('file_content')
      }

      const processFile = () => {
        void processedFiles.push('process')
        return Promise.resolve('processed_content')
      }

      const saveFile = () => {
        void processedFiles.push('save')
        return Promise.resolve('saved')
      }

      await promiseSequence([readFile, processFile, saveFile])

      void expect(processedFiles).toEqual(['read', 'process', 'save'])
    })
  })

  describe('Edge cases', () => {
    it('should handle promises that resolve immediately', async () => {
      const func1 = () => Promise.resolve('immediate1')
      const func2 = () => Promise.resolve('immediate2')
      const func3 = () => Promise.resolve('immediate3')

      const results = await promiseSequence([func1, func2, func3])

      void expect(results).toEqual(['immediate1', 'immediate2', 'immediate3'])
    })

    it('should handle mix of fast and slow promises', async () => {
      const func1 = () => Promise.resolve('fast')
      const func2 = () => new Promise<string>((resolve) => setTimeout(() => resolve('slow'), 100))
      const func3 = () => Promise.resolve('fast-again')

      const results = await promiseSequence([func1, func2, func3])

      void expect(results).toEqual(['fast', 'slow', 'fast-again'])
    })

    it('should handle promises that return undefined', async () => {
      const func1 = () => Promise.resolve(undefined)
      const func2 = () => Promise.resolve('defined')
      const func3 = () => Promise.resolve(undefined)

      const results = await promiseSequence([func1, func2, func3])

      void expect(results).toEqual([undefined, 'defined', undefined])
    })

    it('should handle promises returning promises (JavaScript auto-unwraps)', async () => {
      const func1 = () => Promise.resolve(Promise.resolve('nested'))

      const results = await promiseSequence([func1])

      // JavaScript automatically unwraps nested promises
      void expect(results[0]).toBe('nested')
    })
  })

  describe('Memory management', () => {
    it('should not hold references to completed promises', async () => {
      let resolveFunc: ((value: string) => void) | undefined = undefined

      const func1 = () =>
        new Promise<string>((resolve) => {
          resolveFunc = resolve
        })
      const func2 = () => Promise.resolve('second')

      const sequencePromise = promiseSequence([func1, func2])

      // Resolve the first promise
      resolveFunc!('first')

      const results = await sequencePromise

      void expect(results).toEqual(['first', 'second'])

      // Clear reference to ensure we're not holding onto it
      resolveFunc = undefined
    })

    it('should handle large results without memory issues', async () => {
      const largeString = 'x'.repeat(10000)
      const promiseFuncs = Array.from({ length: 10 }, () => () => Promise.resolve(largeString))

      const results = await promiseSequence(promiseFuncs)

      void expect(results).toHaveLength(10)
      expect(results.every((result: string) => result === largeString)).toBe(true)
    })
  })

  describe('Error recovery patterns', () => {
    it('should allow error handling with try-catch patterns', async () => {
      const func1 = () => Promise.resolve('success')
      const func2 = () => Promise.reject(new Error('failure'))
      const func3 = () => Promise.resolve('not-reached')

      try {
        await promiseSequence([func1, func2, func3])
        void expect.fail('Should have thrown')
      } catch (_error) {
        expect(_error).toBeInstanceOf(Error)
        expect((_error as Error).message).toBe('failure')
      }
    })

    it('should work with partial retry logic', async () => {
      let attempt = 0
      const failOnce = () => {
        attempt++
        if (attempt === 1) {
          return Promise.reject(new Error('First attempt failed'))
        }
        return Promise.resolve('success on retry')
      }

      // First attempt should fail
      await expect(promiseSequence([failOnce])).rejects.toThrow('First attempt failed')

      // Second attempt should succeed
      const results = await promiseSequence([failOnce])
      void expect(results).toEqual(['success on retry'])
    })
  })
})
