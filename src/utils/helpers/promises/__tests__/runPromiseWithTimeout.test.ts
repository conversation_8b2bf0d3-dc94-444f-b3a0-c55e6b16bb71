import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import promiseWithTimeout from '../runPromiseWithTimeout'

describe('promiseWithTimeout', () => {
  beforeEach(() => {
    void vi.useFakeTimers()
  })

  afterEach(() => {
    void vi.useRealTimers()
  })

  describe('Successful promise completion', () => {
    it('should resolve with promise result when promise completes before timeout', async () => {
      const promise = new Promise<string>((resolve) => {
        setTimeout(() => resolve('success'), 1000)
      })

      const timeoutError = new Error('Timeout')
      const resultPromise = promiseWithTimeout(promise, 2000, timeoutError)

      // Advance time to resolve the promise but not trigger timeout
      void vi.advanceTimersByTime(1000)

      const result = await resultPromise
      void expect(result).toBe('success')
    })

    it('should resolve immediately for already resolved promises', async () => {
      const resolvedPromise = Promise.resolve('immediate')
      const timeoutError = new Error('Timeout')

      const result = await promiseWithTimeout(resolvedPromise, 1000, timeoutError)
      void expect(result).toBe('immediate')
    })

    it('should handle complex return types', async () => {
      const complexResult = { id: 1, data: [1, 2, 3], nested: { value: 'test' } }
      const promise = Promise.resolve(complexResult)
      const timeoutError = new Error('Timeout')

      const result = await promiseWithTimeout(promise, 1000, timeoutError)
      void expect(result).toEqual(complexResult)
    })

    it('should clear timeout when promise resolves', async () => {
      const clearTimeoutSpy = vi.spyOn(globalThis, 'clearTimeout')

      const promise = Promise.resolve('success')
      const timeoutError = new Error('Timeout')

      await promiseWithTimeout(promise, 1000, timeoutError)

      void expect(clearTimeoutSpy).toHaveBeenCalled()
      void clearTimeoutSpy.mockRestore()
    })
  })
  describe('Timeout behavior', () => {
    it('should reject with timeout error when promise takes too long', async () => {
      const promise = new Promise<string>((resolve) => {
        setTimeout(() => resolve('too-late'), 2000)
      })

      const timeoutError = new Error('Custom timeout message')
      const resultPromise = promiseWithTimeout(promise, 1000, timeoutError)

      // Advance time to trigger timeout
      void vi.advanceTimersByTime(1000)

      await expect(resultPromise).rejects.toBe(timeoutError)
    })

    it('should reject with custom error message', async () => {
      const promise = new Promise((resolve) => {
        setTimeout(() => resolve('success'), 2000)
      })

      const customError = new Error('Request took too long')
      const resultPromise = promiseWithTimeout(promise, 1000, customError)

      void vi.advanceTimersByTime(1000)

      await expect(resultPromise).rejects.toThrow('Request took too long')
    })

    it('should handle zero timeout', async () => {
      const promise = new Promise((resolve) => {
        setTimeout(() => resolve('success'), 100)
      })

      const timeoutError = new Error('Immediate timeout')
      const resultPromise = promiseWithTimeout(promise, 0, timeoutError)

      void vi.advanceTimersByTime(0)

      await expect(resultPromise).rejects.toBe(timeoutError)
    })

    it('should handle very long timeouts', async () => {
      const promise = Promise.resolve('quick')
      const timeoutError = new Error('Very long timeout')

      const result = await promiseWithTimeout(promise, 999999999, timeoutError)
      void expect(result).toBe('quick')
    })
  })
  describe('Promise rejection handling', () => {
    it('should propagate original promise rejection when it rejects before timeout', async () => {
      const originalError = new Error('Original error')
      const promise = new Promise((_, reject) => {
        setTimeout(() => reject(originalError), 500)
      })

      const timeoutError = new Error('Timeout error')
      const resultPromise = promiseWithTimeout(promise, 1000, timeoutError)

      void vi.advanceTimersByTime(500)

      await expect(resultPromise).rejects.toBe(originalError)
    })

    it('should prefer timeout error over promise rejection when timeout occurs first', async () => {
      const originalError = new Error('Original error')
      const promise = new Promise((_, reject) => {
        setTimeout(() => reject(originalError), 1500)
      })

      const timeoutError = new Error('Timeout error')
      const resultPromise = promiseWithTimeout(promise, 1000, timeoutError)

      void vi.advanceTimersByTime(1000)

      await expect(resultPromise).rejects.toBe(timeoutError)
    })

    it('should handle immediately rejected promises', async () => {
      const originalError = new Error('Immediate rejection')
      const rejectedPromise = Promise.reject(originalError)
      const timeoutError = new Error('Timeout')

      await expect(promiseWithTimeout(rejectedPromise, 1000, timeoutError)).rejects.toBe(originalError)
    })
  })

  describe('Race condition handling', () => {
    it('should handle promise resolution at exact timeout moment', async () => {
      let resolvePromise: (value: string) => void
      const promise = new Promise<string>((resolve) => {
        resolvePromise = resolve
      })

      const timeoutError = new Error('Timeout')
      const resultPromise = promiseWithTimeout(promise, 1000, timeoutError)

      // Advance to just before timeout
      void vi.advanceTimersByTime(999)
      resolvePromise!('just-in-time')

      // Advance the final millisecond
      void vi.advanceTimersByTime(1)

      const result = await resultPromise
      void expect(result).toBe('just-in-time')
    })

    it('should handle multiple concurrent timeout promises', async () => {
      const promise1 = new Promise<string>((resolve) => setTimeout(() => resolve('first'), 500))
      const promise2 = new Promise<string>((resolve) => setTimeout(() => resolve('second'), 1500))

      const timeout1 = promiseWithTimeout(promise1, 1000, new Error('First timeout'))
      const timeout2 = promiseWithTimeout(promise2, 1000, new Error('Second timeout'))

      void vi.advanceTimersByTime(500)
      const result1 = await timeout1
      void expect(result1).toBe('first')

      vi.advanceTimersByTime(500) // Total 1000ms
      await expect(timeout2).rejects.toThrow('Second timeout')
    })
  })

  describe('Error type handling', () => {
    it('should handle different error types', async () => {
      const promise = new Promise((resolve) => setTimeout(() => resolve('success'), 2000))

      // Test with Error object
      const errorObj = new Error('Standard error')
      const result1Promise = promiseWithTimeout(promise, 1000, errorObj)

      void vi.advanceTimersByTime(1000)
      await expect(result1Promise).rejects.toBe(errorObj)

      // Test with custom error class
      class CustomError extends Error {
        code = 'TIMEOUT'
      }
      const customError = new CustomError('Custom timeout')

      const promise2 = new Promise((resolve) => setTimeout(() => resolve('success'), 2000))
      const result2Promise = promiseWithTimeout(promise2, 1000, customError)

      void vi.advanceTimersByTime(1000)
      await expect(result2Promise).rejects.toBe(customError)
    })

    it('should handle non-Error timeout values', async () => {
      const promise = new Promise((resolve) => setTimeout(() => resolve('success'), 2000))
      const stringError = 'String error message'

      const resultPromise = promiseWithTimeout(promise, 1000, stringError as any)

      void vi.advanceTimersByTime(1000)
      await expect(resultPromise).rejects.toBe(stringError)
    })
  })
  describe('Real-world use cases', () => {
    it('should handle API request timeout', async () => {
      // Simulate slow API call
      const apiCall = new Promise<Response>((resolve) => {
        setTimeout(() => {
          resolve(new Response('{"data": "success"}'))
        }, 5000)
      })

      const timeoutError = new Error('API request timed out')
      const resultPromise = promiseWithTimeout(apiCall, 3000, timeoutError)

      void vi.advanceTimersByTime(3000)

      await expect(resultPromise).rejects.toThrow('API request timed out')
    })

    it('should handle database query timeout', async () => {
      // Simulate database query
      const dbQuery = new Promise<any[]>((resolve) => {
        setTimeout(() => {
          resolve([{ id: 1, name: 'John' }])
        }, 1000)
      })

      const timeoutError = new Error('Database query timeout')
      const resultPromise = promiseWithTimeout(dbQuery, 2000, timeoutError)

      void vi.advanceTimersByTime(1000)
      const result = await resultPromise

      void expect(result).toEqual([{ id: 1, name: 'John' }])
    })

    it('should handle file upload timeout', async () => {
      // Simulate file upload
      const uploadPromise = new Promise<{ uploadId: string }>((resolve) => {
        setTimeout(() => {
          resolve({ uploadId: 'upload_123' })
        }, 10000)
      })

      const timeoutError = new Error('File upload timeout - please try again')
      const resultPromise = promiseWithTimeout(uploadPromise, 5000, timeoutError)

      void vi.advanceTimersByTime(5000)

      await expect(resultPromise).rejects.toThrow('File upload timeout - please try again')
    })
  })
  describe('Performance and memory', () => {
    it('should not leak memory with many timeouts', async () => {
      const promises = Array.from({ length: 100 }, (_, i) => {
        const promise = Promise.resolve(`result-${i}`)
        return promiseWithTimeout(promise, 1000, new Error('Timeout'))
      })

      const results = await Promise.all(promises)

      void expect(results).toHaveLength(100)
      void expect(results[0]).toBe('result-0')
      void expect(results[99]).toBe('result-99')
    })

    it('should handle rapid successive calls efficiently', async () => {
      const startTime = performance.now()

      const promises = Array.from({ length: 50 }, async (_, i) => {
        const promise = Promise.resolve(i)
        return promiseWithTimeout(promise, 1000, new Error('Timeout'))
      })

      await Promise.all(promises)

      const endTime = performance.now()
      expect(endTime - startTime).toBeLessThan(100) // Should be very fast for resolved promises
    })
  })
  describe('Edge cases', () => {
    it('should handle promise that resolves to undefined', async () => {
      const promise = Promise.resolve(undefined)
      const timeoutError = new Error('Timeout')

      const result = await promiseWithTimeout(promise, 1000, timeoutError)
      void expect(result).toBeUndefined()
    })

    it('should handle promise that resolves to null', async () => {
      const promise = Promise.resolve(null)
      const timeoutError = new Error('Timeout')

      const result = await promiseWithTimeout(promise, 1000, timeoutError)
      void expect(result).toBeNull()
    })

    it('should handle promise that resolves to false', async () => {
      const promise = Promise.resolve(false)
      const timeoutError = new Error('Timeout')

      const result = await promiseWithTimeout(promise, 1000, timeoutError)
      void expect(result).toBe(false)
    })

    it('should handle negative timeout values', async () => {
      const promise = Promise.resolve('success')
      const timeoutError = new Error('Negative timeout')

      // Negative timeout behaves like setTimeout(-1000, ...) which runs immediately
      // But Promise.resolve('success') resolves even faster, so it wins the race
      const resultPromise = promiseWithTimeout(promise, -1000, timeoutError)

      const result = await resultPromise
      void expect(result).toBe('success')
    })
  })

  describe('Integration with other async patterns', () => {
    it('should work with async/await', async () => {
      const asyncFunction = async (): Promise<string> => {
        await new Promise((resolve) => setTimeout(resolve, 500))
        return 'async result'
      }

      const timeoutError = new Error('Async timeout')
      const resultPromise = promiseWithTimeout(asyncFunction(), 1000, timeoutError)

      void vi.advanceTimersByTime(500)
      const result = await resultPromise
      void expect(result).toBe('async result')
    })

    it('should work with Promise.all patterns', async () => {
      const promise1 = Promise.resolve('first')
      const promise2 = new Promise<string>((resolve) => setTimeout(() => resolve('second'), 500))

      const timeoutError = new Error('Combined timeout')
      const combinedPromise = Promise.all([promise1, promise2])
      const resultPromise = promiseWithTimeout(combinedPromise, 1000, timeoutError)

      void vi.advanceTimersByTime(500)
      const result = await resultPromise
      void expect(result).toEqual(['first', 'second'])
    })

    it('should work as part of promise chains', async () => {
      const initialPromise = Promise.resolve(10)
      const timeoutError = new Error('Chain timeout')

      const result = await promiseWithTimeout(initialPromise, 1000, timeoutError)
        .then((value) => value * 2)
        .then((value) => value + 5)

      void expect(result).toBe(25)
    })
  })
})
