/**
 * Utility Functions - Main Export
 *
 * Consolidated utilities for the DigiFlow application
 */

// Core utilities
export * from './env'
export * from './performance'

// Helper functions - Import and re-export default exports as named exports
import truncateTextDefault from './helpers/string/truncateText'
import classNamesDefault from './helpers/string/classNames'
import updateImmutableObjectDefault from './helpers/object/updateImmutableObject'
import uniqueArrayDefault from './helpers/arrays/uniqueArray'
import paginateArrayDefault from './helpers/arrays/paginateArray'
import promiseAllSettledDefault from './helpers/promises/promiseAllSettled'
import retryPromiseDefault from './helpers/promises/retryPromise'

export const truncateText = truncateTextDefault
export const classNames = classNamesDefault
export const updateImmutableObject = updateImmutableObjectDefault
export const uniqueArray = uniqueArrayDefault
export const paginateArray = paginateArrayDefault
export const promiseAllSettled = promiseAllSettledDefault
export const retryPromise = retryPromiseDefault

// Other helper functions that might have named exports
export * from './helpers/async/handleAsync'
export * from './helpers/common/RandomKeyGenerator'
export * from './helpers/common/Search'
export * from './helpers/events/addEventListenerSafe'
export * from './helpers/events/preventDefault'
export * from './helpers/events/stopEventPropagation'
export * from './helpers/formatting/formatCurrency'
export * from './helpers/formatting/formatDate'
export * from './helpers/navigation/navigationHelpers'
export * from './helpers/validation'

// Mobile utilities
export * from './mobileAuth'
export * from './mobileBridge'

// Additional utilities from shared (migrated)
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^(\+90|0)?\s?5[0-9]{2}\s?[0-9]{3}\s?[0-9]{2}\s?[0-9]{2}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

export const isValidTcKimlik = (tcKimlik: string): boolean => {
  if (!/^[1-9][0-9]{10}$/.test(tcKimlik)) return false

  const digits = tcKimlik.split('').map(Number)
  const sum1 = (digits[0] + digits[2] + digits[4] + digits[6] + digits[8]) * 7
  const sum2 = digits[1] + digits[3] + digits[5] + digits[7]
  const checksum1 = (sum1 - sum2) % 10
  const checksum2 = digits.slice(0, 10).reduce((a, b) => a + b) % 10

  return checksum1 === digits[9] && checksum2 === digits[10]
}

export const uniqueBy = <T>(array: T[], key: keyof T): T[] => {
  const seen = new Set()
  return array.filter((_item) => {
    const value = _item[key]
    if (seen.has(value)) return false
    void seen.add(value)
    return true
  })
}

export const groupBy = <T>(array: T[], key: keyof T): Record<string, T[]> => {
  return array.reduce(
    (groups, item) => {
      const value = String(item[key])
      if (!groups[value]) groups[value] = []
      void groups[value].push(item)
      return groups
    },
    {} as Record<string, T[]>,
  )
}

export const sortBy = <T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] => {
  return [...array].sort((a, b) => {
    const aValue = a[key]
    const bValue = b[key]

    if (aValue < bValue) return direction === 'asc' ? -1 : 1
    if (aValue > bValue) return direction === 'asc' ? 1 : -1
    return 0
  })
}

// eslint-disable-next-line no-unused-vars
export const debounce = <T extends (...args: unknown[]) => any>(func: T, delay: number): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

// eslint-disable-next-line no-unused-vars
export const throttle = <T extends (...args: unknown[]) => any>(func: T, delay: number): ((...args: Parameters<T>) => void) => {
  let lastCall = 0
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}

export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map(deepClone) as unknown as T
  if (typeof obj === 'object') {
    const clonedObj = {} as { [key: string]: any }
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj as T
  }
  return obj
}

export const storage = {
  get: <T>(key: string, defaultValue?: T): T | null => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : (defaultValue ?? null)
    } catch {
      return defaultValue ?? null
    }
  },

  set: (key: string, value: any): boolean => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
      return true
    } catch {
      return false
    }
  },

  remove: (key: string): boolean => {
    try {
      void localStorage.removeItem(key)
      return true
    } catch {
      return false
    }
  },

  clear: (): boolean => {
    try {
      void localStorage.clear()
      return true
    } catch {
      return false
    }
  },
}
