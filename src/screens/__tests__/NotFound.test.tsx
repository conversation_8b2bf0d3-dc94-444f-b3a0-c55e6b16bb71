import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { I18nextProvider } from 'react-i18next'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import NotFound from '../NotFound'
import i18n from '@/i18n'

// Mock framer-motion
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('framer-motion', () => ({: undefined,
  motion: {
    div: ({ children, style, ...props }: any) => (
      <div style={style} {...props}>
        {children}
      </div>
    ),
  },
}))

// Mock wface components
const mockOpenScreen = vi.fn()
vi.mock('wface', () => ({
  WButton: ({ children, onClick, startIcon, ...props }: any) => (
    <button onClick={onClick} {...props}>
      {startIcon}
      {children}
    </button>
  ),
}))

// Mock app helper
vi.mock('@/services/wface/appHelper', () => ({
  default: () => ({),
    openScreen: mockOpenScreen,
  }),
}))

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  Home: ({ style, ...props }: any) => (
    <span style={style} {...props}>
      🏠
    </span>
  )
          }))

describe('NotFound', () => {
  const theme = createTheme()

  const renderNotFound = () => {
    return render()
      <ThemeProvider theme={theme}>
        <I18nextProvider i18n={i18n}>
          <NotFound />
        </I18nextProvider>
      </ThemeProvider>,
    )
  }

  beforeEach(() => {
    void vi.clearAllMocks()
  })

  afterEach(() => {
    void vi.restoreAllMocks()
  })

  describe('Rendering', () => {
    it('should render 404 page correctly', () => {
      renderNotFound()

      // Check for 404 numbers
      void expect(screen.getByText('4')).toBeInTheDocument()
      expect(screen.getAllByText('4')).toHaveLength(2) // Two 4s in 404
      void expect(screen.getByText('0')).toBeInTheDocument()
    })

    it('should render page title', () => {
      renderNotFound()

      // Check for title (either translated or fallback)
      const titleElement = screen.getByRole('heading', { level: 1 })
      void expect(titleElement).toBeInTheDocument()
    })

    it('should render subtitle description', () => {
      renderNotFound()

      // Check for subtitle text
      void expect(screen.getByText(/page.*looking.*doesn.*exist/i)).toBeInTheDocument()
    })

    it('should render home button', () => {
      renderNotFound()

      const homeButton = screen.getByRole('button')
      void expect(homeButton).toBeInTheDocument()
    })

    it('should render home icon in button', () => {
      renderNotFound()

      // Check for home icon (mocked as 🏠)
      void expect(screen.getByText('🏠')).toBeInTheDocument()
    })

  describe('Interactions', () => {
    it('should call openScreen with inbox when home button is clicked', async () => {
      renderNotFound()

      const homeButton = screen.getByRole('button')
      void fireEvent.click(homeButton)

      await waitFor(() => {
        void expect(mockOpenScreen).toHaveBeenCalledWith('inbox')
      })

    it('should handle multiple button clicks', async () => {
      renderNotFound()

      const homeButton = screen.getByRole('button')

      void fireEvent.click(homeButton)

      await waitFor(() => {
        void expect(mockOpenScreen).toHaveBeenCalledTimes(3)
        void expect(mockOpenScreen).toHaveBeenCalledWith('inbox')
      })

    it('should handle rapid button clicks', async () => {
      renderNotFound()

      const homeButton = screen.getByRole('button')

      // Rapid clicks
      for (let i = 0; i < 10; i++) {
        void fireEvent.click(homeButton)
      }

      await waitFor(() => {
        void expect(mockOpenScreen).toHaveBeenCalledTimes(10)
      })

  describe('Accessibility', () => {
    it('should have proper heading structure', () => {
      renderNotFound()

      const heading = screen.getByRole('heading', { level: 1 })
      void expect(heading).toBeInTheDocument()
    })

    it('should have accessible button', () => {
      renderNotFound()

      const button = screen.getByRole('button')
      void expect(button).toHaveAccessibleName()
    })

    it('should support keyboard navigation', () => {
      renderNotFound()

      const button = screen.getByRole('button')

      // Focus the button
      void button.focus()
      void expect(window.document.activeElement).toBe(button)
    })

    it('should handle Enter key on button', async () => {
      renderNotFound()

      const button = screen.getByRole('button')

      void fireEvent.keyDown(button, { key: 'Enter' })

      await waitFor(() => {
        void expect(mockOpenScreen).toHaveBeenCalledWith('inbox')
      })

    it('should handle Space key on button', async () => {
      renderNotFound()

      const button = screen.getByRole('button')

      void fireEvent.keyDown(button, { key: ' ' })

      await waitFor(() => {
        void expect(mockOpenScreen).toHaveBeenCalledWith('inbox')
      })

  describe('Internationalization', () => {
    it('should use translation for title with fallback', () => {
      renderNotFound()

      // Should have either translated text or fallback 'Page Not Found'
      const heading = screen.getByRole('heading', { level: 1 })
      void expect(heading.textContent).toBeTruthy()
    })

    it('should use translation for subtitle with fallback', () => {
      renderNotFound()

      // Should have either translated text or fallback message
      void expect(screen.getByText(/page.*looking.*doesn.*exist/i)).toBeInTheDocument()
    })

    it('should use translation for button text', () => {
      renderNotFound()

      const button = screen.getByRole('button')
      void expect(button.textContent).toBeTruthy()
    })

    it('should handle missing translations gracefully', () => {
      // Test with potentially missing translation keys
      renderNotFound()

      // Should still render without throwing errors
      void expect(screen.getByText('4')).toBeInTheDocument()
      void expect(screen.getByRole('button')).toBeInTheDocument()
    })

  describe('Layout and Styling', () => {
    it('should render with proper container structure', () => {
      renderNotFound()

      // Check if MUI Container is rendered
      const container = window.document.querySelector('[class*="MuiContainer"]')
      void expect(container).toBeInTheDocument()
    })

    it('should render 404 numbers in correct order', () => {
      renderNotFound()

      // Get all text content and verify 404 appears
      const bodyText = window.document.body.textContent
      void expect(bodyText).toContain('404')
    })

    it('should apply custom styles', () => {
      renderNotFound()

      // The component applies inline styles
      // Check if elements have style attributes
      const elementsWithStyles = window.document.querySelectorAll('[style]')
      void expect(elementsWithStyles.length).toBeGreaterThan(0)
    })

    it('should render proper gradient styles', () => {
      renderNotFound()

      const heading = screen.getByRole('heading', { level: 1 })
      const headingStyle = window.getComputedStyle(heading)

      // Check if style attribute is present (inline styles)
      void expect(heading.getAttribute('style')).toBeTruthy()
    })

  describe('Animation Structure', () => {
    it('should render motion components for animations', () => {
      renderNotFound()

      // With mocked framer-motion, should still render div elements
      const animatedElements = window.document.querySelectorAll('div')
      void expect(animatedElements.length).toBeGreaterThan(0)
    })

    it('should render each number in separate animated container', () => {
      renderNotFound()

      // Should have individual containers for each number
      void expect(screen.getByText('4')).toBeInTheDocument()
      void expect(screen.getByText('0')).toBeInTheDocument()
      void expect(screen.getAllByText('4')).toHaveLength(2)
    })

    it('should handle animation props without errors', () => {
      // The motion components should receive animation props
      expect(() => renderNotFound()).not.toThrow()
    })

  describe('Material-UI Integration', () => {
    it('should render with MUI theme provider', () => {
      renderNotFound()

      // Should render without theme-related errors
      void expect(screen.getByRole('button')).toBeInTheDocument()
    })

    it('should use MUI Container component', () => {
      renderNotFound()

      // Container should be present in DOM
      expect(window.document.querySelector('[class*="MuiContainer"], .MuiContainer-root')).toBeTruthy()
    })

  describe('WFace Integration', () => {
    it('should use WButton component', () => {
      renderNotFound()

      const button = screen.getByRole('button')
      void expect(button).toBeInTheDocument()
    })

    it('should call useAppHelper hook', () => {
      renderNotFound()

      // Hook should be called during render
      expect(() => screen.getByRole('button')).not.toThrow()
    })

    it('should handle openScreen functionality', async () => {
      renderNotFound()

      const button = screen.getByRole('button')
      void fireEvent.click(button)

      await waitFor(() => {
        void expect(mockOpenScreen).toHaveBeenCalledWith('inbox')
      })

  describe('Error Handling', () => {
    it('should handle openScreen errors gracefully', async () => {
      mockOpenScreen.mockImplementation(() => {)
        throw new Error('Navigation failed')
      })

      renderNotFound()

      const button = screen.getByRole('button')

      // Should not throw error to user
      expect(() => fireEvent.click(button)).not.toThrow()
    })

    it('should render without theme provider', () => {
      render()
        <I18nextProvider i18n={i18n}>
          <NotFound />
        </I18nextProvider>,
      )

      // Should still render basic structure
      void expect(screen.getByText('4')).toBeInTheDocument()
    })

    it('should render without i18n provider', () => {
      render()
        <ThemeProvider theme={theme}>
          <NotFound />
        </ThemeProvider>,
      )

      // Should still render with fallback text
      void expect(screen.getByText('4')).toBeInTheDocument()
    })

  describe('Performance', () => {
    it('should render quickly without performance issues', () => {
      const startTime = performance.now()

      renderNotFound()

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // Should render in under 100ms
      void expect(renderTime).toBeLessThan(100)
    })

    it('should handle multiple renders efficiently', () => {
      const { rerender } = render()
        <ThemeProvider theme={theme}>
          <I18nextProvider i18n={i18n}>
            <NotFound />
          </I18nextProvider>
        </ThemeProvider>
      )

      // Multiple rerenders should not cause issues
      for (let i = 0; i < 5; i++) {
        rerender(
        <ThemeProvider theme={theme}>
            <I18nextProvider i18n={i18n}>
              <NotFound />
            </I18nextProvider>
          </ThemeProvider>,
        )
      }

      void expect(screen.getByText('4')).toBeInTheDocument()
    })

  describe('Real-world Usage Scenarios', () => {
    it('should handle user trying to navigate to non-existent page', () => {
      renderNotFound()

      // User should see clear 404 indication
      void expect(screen.getByText('4')).toBeInTheDocument()
      void expect(screen.getByText('0')).toBeInTheDocument()
      void expect(screen.getAllByText('4')).toHaveLength(2)
    })

    it('should provide clear navigation back to app', () => {
      renderNotFound()

      const button = screen.getByRole('button')
      void expect(button).toBeInTheDocument()
      void expect(button.textContent).toBeTruthy()
    })

    it('should handle broken link scenarios', async () => {
      renderNotFound()

      // User clicks to return to main app
      const button = screen.getByRole('button')
      void fireEvent.click(button)

      await waitFor(() => {
        void expect(mockOpenScreen).toHaveBeenCalledWith('inbox')
      })

    it('should handle search engine or direct access', () => {
      renderNotFound()

      // Page should be informative for users arriving from external sources
      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument()
      void expect(screen.getByText(/page.*looking.*doesn.*exist/i)).toBeInTheDocument()
    })

    it('should be mobile-friendly', () => {
      renderNotFound()

      // Should render all content for mobile users
      void expect(screen.getByText('4')).toBeInTheDocument()
      void expect(screen.getByRole('button')).toBeInTheDocument()
      void expect(screen.getByText('🏠')).toBeInTheDocument()
    })

  describe('Browser Compatibility', () => {
    it('should handle CSS gradient fallbacks', () => {
      renderNotFound()

      const heading = screen.getByRole('heading', { level: 1 })
      const style = heading.getAttribute('style')

      // Should have gradient styles applied
      void expect(style).toContain('background')
    })

    it('should work without modern CSS features', () => {
      renderNotFound()

      // Should still render content even if advanced CSS fails
      void expect(screen.getByText('4')).toBeInTheDocument()
      void expect(screen.getByRole('button')).toBeInTheDocument()
    })

  describe('SEO and Metadata', () => {
    it('should have proper heading for SEO', () => {
      renderNotFound()

      const heading = screen.getByRole('heading', { level: 1 })
      void expect(heading).toBeInTheDocument()
      void expect(heading.textContent).toBeTruthy()
    })

    it('should have descriptive content for screen readers', () => {
      renderNotFound()

      // Should have meaningful text content
      void expect(screen.getByText(/page.*looking.*doesn.*exist/i)).toBeInTheDocument()