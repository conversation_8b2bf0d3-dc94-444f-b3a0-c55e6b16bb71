import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { WB<PERSON>, WButton, WLink } from 'wface'
import { useGetMonitorings, useEndMonitoring } from '@/hooks/MonitoringHooks/MonitoringHooks'
import { useLocalStorage } from '@/hooks'
import DigiTable from '@/components/Tables/DigiTable/DigiTable'
import { Loading } from '@/components/Loading/Loading'
import Modal from '@/components/Modal/Modal'
import HeaderComponent from '@/components/layout/HeaderComponent/HeaderComponent'

const EndMonitoringScreen: React.FC = () => {
  const { t } = useTranslation(['endMonitoring', 'common'])
  const [loginId] = useLocalStorage<number>('UserId')
  const [endModalOpen, setEndModalOpen] = useState(false)
  const [selectedMonitorings, setSelectedMonitorings] = useState<any[]>([])

  const { data: monitorings, refetch: refetchMonitorings, isLoading } = useGetMonitorings({ loginId }, !!loginId)
  const endMonitoringMutation = useEndMonitoring()

  useEffect(() => {
    if (loginId) {
      refetchMonitorings()
    }
  }, [loginId, refetchMonitorings])

  const handleEndMonitoring = (rowData: any) => {
    setSelectedMonitorings([rowData])
    setEndModalOpen(true)
  }

  const confirmEndMonitoring = async () => {
    try {
      const monitoringsToEnd = selectedMonitorings.map((monitoring) => ({
        monitoringRequestId: monitoring.monitoringRequestId,
        createdBy: monitoring.personelId,
        flowTypeId: monitoring.flowTypeId,
      }))

      await endMonitoringMutation.mutateAsync(monitoringsToEnd)
      setEndModalOpen(false)
      refetchMonitorings()
    } catch (error) {
      console.error('Error ending monitoring:', error)
    }
  }

  const columns = [
    {
      title: 'ID',
      field: 'monitoringRequestId',
      dateSetting: {
        type: 'number',
      },
    },
    { title: t('mainUser'), field: 'nameSurname' },
    { title: t('flowName'), field: 'flowname' },
    { title: t('workflowNo'), field: 'akisNo' },
    { title: t('flowtype'), field: 'flowtype' },
    {
      title: t('endMonitoring'),
      field: 'actions',
      sorting: false,
      filtering: false,
      render: (rowData: any) => (
        <WBox display="flex" gap={1}>
          <WButton onClick={() => handleEndMonitoring(rowData)} variant="contained" color="secondary" size="small">
            {t('endMonitoringText')}
          </WButton>
        </WBox>
      ),
    },
    {
      title: t('monitor'),
      field: 'actions',
      sorting: false,
      filtering: false,
      render: (rowData: any) => (
        <WBox display="flex" gap={1}>
          <WLink
            target="_blank"
            href={`/main/workflow?name=monitoring&wfInstanceId=${rowData.wfWorkflowInstanceId}&loginId=${rowData.personelId}`}
            color="secondary"
          >
            {t('monitor')}
          </WLink>
        </WBox>
      ),
    },
  ]

  if (isLoading) {
    return <Loading height={500} show={true} />
  }

  return (
    <WBox>
      <HeaderComponent />
      <WBox mx={2.5} style={{ backgroundColor: '#fff', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <DigiTable
          data={monitorings || []}
          columns={columns}
          filtering={true}
          search={true}
          grouping={false}
          exportButton
          sorting={true}
          selection={false}
          toolbar={true}
          draggable={false}
          paging={true}
          pageSize={10}
          mobileConfig={{
            titleFields: ['akisNo', 'flowname'],
            subtitleFields: ['nameSurname'],
          }}
          pageSizeOptions={[5, 10, 20, 50]}
          title={t('endMonitoringTitle')}
        />
      </WBox>
      <Modal open={endModalOpen} title={t('endMonitoringConfirmation')} onClose={() => setEndModalOpen(false)} onConfirm={confirmEndMonitoring}>
        <WBox width={320} mb={2}>
          {selectedMonitorings.length > 1
            ? t('bulkEndMonitoringConfirmationText', { count: selectedMonitorings.length })
            : t('endMonitoringConfirmationText')}
        </WBox>
      </Modal>
    </WBox>
  )
}

export default EndMonitoringScreen
