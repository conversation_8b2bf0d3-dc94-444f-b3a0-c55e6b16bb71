import React, { useState, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { WBox, WButton, WTextField, WDatePicker, WGrid } from 'wface'
import { useGetDelegations, useUpdateDelegation, useEndDelegation } from '@/hooks/DelegationHooks/DelegationHooks'
import { useLocalStorage } from '@/hooks'
import dayjs from 'dayjs'
import { IOption } from '@/types'
import { SelectBox } from '@/components/formElements'
import DigiTable from '@/components/Tables/DigiTable/DigiTable'
import { Loading } from '@/components/Loading/Loading'
import Modal from '@/components/Modal/Modal'
import toast from 'react-hot-toast'
import HeaderComponent from '@/components/layout/HeaderComponent/HeaderComponent'

const EndDelegationScreen: React.FC = () => {
  const { t, i18n } = useTranslation(['endDelegation', 'common'])
  const [loginId] = useLocalStorage<number>('UserId')
  const [delegationType, setDelegationType] = useState<string>('active')
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [endModalOpen, setEndModalOpen] = useState(false)
  const [selectedDelegation, setSelectedDelegation] = useState<any>(null)
  const [selectedDelegations, setSelectedDelegations] = useState<any[]>([])

  const { data: delegations, isLoading, refetch: refetchDelegations } = useGetDelegations({ loginId, delegationType }, !!loginId)
  const updateDelegationMutation = useUpdateDelegation()
  const endDelegationMutation = useEndDelegation()

  const delegationTypeOptions: IOption[] = useMemo(
    () => [
      { value: 'active', label: t('activeDelegations'), labelEn: t('activeDelegations') },
      { value: 'past', label: t('pastDelegations'), labelEn: t('pastDelegations') },
    ],
    [t],
  )

  useEffect(() => {
    if (loginId) {
      refetchDelegations()
    }
  }, [delegationType, loginId, refetchDelegations])

  const handleDelegationTypeChange = (option: any) => {
    if (option) {
      setDelegationType(option.value)
    }

  const handleEdit = (rowData: any) => {
    setSelectedDelegation({
      ...rowData,
      delegationStartDate: dayjs(rowData.delegationStartDate),
      delegationEndDate: dayjs(rowData.delegationEndDate),
    })
    setEditModalOpen(true)
  }

  const handleEndDelegation = (rowData: any) => {
    setSelectedDelegations([rowData])
    setEndModalOpen(true)
  }

  const confirmEndDelegation = async () => {
    try {
      const delegationsToEnd = selectedDelegations.map((_delegation) => ({
        delegationId: delegation.delegationOwnerRefId,
        delegationStartDate: dayjs(delegation.delegationStartDate).toDate(),
        delegatedLoginId: delegation.delegateRefId,
        workflowDefId: delegation.workflowDefId,
      }))

      await endDelegationMutation.mutateAsync(delegationsToEnd)
      setEndModalOpen(false)
      refetchDelegations()
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console._error('Error ending delegation:', _error),
      }

  const handleUpdateDelegation = async () => {
    if (selectedDelegation) {
      try {
        await updateDelegationMutation
          .mutateAsync({
            delegationId: selectedDelegation.wfDelegationId,
            startDate: selectedDelegation.delegationStartDate.format('YYYY-MM-DD'),
            endDate: selectedDelegation.delegationEndDate.format('YYYY-MM-DD'),
          })
          .then((result: unknown) => {
            if (result === 'delegation_update_success') {
              toast.success(t(result))
              setEditModalOpen(false)
              refetchDelegations()
            } else {
              toast.error(t(result))
            }
          })
      } catch (_error) {
        if (process.env.NODE_ENV === 'development') {
          console._error('Error updating delegation:', _error),
        }

  const columns = [
    {
      title: 'ID',
      field: 'wfDelegationId',
      dateSetting: {
        type: 'number',
      },
    { title: t('flowName'), field: 'name' },
    { title: t('mainUser'), field: 'ownerNameSurname' },
    { title: t('target'), field: 'nameSurname' },
    {
      title: t('startDate'),
      field: 'delegationStartDate',
      render: (rowData: any) => dayjs(rowData.delegationStartDate).format('DD.MM.YYYY'),
      dateSetting: {
        format: 'DD.MM.YYYY HH:mm',
        locale: i18n.language,
        type: 'date',
      },
    {
      title: t('endDate'),
      field: 'delegationEndDate',
      render: (rowData: any) => dayjs(rowData.delegationEndDate).format('DD.MM.YYYY'),
      dateSetting: {
        format: 'DD.MM.YYYY HH:mm',
        locale: i18n.language,
        type: 'date',
      },
    {
      title: '',
      field: 'actions',
      sorting: false,
      filtering: false,
      render: (rowData: any) => (
        <div>
          <WButton
            onClick={() => handleEdit(rowData)}
            disabled={dayjs(rowData.delegationEndDate).isBefore(dayjs())}
            variant="contained"
            color="primary"
            size="small"
          >
            {t('edit')}
          </WButton>
          <WButton
            onClick={() => handleEndDelegation(rowData)}
            disabled={dayjs(rowData.delegationEndDate).isBefore(dayjs())}
            variant="contained"
            color="secondary"
            size="small"
          >
            {t('endDelegationText')}
          </WButton>
        </div>
      ),
    },
  ]

  const handleBulkEndDelegation = (_: any, data: unknown[]) => {
    setSelectedDelegations(data)
    setEndModalOpen(true)
  }

  if (isLoading) {
    return <Loading height={500} show={true} />
  }

  return (
    <WBox sx={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      <HeaderComponent />
      <WGrid m={2.5}>
        <WBox
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            padding: '24px',
            marginBottom: '24px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            border: '1px solid #e5e7eb',
          }}
        >
          <SelectBox
            label={t('delegationType')}
            value={delegationTypeOptions.find((opt) => opt.value === delegationType) ?? null}
            options={delegationTypeOptions}
            onChange={handleDelegationTypeChange}
            fullWidth={false}
            defaultText={null}
            disabled={isLoading}
            sx={{
              width: { xs: '100%', sm: '300px' },
              '& .MuiOutlinedInput-root': {
                backgroundColor: '#f9fafb',
              },
            }}
          />
        </WBox>
        <WBox
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            border: '1px solid #e5e7eb',
            overflow: 'hidden',
          }}
        >
          <DigiTable
            data={delegations ?? []}
            columns={columns}
            filtering={true}
            search={false}
            grouping={false}
            exportButton
            sorting={true}
            selection={true}
            toolbar={true}
            draggable={false}
            paging={true}
            mobileConfig={{
              titleFields: ['wfDelegationId', 'name'],
              rightFields: ['delegationStartDate', 'delegationEndDate'],
              subtitleFields: ['ownerNameSurname', 'nameSurname'],
            }}
            pageSize={10}
            actions={[
              {
                icon: () => (
                  <WButton variant="contained" color="primary">
                    {t('bulkEndDelegation')}
                  </WButton>
                ),
                tooltip: t('bulkEndDelegationTooltip'),
                isFreeAction: false,
                onClick: handleBulkEndDelegation,
              },
            ]}
            pageSizeOptions={[5, 10, 20, 50]}
            title={t('endDelegationTitle')}
          />
        </WBox>
      </WGrid>
      <Modal open={editModalOpen} title={t('editDelegationDate')} onClose={() => setEditModalOpen(false)} onConfirm={handleUpdateDelegation}>
        <WBox display="flex" flexDirection="column" gap={2} width={320}>
          <WTextField label={t('workflow')} value={selectedDelegation?.name} disabled fullWidth />
          <WDatePicker
            label={t('startDate')}
            value={selectedDelegation?.delegationStartDate}
            onChange={(date: any) => setSelectedDelegation({ ...selectedDelegation, delegationStartDate: dayjs(date) })}
            fullWidth
          />
          <WDatePicker
            label={t('endDate')}
            value={selectedDelegation?.delegationEndDate}
            onChange={(date: any) => setSelectedDelegation({ ...selectedDelegation, delegationEndDate: dayjs(date) })}
            fullWidth
          />
        </WBox>
      </Modal>
      <Modal open={endModalOpen} title={t('endDelegationConfirmation')} onClose={() => setEndModalOpen(false)} onConfirm={confirmEndDelegation}>
        <WBox width={320} mb={2}>
          {selectedDelegations.length > 1
            ? t('bulkEndDelegationConfirmationText', { count: selectedDelegations.length }),
            : t('endDelegationConfirmationText')}
        </WBox>
      </Modal>
    </WBox>
  )
}

export default EndDelegationScreen
          </WTextField>
        </WBox>
      </Modal>
      </HeaderComponent>
    </WBox>
    </Loading>
  </any>
  </string>
  </number>
