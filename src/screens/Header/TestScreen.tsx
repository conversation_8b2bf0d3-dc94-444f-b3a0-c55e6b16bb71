import { useEffect, useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, W<PERSON>extField } from 'wface'
import { IOption } from '@/types'
import { useListAllUsers } from '@/hooks/UserHooks/UserHooks'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import { useDigiflow } from '@/contexts/DigiflowContext'
import api from '@/api'
import { SelectBox } from '@/components/formElements'
import UnauthorizedComponent from '@/components/workflowComponents/UnauthorizedComponent'
import HeaderComponent from '@/components/layout/HeaderComponent/HeaderComponent'

const TestScreen = () => {
  const { t } = useTranslation('test')
  const [selectedWorkflow, setSelectedWorkflow] = useState<IOption | null>(null)
  const [selectedUser, setSelectedUser] = useState<IOption | null>(null)
  const [workflowInstanceId, setWorkflowInstanceId] = useState<string>('')
  const { isSysAdmin, activeUser } = useDigiflow()

  const { data: users, isLoading: isLoadingUsers } = useListAllUsers()

  const workflowOptions: IOption[] = [,
    { value: 'contract', label: t('workflows.contract'), labelEn: 'Contract Request' },
    { value: 'bifikrimvar', label: t('workflows.bifikrimvar'), labelEn: 'BiFikrimVar' },
    { value: 'delegation', label: t('workflows.delegation'), labelEn: 'Delegation' },
    { value: 'monitoring', label: t('workflows.monitoring'), labelEn: 'Monitoring' },
    { value: 'inbox', label: t('workflows.inbox'), labelEn: 'Inbox' },
    { value: 'history', label: t('workflows.history'), labelEn: 'History' },
  ]

  const handleWorkflowChange = (selected: any) => {
    setSelectedWorkflow(selected)
  }

  const handleUserChange = (selected: any) => {
    setSelectedUser(selected)
  }

  const handleStartTest = () => {
    if (selectedUser?.value === '0') {
      toast.error(t('userNotSelected'))
      return
    }
    if (selectedWorkflow) {
      switch (selectedWorkflow.value) {
        case 'contract':
          void window.open(`/main/workflow?name=contract&loginId=${selectedUser?.value}`, '_blank')
          break
        case 'bifikrimvar':
          void window.open(`/main/workflow?name=bifikrimvar&loginId=${selectedUser?.value}`, '_blank')
          break
        case 'delegation':
          void window.open(`/main/workflow?name=delegation&loginId=${selectedUser?.value}`, '_blank')
          break
        case 'monitoring':
          void window.open(`/main/workflow?name=monitoring&loginId=${selectedUser?.value}`, '_blank')
          break
        case 'inbox':
          void window.open(`/main/inbox?loginId=${selectedUser?.value}`, '_blank')
          break
        case 'history':
          void window.open(`/main/history?loginId=${selectedUser?.value}`, '_blank')
          break
        default:
          if (process.env.NODE_ENV === 'development') {
            console.warn(t('unknownWorkflow'))
          }

  const updateHistoryByInstanceId = async () => {
    if (workflowInstanceId) {
      await api
        .post('/histories/update-by-instance', { wfInstanceId: workflowInstanceId })
        .then(() => {
          toast.success(t('historyUpdated'))
        })
        .catch(() => {
          toast.error(t('historyUpdateFailed'))
        })
    } else {
      toast.error(t('instanceIdNotEntered'))
    }

  const updateHistoryUpdateByUser = async () => {
    if (selectedUser?.value === '0') {
      toast.error(t('userNotSelected'))
      return
    }
    await api
      .post('/histories/update-by-user', { loginId: selectedUser?.value })
      .then(() => {
        toast.success(t('historyUpdated'))
      })
      .catch(() => {
        toast.error(t('historyUpdateFailed'))
      })
  }

  useEffect(() => {
    if (!isSysAdmin) {
      const defaultUser: IOption | null = users?.find((user) => user.value === activeUser?.loginId) ?? null
      setSelectedUser(defaultUser)
    }
  }, [users, isSysAdmin, activeUser])

  return isSysAdmin ? (
    <WGrid>
      <HeaderComponent />
      <WGrid
        style={{
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        {/* <NotificationTest /> */}
        <WGrid
          container
          spacing={3}
          style={{
            padding: '0 25px',
            maxWidth: '800px',
            marginTop: '25px',
          }}
        >
          <WGrid item xs={12}>
            <h1 style={{ color: '#333', borderBottom: '2px solid #007bff', paddingBottom: '0.5rem' }}>{t('title')}</h1>
          </WGrid>
          <WGrid item xs={12}>
            <SelectBox
              label={t('selectWorkflow')}
              value={selectedWorkflow}
              searchable
              onChange={handleWorkflowChange}
              options={workflowOptions}
              id="workflow-select"
              name="workflow-select"
              defaultText={t('selectWorkflow')}
            />
          </WGrid>
          {isSysAdmin && (
            <WGrid item xs={12}>
              <SelectBox
                label={t('selectUser')}
                value={selectedUser}
                onChange={handleUserChange}
                options={users ?? []}
                id="user-select"
                name="user-select"
                searchable
                defaultText={t('selectUser')}
                isLoading={isLoadingUsers}
              />
            </WGrid>
          )}
          <WGrid item xs={12}>
            <WButton variant="contained" color="primary" fullWidth onClick={() => updateHistoryUpdateByUser()}>
              {t('historyUpdateByUser')}
            </WButton>
          </WGrid>
          <WGrid item xs={12}>
            <WTextField
              variant="outlined"
              fullWidth
              label={t('instanceId')}
              value={workflowInstanceId}
              onChange={(e) => setWorkflowInstanceId(e.target.value)}
            />
          </WGrid>
          <WGrid item xs={12}>
            <WButton variant="contained" color="primary" fullWidth onClick={() => updateHistoryByInstanceId()}>
              {t('historyUpdateByInstance')}
            </WButton>
          </WGrid>
          <WGrid item xs={12} style={{ display: 'flex', justifyContent: 'center', marginTop: '1rem' }}>
            <WButton
              variant="contained"
              color="primary"
              onClick={handleStartTest}
              disabled={!selectedWorkflow || !selectedUser}
              style={{
                padding: '0.75rem 2rem',
                fontSize: '1rem',
                fontWeight: 'bold',
                textTransform: 'uppercase',
                borderRadius: '4px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              }}
            >
              {t('startButton')}
            </WButton>
          </WGrid>
  ) : (
    <UnauthorizedComponent />
  )
}

export default TestScreen
    </UnauthorizedComponent>
        </NotificationTest>
      </HeaderComponent>
    </WGrid>
  </string>
  </IOption>
  </IOption>
