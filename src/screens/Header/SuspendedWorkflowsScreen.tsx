import React, { useState, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { WBox, WLink } from 'wface'
import { useLocalStorage } from '@/hooks'
import DigiTable from '@/components/Tables/DigiTable/DigiTable'
import { Loading } from '@/components/Loading/Loading'
import api from '@/api'
import i18n from '@/i18n'
import HeaderComponent from '@/components/layout/HeaderComponent/HeaderComponent'

const SuspendedWorkflowsScreen: React.FC = () => {
  const { t } = useTranslation('suspendedTasks')
  const [loginId] = useLocalStorage<number>('UserId')
  const [isLoading, setIsLoading] = useState(false)
  const [suspendedTasks, setSuspendedTasks] = useState<any[]>([])

  const fetchSuspendedTasks = async () => {
    setIsLoading(true)
    try {
      const suspendedList = await api.get('/histories/suspended-workflows')

      const combinedTasks = suspendedList.data.map((task: any) => ({
        wfInsId: task.wfInsId,
        name: task.name,
        wfInstanceLink: `${import.meta.env.VITE_APP_OLD_URL}/${task.wfInstanceLink}}`,
        wfOwner: task.wfOwner,
        wfLastModifiedBy: task.wfLastModifiedBy,
        wfDate: task.wfDate,
      }))

      setSuspendedTasks(combinedTasks)
    } catch (error) {
      console.error('Error fetching suspended tasks:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (loginId) {
      fetchSuspendedTasks()
    }
  }, [loginId])

  // Function to handle workflow link clicks
  const handleWorkflowOpen = (rowData: any, fieldName: keyof any) => {
    return (
      <WLink href={rowData.wfInstanceLink} target="_blank" underline="none" color="black">
        {fieldName === 'wfLastModifiedBy' ? <span>{String(rowData[fieldName])}</span> : String(rowData[fieldName])}
      </WLink>
    )
  }

  const columns: any[] = useMemo(
    () => [
      {
        title: 'ID',
        field: 'wfInsId',
        render: (rowData: any) => handleWorkflowOpen(rowData, 'wfInsId'),
        width: 100,
        dateSetting: {
          type: 'number',
        },
      },
      {
        title: t('main_grid_flow_name'),
        field: 'name',
        render: (rowData: any) => handleWorkflowOpen(rowData, 'name'),
        width: 250,
      },
      {
        title: t('main_grid_flow_owner'),
        field: 'wfOwner',
        render: (rowData: any) => handleWorkflowOpen(rowData, 'wfOwner'),
      },
      {
        title: t('main_grid_last_modified'),
        field: 'wfLastModifiedBy',
        render: (rowData: any) => handleWorkflowOpen(rowData, 'wfLastModifiedBy'),
      },
      {
        title: t('main_grid_flow_tarih'),
        field: 'wfDate',
        render: (rowData: any) => handleWorkflowOpen(rowData, 'wfDate'),
        dateSetting: {
          format: 'DD.MM.YYYY HH:mm',
          locale: i18n.language,
          type: 'date',
        },
        width: '15%',
      },
    ],
    [t, i18n.language],
  )

  if (isLoading) {
    return <Loading height={500} show={true} />
  }

  return (
    <WBox sx={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      <HeaderComponent />
      <WBox m={2.5} className="digi-history-container">
        <WBox
          className="digi-history-section"
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            border: '1px solid #e5e7eb',
            overflow: 'hidden',
          }}
        >
          <WBox sx={{ padding: '24px 24px 0 24px' }}>
            <div className="digi-section-header">
              <h3
                className="digi-section-title"
                style={{
                  fontSize: '20px',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '8px',
                }}
              >
                {t('suspended_tasks')}
              </h3>
              <div
                className="digi-section-description"
                style={{
                  fontSize: '14px',
                  color: '#6b7280',
                }}
              >
                {t('suspended_workflows_description', 'All workflows that have been suspended and require attention')}
              </div>
            </div>
          </WBox>
          <DigiTable
            languageFile={t}
            data={suspendedTasks}
            columns={columns}
            filtering={true}
            search={false}
            grouping={true}
            exportButton={true}
            sorting={true}
            selection={false}
            toolbar={true}
            draggable={false}
            paging={true}
            pageSize={10}
            pageSizeOptions={[5, 10, 20, 50]}
            options={{
              grouping: true,
              filtering: true,
              sorting: true,
              headerStyle: {
                backgroundColor: '#f5f5f5',
                fontWeight: 'bold',
              },
              rowStyle: (rowData) => ({
                backgroundColor: rowData.tableData.id % 2 === 0 ? '#ffffff' : '#f5f5f5',
              }),
            }}
          />
        </WBox>
      </WBox>
    </WBox>
  )
}

export default SuspendedWorkflowsScreen
