import React, { useEffect } from 'react'
import { IInbox } from '@/types'
import { useTranslation } from 'react-i18next'
import { WBox, WLink } from 'wface'
import { useLocalStorage } from '@/hooks'
import { useGetInbox } from '@/hooks/InboxHooks/InboxHooks'
import { Loading } from '@/components/Loading/Loading'
import DigiTable from '@/components/Tables/DigiTable/DigiTable'
import dayjs from 'dayjs'
import HeaderComponent from '@/components/layout/HeaderComponent/HeaderComponent'

const InboxScreen: React.FC = () => {
  const { t, i18n } = useTranslation('inbox')
  const [storedValue] = useLocalStorage<number>('UserId')

  const { data, isLoading, isFetching, refetch: refetchInboxList } = useGetInbox(!!storedValue)

  // Initial data fetch when userId is available
  useEffect(() => {
    if (storedValue) {
      refetchInboxList()
    }
  }, [storedValue, refetchInboxList])

  const handleWorkflowOpen = (rowData: IInbox, field: keyof IInbox) => {
    const content =
      field === 'wfDate' ? (
        dayjs(rowData[field]).format('DD.MM.YYYY HH:mm:ss')
      ) : field === 'wfLastModifiedBy' || field === 'wfOwner' || field === 'detail' ? (
        <span>{rowData[field]}</span>
      ) : field === 'flowName' || field === 'flowDesc' ? (
        i18n.language === 'tr' ? (
          rowData.flowName
        ) : (
          rowData.flowDesc
        )
      ) : field === 'stateName' || field === 'stateDesc' ? (
        i18n.language === 'tr' ? (
          rowData.stateName
        ) : (
          rowData.stateDesc
        )
      ) : field === 'bolum' || field === 'bolumEn' ? (
        i18n.language === 'tr' ? (
          rowData.bolum
        ) : (
          rowData.bolumEn
        )
      ) : (
        rowData[field]
      )

    const href = rowData.route
      ? `/main/workflow?${new URLSearchParams({
          name: encodeURIComponent(rowData.route),
          loginId: encodeURIComponent(storedValue.toString()),
        })}`
      : `${import.meta.env.VITE_APP_OLD_URL}/${encodeURIComponent(rowData.wfInstanceLink || '')}`

    return (
      <WLink href={href} target="_blank" underline="none" color="black">
        {content as string}
      </WLink>
    )
  }

  const assignedInboxColumns = [
    {
      title: t('id'),
      field: 'wfInsId',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'wfInsId'),
    },
    {
      title: t('workflowName'),
      field: i18n.language === 'tr' ? 'flowName' : 'flowDesc',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, i18n.language === 'tr' ? 'flowName' : 'flowDesc'),
    },
    {
      title: t('owner'),
      field: 'wfOwner',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'wfOwner'),
    },
    {
      title: t('description'),
      field: 'detail',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'detail'),
    },
    {
      title: t('amount'),
      field: 'amount',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'amount'),
    },
    {
      title: t('currency'),
      field: 'currency',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'currency'),
    },
    {
      title: t('state'),
      field: i18n.language === 'tr' ? 'stateName' : 'stateDesc',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, i18n.language === 'tr' ? 'stateName' : 'stateDesc'),
    },
    {
      title: t('forwarder'),
      field: 'wfLastModifiedBy',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'wfLastModifiedBy'),
    },
    {
      title: t('date'),
      field: 'wfDate',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'wfDate'),
      dateSetting: {
        format: 'DD.MM.YYYY HH:mm',
        locale: i18n.language,
        type: 'date',
      },
    },
    {
      title: t('department'),
      field: i18n.language === 'tr' ? 'bolum' : 'bolumEn',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, i18n.language === 'tr' ? 'bolum' : 'bolumEn'),
    },
  ]

  const delegatedInboxColumns = [
    {
      title: t('id'),
      field: 'wfInsId',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'wfInsId'),
    },
    {
      title: t('workflowName'),
      field: 'flowName',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, i18n.language === 'tr' ? 'flowName' : 'flowDesc'),
    },
    {
      title: t('owner'),
      field: 'wfOwner',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'wfOwner'),
    },
    {
      title: t('state'),
      field: 'stateName',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'stateName'),
    },
    {
      title: t('forwarder'),
      field: 'wfLastModifiedBy',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'wfLastModifiedBy'),
    },
    {
      title: t('date'),
      field: 'wfDate',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'wfDate'),
      dateSetting: {
        format: 'DD.MM.YYYY HH:mm',
        locale: i18n.language,
        type: 'date',
      },
    },
    {
      title: t('amount'),
      field: 'amount',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'amount'),
    },
    {
      title: t('currency'),
      field: 'currency',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'currency'),
    },
    {
      title: t('department'),
      field: 'bolum',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, i18n.language === 'tr' ? 'bolum' : 'bolumEn'),
    },
  ]

  const commentedInboxColumns = [
    {
      title: t('id'),
      field: 'wfInsId',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'wfInsId'),
    },
    {
      title: t('workflowName'),
      field: 'flowName',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, i18n.language === 'tr' ? 'flowName' : 'flowDesc'),
    },
    {
      title: t('owner'),
      field: 'wfOwner',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'wfOwner'),
    },
    {
      title: t('state'),
      field: 'stateName',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'stateName'),
    },
    {
      title: t('forwarder'),
      field: 'wfLastModifiedBy',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'wfLastModifiedBy'),
    },
    {
      title: t('date'),
      field: 'wfDate',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, 'wfDate'),
      dateSetting: {
        format: 'DD.MM.YYYY HH:mm',
        locale: i18n.language,
        type: 'date',
      },
    },
    {
      title: t('department'),
      field: 'bolum',
      render: (rowData: IInbox) => handleWorkflowOpen(rowData, i18n.language === 'tr' ? 'bolum' : 'bolumEn'),
    },
  ]

  // Show loading state only during initial load, not during refetch
  if (isLoading && !isFetching) {
    return <Loading show={true} fullScreen />
  }

  return (
    <WBox sx={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      <HeaderComponent />
      <WBox mx={3} my={2.5} className="digi-inbox-container">
        <WBox
          className="digi-inbox-section"
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            marginBottom: '24px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            border: '1px solid #e5e7eb',
            overflow: 'hidden',
          }}
        >
          <WBox sx={{ padding: '12px 12px 0 12px' }}>
            <div className="digi-section-header">
              <h3
                className="digi-section-title"
                style={{
                  fontSize: '20px',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '8px',
                }}
              >
                {t('my_inbox')}
              </h3>
              <div
                className="digi-section-description"
                style={{
                  fontSize: '14px',
                  color: '#6b7280',
                }}
              >
                {t('assigned_workflows_description', 'Workflows assigned to you')}
              </div>
            </div>
          </WBox>
          <DigiTable
            data={data?.inbox ?? []}
            paging
            toolbar
            filtering
            sorting
            search
            columns={assignedInboxColumns}
            loading={isFetching}
            mobileConfig={{
              titleFields: ['wfInsId', i18n.language === 'tr' ? 'flowName' : 'flowDesc'],
              subtitleFields: ['wfOwner'],
              rightFields: ['wfDate'],
            }}
          />
        </WBox>

        <WBox
          className="digi-inbox-section"
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            marginBottom: '24px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            border: '1px solid #e5e7eb',
            overflow: 'hidden',
          }}
        >
          <WBox sx={{ padding: '24px 24px 0 24px' }}>
            <div className="digi-section-header">
              <h3
                className="digi-section-title"
                style={{
                  fontSize: '20px',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '8px',
                }}
              >
                {t('delegate_inbox')}
              </h3>
              <div
                className="digi-section-description"
                style={{
                  fontSize: '14px',
                  color: '#6b7280',
                }}
              >
                {t('delegated_workflows_description', 'Workflows delegated to you')}
              </div>
            </div>
          </WBox>
          <DigiTable filtering search data={data?.delegated ?? []} paging toolbar columns={delegatedInboxColumns} loading={isFetching} />
        </WBox>

        <WBox
          className="digi-inbox-section"
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            border: '1px solid #e5e7eb',
            overflow: 'hidden',
          }}
        >
          <WBox sx={{ padding: '24px 24px 0 24px' }}>
            <div className="digi-section-header">
              <h3
                className="digi-section-title"
                style={{
                  fontSize: '20px',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '8px',
                }}
              >
                {t('comment_inbox')}
              </h3>
              <div
                className="digi-section-description"
                style={{
                  fontSize: '14px',
                  color: '#6b7280',
                }}
              >
                {t('commented_workflows_description', 'Workflows with your comments')}
              </div>
            </div>
          </WBox>
          <DigiTable filtering sorting search data={data?.commented ?? []} paging toolbar columns={commentedInboxColumns} loading={isFetching} />
        </WBox>
      </WBox>
    </WBox>
  )
}

export default InboxScreen
