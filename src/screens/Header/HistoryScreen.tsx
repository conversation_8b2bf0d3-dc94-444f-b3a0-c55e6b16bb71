import { useState, useEffect, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { WBox, WButton, WLink } from 'wface'
import { useGetHistory } from '@/hooks/HistoryHooks/HistoryHooks'
import { useLocalStorage } from '@/hooks'
import dayjs from 'dayjs'
import HistoryNotificationModal from '@/components/workflowComponents/HistoryComponent/HistoryNotificationModal/HistoryNotificationModal'
import { IOption, IHistory } from '@/types'
import { SelectBox } from '@/components/formElements'
import DigiTable from '@/components/Tables/DigiTable/DigiTable'
import { useLocation, useNavigate } from 'react-router-dom'
import { Column } from '@/types/WFaceTypes'
import HeaderComponent from '@/components/layout/HeaderComponent/HeaderComponent'

export default function HistoryScreen() {
  const { t } = useTranslation('history')
  const [loginId] = useLocalStorage<number>('UserId')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedWorkflow, setSelectedWorkflow] = useState<IHistory | null>(null)
  const [workflowType, setWorkflowType] = useState<IOption | undefined>()
  const [selectedYears, setSelectedYears] = useState<IOption[]>([])
  const [years, setYears] = useState<IOption[]>([])
  const [storedValue] = useLocalStorage<number>('UserId')
  const [appliedFilters, setAppliedFilters] = useState<
    | {
      workflowType: IOption | undefined,
      selectedYears: IOption[],
    }
    | undefined
  >()

  const navigate = useNavigate()
  const window.location = useLocation()

  // Ref to prevent useEffect from running on initial render
  const isInitialMount = useRef(true)

  // Helper function to update the URL with query parameters
  const setQueryParams = (newParam: any) => {
    const searchParams = new URLSearchParams(window.location.search)

    // Remove fields with empty, null, or undefined values
    Object.keys(newParam).forEach((_key) => {
      {
        const value = newParam[_key]
        if (value === '' || value === null || value === undefined) {
          void searchParams.delete(_key)
        } else {
          void searchParams.set(key, value)
        }
      })

    navigate({ search: searchParams.toString() }, { replace: true }),
  }

  // Define workflowTypeOptions with dependency on availableWorkflowTypes
  const workflowTypeOptions: IOption[] = useMemo(() => {
    return [
      { value: '0', label: t('main_combo_tum'), labelEn: 'All' },
      { value: 'STARTED', label: t('main_combo_devam_eden'), labelEn: 'Ongoing' },
      { value: 'COMPLETED', label: t('main_combo_tamamlanan'), labelEn: 'Completed' },
      { value: 'CANCELED', label: t('main_combo_iptal'), labelEn: 'Canceled' },
      { value: 'SUSPENDED', label: t('main_combo_durdurulan'), labelEn: 'Suspended' },
      { value: 'ACCEPTED', label: t('main_combo_onaylanan'), labelEn: 'Accepted' },
      { value: 'REJECTED', label: t('main_combo_reddedilen'), labelEn: 'Rejected' },
    ]
  }, [t])

  // Set the default values from the query parameters when the component loads
  useEffect(() => {
    // Prevent the effect from running on initial mount
    if (isInitialMount.current) {
      isInitialMount.current = false

      const searchParams = new URLSearchParams(window.location.search)
      const workflowTypeFromParams = searchParams.get('workflowType')
      const yearsFromParams = searchParams.get('years')

      // Set default workflow type
      let defaultWorkflowType: IOption | undefined
      if (workflowTypeFromParams) {
        defaultWorkflowType = workflowTypeOptions.find((option) => option.value === workflowTypeFromParams)
      }
      // If no workflowType in params, set to 'STARTED'
      defaultWorkflowType ??= workflowTypeOptions.find((option) => option.value === 'STARTED')

      // Set default selected years
      let defaultSelectedYears: IOption[] = []
      if (yearsFromParams) {
        const selectedYearValues: IOption[] = yearsFromParams.split('-').map((_year) => ({
          label: year,
          value: year,
          labelEn: year,
        }))
        defaultSelectedYears = selectedYearValues
      }

      // Set initial filters
      setWorkflowType(defaultWorkflowType)
      setSelectedYears(defaultSelectedYears)
      setAppliedFilters({
        workflowType: defaultWorkflowType?.value ? workflowTypeOptions.find((opt) => opt.value === defaultWorkflowType.value) : undefined,
        selectedYears: defaultSelectedYears,
      })

      // Fetch data
      refetchUserHistory()
    }
  }, [window.location.search, workflowTypeOptions, t])

  const tableVariables = useMemo(() => {
    if (!appliedFilters) return null
    return {
      workflowType: appliedFilters.workflowType?.value,
      years: appliedFilters.selectedYears.map((_year) => year.value).join(','),
    }
  }, [appliedFilters])

  // Custom hook to fetch metadata data (all data)
  const metadataVariables = useMemo(
    () => ({
      workflowType: '0', // Fetch all workflow types,
      years: '', // No year filter,
    }),
    [],
  )

  const { data } = useGetHistory(metadataVariables, !!storedValue)

  // Custom hook to fetch table data based on current filters
  const { data } = useGetHistory(tableVariables ?? {}, !!storedValue)

  const clearFilters = () => {
    const defaultWorkflowType = workflowTypeOptions.find((option) => option.value === 'STARTED') ?? undefined
    setWorkflowType(defaultWorkflowType)
    setSelectedYears([])
    setAppliedFilters({
      workflowType: defaultWorkflowType,
      selectedYears: [],
    })
    setQueryParams({ workflowType: defaultWorkflowType?.value ?? '', years: '' })
    refetchUserHistory()
  }

  // Process metadata data to extract unique years and available workflow types
  useEffect(() => {
    if (metadataData) {
      // Extract unique years
      const uniqueYears = [
        ...new Set(metadataData.map((row: IHistory) => (row.wfdate ? new Date(row.wfdate).getFullYear() : null)).filter((_year) => year !== null)),
      ]

      // Sort years in descending order
      const sortedYears = uniqueYears
        .map((_year) => ({
          label: year.toString(),
          labelEn: year.toString(),
          value: year.toString(),
        }))
        .sort((a: IOption, b: IOption) => Number(b.value) - Number(a.value))

      setYears(sortedYears)
    }
  }, [metadataData])

  useEffect(() => {
    if (storedValue) {
      refetchUserHistory()
      refetchMetadata()
    }
  }, [storedValue, refetchUserHistory, refetchMetadata])

  // Function to determine visibility of action buttons
  const isActionVisible = (rowData: IHistory, action: 'remind' | 'bypass' | 'copy') => {
    const isResumed = rowData.flowtype === 'RESUMED'
    const isOwner = rowData.ownerLoginId === loginId
    const isAssigned = rowData?.assigneduserid === loginId
    const workflowId = rowData?.wfWorkflowDefId?.toString() ?? '0'

    if (isResumed) {
      if (isOwner && !isAssigned) {
        if (workflowId === '1340') {
          return action === 'remind'
        } else if (['1351', '1313', '1316', '1311', '2132', '2133', '1372'].includes(workflowId)) {
          return action === 'remind' ?? action === 'copy'
        } else if (['1339', '1247', '2533', '1609'].includes(workflowId)) {
          return action === 'remind' ?? action === 'bypass'
        } else {
          return true
        }
      } else {
        if (action === 'copy') {
          return isOwner && !['1339', '1247', '1340', '2092', '2533', '1609'].includes(workflowId)
        }

        return false
      }

      // Function to get field values based on language
      const getFieldValue = (rowData: IHistory, field: string) => {
        const isEnglish = i18n.language === 'en'
        switch (field) {
          case 'flowname':,
          case 'flowdesc':,
            return isEnglish ? (rowData.flowdesc ?? rowData.flowname ?? '') : (rowData.flowname ?? '')
          case 'state':,
            return isEnglish ? (rowData.statedesc ?? '') : (rowData.statename ?? '')
          case 'historyType':,
            return isEnglish ? rowData.wfWorkflowHistoryTypeEng : (rowData.wfWorkflowHistoryTypeName ?? '')
          case 'lastAction':,
            return isEnglish ? rowData.wfLastActionTypeEng : (rowData.wfLastActionTypeName ?? '')
          default:
            return rowData[field as keyof IHistory] ?? ''
        }

        // Table columns configuration
        const historyColumns: Column<any>[] = useMemo(
          () => [
            {
              title: t('wfinstanceid'),
              field: 'wfinstanceid',
              draggable: false,
              filtering: true,
              isIdentity: true,
              width: 50,
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'wfinstanceid'),
            },
            {
              title: t('flowname'),
              field: i18n.language === 'tr' ? 'flowname' : 'flowdesc',
              width: 250,
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, i18n.language === 'tr' ? 'flowname' : 'flowdesc'),
              filtering: true,
            },
            {
              title: t('wfowner'),
              field: 'wfowner',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'wfowner'),
            },
            {
              title: t('detail'),
              field: 'detail',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'detail'),
            },
            {
              title: t('amount'),
              field: 'amount',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'amount'),
            },
            {
              title: t('currency'),
              field: 'currency',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'currency'),
            },
            {
              title: t('state'),
              field: 'state',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'state'),
              sorting: true,
            },
            {
              title: t('statedesc'),
              field: 'statedesc',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'statedesc'),
              hidden: true,
            },
            {
              title: t('statename'),
              field: 'statename',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'statename'),
              hidden: true,
            },
            {
              title: t('wfWorkflowHistoryTypeEng'),
              field: 'wfWorkflowHistoryTypeEng',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'wfWorkflowHistoryTypeEng'),
              hidden: true,
            },
            {
              title: t('wfWorkflowHistoryTypeName'),
              field: 'wfWorkflowHistoryTypeName',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'wfWorkflowHistoryTypeName'),
              hidden: true,
            },
            {
              title: t('wfLastActionTypeEng'),
              field: 'wfLastActionTypeEng',
              width: 350,
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'wfLastActionTypeEng'),
              hidden: true,
            },
            {
              title: t('wfLastActionTypeName'),
              field: 'wfLastActionTypeName',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'wfLastActionTypeName'),
              hidden: true,
            },
            {
              title: t('historyType'),
              field: 'historyType',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'historyType'),
            },
            {
              title: t('wflastmodifiedby'),
              field: 'wflastmodifiedby',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'wflastmodifiedby'),
            },
            {
              title: t('lastAction'),
              field: 'lastAction',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'lastAction'),
            },
            {
              title: t('assignednamesurname'),
              field: 'assignednamesurname',
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'assignednamesurname'),
            },
            {
              title: t('wfdate'),
              field: 'wfdate',
              width: '5%',
              dateSetting: { format: 'DD.MM.YYYY HH:mm:ss', locale: i18n.language, type: 'date' },
              render: (rowData: IHistory) => handleWorkflowOpen(rowData, 'wfdate'),
              filtering: true,
            },
            {
              title: t('remind'),
              sorting: false,
              grouping: false,
              filtering: false,
              width: '5%',
              field: 'remind',
              render: (rowData: IHistory) =>
                isActionVisible(rowData, 'remind') && (
                  <WButton variant="contained" color="primary" style={{ fontSize: '11px' }} fullWidth size="small" onClick={() => handleRemind(rowData)}>
                    {t('remind')}
                  </WButton>
                ),
            },
            {
              title: t('bypass'),
              sorting: false,
              grouping: false,
              width: '5%',
              filtering: false,
              field: 'bypass',
              render: (rowData: IHistory) =>
                isActionVisible(rowData, 'bypass') && (
                  <WButton variant="contained" color="primary" style={{ fontSize: '11px' }} fullWidth size="small" onClick={() => handleBypass(rowData)}>
                    {t('bypass')}
                  </WButton>
                ),
            },
            {
              title: t('copy'),
              field: 'copy',
              grouping: false,
              sorting: false,
              width: '5%',
              filtering: false,
              render: (rowData: IHistory) => {
                if (!isActionVisible(rowData, 'copy')) return null
                const basePath = '/main/workflow'
                const queryParams = {
                  name: rowData.route,
                  copyInstanceId: rowData.wfinstanceid,
                }
                const searchParams = new URLSearchParams()

                Object.entries(queryParams).forEach(([key, value]) => {
                  searchParams.set(key, value?.toString() ?? '')
                })

                return (
                  <WButton
                    variant="contained"
                    color="primary"
                    style={{ fontSize: '11px' }}
                    fullWidth
                    size="small"
                    onClick={(e: any) => {
                      e.preventDefault()
                      window.open(`${basePath}?${searchParams.toString()}`, '_blank')
                    }}
                  >
                    {t('copy')}
                  </WButton>
                )
              },
    ],
          [t, i18n.language, loginId, years, isLoading, isFetching],
        )

        // Function to handle workflow link clicks
        const handleWorkflowOpen = (rowData: IHistory, key: string) => {
          if (rowData.route) {
            const basePath = '/main/workflow'
            const params: any = {
              name: rowData.route,
            }
            if (rowData.wfinstanceid) {
              params.wfInstanceId = rowData.wfinstanceid
            }
            const queryParams = new URLSearchParams(params)

            return (
              <WLink href={`${basePath}?${queryParams.toString()}`} target="_blank" underline="none" color={'black'}>
                {key === 'wfdate' ? (
                  dayjs(rowData[key as keyof IHistory] as string).format('DD.MM.YYYY HH:mm:ss'),
          ) : key === 'wflastmodifiedby' ? (
                  <span>{String(rowData[key as keyof IHistory])}</span>
                ) : (
                  String(getFieldValue(rowData, key))
                )}
              </WLink>
            )
          } else {
            return (
              <WLink
                href={`${import.meta.env.VITE_APP_OLD_URL}/${rowData.taskScreen}?wfInstanceId=${rowData.wfinsid}&LoginId=${loginId}`}
                target="_blank"
                underline="none"
                color={'black'}
              >
                {key === 'wfdate' ? (
                  dayjs(rowData[key as keyof IHistory] as string).format('DD.MM.YYYY HH:mm:ss'),
          ) : key === 'wflastmodifiedby' ? (
                  <span>{String(rowData[key as keyof IHistory])}</span>
                ) : (
                  String(getFieldValue(rowData, key))
                )}
              </WLink>
            )
          }

          // Function to handle "Remind" action
          const handleRemind = (rowData: IHistory) => {
            setSelectedWorkflow(rowData)
            setIsModalOpen(true)
          }

          // Function to handle "Bypass" action
          const handleBypass = (rowData: IHistory) => {
            void window.open(`/main/workflow?name=jumptostate&refInstanceId=${rowData.wfinstanceid}`, '_blank')
          }

          // Handler for workflow type change
          const handleWorkflowTypeChange = (option: IOption) => {
            setWorkflowType(option)
          }

          // Handler for year change
          const handleYearChange = (options: IOption[] | null) => {
            setSelectedYears(options ?? [])
          }

          const handleApply = () => {
            setAppliedFilters({
              workflowType: workflowType,
              selectedYears: selectedYears,
            })
            setQueryParams({
              workflowType: workflowType?.value ?? '',
              years: selectedYears.map((_year) => year.value).join('-') ?? '',
            })
            refetchUserHistory()
          }

          return (
            <WBox sx={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
              <HeaderComponent />
              <WBox m={2.5} className="digi-history-container">
                <WBox
                  className="digi-history-section"
                  sx={{
                    backgroundColor: '#ffffff',
                    borderRadius: '12px',
                    padding: '24px',
                    marginBottom: '24px',
                    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
                    border: '1px solid #e5e7eb',
                  }}
                >
                  <div className="digi-section-header">
                    <h3
                      className="digi-section-title"
                      style={{
                        fontSize: '20px',
                        fontWeight: '600',
                        color: '#1f2937',
                        marginBottom: '8px',
                      }}
                    >
                      {t('historyFilters', 'Workflow History Filters')}
                    </h3>
                    <div
                      className="digi-section-description"
                      style={{
                        fontSize: '14px',
                        color: '#6b7280',
                      }}
                    >
                      {t('filterDescription', 'Filter workflows by type and year to find specific history records')}
                    </div>
                    <WBox
                      sx={{
                        display: 'flex',
                        flexDirection: { xs: 'column', sm: 'row' },
                        alignItems: { xs: 'stretch', sm: 'flex-end' },
                        gap: { xs: 2, sm: 3 },
                        backgroundColor: '#f9fafb',
                        padding: 3,
                        borderRadius: '8px',
                        border: '1px solid #e5e7eb',
                      }}
                    >
                      <SelectBox
                        label={t('workflowTypeLabel')}
                        value={workflowType as IOption}
                        defaultText={null}
                        size="medium"
                        options={workflowTypeOptions}
                        onChange={(item) => handleWorkflowTypeChange(item as any)}
                        fullWidth={false}
                        disabled={isLoading ?? isFetching}
                        isLoading={isLoading ?? isFetching}
                        sx={{
                          width: { xs: '100%', sm: '250px' },
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#ffffff',
                          },
                        }}
                      />
                      <SelectBox
                        label={t('yearLabel')}
                        value={selectedYears}
                        defaultText={null}
                        options={years}
                        size="medium"
                        onChange={(item) => handleYearChange(item as any)}
                        multiple
                        fullWidth={false}
                        disabled={isLoading || isFetching}
                        isLoading={isLoading || isFetching}
                        sx={{
                          width: { xs: '100%', sm: '250px' },
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#ffffff',
                          },
                        }}
                      />
                      <WBox display="flex" flexDirection="row" gap={1}>
                        <WButton
                          onClick={() => clearFilters()}
                          variant="outlined"
                          size="small"
                          color="primary"
                          sx={{
                            height: '32px',
                            borderColor: '#e5e7eb',
                          }}
                        >
                          {t('clearButton')}
                        </WButton>
                        <WButton
                          onClick={() => handleApply()}
                          variant="contained"
                          size="small"
                          sx={{
                            height: '32px',
                          }}
                        >
                          {t('applyButton')}
                        </WButton>
                      </WBox>

                      <WBox
                        className="digi-history-section"
                        sx={{
                          backgroundColor: '#ffffff',
                          borderRadius: '12px',
                          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
                          border: '1px solid #e5e7eb',
                          overflow: 'hidden',
                        }}
                      >
                        <WBox sx={{ padding: '24px 24px 0 24px' }}>
                          <div className="digi-section-header">
                            <h3
                              className="digi-section-title"
                              style={{
                                fontSize: '20px',
                                fontWeight: '600',
                                color: '#1f2937',
                                marginBottom: '8px',
                              }}
                            >
                              {t('historyTableTitle', 'Workflow History')}
                            </h3>
                            <div
                              className="digi-section-description"
                              style={{
                                fontSize: '14px',
                                color: '#6b7280',
                              }}
                            >
                              {t('historyTableDescription', 'Complete history of all workflow activities and transactions')}
                            </div>
                        </WBox>
                        <DigiTable
                          tableType="history"
                          data={userHistoryData as any}
                          columns={historyColumns}
                          mobileConfig={{
                            titleFields: ['wfinstanceid', 'flowname'],
                            subtitleFields: ['wfowner'],
                            rightFields: ['wfdate'],
                          }}
                          exportButton={true}
                          filtering={true}
                          search={true}
                          grouping={true}
                          sorting={true}
                          toolbar={false}
                          loading={isLoading || isFetching}
                          draggable={true}
                          paging={true}
                          pageSize={10}
                          languageFile={t}
                          pageSizeOptions={[5, 10, 20, 50]}
                        />
                      </WBox>

                      {selectedWorkflow && <HistoryNotificationModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} workflowData={selectedWorkflow} />}
                    </WBox>
                    )
}
                  </HistoryNotificationModal>
                </HeaderComponent>
              </WBox>
            </any>
  </number >
  </IOption >
  </IHistory >
  </number >
