import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { WB<PERSON>, WButton, WLink } from 'wface'
import { useLocalStorage } from '@/hooks'
import { SelectBox } from '@/components/formElements'
import DigiTable from '@/components/Tables/DigiTable/DigiTable'
import { Loading } from '@/components/Loading/Loading'
import api from '@/api'
import dayjs from 'dayjs'
import HeaderComponent from '@/components/layout/HeaderComponent/HeaderComponent'

const WorkflowManagement: React.FC = () => {
  const { t, i18n } = useTranslation('workflowManagement')
  const [loginId] = useLocalStorage<number>('UserId')
  const [workflowType, setWorkflowType] = useState<string>('STARTED')
  const [isLoading, setIsLoading] = useState(false)
  const [workflowData, setWorkflowData] = useState<any[]>([])

  const workflowTypeOptions = [
    { value: '0', label: t('main_combo_tum'), labelEn: 'All' },
    { value: 'STARTED', label: t('main_combo_devam_eden'), labelEn: 'In Progress' },
    { value: 'COMPLETED', label: t('main_combo_tamamlanan'), labelEn: 'Completed' },
    { value: 'CANCELED', label: t('main_combo_iptal'), labelEn: 'Cancelled' },
    { value: 'SUSPENDED', label: t('main_combo_durdurulan'), labelEn: 'Suspended' },
    { value: 'ACCEPTED', label: t('main_combo_onaylanan'), labelEn: 'Approved' },
    { value: 'REJECTED', label: t('main_combo_reddedilen'), labelEn: 'Rejected' },
    { value: 'AYRILAN', label: t('ayrilan_kullanici_akislar'), labelEn: 'Workflows of Former Users' },
  ]

  const fetchWorkflowData = async () => {
    setIsLoading(true)
    try {
      const response = await api.post(`/histories/admin-workflows?workflowType=${workflowType}`)
      const data = await response.data
      setWorkflowData(data)
    } catch (error) {
      // toast.error(t('error_loading_workflows'))
      console.error('Error fetching workflow data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (loginId) {
      fetchWorkflowData()
    }
  }, [workflowType, loginId])

  const handleWorkflowTypeChange = (option: any) => {
    if (option) {
      setWorkflowType(option.value)
    }
  }

  const columns = [
    {
      title: 'ID',
      field: 'wfInsId',
      dateSetting: {
        type: 'number',
      },
      render: (rowData: any) => handleWorkflowOpen(rowData, 'wfInsId'),
    },
    {
      title: t('main_grid_flow_name'),
      field: 'flowName',
      render: (rowData: any) => handleWorkflowOpen(rowData, 'flowName'),
    },
    { title: t('main_grid_flow_state'), field: 'stateName', render: (rowData: any) => handleWorkflowOpen(rowData, 'stateName') },
    { title: t('main_grid_flow_owner'), field: 'wfOwner', render: (rowData: any) => handleWorkflowOpen(rowData, 'wfOwner') },
    {
      title: t('main_grid_last_modified'),
      field: 'wfLastModifiedBy',
      render: (rowData: any) => handleWorkflowOpen(rowData, 'wfLastModifiedBy'),
    },
    { title: t('main_grid_atanan'), field: 'atanan', render: (rowData: any) => handleWorkflowOpen(rowData, 'atanan') },
    {
      title: t('main_grid_flow_tarih'),
      field: 'wfDate',
      render: (rowData: any) => handleWorkflowOpen(rowData, 'wfDate'),
      dateSetting: {
        format: 'DD.MM.YYYY HH:mm',
        locale: i18n.language,
        type: 'date',
      },
    },
  ]

  // Function to get field values based on language
  const getFieldValue = (rowData: any, field: string) => {
    const isEnglish = i18n.language === 'en'
    switch (field) {
      case 'state':
        return isEnglish ? (rowData.statedesc ?? '') : (rowData.statename ?? '')
      case 'historyType':
        return isEnglish ? rowData.wfWorkflowHistoryTypeEng : (rowData.wfWorkflowHistoryTypeName ?? '')
      case 'lastAction':
        return isEnglish ? rowData.wfLastActionTypeEng : (rowData.wfLastActionTypeName ?? '')
      default:
        return rowData[field as keyof any] ?? ''
    }
  }

  // Function to handle workflow link clicks
  const handleWorkflowOpen = (rowData: any, key: string) => {
    if (rowData.route) {
      const basePath = '/main/workflow'
      const params: any = {
        name: rowData.route,
      }
      if (rowData.wfinstanceid) {
        params.wfInstanceId = rowData.wfInsId
      }
      const queryParams = new URLSearchParams(params)

      return (
        <WLink href={`${basePath}?${queryParams.toString()}`} target="_blank" underline="none" color={'black'}>
          {key === 'wfDate' ? (
            dayjs(rowData[key as keyof any] as string).format('DD.MM.YYYY HH:mm:ss')
          ) : key === 'flowName' || key === 'wfLastModifiedBy' ? (
            <span>{String(rowData[key as keyof any])}</span>
          ) : (
            String(getFieldValue(rowData, key))
          )}
        </WLink>
      )
    } else {
      return (
        <WLink href={`${import.meta.env.VITE_APP_OLD_URL}/${rowData.wfInstanceLink}`} target="_blank" underline="none" color={'black'}>
          {key === 'wfDate' ? (
            dayjs(rowData[key as keyof any] as string).format('DD.MM.YYYY HH:mm:ss')
          ) : key === 'flowName' || key === 'wfLastModifiedBy' ? (
            <span>{String(rowData[key as keyof any])}</span>
          ) : (
            String(getFieldValue(rowData, key))
          )}
        </WLink>
      )
    }
  }

  if (isLoading) {
    return <Loading height={500} show={true} />
  }

  return (
    <WBox sx={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      <HeaderComponent />
      <WBox m={2.5} className="digi-history-container">
        <WBox
          className="digi-history-section"
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            padding: '24px',
            marginBottom: '24px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            border: '1px solid #e5e7eb',
          }}
        >
          <div className="digi-section-header">
            <h3
              className="digi-section-title"
              style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '8px',
              }}
            >
              {t('workflow_management_filters', 'Workflow Management Filters')}
            </h3>
            <div
              className="digi-section-description"
              style={{
                fontSize: '14px',
                color: '#6b7280',
              }}
            >
              {t('workflow_management_description', 'Manage and monitor all workflows in the system')}
            </div>
          </div>
          <WBox
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: { xs: 'stretch', sm: 'flex-end' },
              gap: { xs: 2, sm: 3 },
              backgroundColor: '#f9fafb',
              padding: 3,
              borderRadius: '8px',
              border: '1px solid #e5e7eb',
            }}
          >
            <SelectBox
              label={t('workflow_type')}
              defaultText={null}
              value={workflowTypeOptions.find((opt) => opt.value === workflowType) || null}
              options={workflowTypeOptions}
              onChange={handleWorkflowTypeChange}
              fullWidth={false}
              disabled={isLoading}
              sx={{
                width: { xs: '100%', sm: '250px' },
                '& .MuiOutlinedInput-root': {
                  backgroundColor: '#ffffff',
                },
              }}
            />
            <a href="/react/main/flow-admin-history" style={{ textDecoration: 'none' }}>
              <WButton
                variant="contained"
                color="primary"
                sx={{
                  height: '36px',
                  width: '150px',
                  px: 1,
                }}
              >
                {t('workflow_history')}
              </WButton>
            </a>
          </WBox>
        </WBox>

        <WBox
          className="digi-history-section"
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            border: '1px solid #e5e7eb',
            overflow: 'hidden',
          }}
        >
          <WBox sx={{ padding: '24px 24px 0 24px' }}>
            <div className="digi-section-header">
              <h3
                className="digi-section-title"
                style={{
                  fontSize: '20px',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '8px',
                }}
              >
                {t('workflow_management')}
              </h3>
              <div
                className="digi-section-description"
                style={{
                  fontSize: '14px',
                  color: '#6b7280',
                }}
              >
                {t('workflow_table_description', 'Complete list of all workflows with their current status and assignments')}
              </div>
            </div>
          </WBox>
          <DigiTable
            languageFile={t}
            data={workflowData}
            columns={columns}
            filtering={true}
            search={false}
            grouping={true}
            exportButton={true}
            sorting={true}
            selection={false}
            toolbar={true}
            draggable={false}
            paging={true}
            mobileConfig={{
              titleFields: ['wfInsId', 'flowName'],
              subtitleFields: ['stateName'],
              rightFields: ['wfDate'],
            }}
            pageSize={10}
            pageSizeOptions={[5, 10, 20, 50]}
          />
        </WBox>
      </WBox>
    </WBox>
  )
}

export default WorkflowManagement
