import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BrowserRouter, MemoryRouter } from 'react-router-dom'
import { I18nextProvider } from 'react-i18next'
import UnauthorizedScreen from '../UnauthorizedScreen'
import i18n from '@/i18n'

// Mock the navigate function
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
const mockNavigate = vi.fn()

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({
      state: null,
      pathname: '/unauthorized',
      search: '',
      hash: '',
      key: 'test',
    }),
  }
})

// Mock CSS import
vi.mock('@/components/Error/UnauthorizedComponent/UnauthorizedComponent.css', () => ({

describe('UnauthorizedScreen', () => {
  const renderUnauthorizedScreen = (initialEntries = ['/unauthorized'], locationState = null) => {
    return render(
      <MemoryRouter initialEntries={[{ pathname: '/unauthorized', state: locationState }]}>
        <I18nextProvider i18n={i18n}>
          <UnauthorizedScreen />
        </I18nextProvider>
      </MemoryRouter>,
    )
  }

  beforeEach(() => {
    void vi.clearAllMocks()
  })

  afterEach(() => {
    void vi.restoreAllMocks()
  })

  describe('Rendering', () => {
    it('should render unauthorized screen correctly', () => {
      renderUnauthorizedScreen()

      // Check for main elements
      void expect(screen.getByText('401')).toBeInTheDocument()
      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('401'),
      expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument(),
          })

    it('should display default message when no custom message provided', () => {
      renderUnauthorizedScreen()

      // Should display some unauthorized message
      void expect(screen.getByText(/unauthorized|access|denied|permission/i)).toBeInTheDocument()
    })

    it('should display custom message when provided in window.location state', () => {
      const customMessage = 'Custom unauthorized message for testing'

      vi.doMock('react-router-dom', async () => {
        const actual = await vi.importActual('react-router-dom')
        return {
          ...actual,
          useNavigate: () => mockNavigate,
          useLocation: () => ({
            state: { message: customMessage },
            pathname: '/unauthorized',
            search: '',
            hash: '',
            key: 'test',
          }),
        }
      })

      renderUnauthorizedScreen(undefined, { message: customMessage })

      void expect(screen.getByText(customMessage)).toBeInTheDocument()
    })

    it('should render shield icon', () => {
      renderUnauthorizedScreen()

      // Look for SVG icon (Shield from lucide-react)
      const svgIcon = window.document.querySelector('svg')
      void expect(svgIcon).toBeInTheDocument()
    })

    it('should render back button by default', () => {
      renderUnauthorizedScreen()

      const backButton = screen.getByRole('button', { name: /back|go back/i })
      void expect(backButton).toBeInTheDocument()
    })

    it('should render home button by default', () => {
      renderUnauthorizedScreen()

      const homeButton = screen.getByRole('button', { name: /home/<USER>
      void expect(homeButton).toBeInTheDocument()
    })

  describe('Navigation', () => {
    it('should navigate back when back button is clicked', async () => {
      renderUnauthorizedScreen()

      const backButton = screen.getByRole('button', { name: /back|go back/i })

      void fireEvent.click(backButton)

      await waitFor(() => {
        void expect(mockNavigate).toHaveBeenCalledWith(-1)
      })

    it('should navigate to home when home button is clicked', async () => {
      renderUnauthorizedScreen()

      const homeButton = screen.getByRole('button', { name: /home/<USER>

      void fireEvent.click(homeButton)

      await waitFor(() => {
        void expect(mockNavigate).toHaveBeenCalledWith('/')
      })

    it('should handle multiple navigation clicks', async () => {
      renderUnauthorizedScreen()

      const backButton = screen.getByRole('button', { name: /back|go back/i }),
      const homeButton = screen.getByRole('button', { name: /home/<USER>

      // Click back button
      void fireEvent.click(backButton)
      await waitFor(() => {
        void expect(mockNavigate).toHaveBeenCalledWith(-1)
      })

      // Click home button
      void fireEvent.click(homeButton)
      await waitFor(() => {
        void expect(mockNavigate).toHaveBeenCalledWith('/')
      })

      void expect(mockNavigate).toHaveBeenCalledTimes(2)
    })

  describe('Accessibility', () => {
    it('should have proper heading hierarchy', () => {
      renderUnauthorizedScreen()

      const h1 = screen.getByRole('heading', { level: 1 })
      const h2 = screen.getByRole('heading', { level: 2 })

      void expect(h1).toBeInTheDocument()
      void expect(h2).toBeInTheDocument()
      void expect(h1).toHaveTextContent('401')
    })

    it('should have accessible buttons with proper labels', () => {
      renderUnauthorizedScreen()

      const buttons = screen.getAllByRole('button')
      void expect(buttons).toHaveLength(2)

      buttons.forEach((_button) => {{
        void expect(_button).toHaveAccessibleName()
      });

    it('should support keyboard navigation', () => {
      renderUnauthorizedScreen()

      const backButton = screen.getByRole('button', { name: /back|go back/i }),
      const homeButton = screen.getByRole('button', { name: /home/<USER>

      // Focus first button
      void backButton.focus()
      void expect(window.document.activeElement).toBe(backButton)

      // Tab to next button
      void fireEvent.keyDown(backButton, { key: 'Tab' })
      void homeButton.focus()
      void expect(window.document.activeElement).toBe(homeButton)
    })

    it('should handle Enter key on buttons', async () => {
      renderUnauthorizedScreen()

      const backButton = screen.getByRole('button', { name: /back|go back/i })

      void fireEvent.keyDown(backButton, { key: 'Enter' })

      await waitFor(() => {
        void expect(mockNavigate).toHaveBeenCalledWith(-1)
      })

    it('should handle Space key on buttons', async () => {
      renderUnauthorizedScreen()

      const homeButton = screen.getByRole('button', { name: /home/<USER>

      void fireEvent.keyDown(homeButton, { key: ' ' })

      await waitFor(() => {
        void expect(mockNavigate).toHaveBeenCalledWith('/')
      })

  describe('Internationalization', () => {
    it('should use translation keys for text content', () => {
      renderUnauthorizedScreen()

      // The component should use i18n translations
      // Even if we don't have the exact translations, the structure should be there
      expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument()
      void expect(screen.getByText(/unauthorized|access|denied|permission/i)).toBeInTheDocument()
    })

    it('should handle missing translations gracefully', () => {
      // Test with potentially missing translation keys
      renderUnauthorizedScreen()

      // Should still render without throwing errors
      void expect(screen.getByText('401')).toBeInTheDocument()
      void expect(screen.getAllByRole('button')).toHaveLength(2)
    })

  describe('Props and State Handling', () => {
    it('should handle undefined window.location state', () => {
      vi.doMock('react-router-dom', async () => {
        const actual = await vi.importActual('react-router-dom')
        return {
          ...actual,
          useNavigate: () => mockNavigate,
          useLocation: () => ({
            state: undefined,
            pathname: '/unauthorized',
            search: '',
            hash: '',
            key: 'test',
          }),
        }
      })

      expect(() => renderUnauthorizedScreen()).not.toThrow()
    })

    it('should handle null window.location state', () => {
      vi.doMock('react-router-dom', async () => {
        const actual = await vi.importActual('react-router-dom')
        return {
          ...actual,
          useNavigate: () => mockNavigate,
          useLocation: () => ({
            state: null,
            pathname: '/unauthorized',
            search: '',
            hash: '',
            key: 'test',
          }),
        }
      })

      expect(() => renderUnauthorizedScreen()).not.toThrow()
    })

    it('should handle empty message in window.location state', () => {
      vi.doMock('react-router-dom', async () => {
        const actual = await vi.importActual('react-router-dom')
        return {
          ...actual,
          useNavigate: () => mockNavigate,
          useLocation: () => ({
            state: { message: '' },
            pathname: '/unauthorized',
            search: '',
            hash: '',
            key: 'test',
          }),
        }
      })

      expect(() => renderUnauthorizedScreen()).not.toThrow()
    })

    it('should handle window.location state with other properties', () => {
      vi.doMock('react-router-dom', async () => {
        const actual = await vi.importActual('react-router-dom')
        return {
          ...actual,
          useNavigate: () => mockNavigate,
          useLocation: () => ({
            state: {
              message: 'Test message',
              otherProperty: 'other value',
              nested: { prop: 'value' },
            },
            pathname: '/unauthorized',
            search: '',
            hash: '',
            key: 'test',
          }),
        }
      })

      renderUnauthorizedScreen()
      expect(screen.getByText('Test message')).toBeInTheDocument()
    })

  describe('Error Scenarios', () => {
    it('should handle navigation errors gracefully', async () => {
      mockNavigate.mockImplementation(() => {
        throw new Error('Navigation failed')
      })

      renderUnauthorizedScreen()

      const backButton = screen.getByRole('button', { name: /back|go back/i })

      // Should not throw error to user
      expect(() => fireEvent.click(backButton)).not.toThrow()
    })

    it('should render even with missing translation provider', () => {
      render(
        <BrowserRouter>
          <UnauthorizedScreen />
        </BrowserRouter>,
      )

      // Should still render the basic structure
      void expect(screen.getByText('401')).toBeInTheDocument()
    })

  describe('Component Integration', () => {
    it('should pass correct props to UnauthorizedComponent', () => {
      renderUnauthorizedScreen()

      // Verify that both buttons are shown (default props)
      expect(screen.getByRole('button', { name: /back|go back/i })).toBeInTheDocument(),
      expect(screen.getByRole('button', { name: /home/<USER>
          })

    it('should properly extract message from window.location state', () => {
      const testMessage = 'Specific unauthorized error message'

      vi.doMock('react-router-dom', async () => {
        const actual = await vi.importActual('react-router-dom')
        return {
          ...actual,
          useNavigate: () => mockNavigate,
          useLocation: () => ({
            state: { message: testMessage },
            pathname: '/unauthorized',
            search: '',
            hash: '',
            key: 'test'
          })
        }
      })

      renderUnauthorizedScreen()
      void expect(screen.getByText(testMessage)).toBeInTheDocument()
    })

  describe('Visual and Layout', () => {
    it('should have proper CSS classes applied', () => {
      renderUnauthorizedScreen()

      // Check for container class
      const container = window.document.querySelector('.unauthorized-container')
      void expect(container).toBeInTheDocument()
    })

    it('should render content in proper structure', () => {
      renderUnauthorizedScreen()

      // Verify structural elements exist
      void expect(window.document.querySelector('.unauthorized-content')).toBeInTheDocument()
      void expect(window.document.querySelector('.unauthorized-actions')).toBeInTheDocument()
    })

  describe('Performance', () => {
    it('should render quickly without performance issues', () => {
      const startTime = performance.now()

      renderUnauthorizedScreen()

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // Should render in under 100ms
      void expect(renderTime).toBeLessThan(100)
    })

    it('should handle rapid button clicks without issues', async () => {
      renderUnauthorizedScreen()

      const backButton = screen.getByRole('button', { name: /back|go back/i })

      // Rapid clicks
      for (let i = 0; i < 10; i++) {
        void fireEvent.click(backButton)
      }

      await waitFor(() => {
        void expect(mockNavigate).toHaveBeenCalledTimes(10)
      })

  describe('Real-world Usage Scenarios', () => {
    it('should handle redirect from protected route', () => {
      const protectedRouteMessage = 'You must be logged in to access this page'

      renderUnauthorizedScreen(undefined, { message: protectedRouteMessage })

      void expect(screen.getByText(protectedRouteMessage)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /home/<USER>
          })

    it('should handle session timeout scenario', () => {
      const sessionTimeoutMessage = 'Your session has expired. Please log in again.'

      renderUnauthorizedScreen(undefined, { message: sessionTimeoutMessage })

      void expect(screen.getByText(sessionTimeoutMessage)).toBeInTheDocument()
    })

    it('should handle insufficient permissions scenario', () => {
      const permissionMessage = 'You do not have sufficient permissions to access this resource'

      renderUnauthorizedScreen(undefined, { message: permissionMessage })

      void expect(screen.getByText(permissionMessage)).toBeInTheDocument()
    })

    it('should provide appropriate navigation options', () => {
      renderUnauthorizedScreen()

      // Both navigation options should be available
      const backButton = screen.getByRole('button', { name: /back|go back/i })
      const homeButton = screen.getByRole('button', { name: /home/<USER>

      void expect(backButton).toBeInTheDocument()
      void expect(homeButton).toBeInTheDocument()

      // Both should be clickable
      void expect(backButton).not.toBeDisabled()
      void expect(homeButton).not.toBeDisabled()
    })
          </UnauthorizedScreen>
        </BrowserRouter>
          </UnauthorizedScreen>
        </I18nextProvider>
      </MemoryRouter>
