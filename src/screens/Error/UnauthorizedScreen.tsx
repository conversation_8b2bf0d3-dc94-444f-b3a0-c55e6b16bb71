import React from 'react'
import UnauthorizedComponent from '@/components/Error/UnauthorizedComponent'
import { useLocation } from 'react-router-dom'

const UnauthorizedScreen: React.FC = () => {
  const location = useLocation()
  const message = (location.state as { message?: string })?.message

  return <UnauthorizedComponent message={message} showBackButton={true} showHomeButton={true} />
}

export default UnauthorizedScreen
