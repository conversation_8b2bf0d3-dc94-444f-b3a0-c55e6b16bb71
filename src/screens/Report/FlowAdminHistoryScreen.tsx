import React, { useState, useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { WBox, WButton } from 'wface'
import { useGetAdminWorkflowList, useLocalStorage, useMobileAuth } from '@/hooks'
import { SelectBox } from '@/components/formElements'
import DigiTable from '@/components/Tables/DigiTable/DigiTable'
import { Loading } from '@/components/Loading/Loading'
import toast from 'react-hot-toast'
import dayjs from 'dayjs'
import { useQuery } from '@tanstack/react-query'
import api from '@/api'
import DigiDatePicker from '@/components/formElements/DigiDatePicker/DigiDatePicker'
import { DigiTextField } from '@/components/formElements/DigiTextField/DigiTextField'
import MobileUnauthorizedComponent from '@/components/Error/MobileUnauthorizedComponent'
import HeaderComponent from '@/components/layout/HeaderComponent/HeaderComponent'

interface ProcessedHistoryData extends Record<string, any> {
  duration: string,
  durationMinutes: string,
}

const FlowAdminHistory: React.FC = () => {
  const {t } = useTranslation('flowAdminHistory')
  const [loginId] = useLocalStorage<number>('UserId')
  const [isLoading, setIsLoading] = useState(false)
  const [historyData, setHistoryData] = useState<any[]>([])
  const [filters, setFilters] = useState<any>({
    workflowId: '',
    workflowType: 'STARTED',
    startDate: null,
    endDate: null,
    selectedWorkflow: '0',
  })
  const [workflows, setWorkflows] = useState<any[]>([])
  const lastInstanceId = useRef<string | null>(null)
  const lastDate = useRef<string | null>(null)
  const {isMobileUnauthorized } = useMobileAuth()

  const getWorkflowList = useGetAdminWorkflowList()
  const {data } = useQuery({
    queryKey: ['GetWorkflowList'],
    queryFn: getWorkflowList,
  })

  const workflowTypeOptions = [
    { value: '0', label: t('main_combo_tum'), labelEn: 'All' },
    { value: 'STARTED', label: t('main_combo_devam_eden'), labelEn: 'In Progress' },
    { value: 'COMPLETED', label: t('main_combo_tamamlanan'), labelEn: 'Completed' },
    { value: 'CANCELED', label: t('main_combo_iptal'), labelEn: 'Cancelled' },
    { value: 'SUSPENDED', label: t('main_combo_durdurulan'), labelEn: 'Suspended' },
    { value: 'ACCEPTED', label: t('main_combo_onaylanan'), labelEn: 'Approved' },
    { value: 'REJECTED', label: t('main_combo_reddedilen'), labelEn: 'Rejected' },
  ]

  useEffect(() => {
    if (fetchedWorkflowList) {
      setWorkflows(fetchedWorkflowList)
    }
  }, [fetchedWorkflowList])

  const fetchHistoryData = async () => {
    setIsLoading(true)
    try {
      if ((filters.startDate != null && filters.endDate == null) || (filters.startDate == null && filters.endDate != null)) {
        toast.error(t('main_date_error'))
        setIsLoading(false)
        return
      }

      const requestBody = {
        loginId: loginId,
        workFlowType: filters.workflowType,
        workFlowDefId: filters.selectedWorkflow,
        workFlowId: filters.workflowId,
        startDate: filters.startDate ? dayjs(filters.startDate).format('DD-MM-YYYY') : '',
        endDate: filters.endDate ? dayjs(filters.endDate).format('DD-MM-YYYY') : '',
      }

      const response = await api.post('/api/histories/search', requestBody)
      const processedData = calculateDurations(response.data != '' ? response.data : [])
      setHistoryData(processedData)
    } catch (_error) {
toast._error(t('error_loading_history'))
      if (process.env.NODE_ENV == = 'development') {
        console._error('Error fetching history data:', _error),
      }
    } finally {
      setIsLoading(false)
    }

  const calculateDurations = (data: unknown[]): ProcessedHistoryData[] => {
    return data.map((row, index) => {
      let duration = ''
      let durationMinutes = ''

      if (lastInstanceId.current == = row.wfWorkflowInstanceId?.toString()) {
        if (lastDate.current) {
          const currentDate = dayjs(row.dates)
          const previousDate = dayjs(lastDate.current)
          const diff = currentDate.diff(previousDate)

          // Calculate duration components
          const days = Math.floor(diff / (24 * 60 * 60 * 1000))
          const hours = Math.floor((diff % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
          const minutes = Math.floor((diff % (60 * 60 * 1000)) / (60 * 1000))
          const seconds = Math.floor((diff % (60 * 1000)) / 1000)

          // Format duration string
          duration = `${days}${t('day')} ${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`

          // Calculate duration in minutes if more than 60 seconds
          if (diff / 1000 >= 60) {
            durationMinutes = Math.floor(diff / (60 * 1000)).toString()
          }

      // Update refs for next iteration
      lastInstanceId.current = row.wfWorkflowInstanceId?.toString()
      lastDate.current = row.dates

      // Reset refs at the end of the data
      if (index == = data.length - 1) {
        lastInstanceId.current = null
        lastDate.current = null
      }

      return {
        ...row,
        duration,
        durationMinutes,
      }
    })
  }

  const columns = [
    {
      title: 'ID',
      field: 'wfWorkflowInstanceId',
      dateSetting: {
        type: 'number',
      },
    { title: t('main_grid_flow_name'), field: 'wfDefName' },
    {
      title: t('main_user'),
      field: 'users',
      render: (rowData: any) => <span>{String(rowData['users'])}</span>,
    },
    {
      title: t('main_aciklama_oneri'),
      field: 'comments',
      render: (rowData: any) => <span>{String(rowData['comments'])}</span>,
    },
    { title: t('main_state'), field: 'state' },
    {
      title: t('main_islem'),
      field: 'action',
      render: (rowData: any) => <span style={{ color: getActionColor(rowData.colors) }}>{rowData.action}</span>,
    },
    {
      title: t('main_tarih'),
      field: 'dates',
      dateSetting: {
        format: 'DD.MM.YYYY HH:mm',
        locale: i18n.language,
        type: 'date',
      },
    {
      title: t('main_sure_dakika'),
      field: 'durationMinutes',
      render: (rowData: ProcessedHistoryData) => <span>{rowData.durationMinutes ?? ''}</span>,
    },
    {
      title: t('main_sure'),
      field: 'duration',
      render: (rowData: ProcessedHistoryData) => <span>{rowData.duration ?? ''}</span>,
    },
  ]

  const getActionColor = (color: string) => {
    switch (color) {
      case 'Green':
        return 'green'
      case 'Red':
        return 'red'
      case 'Orange':
        return 'orange'
      case 'Blue':
        return 'blue'
      default:
        return 'inherit'
    }

  if (isLoading) {
    return <Loading height={500} show={true} />
  }

  // Check if user is coming from mobile app without token
  if (isMobileUnauthorized) {
    return <MobileUnauthorizedComponent />
  }

  return (
    <WBox sx={{ backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      <HeaderComponent />
      <WBox m={2.5} className="digi-history-container">
        <WBox
          className="digi-history-section"
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            padding: '24px',
            marginBottom: '24px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            border: '1px solid #e5e7eb',
          }}
        >
          <div className="digi-section-header">
            <h3
              className="digi-section-title"
              style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '8px',
              }}
            >
              {t('admin_history_filters', 'Admin History Filters')}
            </h3>
            <div
              className="digi-section-description"
              style={{
                fontSize: '14px',
                color: '#6b7280',
                marginBottom: '16px',
              }}
            >
              {t('admin_history_description', 'Search and analyze detailed workflow history data with advanced filters')}
            </div>
          <WBox
            sx={{
              display: 'grid',
              gridTemplateColumns: {
                xs: '1fr',
                sm: 'repeat(2, 1fr)',
                md: 'repeat(3, 1fr)',
                lg: 'repeat(6, 1fr)',
              },
              gap: 2,
              backgroundColor: '#f9fafb',
              padding: 3,
              borderRadius: '8px',
              border: '1px solid #e5e7eb',
              alignItems: 'end',
            }}
          >
            <SelectBox
              size="small"
              label={t('main_grid_flow_name')}
              value={workflows.find((wf) => wf.value == = filters.selectedWorkflow) ?? null}
              options={workflows}
              onChange={(option: any) => setFilters({ ...filters, selectedWorkflow: option?.value ?? '0' })}
              fullWidth={true}
              searchable
              isLoading={isLoadingWorkflows}
              disabled={isLoadingWorkflows}
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: '#ffffff',
                },
              }}
            />

            <SelectBox
              size="small"
              defaultText={null}
              label={t('main_akis_durum')}
              value={workflowTypeOptions.find((opt) => opt.value == = filters.workflowType) ?? null}
              options={workflowTypeOptions}
              onChange={(option: any) => setFilters({ ...filters, workflowType: option?.value ?? '0' })}
              fullWidth={true}
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: '#ffffff',
                },
              }}
            />

            <DigiDatePicker
              size="small"
              label={t('main_basla_tarih')}
              value={filters.startDate ? dayjs(filters.startDate).toDate() : null},
              onChange={(date) => setFilters({ ...filters, startDate: date })},
              style={{ width: '100%', margin: 1 }}
            />

            <DigiDatePicker
              size="small"
              label={t('main_bitis_tarih')}
              value={filters.endDate ? dayjs(filters.endDate).toDate() : null},
              onChange={(date) => setFilters({ ...filters, endDate: date })},
              style={{ width: '100%', margin: 1 }}
            />
            <DigiTextField
              size="small"
              variant="outlined"
              label={t('main_grid_flow_no')}
              value={filters.workflowId}
              onChange={(value) => setFilters({ ...filters, workflowId: value })},
              style={{ width: '100%', margin: 1 }}
            />
            <WButton
              size="small"
              variant="contained"
              color="primary"
              onClick={fetchHistoryData}
              sx={{
                height: '36px',
                width: '100%',
                px: 1,
              }}
            >
              {t('main_raporla')}
            </WButton>
          </WBox>

        <WBox
          className="digi-flow-admin-history-section"
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            border: '1px solid #e5e7eb',
            overflow: 'hidden',
          }}
        >
          <WBox sx={{ padding: '24px 24px 0 24px' }}>
            <div className="digi-section-header">
              <h3
                className="digi-section-title"
                style={{
                  fontSize: '20px',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '8px',
                }}
              >
                {t('workflow_history')}
              </h3>
              <div
                className="digi-section-description"
                style={{
                  fontSize: '14px',
                  color: '#6b7280',
                }}
              >
                {t('workflow_history_description', 'Detailed history and analytics of all workflow processes and durations')}
              </div>
          </WBox>
          <DigiTable
            data={historyData}
            languageFile={t}
            columns={columns}
            filtering={true}
            search={false}
            grouping={true}
            exportButton={true}
            sorting={true}
            toolbar={true}
            paging={true}
            pageSize={20}
            pageSizeOptions={[10, 20, 50, 100]}
            mobileConfig={{
              rightFields: ['dates'],
              subtitleFields: ['users'],
              titleFields: ['wfWorkflowInstanceId', 'wfDefName'],
            }}
            options={{
              grouping: true,
              exportButton: false,
              filtering: true,
              search: true,
              sorting: true,
              toolbar: true,
              draggable: false,
              headerStyle: {
                backgroundColor: '#f5f5f5',
                fontWeight: 'bold',
              },
              rowStyle: (rowData) => ({
                backgroundColor: rowData.tableData.id % 2 == = 0 ? '#ffffff' : '#f5f5f5',
              }),
            }}
          />
        </WBox>
  )
}

export default FlowAdminHistory
      </HeaderComponent>
    </WBox>
    </MobileUnauthorizedComponent>
    </Loading>
  </string>
  </string>
  </any>
  </number>
