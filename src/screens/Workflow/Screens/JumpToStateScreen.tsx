import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useFormContext, Controller } from 'react-hook-form'
import dayjs from 'dayjs'
import { WTextField, WGrid, WTypography, WLink } from 'wface'
import { useWorkflow } from '@/contexts/WorkflowContext'
import { useLocalStorage } from '@/hooks'
import { WorkflowSection, WorkflowWarning } from '@/components/workflowComponents'

/**
 * Represents the JumpToStateScreen component.
 * This component is responsible for rendering a screen that allows the user to jump to a specific state in a workflow.
 * It displays information about the current workflow instance and provides a form for entering a description.
 * The user can click a button to open the workflow in a new tab.
 *
 * @returns The JumpToStateScreen component.
 */
const JumpToStateScreen = () => {
  const { t } = useTranslation('jumpToStateSchema')
  const { initialData, setDefinitionId } = useWorkflow()
  const [storedValue] = useLocalStorage<number>('UserId')
  const {
    control,
    setValue,
    formState: { errors },
  } = useFormContext()

  useEffect(() => {
    setDefinitionId(1340)
    setValue('WorkflowDefId', 1340)
    if (initialData?.entity == null) {
      setValue('CreatedBy', storedValue)
      setValue('Created', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
      setValue('LastUpdatedBy', storedValue)
      setValue('LastUpdated', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
      setValue('OwnerLoginId', storedValue)
    } else {
      setValue('CreatedBy', initialData.entity.createdBy)
      setValue('Created', initialData.entity.created)
      setValue('LastUpdatedBy', storedValue)
      setValue('LastUpdated', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
      setValue('OwnerLoginId', initialData.entity.ownerLoginId ?? undefined)
    }
  }, [initialData, setDefinitionId, setValue, storedValue, t])

  const fields = [
    { label: 'flowInstanceId', value: initialData?.refInstance?.wfWorkflowInstanceId },
    { label: 'flowName', value: initialData?.refInstance?.workflowName },
    { label: 'stateName', value: initialData?.refInstance?.stateName },
    { label: 'starterUser', value: initialData?.starterUser },
    { label: 'assignerUsers', value: initialData?.assignerUsers?.join(', ') },
  ]

  useEffect(() => {
    if (initialData?.refInstance) {
      setValue('FlowName', initialData.refInstance.workflowName)
      setValue('FlowInstanceID', initialData.refInstance.wfWorkflowInstanceId)
      setValue('StateName', initialData.refInstance.stateName)
      setValue('StateInstanceID', initialData.refInstance.stateInstanceId)
      setValue('StarterUser', initialData.starterUser)
      setValue('Link', initialData.refInstance.flowLink)
      setValue('Description', initialData?.entity?.description ?? '')
    }
  }, [initialData])

  return (
    <WGrid container spacing={3}>
      {/* Workflow Instance Information Section */}
      <WorkflowSection title={t('jumpToStateRequest')}>
        {fields.map((item, index) => (
          <WGrid item xs={12} key={index}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '8px 0',
                borderBottom: '1px solid #f0f0f0',
              }}
            >
              <WTypography
                variant="subtitle2"
                style={{
                  minWidth: '150px',
                  fontWeight: 600,
                  color: '#666',
                }}
              >
                {t(item.label)}:
              </WTypography>
              <WTypography
                variant="body1"
                style={{
                  marginLeft: '16px',
                  flex: 1,
                }}
              >
                {item.value ?? '-'}
              </WTypography>
            </div>
          </WGrid>
        ))}
      </WorkflowSection>

      {/* Description Section */}
      <WorkflowSection title={t('description')}>
        <WGrid item xs={12}>
          <Controller
            name="Description"
            control={control}
            rules={{
              required: t('descriptionRequired'),
              maxLength: { value: 200, message: t('descriptionMaxLength') },
            }}
            render={({ field }) => (
              <WTextField
                {...field}
                variant="outlined"
                fullWidth
                multiline
                rows={4}
                label={t('description')}
                error={!!errors?.Description?.message}
                helperText={errors?.Description?.message?.toString() ?? ''}
                disabled={!initialData?.enabilitySettings?.canEditJumpToStateComment}
              />
            )}
          />
        </WGrid>
      </WorkflowSection>

      {/* Action Section */}
      {initialData?.refInstance?.flowLink && initialData?.entity && (
        <WorkflowSection title={t('actions')}>
          <WGrid item xs={12}>
            {initialData?.enabilitySettings?.canJumpToStateMessage === '' && (
              <div style={{ textAlign: 'center' }}>
                <WLink
                  href={initialData.refInstance.flowLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    display: 'inline-block',
                    padding: '12px 24px',
                    backgroundColor: '#5c2d91',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '4px',
                    fontWeight: 500,
                  }}
                >
                  {t('jumpToStateLink')}
                </WLink>
              </div>
            )}
          </WGrid>
        </WorkflowSection>
      )}

      {/* Error/Warning Section */}
      {initialData?.enabilitySettings?.canJumpToStateMessage && initialData?.enabilitySettings?.canJumpToStateMessage !== '' && (
        <WorkflowWarning title={t('warning')} message={t(initialData?.enabilitySettings?.canJumpToStateMessage)} />
      )}
    </WGrid>
  )
}

export default JumpToStateScreen
