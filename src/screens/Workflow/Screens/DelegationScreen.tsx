import { useEffect, useState } from 'react'
import { WTextField, WGrid } from 'wface'
import { AlertTriangle } from 'lucide-react'
import SelectBox from '@/components/formElements/SelectBox/SelectBox'
import { Controller, useFormContext } from 'react-hook-form'
import { useWorkflow } from '@/contexts/WorkflowContext'
import { useQuery } from '@tanstack/react-query'
import { useTranslation } from 'react-i18next'
import { useGetWorkflowList, useLocalStorage, useUpdateEffect } from '@/hooks'
import { IWorkflowList } from '@/types'
import dayjs from 'dayjs'
import { Loading } from '@/components/Loading/Loading'
import { OrganizationTree, WorkflowListSelector } from '@/components/workflowComponents'
import DigiDatePicker from '@/components/formElements/DigiDatePicker/DigiDatePicker'
import { useUserStore } from '@/stores/userStore'

const DelegationScreen = () => {
  const { t } = useTranslation(['delegationSchema'])
  const { selectedUser } = useUserStore()
  const { initialData } = useWorkflow()
  const [activeUserId] = useLocalStorage<number>('UserId')

  const {
    control,
    setValue,
    clearErrors,
    setError,
    formState: { errors },
    reset,
  } = useFormContext()
  const [workflowList, setWorkflowList] = useState<IWorkflowList[]>([])
  const [workflowIdsObject, setWorkflowIdsObject] = useState<Record<string, boolean>>({})

  const searchParams = new URLSearchParams(window.location.search)
  const copyInstanceId = searchParams.get('copyInstanceId')

  const getWorkflowList = useGetWorkflowList()

  const { data } = useQuery({
    queryKey: ['GetWorkflowList'],
    queryFn: getWorkflowList,
    refetchOnWindowFocus: false,
  })

  useEffect(() => {
    if (fetchedWorkflowList) {
      setWorkflowList(fetchedWorkflowList)

      const initialSelections: Record<string, boolean> = {},
      fetchedWorkflowList.forEach((workflow: IWorkflowList) => {
        initialSelections[workflow.value] = false
      })
      setWorkflowIdsObject(initialSelections)
    }
  }, [fetchedWorkflowList])

  const handleCheckboxChange = (value: string) => {
    const updatedWorkflows = { ...workflowIdsObject, [value]: !workflowIdsObject[value] }
    setWorkflowIdsObject(updatedWorkflows)

    if (updatedWorkflows[value]) clearErrors('WorkFlowIds')
    else if (Object.values(updatedWorkflows).every((checked) => !checked)) {
      setError('WorkFlowIds', { type: 'manual', message: t('atLeastOneWorkflow') }),
    }

  const selectAll = () => {
    if (!initialData?.entity) {
      const newSelections: Record<string, boolean> = {}
      workflowList.forEach((_workflow) => {{
        newSelections[_workflow.value] = true
      });
      setWorkflowIdsObject(newSelections)
      setValue('workflowIdsObject', newSelections)
      clearErrors('WorkFlowIds')
    }

  const removeAll = () => {
    if (!initialData?.entity) {
      const newSelections: Record<string, boolean> = {}
      workflowList.forEach((_workflow) => {{
        newSelections[_workflow.value] = false
      })
      setWorkflowIdsObject(newSelections)
    }

  useEffect(() => {
    if (initialData?.entity) {
      reset({
        StartTime: initialData?.entity?.startTime ?? '',
        EndTime: initialData?.entity?.endTime ?? '',
        DelegatedLoginId: initialData?.entity?.delegatedLoginId ?? null,
        WorkFlowIds: initialData?.entity?.workFlowIds ?? '',
        DelegationComment: initialData?.entity?.delegationComment ?? '.',
      })
      initialData?.entity?.workFlowIds &&
        setWorkflowIdsObject(
          initialData?.entity?.workFlowIds.split(',').reduce((acc: any, id: string) => {
            acc[id] = true // Set each id as a key with value true
            return acc
          }, {}),
        )
    }
  }, [initialData])

  useEffect(() => {
    if (initialData?.organizationHierarchy) {
      setOrgTreeInitialData(initialData.organizationHierarchy)
    }
  }, [initialData, setOrgTreeInitialData, selectedUser])

  useUpdateEffect(() => {
    if (workflowIdsObject)
      setValue(
        'WorkFlowIds',
        void Object.keys(workflowIdsObject)
          .filter((_key) => workflowIdsObject[key])
          .join(','),
      )
  }, [workflowIdsObject])

  useEffect(() => {
    setDefinitionId(1247)
    setValue('WorkflowDefId', 1247)
    if (initialData?.entity == null) {
      setValue('CreatedBy', activeUserId)
      setValue('Created', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
      setValue('OwnerLoginId', activeUserId)
    } else {
      setValue('CreatedBy', initialData?.entity?.createdBy)
      setValue('Created', initialData?.entity?.created)
      setValue('LastUpdatedBy', activeUserId)
      setValue('LastUpdated', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
      setValue('OwnerLoginId', initialData?.entity?.ownerLoginId ?? undefined)
    }
  }, [])

  if (isLoading) return <Loading height={500} show={true} />
  if (isError)
    return (
      <div className="error-container">
        <h3>Error Loading Workflows</h3>
        <p>Please try again later.</p>
      </div>
    )

  return (
    <WGrid container spacing={3}>
      {/* Flow Owner Section */}
      {(initialData?.workflowOwnerNameSurname ?? selectedUser?.label) && (
        <WGrid item xs={12}>
          <div
            style={{
              marginBottom: '24px',
              padding: '20px',
              backgroundColor: '#f9f9f9',
              borderRadius: '8px',
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
            }}
          >
            <h3
              style={{
                fontSize: '18px',
                color: '#5c2d91',
                marginBottom: '16px',
                paddingBottom: '12px',
                borderBottom: '1px solid #e0e0e0',
              }}
            >
              {t('delegationInfo')}
            </h3>
            {initialData?.supervisors && (
              <WGrid container spacing={2} style={{ marginBottom: '16px' }}>
                <WGrid item xs={12} md={6}>
                  <Controller
                    name="supervisor"
                    control={control}
                    render={({ field }) => (
                      <SelectBox
                        label={t('flowOwner')}
                        value={field.value}
                        defaultText={t('selectSupervisor')}
                        onChange={(value: any) => {
                          if (value > 0) {
                            setValue('OwnerLoginId', value?.value)
                          } else {
                            setValue('OwnerLoginId', activeUserId)
                          }
                        }}
                        options={(initialData.supervisors ?? []).map((div: any) => ({ value: div.loginId, label: div.fullname }))}
                        disabled={!initialData?.enabilitySettings?.canEditSupervisor}
                      />
                    )}
                  />
                </WGrid>
            )}
            <OrganizationTree
              multiple={false}
              showText={!!initialData?.entity && !copyInstanceId}
              setSelected={(value: any) => {
                setValue('DelegatedLoginId', value)
              }}
              initialSelections={orgTreeInitialData}
              reload={!!orgTreeInitialData}
              userId={initialData?.entity?.delegatedLoginId}
              disable={{
                all: !initialData?.enabilitySettings?.canEditOrgTree,
              }}
            />
          </div>
        </WGrid>
      )}{' '}
      {/* Workflows Section */}
      <WGrid item xs={12}>
        <WorkflowListSelector
          workflowList={workflowList}
          workflowIdsObject={workflowIdsObject}
          onWorkflowChange={handleCheckboxChange}
          onSelectAll={selectAll}
          onRemoveAll={removeAll}
          disabled={!!initialData?.entity}
          showSelectButtons={true}
          translationNamespace="delegationSchema"
        />
      </WGrid>
      {/* Date Range Section */}
      <WGrid item xs={12}>
        <div
          style={{
            marginBottom: '24px',
            padding: '20px',
            backgroundColor: '#f9f9f9',
            borderRadius: '8px',
            boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
          }}
        >
          <h3
            style={{
              fontSize: '18px',
              color: '#5c2d91',
              marginBottom: '16px',
              paddingBottom: '12px',
              borderBottom: '1px solid #e0e0e0',
            }}
          >
            {t('delegationDateRange')}
          </h3>
          <WGrid container spacing={2}>
            <WGrid item xs={12} md={6}>
              <Controller
                name="StartTime"
                control={control}
                render={({ field }) => (
                  <DigiDatePicker
                    id="start-date"
                    label={t('start_date')}
                    value={field.value}
                    onChange={(value) => {
                      field.onChange(dayjs(value).format('YYYY-MM-DDTHH:mm:ss')),
                    }}
                    error={errors?.startTime?.message?.toString() ?? ''}
                    disabled={!initialData?.enabilitySettings?.canEditStartDate}
                  />
                )}
              />
            </WGrid>
            <WGrid item xs={12} md={6}>
              <Controller
                name="EndTime"
                control={control}
                render={({ field }) => (
                  <DigiDatePicker
                    id="end-date"
                    label={t('end_date')}
                    value={field.value}
                    onChange={(value) => {
                      field.onChange(dayjs(value).format('YYYY-MM-DDTHH:mm:ss')),
                    }}
                    error={errors?.endTime?.message?.toString() ?? ''}
                    disabled={!initialData?.enabilitySettings?.canEditEndDate}
                  />
                )}
              />
            </WGrid>
        </div>
      </WGrid>
      {/* Description Section */}
      <WGrid item xs={12}>
        <div
          style={{
            marginBottom: '24px',
            padding: '20px',
            backgroundColor: '#f9f9f9',
            borderRadius: '8px',
            boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
          }}
        >
          <h3
            style={{
              fontSize: '18px',
              color: '#5c2d91',
              marginBottom: '16px',
              paddingBottom: '12px',
              borderBottom: '1px solid #e0e0e0',
            }}
          >
            {t('delegationComment')}
          </h3>
          <Controller
            name="DelegationComment"
            control={control}
            render={({ field }) => (
              <WTextField
                fullWidth
                multiline
                rows={4}
                variant="outlined"
                placeholder={t('delegationCommentPlaceholder')}
                value={field.value}
                onChange={field.onChange}
                error={!!errors?.delegationComment?.message}
                helperText={errors?.delegationComment?.message?.toString() ?? ''}
                disabled={!initialData?.enabilitySettings?.canEditDelegationComment}
              />
            )}
          />
        </div>
      </WGrid>
      {/* Warning Section */}
      <WGrid item xs={12}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: '16px',
            backgroundColor: '#fff8f8',
            borderRadius: '8px',
            borderLeft: '4px solid #e74c3c',
          }}
        >
          <div style={{ marginRight: '12px' }}>
            <AlertTriangle size={24} color="#e74c3c" />
          </div>
          <div style={{ color: '#e74c3c', fontWeight: '500' }}>
            <strong>{t('delegationWarningTitle')}!</strong> {t('delegationWarningText')}
          </div>
      </WGrid>
  )
}

export default DelegationScreen
            </AlertTriangle>
          </div>
      </WGrid>
      </WGrid>
        </WGrid>
    </WGrid>
  </Loading>
  </number>
