import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useFormContext, Controller } from 'react-hook-form'
import { useWorkflow } from '@/contexts/WorkflowContext'
import dayjs from 'dayjs'
import { WGrid } from 'wface'
import { useQueryClient } from '@tanstack/react-query'
import { IFile, IOption } from '@/types'
import PartyListBox from '@/components/formElements/PartyListBox/PartyListBox'
import { useLocalStorage } from '@/hooks'
import { FileUpload, SelectBox } from '@/components/formElements'
import { OrganizationTree, WorkflowSection } from '@/components/workflowComponents'
import DigiTextField from '@/components/formElements/DigiTextField/DigiTextField'
import DigiDatePicker from '@/components/formElements/DigiDatePicker/DigiDatePicker'
import toast from 'react-hot-toast'

const ContractRequestScreen = () => {
  const { t } = useTranslation('contractRequestSchema')
  const { initialData } = useWorkflow()
  const [storedValue] = useLocalStorage<number>('UserId')

  const [selectedFirm, setSelectedFirm] = useState<any>('DT')
  const {
    control,
    setValue,
    formState: { errors },
    reset,
    trigger,
    watch,
  } = useFormContext()
  const queryClient = useQueryClient()

  const [parties, setParties] = useState<string[]>([])
  const [newParty, setNewParty] = useState('')
  const [documents, setDocuments] = useState<any>([])

  const handleFirmChange = (value: any) => {
    setValue('Firms', value?.value)
    setSelectedFirm(value)
  }

  useEffect(() => {
    if (initialData?.entity) {
      reset({
        Firms: Number(initialData.entity.firms) ?? 'DT',
        TaxNo: initialData.entity.taxNo ?? '',
        TaxRegion: initialData.entity.taxRegion ?? '',
        StartDate: initialData.entity.startDate ?? null,
        EndDate: initialData.entity.endDate ?? null,
        Other: initialData.entity.other ?? '',
        UrunServis: initialData.entity.urunServis ?? '',
        ContractCategory: initialData.entity.contractCategory ?? 0,
        Subject: initialData.entity.subject ?? '',
        PaymentAmount: initialData.entity.paymentAmount ?? 0,
        PaymentType: initialData.entity.paymentType ?? 0,
        Description: initialData.entity.description ?? '',
        CancelByDigiTurk: initialData.entity.cancelByDigiTurk ?? 0,
        CancelTwoSide: initialData.entity.cancelTwoSide ?? 0,
        CancelCurePeriod: initialData.entity.cancelCurePeriod ?? 0,
        CancelOther: initialData.entity.cancelOther ?? 0,
        CurePeriodDay: initialData.entity.curePeriodDay ?? 0,
        Confidence: initialData.entity.confidence ?? '',
        PunishmentClauses: initialData.entity.punismentClauses ?? '',
        ContractControlDate: initialData.entity.contractControlDate ?? null,
        ContractType: Number(initialData.entity.contractType) ?? '0',
        PaymentCurrencyType: initialData.entity.paymentCurrencyType ?? 'TL',
        ContractSoftFile: initialData.entity.contractSoftFile ?? '',
        ContractSignedSoftFile: initialData.entity.contractSignedSoftFile ?? '',
      })
      setSelectedFirm(initialData.entity.firms)
      setOrgTreeInitialData(initialData.organizationHierarchy)
      setParties(initialData.entity.parties ? initialData.entity.parties.split(';') : [])
      setDocuments(initialData.entity.files ?? [])
      if (initialData?.enabilitySettings?.canSendBack) setIsSendBackVisible(true)
    }
  }, [initialData, reset])

  useEffect(() => {
    if (initialData?.organizationHierarchy) {
      setOrgTreeInitialData(initialData.organizationHierarchy)
    }
  }, [initialData, setOrgTreeInitialData])

  useEffect(() => {
    setDefinitionId(1338)
    if (initialData?.entity == null) {
      setValue('CreatedBy', storedValue)
      setValue('Created', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
      setValue('OwnerLoginId', storedValue)
    } else {
      setValue('CreatedBy', initialData?.entity?.createdBy)
      setValue('Created', initialData?.entity?.created)
      setValue('LastUpdatedBy', storedValue)
      setValue('LastUpdated', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
      setValue('OwnerLoginId', initialData?.entity?.ownerLoginId ?? undefined)
    }
  }, [setDefinitionId])

  const handleAddParty = () => {
    if (newParty && !parties.includes(newParty)) {
      setParties([...parties, newParty])
      setNewParty('')
      setValue('Parties', [...parties, newParty].join(';'))
    }

  const handleRemoveParty = (partyToRemove: string) => {
    const updatedParties = parties.filter((_party) => party !== partyToRemove)
    setParties(updatedParties)
    setValue('Parties', updatedParties.join(';'))
  }

  const handleFileUpload = async (files: IFile[]) => {
    if (files.length === 0) return

    const file = files[0]
    try {
      if (!initialData?.entity) {
        // Initial upload - regular contract file
        const filePath = `${initialData?.sharePointPath ?? ''}${file.name}`
        setValue('ContractSoftFile', filePath, {
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true,
        })
        // Update documents state for display
        setDocuments([...documents, { name: file.name, url: filePath }]),
      } else if (initialData?.workflowState === 'SozlesmeFinalAdim') {
        // Signed contract upload
        const filePath = `${initialData?.entity?.sharePointFinalPath ?? ''}${file.name}`

        // Set value with validation options
        setValue('ContractSignedSoftFile', filePath, {
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true,
        })

        // Manually trigger validation
        const isValid = await trigger('ContractSignedSoftFile')
        if (!isValid) {
          toast.error(t('signedContractRequired'))
          return
        }

        setUpdateEntity({
          id: initialData.entity.requestId,
          properties: {
            ContractSignedSoftFile: filePath,
          },
          emailRule: null,
        })

        // Update documents state for display
        setDocuments([...documents, { name: file.name, url: filePath }]),
      } else if (file) {
        // Update file when the state is not initial and SozlesmeFinalAdim, use updateentity parameter for approval
        const filePath = `${initialData?.sharePointPath ?? ''}${file.name}`

        // Update documents state for display
        setDocuments([...documents, { name: file.name, url: filePath }])

        setUpdateEntity({
          id: initialData.entity.requestId,
          properties: {
            ContractSoftFile: filePath,
          },
          emailRule: null,
        })
      }

      // Refresh documents query
      void queryClient.invalidateQueries({ queryKey: ['documents'] }),
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console._error('File upload _error:', _error),
      }
      toast.error(t('fileUploadError'))
    }

  const handleFileDelete = (fileName: string) => {
    try {
      if (!initialData?.entity) {
        // Clear regular contract file
        setValue('ContractSoftFile', '', {
          shouldValidate: true,
        })
      } else if (initialData?.workflowState === 'SozlesmeFinalAdim') {
        // Clear signed contract file
        setValue('ContractSignedSoftFile', '', {
          shouldValidate: true,
        })
      }

      // Update documents state
      setDocuments(documents.filter((doc: any) => doc.name !== fileName))

      // Refresh documents query
      void queryClient.invalidateQueries({ queryKey: ['documents'] }),
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console._error('File delete _error:', _error),
      }
      toast.error(t('fileDeleteError'))
    }
  useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      // Debug logging removed for production
    })
    return () => subscription.unsubscribe()
  }, [watch])

  return (
    <WGrid container spacing={3}>
      {/* Organization Section */}
      <WorkflowSection title={t('organization_information')}>
        <WGrid item xs={12}>
          <OrganizationTree
            multiple={false}
            showText={true}
            setSelected={(value) => setValue('OwnerLoginId', value)}
            initialSelections={orgTreeInitialData}
            reload={!!orgTreeInitialData}
            disable={{
              all: !initialData?.enabilitySettings?.canEditOrgTree,
            }}
          />
        </WGrid>
      </WorkflowSection>

      {/* Contract Information Section */}
      <WorkflowSection title={t('contract_information')}>
        {/* Parties */}
        <WGrid item xs={12}>
          <PartyListBox
            label={t('newParty')}
            parties={parties}
            onRemoveParty={handleRemoveParty}
            newParty={newParty}
            setNewParty={setNewParty}
            onAddParty={handleAddParty}
            disabled={!initialData?.enabilitySettings?.canEditParties}
          />
          <Controller
            name="Parties"
            control={control}
            defaultValue=""
            render={({ field }) => <input type="hidden" {...field} value={parties.join(';')} />}
          />
        </WGrid>

        {/* Firms */}
        <WGrid item xs={12} md={6}>
          <SelectBox
            label={t('firms')}
            value={selectedFirm}
            onChange={(value: any) => handleFirmChange(value)}
            options={initialData?.firms ?? []}
            id="firms"
            name="firms"
            defaultText="Firma Seçiniz"
            disabled={!initialData?.enabilitySettings?.canEditFirms}
          />
        </WGrid>

        {/* Tax Information */}
        <WGrid item xs={12} md={6}>
          <Controller
            name="TaxNo"
            control={control}
            render={({ field }) => (
              <DigiTextField
                label={t('taxNo')}
                fullWidth
                {...field}
                variant="outlined"
                error={!!errors?.TaxNo?.message}
                helperText={errors?.TaxNo?.message?.toString() ?? ''}
                disabled={!initialData?.enabilitySettings?.canEditTaxNo}
              />
            )}
          />
        </WGrid>

        <WGrid item xs={12} md={6}>
          <Controller
            name="TaxRegion"
            control={control}
            render={({ field }) => (
              <DigiTextField
                label={t('taxRegion')}
                fullWidth
                {...field}
                variant="outlined"
                error={!!errors?.TaxRegion?.message}
                helperText={errors?.TaxRegion?.message?.toString() ?? ''}
                disabled={!initialData?.enabilitySettings?.canEditTaxRegion}
              />
            )}
          />
        </WGrid>

        {/* UrunServis */}
        <WGrid item xs={12}>
          <Controller
            name="UrunServis"
            control={control}
            render={({ field }) => (
              <DigiTextField
                fullWidth
                label={t('urunServis')}
                {...field}
                variant="outlined"
                error={!!errors?.UrunServis?.message}
                helperText={errors?.UrunServis?.message?.toString() ?? ''}
                disabled={!initialData?.enabilitySettings?.canEditUrunServis}
              />
            )}
          />
        </WGrid>

        {/* Contract Dates */}
        <WGrid item xs={12} md={6}>
          <Controller
            name="StartDate"
            control={control}
            render={({ field }) => (
              <DigiDatePicker
                label={t('startDate')}
                value={field.value}
                size="small"
                onChange={(value) => {
                  field.onChange(dayjs(value).format('YYYY-MM-DD'))
                }}
                error={errors?.StartDate?.message?.toString() ?? ''}
                disabled={!initialData?.enabilitySettings?.canEditStartDate}
              />
            )}
          />
        </WGrid>

        <WGrid item xs={12} md={6}>
          <Controller
            name="EndDate"
            control={control}
            render={({ field }) => (
              <DigiDatePicker
                label={t('endDate')}
                value={field.value}
                size="small"
                onChange={(value) => {
                  field.onChange(dayjs(value).format('YYYY-MM-DD'))
                }}
                error={errors?.EndDate?.message?.toString() ?? ''}
                disabled={!initialData?.enabilitySettings?.canEditEndDate}
              />
            )}
          />
        </WGrid>

        {/* Other Information */}
        <WGrid item xs={12}>
          <Controller
            name="Other"
            control={control}
            render={({ field }) => (
              <DigiTextField
                fullWidth
                label={t('other')}
                {...field}
                variant="outlined"
                error={!!errors?.Other?.message}
                helperText={errors?.Other?.message?.toString() ?? ''}
                disabled={!initialData?.enabilitySettings?.canEditOther}
              />
            )}
          />
        </WGrid>

        {/* Contract Category */}
        <WGrid item xs={12}>
          <Controller
            name="ContractCategory"
            control={control}
            render={({ field }) => (
              <SelectBox
                label={t('contractCategory')}
                value={field.value}
                onChange={(value: any) => {
                  setValue('ContractCategory', Number(value?.value))
                }}
                options={
                  initialData?.contractCategories?.map((c: IOption) => {
                    return {
                      value: Number(c.value),
                      label: c.label,
                      labelEn: c.labelEn,
                    }
                  }) ?? []
                }
                disabled={!initialData?.enabilitySettings?.canEditContractCategory}
              />
            )}
          />
        </WGrid>

        {/* Subject */}
        <WGrid item xs={12}>
          <Controller
            name="Subject"
            control={control}
            render={({ field }) => (
              <DigiTextField
                fullWidth
                label={t('subject')}
                {...field}
                multiline
                rows={4}
                variant="outlined"
                error={!!errors?.Subject?.message}
                helperText={errors?.Subject?.message?.toString() ?? ''}
                disabled={!initialData?.enabilitySettings?.canEditSubject}
              />
            )}
          />
        </WGrid>
      </WorkflowSection>

      {/* Payment Information Section */}
      <WorkflowSection title={t('paymentInformation')}>
        <WGrid item xs={8}>
          <Controller
            name="PaymentAmount"
            control={control}
            render={({ field }) => (
              <DigiTextField
                fullWidth
                label={t('paymentAmount')}
                {...field}
                onChange={(value) => setValue('PaymentAmount', Number(value))}
                type="number"
                variant="outlined"
                error={!!errors?.PaymentAmount?.message}
                helperText={errors?.PaymentAmount?.message?.toString() ?? ''}
                disabled={!initialData?.enabilitySettings?.canEditPaymentAmount}
              />
            )}
          />
        </WGrid>

        <WGrid item xs={4}>
          <Controller
            name="PaymentCurrencyType"
            control={control}
            render={({ field }) => (
              <SelectBox
                label={t('paymentCurrencyType')}
                value={field.value}
                onChange={(value: any) => setValue('PaymentCurrencyType', value?.value)}
                options={[
                  { value: 'TL', label: 'TL', labelEn: 'TL' },
                  { value: 'USD', label: 'USD', labelEn: 'USD' },
                  { value: 'EUR', label: 'EUR', labelEn: 'EUR' },
                  { value: 'GBP', label: 'GBP', labelEn: 'GBP' },
                ]}
                disabled={!initialData?.enabilitySettings?.canEditPaymentCurrencyType}
              />
            )}
          />
        </WGrid>

        {/* Payment Type */}
        <WGrid item xs={12}>
          <Controller
            name="PaymentType"
            control={control}
            render={({ field }) => (
              <SelectBox
                label={t('paymentType')}
                value={field.value}
                onChange={(value: any) => setValue('PaymentType', Number(value?.value))}
                options={[
                  { value: 0, label: t('oneTime'), labelEn: t('oneTime') },
                  { value: 1, label: t('monthly'), labelEn: t('monthly') },
                  { value: 2, label: t('quarterly'), labelEn: t('quarterly') },
                  { value: 3, label: t('yearly'), labelEn: t('yearly') },
                ]}
                disabled={!initialData?.enabilitySettings?.canEditPaymentType}
              />
            )}
          />
        </WGrid>
      </WorkflowSection>

      {/* File Upload Section */}
      <WorkflowSection title={t('file_upload')}>
        <WGrid item xs={12}>
          <FileUpload
            onUpload={handleFileUpload}
            disabled={!initialData?.enabilitySettings?.canPressUploadButton}
            onDelete={handleFileDelete}
            pathKey="SharePointContractDocs"
            initialFiles={documents}
          />
        </WGrid>
      </WorkflowSection>
    </WGrid>
  )
}

export default ContractRequestScreen

}
}
}
  </any>
  </any>
  </number>