import React, { useState, useEffect } from 'react'
import { useFormContext, Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { useUpdateEffect } from '@/hooks'
import { WGrid, WButton, WCard } from 'wface'
import DigiTable from '@/components/Tables/DigiTable/DigiTable'
import { WorkflowSection } from '@/components/workflowComponents'
import { useWorkflow } from '@/contexts/WorkflowContext'
import { IOption, IUploadedFile } from '@/types'
import DigiTextField from '@/components/formElements/DigiTextField/DigiTextField'
import DigiDatePicker from '@/components/formElements/DigiDatePicker/DigiDatePicker'
import { SelectBox } from '@/components/formElements'
import toast from 'react-hot-toast'
import dayjs from 'dayjs'

interface StationeryRequestFormData {
  WorkflowDefId?: number
  CreatedBy?: number
  Created?: string
  LastUpdatedBy?: number
  LastUpdated?: string
  RequestId?: number
  RequestNo?: string
  RequestDate?: string
  RequestingDepartment?: string
  RequestingPerson?: string
  ReqType?: string // 1: For Myself, 2: For Someone Else
  Owner?: number // Who opened the form
  RequestFor?: string // 'self' or 'other' - kept for backward compatibility
  RequestForUserId?: number
  LocationId?: string
  FloorId?: string
  Address?: string
  PackageId?: string
  RequestReason?: string
  DeliveryDate?: string
  DeliveryType?: string
  Notes?: string
  TotalAmount?: number
  StationeryRequestDetailDetails?: StationeryRequestDetail[]
  StationeryAddressDetailRequestDetails?: StationeryAddressDetailRequest[]
  [key: string]: any
}

interface StationeryRequestDetail {
  RequestId?: number
  RelatedRequestId?: number
  GroupId?: string
  GroupName?: string
  TypeId?: string
  TypeName?: string
  RequestedQuantity?: number
  DeliveredQuantity?: number
  StockQuantity?: number
  Unit?: string
  UnitPrice?: number
  CurrencyCode?: string
  Description?: string
  [key: string]: any
}

interface StationeryAddressDetailRequest {
  RequestId?: number
  RelatedRequestId?: number
  AddressType?: string
  RecipientName?: string
  FullAddress?: string
  City?: string
  District?: string
  PostalCode?: string
  ContactPerson?: string
  ContactPhone?: string
  DeliveryInstructions?: string
  [key: string]: any
}

interface MaterialGroup {
  value: string
  label: string
  labelEn: string
}

interface MaterialType {
  value: string
  label: string
  labelEn: string
  groupId: string
  unit?: string
  stockQuantity?: number
}

const StationeryRequestScreen: React.FC = () => {
  const { t } = useTranslation(['stationeryRequestSchema', 'common'])
  const { initialData, setDefinitionId, setUpdateEntity } = useWorkflow()
  const {
    control,
    reset,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useFormContext<StationeryRequestFormData>()

  const [stationeryRequestDetailDetails, setStationeryRequestDetailDetails] = useState<StationeryRequestDetail[]>([])
  const [stationeryAddressDetailRequestDetails, setStationeryAddressDetailRequestDetails] = useState<StationeryAddressDetailRequest[]>([])
  const [selectedDetailRow, setSelectedDetailRow] = useState<StationeryRequestDetail | null>(null)
  const [selectedAddressRow, setSelectedAddressRow] = useState<StationeryAddressDetailRequest | null>(null)
  const [detailModalOpen, setDetailModalOpen] = useState(false)
  const [addressModalOpen, setAddressModalOpen] = useState(false)
  const [editingDetailIndex, setEditingDetailIndex] = useState<number | null>(null)
  const [editingAddressIndex, setEditingAddressIndex] = useState<number | null>(null)
  const [documents] = useState<IUploadedFile[]>([])

  // New state variables
  const [reqType, setReqType] = useState<string>('1') // Default to "For Myself"
  const [selectedLocation, setSelectedLocation] = useState<string>('')
  const [selectedGroup, setSelectedGroup] = useState<string>('')
  const [selectedType, setSelectedType] = useState<string>('')
  const [requestedQuantity, setRequestedQuantity] = useState<string>('')
  const [materialTypes, setMaterialTypes] = useState<MaterialType[]>([])
  const [showPackageSection, setShowPackageSection] = useState(false)

  const enabilitySettings = initialData?.enabilitySettings || {}
  const visibilitySettings = initialData?.visibilitySettings || {}
  const dropdownOptions = initialData || {}

  // Get dropdown options from initial data
  const requestTypes = initialData?.requestTypes || []
  const locations = initialData?.locations || []
  const floors = initialData?.floors || []
  const packages = initialData?.packages || []
  const materialGroups: MaterialGroup[] = initialData?.materialGroups || []
  const allMaterialTypes: MaterialType[] = initialData?.materialTypes || []
  const users = initialData?.users || []

  const stationeryDetailColumns = [
    { field: 'Id', headerName: 'ID', width: 60, renderCell: (params: any) => params.dataIndex + 1 },
    { field: 'GroupName', headerName: t('materialGroup'), flex: 1 },
    { field: 'TypeName', headerName: t('material'), flex: 1.5 },
    { field: 'RequestedQuantity', headerName: t('requestedQuantity'), flex: 0.8, type: 'numeric' as const },
    { field: 'DeliveredQuantity', headerName: t('deliveredQuantity'), flex: 0.8, type: 'numeric' as const },
    { field: 'StockQuantity', headerName: t('stockQuantity'), flex: 0.8, type: 'numeric' as const },
    { field: 'Unit', headerName: t('unit'), flex: 0.6 },
  ]

  const addressDetailColumns = [
    {
      field: 'AddressType',
      headerName: t('addressType'),
      flex: 1,
      renderCell: (params: { value: string }) => (params.value ? t(params.value as any) : ''),
    },
    { field: 'RecipientName', headerName: t('recipientName'), flex: 1.5 },
    { field: 'FullAddress', headerName: t('fullAddress'), flex: 2 },
    { field: 'City', headerName: t('city'), flex: 1 },
    { field: 'District', headerName: t('district'), flex: 1 },
    { field: 'ContactPerson', headerName: t('contactPerson'), flex: 1.5 },
    { field: 'ContactPhone', headerName: t('contactPhone'), flex: 1 },
  ]

  useEffect(() => {
    setDefinitionId(118) // StationeryRequest workflow ID
  }, [setDefinitionId])

  useUpdateEffect(() => {
    if (initialData?.entity) {
      const entity = initialData.entity as StationeryRequestFormData
      reset(entity)

      // Set the request type
      if (entity.ReqType) {
        setReqType(entity.ReqType.toString())
      }

      if (entity.StationeryRequestDetailDetails) {
        setStationeryRequestDetailDetails(entity.StationeryRequestDetailDetails)
      }
    }
  }, [initialData, reset])

  const handleEditDetail = (row: StationeryRequestDetail, index: number) => {
    setSelectedDetailRow(row)
    setEditingDetailIndex(index)
    setDetailModalOpen(true)
  }

  const handleSaveDetail = () => {
    if (!selectedDetailRow) return

    const newDetail: StationeryRequestDetail = {
      ...selectedDetailRow,
    }

    if (editingDetailIndex !== null) {
      const updatedDetails = [...stationeryRequestDetailDetails]
      updatedDetails[editingDetailIndex] = newDetail
      setStationeryRequestDetailDetails(updatedDetails)
      setValue('StationeryRequestDetailDetails', updatedDetails)
      toast.success(t('common:workflow.itemUpdatedSuccess'))
    } else {
      const updatedDetails = [...stationeryRequestDetailDetails, newDetail]
      setStationeryRequestDetailDetails(updatedDetails)
      setValue('StationeryRequestDetailDetails', updatedDetails)
      toast.success(t('common:workflow.itemAddedSuccess'))
    }

    calculateTotalAmount()
    setDetailModalOpen(false)
    setSelectedDetailRow(null)
  }

  const handleDeleteDetail = (index: number) => {
    const updatedDetails = stationeryRequestDetailDetails.filter((_, i) => i !== index)
    setStationeryRequestDetailDetails(updatedDetails)
    setValue('StationeryRequestDetailDetails', updatedDetails)
    calculateTotalAmount()
    toast.success(t('common:workflow.itemDeletedSuccess'))
  }

  // Initialize with proper request type
  useEffect(() => {
    if (!reqType) {
      setReqType('1')
      setValue('ReqType', '1')
    }
  }, [])

  const handleAddAddress = () => {
    setSelectedAddressRow({
      AddressType: 'addressTypeDelivery',
      City: '',
      District: '',
    })
    setEditingAddressIndex(null)
    setAddressModalOpen(true)
  }

  const handleEditAddress = (row: StationeryAddressDetailRequest, index: number) => {
    setSelectedAddressRow(row)
    setEditingAddressIndex(index)
    setAddressModalOpen(true)
  }

  const handleSaveAddress = () => {
    if (!selectedAddressRow) return

    if (editingAddressIndex !== null) {
      const updatedAddresses = [...stationeryAddressDetailRequestDetails]
      updatedAddresses[editingAddressIndex] = selectedAddressRow
      setStationeryAddressDetailRequestDetails(updatedAddresses)
      setValue('StationeryAddressDetailRequestDetails', updatedAddresses)
      toast.success(t('common:workflow.addressUpdatedSuccess'))
    } else {
      const updatedAddresses = [...stationeryAddressDetailRequestDetails, selectedAddressRow]
      setStationeryAddressDetailRequestDetails(updatedAddresses)
      setValue('StationeryAddressDetailRequestDetails', updatedAddresses)
      toast.success(t('common:workflow.addressAddedSuccess'))
    }

    setAddressModalOpen(false)
    setSelectedAddressRow(null)
  }

  const handleDeleteAddress = (index: number) => {
    const updatedAddresses = stationeryAddressDetailRequestDetails.filter((_, i) => i !== index)
    setStationeryAddressDetailRequestDetails(updatedAddresses)
    setValue('StationeryAddressDetailRequestDetails', updatedAddresses)
    toast.success(t('common:workflow.addressDeletedSuccess'))
  }

  const calculateTotalAmount = () => {
    const total = stationeryRequestDetailDetails.reduce((sum, detail) => {
      return sum + (detail.RequestedQuantity || 0) * (detail.UnitPrice || 0)
    }, 0)
    setValue('TotalAmount', total)
  }

  const onSubmit = async (data: StationeryRequestFormData) => {
    try {
      const submitData = {
        ...data,
        StationeryRequestDetailDetails: stationeryRequestDetailDetails,
        StationeryAddressDetailRequestDetails: stationeryAddressDetailRequestDetails,
        Documents: documents,
      }

      setUpdateEntity(submitData)
      toast.success(t('common:saveSuccess'))
    } catch (error) {
      toast.error(t('common:saveError'))
    }
  }

  // Handle request type change
  const handleReqTypeChange = (value: string) => {
    setReqType(value)
    setValue('ReqType', value)

    // Also set the backward compatible RequestFor field
    setValue('RequestFor', value === '1' ? 'self' : 'other')

    // Show package section for new employee requests
    if (value === '2') {
      // For Someone Else
      // Check if selected user is a new employee (you may need to add this logic based on your business rules)
      setShowPackageSection(true)
    } else {
      setShowPackageSection(false)
      setValue('PackageId', '')
      setValue('RequestForUserId', 0)
    }
  }

  // Handle location change
  const handleLocationChange = (option: any) => {
    setSelectedLocation(option?.value || '')
    setValue('LocationId', option?.value || '')
    // Reset floor when location changes
    setValue('FloorId', '')
  }

  // Handle material group change
  const handleMaterialGroupChange = (option: any) => {
    setSelectedGroup(option?.value || '')
    // Filter material types based on selected group
    const filteredTypes = allMaterialTypes.filter((type) => type.groupId === option?.value)
    setMaterialTypes(filteredTypes)
    // Reset selected type
    setSelectedType('')
  }

  // Add material to grid
  const handleAddMaterial = () => {
    if (!selectedGroup || !selectedType || !requestedQuantity) {
      toast.error(t('pleaseFillAllFields'))
      return
    }

    const selectedGroupData = materialGroups.find((g) => g.value === selectedGroup)
    const selectedTypeData = materialTypes.find((t) => t.value === selectedType)

    if (!selectedGroupData || !selectedTypeData) return

    const newMaterial: StationeryRequestDetail = {
      GroupId: selectedGroup,
      GroupName: selectedGroupData.label,
      TypeId: selectedType,
      TypeName: selectedTypeData.label,
      RequestedQuantity: parseInt(requestedQuantity),
      DeliveredQuantity: 0,
      StockQuantity: selectedTypeData.stockQuantity || 0,
      Unit: selectedTypeData.unit || '',
    }

    const updatedDetails = [...stationeryRequestDetailDetails, newMaterial]
    setStationeryRequestDetailDetails(updatedDetails)
    setValue('StationeryRequestDetailDetails', updatedDetails)

    // Reset form
    setSelectedGroup('')
    setSelectedType('')
    setRequestedQuantity('')
    setMaterialTypes([])

    toast.success(t('common:workflow.itemAddedSuccess'))
  }

  return (
    <>
      <WGrid container spacing={2}>
        {/* Request Owner Selection */}
        {visibilitySettings.requestOwnerSelection && (
          <WorkflowSection title={t('requestOwnerSelection')}>
            <WGrid item xs={12}>
              <div style={{ marginBottom: '10px', color: '#e20821', fontWeight: 'bold' }}>*{t('pleaseSelectRequestOwner')}</div>
            </WGrid>
            <WGrid item xs={12}>
              <Controller
                name="ReqType"
                control={control}
                render={({ field }) => (
                  <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
                    {requestTypes.map((reqType: any) => (
                      <label key={reqType.value} style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                        <input
                          type="radio"
                          value={reqType.value}
                          checked={field.value === reqType.value || (!field.value && reqType.value === '1')}
                          onChange={(e) => {
                            field.onChange(e.target.value)
                            handleReqTypeChange(e.target.value)
                          }}
                          disabled={!enabilitySettings.reqType}
                          style={{ marginRight: '8px' }}
                        />
                        {reqType.label}
                      </label>
                    ))}
                  </div>
                )}
              />
              {errors.ReqType && <div style={{ color: 'red', marginTop: 4, fontSize: '0.875rem' }}>{errors.ReqType.message}</div>}
            </WGrid>
            {reqType === '2' && (
              <WGrid item xs={12} md={6}>
                <Controller
                  name="RequestForUserId"
                  control={control}
                  render={({ field }) => (
                    <SelectBox
                      label={t('selectUserForRequest')}
                      value={field.value ? users.find((u: any) => u.value === field.value) : null}
                      onChange={(option: any) => field.onChange(option?.value || '')}
                      options={users}
                      disabled={!enabilitySettings.requestForUserId}
                      fullWidth
                      error={errors.RequestForUserId?.message}
                    />
                  )}
                />
              </WGrid>
            )}
          </WorkflowSection>
        )}

        {/* Package Selection for New Employees */}
        {showPackageSection && (
          <WorkflowSection title={t('packageSelection')}>
            <WGrid item xs={12}>
              <div style={{ marginBottom: '10px', color: '#e20821', fontWeight: 'bold' }}>{t('newEmployeePackageInfo')}</div>
            </WGrid>
            <WGrid item xs={12} md={6}>
              <Controller
                name="PackageId"
                control={control}
                render={({ field }) => (
                  <SelectBox
                    label={t('selectPackage')}
                    value={field.value ? packages.find((p: any) => p.value === field.value) : null}
                    onChange={(option: any) => {
                      field.onChange(option?.value || '')
                      // When package is selected, auto-populate materials
                      if (option?.value) {
                        // This would load package items - implement based on your backend
                        toast.success(t('packageItemsLoaded'))
                      }
                    }}
                    options={packages}
                    disabled={!enabilitySettings.packageId}
                    fullWidth
                    error={errors.PackageId?.message}
                  />
                )}
              />
            </WGrid>
          </WorkflowSection>
        )}

        <WorkflowSection title={t('requestInformation')}>
          <WGrid item xs={12} md={3}>
            <Controller
              name="RequestNo"
              control={control}
              render={({ field }) => <DigiTextField {...field} label={t('requestNo')} disabled fullWidth />}
            />
          </WGrid>
          <WGrid item xs={12} md={3}>
            <Controller
              name="RequestDate"
              control={control}
              render={({ field }) => (
                <DigiDatePicker
                  {...field}
                  label={t('requestDate')}
                  disabled={!enabilitySettings.requestDate}
                  fullWidth
                  error={errors.RequestDate?.message}
                  helperText={errors.RequestDate?.message}
                  value={field.value ? new Date(field.value) : null}
                  onChange={(date) => field.onChange(date ? dayjs(date).format('YYYY-MM-DD') : '')}
                />
              )}
            />
          </WGrid>
          <WGrid item xs={12} md={3}>
            <Controller
              name="RequestingDepartment"
              control={control}
              render={({ field }) => (
                <SelectBox
                  label={t('requestingDepartment')}
                  value={field.value ? { value: field.value, label: field.value, labelEn: field.value } : null}
                  onChange={(option: any) => field.onChange(option?.value || '')}
                  options={dropdownOptions.departments || []}
                  disabled={!enabilitySettings.requestingDepartment}
                  fullWidth
                  error={errors.RequestingDepartment?.message}
                />
              )}
            />
          </WGrid>
          <WGrid item xs={12} md={3}>
            <Controller
              name="RequestingPerson"
              control={control}
              render={({ field }) => (
                <DigiTextField
                  {...field}
                  label={t('requestingPerson')}
                  disabled={!enabilitySettings.requestingPerson}
                  fullWidth
                  error={errors.RequestingPerson?.message}
                  helperText={errors.RequestingPerson?.message}
                />
              )}
            />
          </WGrid>
          <WGrid item xs={12} md={3}>
            <Controller
              name="LocationId"
              control={control}
              render={({ field }) => (
                <SelectBox
                  label={t('location')}
                  value={field.value ? locations.find((l: any) => l.value === field.value) : null}
                  onChange={(option: any) => handleLocationChange(option)}
                  options={locations}
                  disabled={!enabilitySettings.locationId}
                  fullWidth
                  error={errors.LocationId?.message}
                />
              )}
            />
          </WGrid>
          <WGrid item xs={12} md={3}>
            <Controller
              name="FloorId"
              control={control}
              render={({ field }) => (
                <SelectBox
                  label={t('floor')}
                  value={field.value ? floors.find((f: any) => f.value === field.value) : null}
                  onChange={(option: any) => field.onChange(option?.value || '')}
                  options={floors.filter((f: any) => !selectedLocation || f.locationId === selectedLocation)}
                  disabled={!enabilitySettings.floorId || !selectedLocation}
                  fullWidth
                  error={errors.FloorId?.message}
                />
              )}
            />
          </WGrid>
          {selectedLocation && (
            <WGrid item xs={12}>
              <Controller
                name="Address"
                control={control}
                render={({ field }) => (
                  <DigiTextField
                    {...field}
                    label={t('address')}
                    multiline
                    rows={2}
                    disabled={!enabilitySettings.address}
                    fullWidth
                    error={errors.Address?.message}
                    helperText={errors.Address?.message}
                  />
                )}
              />
            </WGrid>
          )}
          <WGrid item xs={12} md={6}>
            <Controller
              name="RequestReason"
              control={control}
              render={({ field }) => (
                <DigiTextField
                  {...field}
                  label={t('requestReason')}
                  multiline
                  rows={3}
                  disabled={!enabilitySettings.requestReason}
                  fullWidth
                  error={errors.RequestReason?.message}
                  helperText={errors.RequestReason?.message}
                />
              )}
            />
          </WGrid>
          <WGrid item xs={12} md={3}>
            <Controller
              name="DeliveryDate"
              control={control}
              render={({ field }) => (
                <DigiDatePicker
                  {...field}
                  label={t('deliveryDate')}
                  disabled={!enabilitySettings.deliveryDate}
                  fullWidth
                  error={errors.DeliveryDate?.message}
                  helperText={errors.DeliveryDate?.message}
                  value={field.value ? new Date(field.value) : null}
                  onChange={(date) => field.onChange(date ? dayjs(date).format('YYYY-MM-DD') : '')}
                />
              )}
            />
          </WGrid>
          <WGrid item xs={12} md={3}>
            <Controller
              name="DeliveryType"
              control={control}
              render={({ field }) => (
                <SelectBox
                  label={t('deliveryType')}
                  value={field.value ? { value: field.value, label: field.value, labelEn: field.value } : null}
                  onChange={(option: any) => field.onChange(option?.value || '')}
                  options={dropdownOptions.deliveryTypes || []}
                  disabled={!enabilitySettings.deliveryType}
                  fullWidth
                  error={errors.DeliveryType?.message}
                />
              )}
            />
          </WGrid>
        </WorkflowSection>

        {/* Material Selection Section */}
        <WorkflowSection title={t('materialSelection')}>
          <WGrid item xs={12} md={4}>
            <SelectBox
              label={t('materialGroup')}
              value={selectedGroup ? materialGroups.find((g) => g.value === selectedGroup) || null : null}
              onChange={handleMaterialGroupChange}
              options={materialGroups}
              disabled={!enabilitySettings.stationeryRequestDetailDetails}
              fullWidth
            />
          </WGrid>
          <WGrid item xs={12} md={4}>
            <SelectBox
              label={t('materialType')}
              value={selectedType ? materialTypes.find((t) => t.value === selectedType) || null : null}
              onChange={(option: any) => setSelectedType(option?.value || '')}
              options={materialTypes}
              disabled={!enabilitySettings.stationeryRequestDetailDetails || !selectedGroup}
              fullWidth
            />
          </WGrid>
          <WGrid item xs={12} md={2}>
            <DigiTextField
              label={t('quantity')}
              type="number"
              value={requestedQuantity}
              onChange={(value) => setRequestedQuantity(value)}
              disabled={!enabilitySettings.stationeryRequestDetailDetails}
              fullWidth
            />
          </WGrid>
          <WGrid item xs={12} md={2}>
            <WButton
              variant="contained"
              color="primary"
              onClick={handleAddMaterial}
              disabled={!enabilitySettings.stationeryRequestDetailDetails || !selectedGroup || !selectedType || !requestedQuantity}
              fullWidth
              style={{ height: '56px' }}
            >
              {t('add')}
            </WButton>
          </WGrid>
        </WorkflowSection>

        <WorkflowSection title={t('stationeryDetails')}>
          <WGrid item xs={12}>
            <DigiTable
              data={stationeryRequestDetailDetails}
              columns={stationeryDetailColumns}
              actions={[
                {
                  icon: 'edit',
                  tooltip: t('common:edit'),
                  onClick: (row: any, index: any) => handleEditDetail(row, index),
                  disabled: !enabilitySettings.stationeryRequestDetailDetails,
                },
                {
                  icon: 'delete',
                  tooltip: t('common:delete'),
                  onClick: (_row: any, index: any) => handleDeleteDetail(index),
                  disabled: !enabilitySettings.stationeryRequestDetailDetails,
                },
              ]}
              pageSize={10}
            />
            {errors.StationeryRequestDetailDetails && (
              <div style={{ color: 'red', marginTop: 8 }}>{errors.StationeryRequestDetailDetails.message}</div>
            )}
          </WGrid>
        </WorkflowSection>

        <WorkflowSection title={t('addressDetails')}>
          <WGrid item xs={12}>
            <WButton
              variant="contained"
              color="primary"
              onClick={handleAddAddress}
              disabled={!enabilitySettings.stationeryAddressDetailRequestDetails}
              style={{ marginBottom: 16 }}
            >
              {t('addAddress')}
            </WButton>
            <DigiTable
              data={stationeryAddressDetailRequestDetails}
              columns={addressDetailColumns}
              actions={[
                {
                  icon: 'edit',
                  tooltip: t('common:edit'),
                  onClick: (row: any, index: any) => handleEditAddress(row, index),
                  disabled: !enabilitySettings.stationeryAddressDetailRequestDetails,
                },
                {
                  icon: 'delete',
                  tooltip: t('common:delete'),
                  onClick: (_row: any, index: any) => handleDeleteAddress(index),
                  disabled: !enabilitySettings.stationeryAddressDetailRequestDetails,
                },
              ]}
              pageSize={10}
            />
          </WGrid>
        </WorkflowSection>

        <WorkflowSection title={t('additionalInformation')}>
          <WGrid item xs={12} md={9}>
            <Controller
              name="Notes"
              control={control}
              render={({ field }) => <DigiTextField {...field} label={t('notes')} multiline rows={4} disabled={!enabilitySettings.notes} fullWidth />}
            />
          </WGrid>
          <WGrid item xs={12} md={3}>
            <Controller
              name="TotalAmount"
              control={control}
              render={({ field }) => <DigiTextField {...field} label={t('totalAmount')} type="number" disabled fullWidth readOnly />}
            />
          </WGrid>
        </WorkflowSection>

        <WGrid item xs={12}>
          <WButton onClick={handleSubmit(onSubmit)} variant="contained" color="primary" fullWidth>
            {t('common:save')}
          </WButton>
        </WGrid>
      </WGrid>

      {/* Detail Modal */}
      {detailModalOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0,0,0,0.5)',
            zIndex: 1000,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <WCard style={{ width: 600, maxWidth: '90vw' }}>
            <div style={{ padding: 20 }}>
              <h3>{editingDetailIndex !== null ? t('editStationeryItem') : t('addStationeryItem')}</h3>
              <WGrid container spacing={2}>
                <WGrid item xs={12} md={6}>
                  <DigiTextField label={t('materialGroup')} value={selectedDetailRow?.GroupName || ''} disabled fullWidth />
                </WGrid>
                <WGrid item xs={12} md={6}>
                  <DigiTextField label={t('material')} value={selectedDetailRow?.TypeName || ''} disabled fullWidth />
                </WGrid>
                <WGrid item xs={12} md={6}>
                  <DigiTextField
                    label={t('requestedQuantity')}
                    type="number"
                    value={selectedDetailRow?.RequestedQuantity || ''}
                    onChange={(value) => setSelectedDetailRow({ ...selectedDetailRow, RequestedQuantity: Number(value) })}
                    fullWidth
                  />
                </WGrid>
                <WGrid item xs={12} md={6}>
                  <DigiTextField label={t('unit')} value={selectedDetailRow?.Unit || ''} disabled fullWidth />
                </WGrid>
                <WGrid item xs={12}>
                  <DigiTextField
                    label={t('description')}
                    multiline
                    rows={3}
                    value={selectedDetailRow?.Description || ''}
                    onChange={(value) => setSelectedDetailRow({ ...selectedDetailRow, Description: value })}
                    fullWidth
                  />
                </WGrid>
              </WGrid>
              <div style={{ marginTop: 20, display: 'flex', justifyContent: 'flex-end' }}>
                <WButton onClick={handleSaveDetail} variant="contained" color="primary">
                  {t('common:save')}
                </WButton>
                <WButton
                  onClick={() => {
                    setDetailModalOpen(false)
                    setSelectedDetailRow(null)
                    setEditingDetailIndex(null)
                  }}
                  style={{ marginLeft: 8 }}
                >
                  {t('common:cancel')}
                </WButton>
              </div>
            </div>
          </WCard>
        </div>
      )}

      {/* Address Modal */}
      {addressModalOpen &&
        (() => {
          const addressTypes: IOption[] = [
            { value: 'addressTypeDelivery', label: t('addressTypeDelivery'), labelEn: t('addressTypeDelivery') },
            { value: 'addressTypeBilling', label: t('addressTypeBilling'), labelEn: t('addressTypeBilling') },
          ]
          return (
            <div
              style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0,0,0,0.5)',
                zIndex: 1000,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <WCard style={{ width: 800, maxWidth: '90vw' }}>
                <div style={{ padding: 20 }}>
                  <h3>{editingAddressIndex !== null ? t('editAddress') : t('addAddress')}</h3>
                  <WGrid container spacing={2} style={{ marginTop: 16 }}>
                    <WGrid item xs={12} md={6}>
                      <SelectBox
                        label={t('addressType')}
                        value={addressTypes.find((at) => at.value === selectedAddressRow?.AddressType) || null}
                        onChange={(option: any) => setSelectedAddressRow({ ...selectedAddressRow, AddressType: option?.value || '' })}
                        options={addressTypes}
                        fullWidth
                      />
                    </WGrid>
                    <WGrid item xs={12} md={6}>
                      <DigiTextField
                        label={t('recipientName')}
                        value={selectedAddressRow?.RecipientName || ''}
                        onChange={(value) => setSelectedAddressRow({ ...selectedAddressRow, RecipientName: value })}
                        fullWidth
                      />
                    </WGrid>
                    <WGrid item xs={12}>
                      <DigiTextField
                        label={t('fullAddress')}
                        multiline
                        rows={3}
                        value={selectedAddressRow?.FullAddress || ''}
                        onChange={(value) => setSelectedAddressRow({ ...selectedAddressRow, FullAddress: value })}
                        fullWidth
                      />
                    </WGrid>
                    <WGrid item xs={12} md={4}>
                      <DigiTextField
                        label={t('city')}
                        value={selectedAddressRow?.City || ''}
                        onChange={(value) => setSelectedAddressRow({ ...selectedAddressRow, City: value })}
                        fullWidth
                      />
                    </WGrid>
                    <WGrid item xs={12} md={4}>
                      <DigiTextField
                        label={t('district')}
                        value={selectedAddressRow?.District || ''}
                        onChange={(value) => setSelectedAddressRow({ ...selectedAddressRow, District: value })}
                        fullWidth
                      />
                    </WGrid>
                    <WGrid item xs={12} md={4}>
                      <DigiTextField
                        label={t('postalCode')}
                        value={selectedAddressRow?.PostalCode || ''}
                        onChange={(value) => setSelectedAddressRow({ ...selectedAddressRow, PostalCode: value })}
                        fullWidth
                      />
                    </WGrid>
                    <WGrid item xs={12} md={6}>
                      <DigiTextField
                        label={t('contactPerson')}
                        value={selectedAddressRow?.ContactPerson || ''}
                        onChange={(value) => setSelectedAddressRow({ ...selectedAddressRow, ContactPerson: value })}
                        fullWidth
                      />
                    </WGrid>
                    <WGrid item xs={12} md={6}>
                      <DigiTextField
                        label={t('contactPhone')}
                        value={selectedAddressRow?.ContactPhone || ''}
                        onChange={(value) => setSelectedAddressRow({ ...selectedAddressRow, ContactPhone: value })}
                        fullWidth
                      />
                    </WGrid>
                    <WGrid item xs={12}>
                      <DigiTextField
                        label={t('deliveryInstructions')}
                        multiline
                        rows={2}
                        value={selectedAddressRow?.DeliveryInstructions || ''}
                        onChange={(value) =>
                          setSelectedAddressRow({
                            ...selectedAddressRow,
                            DeliveryInstructions: value,
                          })
                        }
                        fullWidth
                      />
                    </WGrid>
                  </WGrid>
                  <div style={{ marginTop: 20, display: 'flex', justifyContent: 'flex-end' }}>
                    <WButton onClick={handleSaveAddress} variant="contained" color="primary">
                      {t('common:save')}
                    </WButton>
                    <WButton onClick={() => setAddressModalOpen(false)} style={{ marginLeft: 8 }}>
                      {t('common:cancel')}
                    </WButton>
                  </div>
                </div>
              </WCard>
            </div>
          )
        })()}
    </>
  )
}

export default StationeryRequestScreen
