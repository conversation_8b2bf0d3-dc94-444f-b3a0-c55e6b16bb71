import React, { useEffect } from 'react'
import dayjs from 'dayjs'
import { useFormContext, Controller } from 'react-hook-form'
import { useUpdateEffect, useLocalStorage, useUpdateEntity } from '@/hooks'
import { useTranslation } from 'react-i18next'
import { useWorkflow } from '@/contexts/WorkflowContext'
import { WGrid, WButton, WCircularProgress } from 'wface'
import { useLocalStorage, useUpdateEntity } from '@/hooks'
import toast from 'react-hot-toast'
import { DigiTextField } from '@/components/formElements/DigiTextField/DigiTextField'
import DigiDatePicker from '@/components/formElements/DigiDatePicker/DigiDatePicker'
import CharacterLimitContainer from '@/components/formElements/CharacterLimitContainer/CharacterLimitContainer'
import { Loading } from '@/components/Loading/Loading'
import { WorkflowSection } from '@/components/workflowComponents'

interface LeaveFormData {
  WorkflowDefId?: number
  CreatedBy?: number
  Created?: string
  LastUpdatedBy?: number
  LastUpdated?: string
  LeaveRequestId?: string
  OwnerLogınId?: string
  LeaveDayCount?: string
  StartTıme?: Date | null
  IsStartHalfDay?: string
  EndTıme?: Date | null
  IsEndHalfDay?: string
  Descrıptıon?: string
  IsNotDelegatıon?: string
  LeaveHourCount?: string
}

const LeaveScreen: React.FC = () => {
  const { t } = useTranslation(['LeaveSchema', 'common'])
  const { initialData, setDefinitionId, setUpdateEntity } = useWorkflow()
  const {
    control,
    reset,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useFormContext<LeaveFormData>()
  const [storedValue] = useLocalStorage<number>('UserId')
  const updateEntityMutation = useUpdateEntity()

  useUpdateEffect(() => {
    if (initialData?.entity) {
      reset({
        LeaveRequestId: initialData.entity.LeaveRequestId ?? '',
        OwnerLogınId: initialData.entity.OwnerLogınId ?? '',
        LeaveDayCount: initialData.entity.LeaveDayCount ?? '',
        StartTıme: initialData.entity.StartTıme ? new Date(initialData.entity.StartTıme) : null,
        IsStartHalfDay: initialData.entity.IsStartHalfDay ?? '',
        EndTıme: initialData.entity.EndTıme ? new Date(initialData.entity.EndTıme) : null,
        IsEndHalfDay: initialData.entity.IsEndHalfDay ?? '',
        Descrıptıon: initialData.entity.Descrıptıon ?? '',
        IsNotDelegatıon: initialData.entity.IsNotDelegatıon ?? '',
        LeaveHourCount: initialData.entity.LeaveHourCount ?? '',
      })
      setUpdateEntity({
        id: initialData.entity.requestId,
        properties: {
          LeaveRequestId: initialData.entity.LeaveRequestId ?? '',
          OwnerLogınId: initialData.entity.OwnerLogınId ?? '',
          LeaveDayCount: initialData.entity.LeaveDayCount ?? '',
          StartTıme: initialData.entity.StartTıme ? dayjs(initialData.entity.StartTıme).format('YYYY-MM-DDTHH:mm:ss') : null,
          IsStartHalfDay: initialData.entity.IsStartHalfDay ?? '',
          EndTıme: initialData.entity.EndTıme ? dayjs(initialData.entity.EndTıme).format('YYYY-MM-DDTHH:mm:ss') : null,
          IsEndHalfDay: initialData.entity.IsEndHalfDay ?? '',
          Descrıptıon: initialData.entity.Descrıptıon ?? '',
          IsNotDelegatıon: initialData.entity.IsNotDelegatıon ?? '',
          LeaveHourCount: initialData.entity.LeaveHourCount ?? '',
          LastUpdated: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
          LastUpdatedBy: storedValue,
        },
        emailRule: null,
      })
    }
  }, [initialData])

  useEffect(() => {
    setDefinitionId(1313)
    setValue('WorkflowDefId', 1313)
    if (!initialData?.entity) {
      setValue('CreatedBy', storedValue)
      setValue('Created', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
    } else {
      setValue('CreatedBy', initialData.entity.createdBy)
      setValue('Created', initialData.entity.created)
      setValue('LastUpdatedBy', storedValue)
      setValue('LastUpdated', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
    }
  }, [setDefinitionId, setValue, initialData, storedValue])

  const onSubmit = async (data: any) => {
    if (!initialData?.entity?.requestId) {
      toast.error(t('entityIdNotFound'))
      return
    }

    try {
      const formData = {
        LeaveRequestId: data.LeaveRequestId,
        OwnerLogınId: data.OwnerLogınId,
        LeaveDayCount: data.LeaveDayCount,
        StartTıme: data.StartTıme ? dayjs(data.StartTıme).format('YYYY-MM-DDTHH:mm:ss') : null,
        IsStartHalfDay: data.IsStartHalfDay,
        EndTıme: data.EndTıme ? dayjs(data.EndTıme).format('YYYY-MM-DDTHH:mm:ss') : null,
        IsEndHalfDay: data.IsEndHalfDay,
        Descrıptıon: data.Descrıptıon,
        IsNotDelegatıon: data.IsNotDelegatıon,
        LeaveHourCount: data.LeaveHourCount,
        LastUpdated: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
        LastUpdatedBy: storedValue,
      }

      await updateEntityMutation.mutateAsync({
        request: {
          id: initialData.entity.requestId,
          properties: formData,
          emailRule: null,
        },
        workflowName: 'leave',
      })
    } catch (error) {
      console.error('Update error:', error)
      toast.error(t('updateError'))
    }
  }

  const handleSave = () => {
    handleSubmit(onSubmit)()
  }

  return (
    <>
      {!initialData ? (
        <Loading show={true} />
      ) : (
        <WGrid container spacing={3}>
          {/* Leave Request Information Section */}
          <WorkflowSection title={t('leaveRequestInfo')}>
            {initialData?.visibilitySettings?.canSeeLeaveRequestId && (
              <WGrid item xs={12} md={6}>
                <Controller
                  name="LeaveRequestId"
                  control={control}
                  render={({ field }) => (
                    <CharacterLimitContainer name="LeaveRequestId" control={control}>
                      <DigiTextField
                        label={t('fields.LeaveRequestId')}
                        value={field.value || ''}
                        onChange={field.onChange}
                        error={errors && errors.LeaveRequestId?.message ? errors.LeaveRequestId.message.toString() : ''}
                        type="number"
                        inputMode="numeric"
                        disabled={!initialData?.enabilitySettings?.canEditLeaveRequestId}
                      />
                    </CharacterLimitContainer>
                  )}
                />
              </WGrid>
            )}

            {initialData?.visibilitySettings?.canSeeOwnerLogınId && (
              <WGrid item xs={12} md={6}>
                <Controller
                  name="OwnerLogınId"
                  control={control}
                  render={({ field }) => (
                    <CharacterLimitContainer name="OwnerLogınId" control={control}>
                      <DigiTextField
                        label={t('fields.OwnerLogınId')}
                        value={field.value || ''}
                        onChange={field.onChange}
                        error={errors && errors.OwnerLogınId?.message ? errors.OwnerLogınId.message.toString() : ''}
                        type="number"
                        inputMode="numeric"
                        disabled={!initialData?.enabilitySettings?.canEditOwnerLogınId}
                      />
                    </CharacterLimitContainer>
                  )}
                />
              </WGrid>
            )}

            {initialData?.visibilitySettings?.canSeeLeaveDayCount && (
              <WGrid item xs={12} md={6}>
                <Controller
                  name="LeaveDayCount"
                  control={control}
                  render={({ field }) => (
                    <CharacterLimitContainer name="LeaveDayCount" control={control}>
                      <DigiTextField
                        label={t('fields.LeaveDayCount')}
                        value={field.value || ''}
                        onChange={field.onChange}
                        error={errors && errors.LeaveDayCount?.message ? errors.LeaveDayCount.message.toString() : ''}
                        type="number"
                        inputMode="numeric"
                        disabled={!initialData?.enabilitySettings?.canEditLeaveDayCount}
                      />
                    </CharacterLimitContainer>
                  )}
                />
              </WGrid>
            )}

            {initialData?.visibilitySettings?.canSeeLeaveHourCount && (
              <WGrid item xs={12} md={6}>
                <Controller
                  name="LeaveHourCount"
                  control={control}
                  render={({ field }) => (
                    <CharacterLimitContainer name="LeaveHourCount" control={control}>
                      <DigiTextField
                        label={t('fields.LeaveHourCount')}
                        value={field.value || ''}
                        onChange={field.onChange}
                        error={errors && errors.LeaveHourCount?.message ? errors.LeaveHourCount.message.toString() : ''}
                        type="number"
                        inputMode="numeric"
                        disabled={!initialData?.enabilitySettings?.canEditLeaveHourCount}
                      />
                    </CharacterLimitContainer>
                  )}
                />
              </WGrid>
            )}
          </WorkflowSection>

          {/* Leave Dates Section */}
          <WorkflowSection title={t('leaveDates')}>
            {initialData?.visibilitySettings?.canSeeStartTıme && (
              <WGrid item xs={12} md={6}>
                <Controller
                  name="StartTıme"
                  control={control}
                  render={({ field }) => (
                    <DigiDatePicker
                      label={t('fields.StartTıme')}
                      value={field.value ? dayjs(field.value).toDate() : null}
                      onChange={(date) => {
                        field.onChange(date ? dayjs(date).format('YYYY-MM-DDTHH:mm:ss') : null)
                      }}
                      error={errors && errors.StartTıme?.message ? errors.StartTıme.message.toString() : ''}
                      disabled={!initialData?.enabilitySettings?.canEditStartTıme}
                    />
                  )}
                />
              </WGrid>
            )}

            {initialData?.visibilitySettings?.canSeeIsStartHalfDay && (
              <WGrid item xs={12} md={6}>
                <Controller
                  name="IsStartHalfDay"
                  control={control}
                  render={({ field }) => (
                    <CharacterLimitContainer name="IsStartHalfDay" control={control}>
                      <DigiTextField
                        label={t('fields.IsStartHalfDay')}
                        value={field.value || ''}
                        onChange={field.onChange}
                        error={errors && errors.IsStartHalfDay?.message ? errors.IsStartHalfDay.message.toString() : ''}
                        type="number"
                        inputMode="numeric"
                        disabled={!initialData?.enabilitySettings?.canEditIsStartHalfDay}
                      />
                    </CharacterLimitContainer>
                  )}
                />
              </WGrid>
            )}

            {initialData?.visibilitySettings?.canSeeEndTıme && (
              <WGrid item xs={12} md={6}>
                <Controller
                  name="EndTıme"
                  control={control}
                  render={({ field }) => (
                    <DigiDatePicker
                      label={t('fields.EndTıme')}
                      value={field.value ? dayjs(field.value).toDate() : null}
                      onChange={(date) => {
                        field.onChange(date ? dayjs(date).format('YYYY-MM-DDTHH:mm:ss') : null)
                      }}
                      error={errors && errors.EndTıme?.message ? errors.EndTıme.message.toString() : ''}
                      disabled={!initialData?.enabilitySettings?.canEditEndTıme}
                    />
                  )}
                />
              </WGrid>
            )}

            {initialData?.visibilitySettings?.canSeeIsEndHalfDay && (
              <WGrid item xs={12} md={6}>
                <Controller
                  name="IsEndHalfDay"
                  control={control}
                  render={({ field }) => (
                    <CharacterLimitContainer name="IsEndHalfDay" control={control}>
                      <DigiTextField
                        label={t('fields.IsEndHalfDay')}
                        value={field.value || ''}
                        onChange={field.onChange}
                        error={errors && errors.IsEndHalfDay?.message ? errors.IsEndHalfDay.message.toString() : ''}
                        type="number"
                        inputMode="numeric"
                        disabled={!initialData?.enabilitySettings?.canEditIsEndHalfDay}
                      />
                    </CharacterLimitContainer>
                  )}
                />
              </WGrid>
            )}
          </WorkflowSection>

          {/* Description and Additional Info Section */}
          <WorkflowSection title={t('additionalInfo')}>
            {initialData?.visibilitySettings?.canSeeDescrıptıon && (
              <WGrid item xs={12}>
                <Controller
                  name="Descrıptıon"
                  control={control}
                  render={({ field }) => (
                    <CharacterLimitContainer name="Descrıptıon" control={control}>
                      <DigiTextField
                        label={t('fields.Descrıptıon')}
                        value={field.value || ''}
                        onChange={field.onChange}
                        error={errors && errors.Descrıptıon?.message ? errors.Descrıptıon.message.toString() : ''}
                        multiline
                        rows={4}
                        disabled={!initialData?.enabilitySettings?.canEditDescrıptıon}
                      />
                    </CharacterLimitContainer>
                  )}
                />
              </WGrid>
            )}

            {initialData?.visibilitySettings?.canSeeIsNotDelegatıon && (
              <WGrid item xs={12} md={6}>
                <Controller
                  name="IsNotDelegatıon"
                  control={control}
                  render={({ field }) => (
                    <CharacterLimitContainer name="IsNotDelegatıon" control={control}>
                      <DigiTextField
                        label={t('fields.IsNotDelegatıon')}
                        value={field.value || ''}
                        onChange={field.onChange}
                        error={errors && errors.IsNotDelegatıon?.message ? errors.IsNotDelegatıon.message.toString() : ''}
                        type="number"
                        inputMode="numeric"
                        disabled={!initialData?.enabilitySettings?.canEditIsNotDelegatıon}
                      />
                    </CharacterLimitContainer>
                  )}
                />
              </WGrid>
            )}

            {initialData?.visibilitySettings?.canSeeLastUpdated && (
              <WGrid item xs={12} md={6}>
                <Controller
                  name="LastUpdated"
                  control={control}
                  render={({ field }) => (
                    <DigiDatePicker
                      label={t('fields.LastUpdated')}
                      value={field.value ? dayjs(field.value).toDate() : null}
                      onChange={(date) => {
                        field.onChange(date ? dayjs(date).format('YYYY-MM-DDTHH:mm:ss') : null)
                      }}
                      error={errors && errors.LastUpdated?.message ? errors.LastUpdated.message.toString() : ''}
                      disabled={!initialData?.enabilitySettings?.canEditLastUpdated}
                    />
                  )}
                />
              </WGrid>
            )}

            {initialData?.visibilitySettings?.canSeeLastUpdatedBy && (
              <WGrid item xs={12} md={6}>
                <Controller
                  name="LastUpdatedBy"
                  control={control}
                  render={({ field }) => (
                    <CharacterLimitContainer name="LastUpdatedBy" control={control}>
                      <DigiTextField
                        label={t('fields.LastUpdatedBy')}
                        value={field.value || ''}
                        onChange={field.onChange}
                        error={errors && errors.LastUpdatedBy?.message ? errors.LastUpdatedBy.message.toString() : ''}
                        type="number"
                        inputMode="numeric"
                        disabled={!initialData?.enabilitySettings?.canEditLastUpdatedBy}
                      />
                    </CharacterLimitContainer>
                  )}
                />
              </WGrid>
            )}

            {initialData?.visibilitySettings?.canSeeUpdateButton && (
              <WGrid item xs={12}>
                <div style={{ textAlign: 'center', marginTop: '16px' }}>
                  <WButton
                    variant="contained"
                    onClick={handleSave}
                    disabled={
                      updateEntityMutation.isPending || !initialData?.entity?.requestId || !initialData?.enabilitySettings?.canPressUpdateButton
                    }
                  >
                    {updateEntityMutation.isPending && <WCircularProgress size={20} color="inherit" style={{ marginRight: 8 }} />}
                    {t('update')}
                  </WButton>
                </div>
              </WGrid>
            )}
          </WorkflowSection>
        </WGrid>
      )}
    </>
  )
}

export default LeaveScreen
