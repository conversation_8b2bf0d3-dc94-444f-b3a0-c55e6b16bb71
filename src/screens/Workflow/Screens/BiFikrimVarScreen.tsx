import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useFormContext, Controller, useWatch } from 'react-hook-form'
import { useWorkflow } from '@/contexts/WorkflowContext'
import dayjs from 'dayjs'
import { W<PERSON><PERSON>on, WCircularProgress, WGrid } from 'wface'
import { useGetSuggesters, useGetSuggestersTeam } from '@/hooks/PermissionHooks/PermissionProcessHooks'
import { EntityFieldType, IFile, IOption } from '@/types'
import { useQueryClient } from '@tanstack/react-query'
import CharacterLimitContainer from '@/components/formElements/CharacterLimitContainer/CharacterLimitContainer'
import { useLocalStorage, useUpdateEffect, useUpdateEntity } from '@/hooks'
import { FileUpload, SelectBox } from '@/components/formElements'
import { OrganizationTree } from '@/components/workflowComponents'
import toast from 'react-hot-toast'
import DigiDatePicker from '@/components/formElements/DigiDatePicker/DigiDatePicker'
import { DigiTextField } from '@/components/formElements/DigiTextField/DigiTextField'
import { useUserStore } from '@/stores/userStore'

const BiFikrimVarScreen = () => {
  const { t } = useTranslation(['biFikrimVarSchema'])
  const [storedValue] = useLocalStorage<number>('UserId')
  const { selectedUser } = useUserStore()
  const { initialData, updateEntity, wfInstanceId, setUpdateEntity, orgTreeInitialData, setOrgTreeInitialData, setDefinitionId } = useWorkflow()

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
    reset,
  } = useFormContext()

  const queryClient = useQueryClient()
  const [documents, setDocuments] = useState<any>([])
  const [detailJson, setDetailJson] = useState<any[]>([])
  const updateEntityMutation = useUpdateEntity()

  const [suggesters, setSuggesters] = useState<IOption[]>(initialData?.suggesters || [])
  const [suggestersTeam, setSuggestersTeam] = useState<string>(initialData?.entity?.team || '')

  const selectedLocation = useWatch({ control, name: 'Location' }) as string
  const selectedSuggester = useWatch({ control, name: 'Suggester' }) as string

  const { data: fetchedSuggesters, isLoading: isLoadingSuggesters } = useGetSuggesters({ location: selectedLocation }, !!selectedLocation)
  const { data: fetchedSuggestersTeam } = useGetSuggestersTeam({ suggester: selectedSuggester }, !!selectedSuggester)

  const onSubmit = async (data: any) => {
    if (!initialData?.entity?.biFikrimVarRequestId) {
      toast.error(t('entityIdNotFound'))
      return
    }

    try {
      const formData = {
        DelegationNote: data.DelegationNote,
        ExecutionDate: data.ExecutionDate,
        PlanDate: data.PlanDate,
        LastUpdated: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
        LastUpdatedBy: storedValue,
      }

      // Create email rule if execution date has changed
      let emailRule = null

      if (initialData.entity.executionDate !== data.ExecutionDate) {
        emailRule = {
          MailTemplateId: 100036, // In both live and test server, mailTemplateId for this template is the same
          LogicalGroupName: 'BiFikrimVar_HeyetDegerlendirme',
          WorkflowInstanceId: wfInstanceId,
          EntityFields: {
            CreatedBy: EntityFieldType.LoginId,
            Suggester: EntityFieldType.Username,
          },
          CustomParameters: {
            'wfParameter.WfDefName': 'BiFikrimVar',
            'wfParameter.WfWorkflowInstanceId': wfInstanceId.toString(),
            'wfParameter.OldExecutionDate': initialData.entity.executionDate ? dayjs(initialData.entity.executionDate).format('DD.MM.YYYY') : '',
            'wfParameter.NewExecutionDate': dayjs(data.ExecutionDate).format('DD.MM.YYYY'),
          },
        }
      }

      await updateEntityMutation.mutateAsync({
        request: {
          id: initialData.entity.biFikrimVarRequestId,
          properties: formData,
          emailRule: emailRule,
        },
        workflowName: 'bifikrimvar',
      })
    } catch (error) {
      console.error('Update error:', error)
      toast.error(t('updateError'))
    }
  }

  const handleSave = async () => {
    handleSubmit(onSubmit)()
  }

  useUpdateEffect(() => {
    if (initialData?.entity) {
      reset({
        Subject: initialData?.entity.subject || '',
        SuggestionDetail: initialData?.entity.suggestionDetail || '',
        Benefit: initialData?.entity.benefit || '',
        Location: initialData?.entity.location || '',
        Suggester: initialData?.entity.suggester || '',
        PlanDate: initialData?.entity.planDate || null,
        ExecutionDate: initialData?.entity.executionDate || null,
        DelegationNote: initialData?.entity.delegationNote || '',
        SuggestersTeam: initialData?.entity?.suggestersTeam || '',
      })
      setUpdateEntity({
        id: initialData.entity.biFikrimVarRequestId,
        properties: {
          PlanDate: initialData?.entity.planDate || null,
          ExecutionDate: initialData?.entity.executionDate || null,
          DelegationNote: initialData?.entity.delegationNote || '',
          LastUpdated: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
          LastUpdatedBy: storedValue,
        },
        emailRule: null,
      })

      const uploadedDocs =
        initialData?.files?.map((file: any) => {
          return {
            url: file.completeUrl || '',
            size: 0,
            name: file.attachLink || '',
          }
        }) ?? []
      setDocuments(uploadedDocs)
      setSuggesters(initialData?.entity.suggesters || [])
      setSuggestersTeam(initialData?.entity?.suggestersTeam || '')
    }
  }, [initialData, reset])

  useUpdateEffect(() => {
    if (fetchedSuggesters) {
      setSuggesters(fetchedSuggesters)
    }
  }, [fetchedSuggesters])

  useUpdateEffect(() => {
    if (fetchedSuggestersTeam) {
      setValue('SuggestersTeam', fetchedSuggestersTeam)
      setSuggestersTeam(fetchedSuggestersTeam)
    }
  }, [fetchedSuggestersTeam])

  useUpdateEffect(() => {
    if (initialData?.organizationHierarchy) {
      setOrgTreeInitialData(initialData?.organizationHierarchy)
    }
    setValue('CreatorFullname', selectedUser?.label.split(' - ')[0])
  }, [initialData, setOrgTreeInitialData, selectedUser])

  useEffect(() => {
    setDefinitionId(2932)
    setValue('WorkflowDefId', 2932)
    if (!initialData?.entity) {
      setValue('CreatedBy', storedValue)
      setValue('Created', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
    } else {
      setValue('CreatedBy', initialData?.entity?.createdBy)
      setValue('Created', initialData?.entity?.created)
      setValue('LastUpdatedBy', storedValue)
      setValue('LastUpdated', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
    }
  }, [setDefinitionId, setValue, initialData, storedValue, setDefinitionId])

  const handleFileUpload = (files: IFile[]) => {
    const newDetailJson: any[] = files.map((file, index) => ({
      DetailRequestId: Date.now() + index,
      RelatedRequestId: 0,
      Type: 1,
      AttachLink: file.name,
      VersionId: 0,
    }))

    setDetailJson((prevDetailJson) => [...prevDetailJson, ...newDetailJson])
    setValue('DetailJson', [...detailJson, ...newDetailJson])
    queryClient.invalidateQueries({ queryKey: ['documents'] })
  }

  const handleFileDelete = (fileName: string) => {
    setDetailJson((prevDetailJson) => prevDetailJson.filter((detail) => detail.AttachLink !== fileName))
    setValue(
      'DetailJson',
      detailJson.filter((detail) => detail.AttachLink !== fileName),
    )
    queryClient.invalidateQueries({ queryKey: ['documents'] })
  }

  return (
    <WGrid container spacing={3}>
      {/* Organization Section */}
      <WGrid item xs={12}>
        <div
          style={{
            marginBottom: '24px',
            padding: '20px',
            backgroundColor: '#f9f9f9',
            borderRadius: '8px',
            boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
          }}
        >
          <h3
            style={{
              fontSize: '18px',
              color: '#5c2d91',
              marginBottom: '16px',
              paddingBottom: '12px',
              borderBottom: '1px solid #e0e0e0',
            }}
          >
            {t('organizationInfo')}
          </h3>
          <OrganizationTree
            multiple={false}
            showText={true}
            setSelected={(value) => setValue('DelegatedLoginId', value)}
            initialSelections={orgTreeInitialData}
            reload={!!orgTreeInitialData}
            disable={{
              all: !initialData?.enabilitySettings?.canEditOrgTree,
            }}
          />
        </div>
      </WGrid>
      {/* Idea Information Section */}
      <WGrid item xs={12}>
        <div
          style={{
            marginBottom: '24px',
            padding: '20px',
            backgroundColor: '#f9f9f9',
            borderRadius: '8px',
            boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
          }}
        >
          <h3
            style={{
              fontSize: '18px',
              color: '#5c2d91',
              marginBottom: '16px',
              paddingBottom: '12px',
              borderBottom: '1px solid #e0e0e0',
            }}
          >
            {t('ideaInfo')}
          </h3>
          {initialData?.visibilitySettings?.canSeeSubject && (
            <WGrid container spacing={2} style={{ marginBottom: '16px' }}>
              <WGrid item xs={12} md={6}>
                <Controller
                  name="Subject"
                  control={control}
                  render={({ field }) => (
                    <SelectBox
                      label={t('subject')}
                      value={field.value}
                      onChange={(value: any) => setValue('Subject', value?.value)}
                      options={[
                        { label: t('Diger'), labelEn: t('Diger'), value: 'Diğer' },
                        { label: t('Fatura'), labelEn: t('Fatura'), value: 'Fatura' },
                        { label: t('Kampanya'), labelEn: t('Kampanya'), value: 'Kampanya' },
                        { label: t('PaketServis'), labelEn: t('PaketServis'), value: 'Paket/Servis' },
                        { label: t('Teknik'), labelEn: t('Teknik'), value: 'Teknik' },
                        { label: t('TeknikServis'), labelEn: t('TeknikServis'), value: 'Teknik Servis' },
                        { label: t('Üyeİletisimleri'), labelEn: t('Üyeİletisimleri'), value: 'Üye İletişimleri (Mail, SMS)' },
                      ]}
                      size="small"
                      disabled={!initialData?.enabilitySettings?.canEditSubject}
                    />
                  )}
                />
              </WGrid>
            </WGrid>
          )}
          {initialData?.visibilitySettings?.canSeeSuggestionDetail && (
            <WGrid container spacing={2} style={{ marginBottom: '16px' }}>
              <WGrid item xs={12}>
                <CharacterLimitContainer name="SuggestionDetail" control={control}>
                  <Controller
                    name="SuggestionDetail"
                    control={control}
                    render={({ field }) => (
                      <DigiTextField
                        fullWidth
                        id="suggestionDetail"
                        label={t('suggestionDetail')}
                        value={field.value}
                        onChange={field.onChange}
                        error={!!errors?.SuggestionDetail?.message}
                        helperText={errors?.SuggestionDetail?.message?.toString() ?? ''}
                        disabled={!initialData?.enabilitySettings?.canEditSuggestionDetail}
                        multiline
                        size="small"
                        rows={4}
                        inputProps={{ maxLength: 2000 }}
                      />
                    )}
                  />
                </CharacterLimitContainer>
              </WGrid>
            </WGrid>
          )}
          {initialData?.visibilitySettings?.canSeeBenefit && (
            <div className="form-row">
              <div className="form-label required-field">{t('benefit')}</div>
              <div className="form-input" style={{ position: 'relative' }}>
                <CharacterLimitContainer name="Benefit" control={control}>
                  <Controller
                    name="Benefit"
                    control={control}
                    render={({ field }) => (
                      <DigiTextField
                        label={t('benefit')}
                        fullWidth
                        id="benefit"
                        value={field.value}
                        onChange={field.onChange}
                        error={!!errors?.Benefit?.message}
                        helperText={errors?.Benefit?.message?.toString() ?? ''}
                        disabled={!initialData?.enabilitySettings?.canEditBenefit}
                        multiline
                        rows={4}
                        size="small"
                        inputProps={{ maxLength: 2000 }}
                      />
                    )}
                  />
                </CharacterLimitContainer>
              </div>
            </div>
          )}
          {initialData?.visibilitySettings?.canSeeLocation && (
            <div className="form-row">
              <div className="form-label required-field">{t('location')}</div>
              <div className="form-input">
                <Controller
                  name="Location"
                  control={control}
                  render={({ field }) => (
                    <SelectBox
                      label={t('location')}
                      value={field.value}
                      defaultItem={{
                        value: '',
                        label: 'Seçiniz',
                        labelEn: 'Select',
                      }}
                      onChange={(value: any) => {
                        setValue('Location', value?.value)
                        setValue('Suggester', '') // Reset suggester when location changes
                        setValue('SuggestersTeam', '')
                        setValue('SuggesterFullname', selectedUser?.label?.split(' - ')[0])
                        setSuggesters([]) // Reset suggesters list
                        setSuggestersTeam('') // Reset suggesters team
                      }}
                      size="small"
                      options={initialData?.locations}
                      disabled={!initialData?.enabilitySettings?.canEditLocation}
                    />
                  )}
                />
              </div>
            </div>
          )}
          {initialData?.visibilitySettings?.canSeeSuggester && (
            <div className="form-row">
              <div className="form-label required-field">{t('suggester')}</div>
              <div className="form-input">
                <Controller
                  name="Suggester"
                  control={control}
                  render={({ field }) => (
                    <SelectBox
                      label={t('suggester')}
                      value={field.value}
                      onChange={(value: any) => {
                        setValue('Suggester', value?.value)
                        setValue('SuggesterFullname', value?.label?.split(' [')[0])
                        setSuggestersTeam('')
                      }}
                      isLoading={isLoadingSuggesters}
                      size="small"
                      options={suggesters}
                      disabled={!initialData?.enabilitySettings?.canEditSuggester || isLoadingSuggesters}
                    />
                  )}
                />
                {suggestersTeam !== '' && (
                  <div className="form-note">
                    <strong>{t('suggestersTeam')}</strong>: {suggestersTeam}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        {/* Date Settings Section */}
        {(initialData?.visibilitySettings?.canSeePlanDate || initialData?.visibilitySettings?.canSeeExecutionDate) && (
          <div className="section-container">
            <div className="section-title">{t('date_settings')}</div>
            <div className="form-row">
              {initialData?.visibilitySettings?.canSeePlanDate && (
                <div className="form-col" style={{ marginRight: '25px' }}>
                  <div className="form-label">{t('planDate')}</div>
                  <div className="form-input">
                    <Controller
                      name="PlanDate"
                      control={control}
                      render={({ field }) => (
                        <DigiDatePicker
                          id="planDate"
                          value={field.value}
                          onChange={(value) => {
                            if (value) {
                              field.onChange(dayjs(value).format('YYYY-MM-DDTHH:mm:ss'))
                              setUpdateEntity({
                                ...updateEntity,
                                properties: { ...updateEntity.properties, PlanDate: dayjs(value).format('YYYY-MM-DDTHH:mm:ss') },
                              })
                            } else {
                              field.onChange(null)
                              setUpdateEntity({
                                ...updateEntity,
                                properties: { ...updateEntity.properties, PlanDate: null },
                              })
                            }
                          }}
                          error={errors?.PlanDate?.message?.toString() ?? ''}
                          disabled={!initialData?.enabilitySettings?.canEditPlanDate}
                          size="small"
                        />
                      )}
                    />
                  </div>
                </div>
              )}
              {initialData?.visibilitySettings?.canSeeExecutionDate && (
                <div className="form-col" style={{ marginLeft: '25px' }}>
                  <div className="form-label">{t('executionDate')}</div>
                  <div className="form-input">
                    <Controller
                      name="ExecutionDate"
                      control={control}
                      render={({ field }) => (
                        <DigiDatePicker
                          id="executionDate"
                          value={field.value}
                          onChange={(value) => {
                            if (value) {
                              const formattedDate = dayjs(value).format('YYYY-MM-DDTHH:mm:ss')
                              field.onChange(formattedDate)

                              // Create email rule if date has changed
                              const emailRule =
                                initialData.entity.executionDate !== formattedDate
                                  ? {
                                      MailTemplateId: 100036,
                                      LogicalGroupName: 'BiFikrimVar_HeyetDegerlendirme',
                                      WorkflowInstanceId: wfInstanceId,
                                      EntityFields: {
                                        CreatedBy: EntityFieldType.LoginId,
                                        Suggester: EntityFieldType.Username,
                                      },
                                      CustomParameters: {
                                        'wfParameter.WfDefName': 'BiFikrimVar',
                                        'wfParameter.WfWorkflowInstanceId': wfInstanceId.toString(),
                                        'wfParameter.OldExecutionDate': initialData.entity.executionDate
                                          ? dayjs(initialData.entity.executionDate).format('DD.MM.YYYY')
                                          : '',
                                        'wfParameter.NewExecutionDate': dayjs(value).format('DD.MM.YYYY'),
                                      },
                                    }
                                  : null

                              setUpdateEntity({
                                ...updateEntity,
                                properties: { ...updateEntity.properties, ExecutionDate: formattedDate },
                                emailRule: emailRule,
                              })
                            } else {
                              field.onChange(null)
                              setUpdateEntity({
                                ...updateEntity,
                                properties: { ...updateEntity.properties, ExecutionDate: null },
                                emailRule: null,
                              })
                            }
                          }}
                          error={errors?.ExecutionDate?.message?.toString() ?? ''}
                          disabled={!initialData?.enabilitySettings?.canEditExecutionDate}
                          size="small"
                        />
                      )}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        {/* Delegation Note Section */}
        {initialData?.visibilitySettings?.canSeeDelegationNote && (
          <div className="section-container">
            <div className="section-title">{t('delegation_note')}</div>
            <div className="form-row">
              <div className="form-input" style={{ flex: '0 0 97%' }}>
                <CharacterLimitContainer name="DelegationNote" control={control}>
                  <Controller
                    name="DelegationNote"
                    control={control}
                    render={({ field }) => (
                      <DigiTextField
                        label={t('delegationNote')}
                        fullWidth
                        id="delegationNote"
                        value={field.value}
                        onChange={(value) => {
                          field.onChange(value)
                          setUpdateEntity({
                            ...updateEntity,
                            properties: { ...updateEntity.properties, DelegationNote: value },
                          })
                        }}
                        error={!!errors?.DelegationNote?.message}
                        helperText={errors?.DelegationNote?.message?.toString() ?? ''}
                        disabled={!initialData?.enabilitySettings?.canEditDelegationNote}
                        multiline
                        rows={4}
                        inputProps={{ maxLength: 2000 }}
                      />
                    )}
                  />
                </CharacterLimitContainer>
              </div>
            </div>
            {initialData?.visibilitySettings?.canSeeUpdateButton && (
              <div className="form-row">
                <div className="form-input" style={{ flex: '0 0 100%', textAlign: 'center' }}>
                  <WButton
                    variant="contained"
                    onClick={handleSave}
                    disabled={
                      updateEntityMutation.isPending ||
                      !initialData?.entity?.biFikrimVarRequestId ||
                      !initialData?.enabilitySettings?.canPressUpdateButton
                    }
                    className="btn btn-primary"
                  >
                    {updateEntityMutation.isPending ? <WCircularProgress size={20} color="inherit" style={{ marginRight: 8 }} /> : null}
                    {t('update')}
                  </WButton>
                </div>
              </div>
            )}
          </div>
        )}

        {/* File Upload Section */}
        {initialData?.visibilitySettings?.canSeeUploadButton && (
          <div className="section-container">
            <div className="section-title">{t('file_upload')}</div>
            <div className="file-upload-section">
              <FileUpload
                onUpload={handleFileUpload}
                disabled={!initialData?.enabilitySettings?.canPressUploadButton}
                onDelete={handleFileDelete}
                pathKey="SharePointBiFikrimVarDocs"
                initialFiles={documents}
              />
              <Controller name="DetailJson" control={control} render={({ field }) => <input type="hidden" {...field} />} />
            </div>
          </div>
        )}
      </WGrid>
    </WGrid>
  )
}

export default BiFikrimVarScreen
