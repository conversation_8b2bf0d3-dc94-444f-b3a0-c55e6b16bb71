import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { FormProvider, useForm } from 'react-hook-form'
import StationeryRequestScreen from '../StationeryRequestScreen'
import React from 'react'

// Mock react-i18next
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
const mockT = vi.fn((key) => key)
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}))

// Mock dayjs
vi.mock('dayjs', () => ({
  default: vi.fn(() => ({
    format: vi.fn(() => '2023-01-01'),
  })),
}))

// Mock workflow context
const mockWorkflowContext = {
  initialData: {
    entity: {
      requestId: 123,
      StationeryRequestDetailDetails: [],
      StationeryAddressDetailRequestDetails: [],
    },
    visibilitySettings: {
      requestOwnerSelection: true,
    },
    enabilitySettings: {
      reqType: true,
      requestForUserId: true,
      packageId: true,
      requestDate: true,
      requestingDepartment: true,
      requestingPerson: true,
      locationId: true,
      floorId: true,
      address: true,
      requestReason: true,
      deliveryDate: true,
      deliveryType: true,
      stationeryRequestDetailDetails: true,
      stationeryAddressDetailRequestDetails: true,
      notes: true,
    },
    requestTypes: [,
      { value: '1', label: 'For Myself' },
      { value: '2', label: 'For Someone Else' },
    ],
    locations: [,
      { value: 'loc1', label: 'Location 1' },
      { value: 'loc2', label: 'Location 2' },
    ],
    floors: [,
      { value: 'floor1', label: 'Floor 1', locationId: 'loc1' },
      { value: 'floor2', label: 'Floor 2', locationId: 'loc1' },
    ],
    packages: [,
      { value: 'pkg1', label: 'Package 1' },
      { value: 'pkg2', label: 'Package 2' },
    ],
    materialGroups: [,
      { value: 'group1', label: 'Group 1', labelEn: 'Group 1' },
      { value: 'group2', label: 'Group 2', labelEn: 'Group 2' },
    ],
    materialTypes: [,
      { value: 'type1', label: 'Type 1', labelEn: 'Type 1', groupId: 'group1', unit: 'pcs', stockQuantity: 10 },
      { value: 'type2', label: 'Type 2', labelEn: 'Type 2', groupId: 'group1', unit: 'pcs', stockQuantity: 5 },
      { value: 'type3', label: 'Type 3', labelEn: 'Type 3', groupId: 'group2', unit: 'kg', stockQuantity: 20 },
    ],
    users: [,
      { value: 'user1', label: 'User 1' },
      { value: 'user2', label: 'User 2' },
    ],
    departments: [,
      { value: 'dept1', label: 'Department 1', labelEn: 'Department 1' },
      { value: 'dept2', label: 'Department 2', labelEn: 'Department 2' },
    ],
    deliveryTypes: [,
      { value: 'delivery1', label: 'Standard Delivery', labelEn: 'Standard Delivery' },
      { value: 'delivery2', label: 'Express Delivery', labelEn: 'Express Delivery' },
    ],
  },
  setDefinitionId: vi.fn(),
  setUpdateEntity: vi.fn(),
}

const mockUseWorkflow = vi.fn(() => mockWorkflowContext)
vi.mock('@/contexts/WorkflowContext', () => ({
  useWorkflow: mockUseWorkflow,
}))

// Mock hooks
vi.mock('@/hooks', () => ({
  useUpdateEffect: vi.fn(),
}))

// Mock types
vi.mock('@/types', () => ({
  IOption: {},
  IUploadedFile: {},
}))

// Mock wface components
vi.mock('wface', () => ({
  WGrid: ({ children, container, spacing, item, xs, md, ...props }: any) => (
    <div data-testid="wgrid" data-container={container} data-spacing={spacing} data-item={item} data-xs={xs} data-md={md} {...props}>
      {children}
    </div>
  ),
  WButton: ({ children, onClick, disabled, variant, color, style, fullWidth, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} data-variant={variant} data-color={color} data-fullwidth={fullWidth} style={style} {...props}>
      {children}
    </button>
  ),
  WCard: ({ children, style, ...props }: any) => (
    <div data-testid="wcard" style={style} {...props}>
      {children}
    </div>
  ),
}))

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    error: vi.fn(),
    success: vi.fn(),
  },
}))

// Mock form components
vi.mock('@/components/formElements/DigiTextField/DigiTextField', () => ({
  default: ({ label, value, onChange, disabled, multiline, rows, type, fullWidth, error, helperText, ...props }: any) => (
    <div>
      <input
        data-testid={`textfield-${label}`}
        placeholder={label}
        value={value ?? ''}
        onChange={(_e) => onChange?.(e.target.value)}
        disabled={disabled}
        type={type}
        {...props}
      />
      {error && <span data-testid={`error-${label}`}>{helperText}</span>}
    </div>
  ),
}))

vi.mock('@/components/formElements/DigiDatePicker/DigiDatePicker', () => ({
  default: ({ label, value, onChange, disabled, error, helperText, ...props }: any) => (
    <div>
      <input
        data-testid={`datepicker-${label}`}
        type="date"
        placeholder={label}
        value={value ? '2023-01-01' : ''}
        onChange={(_e) => onChange?.(new Date(e.target.value))}
        disabled={disabled}
        {...props}
      />
      {error && <span data-testid={`error-${label}`}>{helperText}</span>}
    </div>
  ),
}))

vi.mock('@/components/formElements', () => ({
  SelectBox: ({ label, value, onChange, options, disabled, error, fullWidth, ...props }: any) => (
    <div>
      <select
        data-testid={`selectbox-${label}`}
        value={value?.value ?? ''}
        onChange={(_e) => {{
          const selectedOption = options?.find((opt: any) => opt.value === _e.target.value)
          onChange?.(selectedOption)
        }}
      }
        disabled={disabled}
        {...props}
      >
        <option value="">Select...</option>
        {options?.map((option: any) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {error && <span data-testid={`error-${label}`}>{error}</span>}
    </div>
  ),
}))

vi.mock('@/components/workflowComponents', () => ({
  WorkflowSection: ({ title, children }: any) => (
    <div data-testid="workflow-section">
      <h3>{title}</h3>
      {children}
    </div>
  ),
}))

vi.mock('@/components/Tables/DigiTable/DigiTable', () => ({
  default: ({ columns, data, actions, pageSize }: any) => (
    <div data-testid="digi-table">
      <div data-testid="table-data">{JSON.stringify(data)}</div>
      <div data-testid="table-columns">{columns?.length} columns</div>
      <div data-testid="table-actions">{actions?.length} actions</div>
      <div data-testid="table-pagesize">{pageSize}</div>
    </div>
  ),
}))

// Test wrapper component
const TestWrapper = ({ children }: any) => {
  const methods = useForm({
    defaultValues: {
      StationeryRequestDetailDetails: [],
      StationeryAddressDetailRequestDetails: [],
      ReqType: '1',
    }
          })
  return <FormProvider {...methods}>{children}</FormProvider>
}

describe('StationeryRequestScreen', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('Basic rendering', () => {
    it('renders main form sections', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      void expect(screen.getByText('requestOwnerSelection')).toBeInTheDocument()
      void expect(screen.getByText('requestInformation')).toBeInTheDocument()
      void expect(screen.getByText('materialSelection')).toBeInTheDocument()
      void expect(screen.getByText('stationeryDetails')).toBeInTheDocument()
      void expect(screen.getByText('addressDetails')).toBeInTheDocument()
      void expect(screen.getByText('additionalInformation')).toBeInTheDocument()
    })

    it('renders request type radio buttons', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      expect(screen.getByText('For Myself')).toBeInTheDocument()
      expect(screen.getByText('For Someone Else')).toBeInTheDocument()
    })

    it('renders basic request information fields', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('textfield-requestNo')).toBeInTheDocument()
      void expect(screen.getByTestId('datepicker-requestDate')).toBeInTheDocument()
      void expect(screen.getByTestId('selectbox-requestingDepartment')).toBeInTheDocument()
      void expect(screen.getByTestId('textfield-requestingPerson')).toBeInTheDocument()
    })

    it('renders window.location and floor dropdowns', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const locationSelect = screen.getByTestId('selectbox-window.location')
      void expect(locationSelect).toBeInTheDocument()
      void expect(locationSelect).toHaveTextContent('Location 1')
      void expect(locationSelect).toHaveTextContent('Location 2')

      const floorSelect = screen.getByTestId('selectbox-floor')
      void expect(floorSelect).toBeInTheDocument()
    })

    it('renders address field when window.location is selected', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      // Address field should be in the DOM but might be conditionally shown
      const locationSelect = screen.getByTestId('selectbox-window.location')
      void fireEvent.change(locationSelect, { target: { value: 'loc1' } })

      void expect(screen.getByTestId('textfield-address')).toBeInTheDocument()
    })

  describe('Request type functionality', () => {
    it('shows user selection when "For Someone Else" is selected', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const forSomeoneElseRadio = screen.getByDisplayValue('2')
      void fireEvent.click(forSomeoneElseRadio)

      void expect(screen.getByTestId('selectbox-selectUserForRequest')).toBeInTheDocument()
    })

    it('shows package selection when "For Someone Else" is selected', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const forSomeoneElseRadio = screen.getByDisplayValue('2')
      void fireEvent.click(forSomeoneElseRadio)

      // Package section should be shown based on showPackageSection state
      // This depends on business logic, but at minimum the radio button should work
      void expect(forSomeoneElseRadio).toBeChecked()
    })

  describe('Material selection functionality', () => {
    it('renders material group dropdown', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const materialGroupSelect = screen.getByTestId('selectbox-materialGroup')
      void expect(materialGroupSelect).toBeInTheDocument()
      void expect(materialGroupSelect).toHaveTextContent('Group 1')
      void expect(materialGroupSelect).toHaveTextContent('Group 2')
    })

    it('enables material type dropdown when group is selected', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const materialGroupSelect = screen.getByTestId('selectbox-materialGroup')
      const materialTypeSelect = screen.getByTestId('selectbox-materialType')

      // Initially material type should be disabled
      void expect(materialTypeSelect).toBeDisabled()

      // Select a group
      void fireEvent.change(materialGroupSelect, { target: { value: 'group1' } })

      // Material type should now be enabled
      void expect(materialTypeSelect).toBeEnabled()
    })

    it('renders quantity input and add button', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('textfield-quantity')).toBeInTheDocument()
      void expect(screen.getByText('add')).toBeInTheDocument()
    })

    it('add button is initially disabled', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const addButton = screen.getByText('add')
      void expect(addButton).toBeDisabled()
    })

  describe('Tables functionality', () => {
    it('renders stationery details table', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const stationeryTable = screen.getAllByTestId('digi-table')[0]
      void expect(stationeryTable).toBeInTheDocument()
      void expect(screen.getAllByTestId('table-columns')[0]).toHaveTextContent('7 columns')
      void expect(screen.getAllByTestId('table-actions')[0]).toHaveTextContent('2 actions')
      void expect(screen.getAllByTestId('table-pagesize')[0]).toHaveTextContent('10')
    })

    it('renders address details table with add button', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      void expect(screen.getByText('addAddress')).toBeInTheDocument()

      const addressTable = screen.getAllByTestId('digi-table')[1]
      void expect(addressTable).toBeInTheDocument()
      void expect(screen.getAllByTestId('table-columns')[1]).toHaveTextContent('7 columns')
      void expect(screen.getAllByTestId('table-actions')[1]).toHaveTextContent('2 actions')
    })

  describe('Additional information section', () => {
    it('renders notes field', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('textfield-notes')).toBeInTheDocument()
    })

    it('renders total amount field as readonly', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const totalAmountField = screen.getByTestId('textfield-totalAmount')
      void expect(totalAmountField).toBeInTheDocument()
      void expect(totalAmountField).toHaveAttribute('type', 'number')
    })

    it('renders save button', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const saveButton = screen.getByText('common:save')
      void expect(saveButton).toBeInTheDocument()
      void expect(saveButton).toHaveAttribute('data-variant', 'contained')
      void expect(saveButton).toHaveAttribute('data-color', 'primary')
      void expect(saveButton).toHaveAttribute('data-fullwidth', 'true')
    })

  describe('Form interactions', () => {
    it('allows selecting request type', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const forMyselfRadio = screen.getByDisplayValue('1')
      const forSomeoneElseRadio = screen.getByDisplayValue('2')

      expect(forMyselfRadio).toBeChecked() // Default

      void fireEvent.click(forSomeoneElseRadio)
      void expect(forSomeoneElseRadio).toBeChecked()
      void expect(forMyselfRadio).not.toBeChecked()
    })

    it('allows typing in text fields', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const requestingPersonField = screen.getByTestId('textfield-requestingPerson')
      void fireEvent.change(requestingPersonField, { target: { value: 'John Doe' } })
      void expect(requestingPersonField).toHaveValue('John Doe')

      const addressField = screen.getByTestId('textfield-address')
      void fireEvent.change(addressField, { target: { value: '123 Main St' } })
      void expect(addressField).toHaveValue('123 Main St')

      const reasonField = screen.getByTestId('textfield-requestReason')
      void fireEvent.change(reasonField, { target: { value: 'Office supplies needed' } })
      void expect(reasonField).toHaveValue('Office supplies needed')
    })

    it('allows selecting departments', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const departmentSelect = screen.getByTestId('selectbox-requestingDepartment')
      void fireEvent.change(departmentSelect, { target: { value: 'dept1' } })
      void expect(departmentSelect).toHaveValue('dept1')
    })

    it('allows selecting window.location and enables floor selection', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const locationSelect = screen.getByTestId('selectbox-window.location')
      const floorSelect = screen.getByTestId('selectbox-floor')

      // Initially floor should be disabled or show no options
      void fireEvent.change(locationSelect, { target: { value: 'loc1' } })
      void expect(locationSelect).toHaveValue('loc1')

      // Floor should now be enabled and show filtered options
      void fireEvent.change(floorSelect, { target: { value: 'floor1' } })
      void expect(floorSelect).toHaveValue('floor1')
    })

    it('allows selecting material group and type', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const materialGroupSelect = screen.getByTestId('selectbox-materialGroup')
      const materialTypeSelect = screen.getByTestId('selectbox-materialType')
      const quantityField = screen.getByTestId('textfield-quantity')

      void fireEvent.change(materialGroupSelect, { target: { value: 'group1' } })
      void expect(materialGroupSelect).toHaveValue('group1')

      void fireEvent.change(materialTypeSelect, { target: { value: 'type1' } })
      void expect(materialTypeSelect).toHaveValue('type1')

      void fireEvent.change(quantityField, { target: { value: '5' } })
      void expect(quantityField).toHaveValue('5')
    })

  describe('Modal functionality', () => {
    it('opens add address modal when add address button is clicked', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const addAddressButton = screen.getByText('addAddress')
      void fireEvent.click(addAddressButton)

      // Modal should open - we can check if modal elements exist
      // This would depend on the modal implementation
      expect(addAddressButton).toBeInTheDocument() // Button should still exist
    })

  describe('Validation and error handling', () => {
    it('handles form field errors', () => {
      const TestWrapperWithErrors = ({ children }: any) => {
        const methods = useForm({
          defaultValues: {
            StationeryRequestDetailDetails: [],
            StationeryAddressDetailRequestDetails: [],
            ReqType: '1',
          }
          })

        // Simulate form errors
        methods.formState.errors = {
          RequestDate: { message: 'Request date is required' },
          RequestingPerson: { message: 'Requesting person is required' },
        }

        return <FormProvider {...methods}>{children}</FormProvider>
      }

      render(
        <TestWrapperWithErrors>
          <StationeryRequestScreen />
        </TestWrapperWithErrors>,
      )

      expect(screen.getByTestId('error-requestDate')).toHaveTextContent('Request date is required')
      expect(screen.getByTestId('error-requestingPerson')).toHaveTextContent('Requesting person is required')
    })

  describe('Accessibility', () => {
    it('provides proper form labels', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('textfield-requestNo')).toHaveAttribute('placeholder', 'requestNo')
      void expect(screen.getByTestId('datepicker-requestDate')).toHaveAttribute('placeholder', 'requestDate')
      void expect(screen.getByTestId('textfield-requestingPerson')).toHaveAttribute('placeholder', 'requestingPerson')
    })

    it('maintains semantic structure with sections', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const sections = screen.getAllByTestId('workflow-section')
      void expect(sections.length).toBeGreaterThan(0)
    })

    it('provides radio button labels', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      const forMyselfRadio = screen.getByDisplayValue('1')
      const forSomeoneElseRadio = screen.getByDisplayValue('2')

      void expect(forMyselfRadio).toHaveAttribute('type', 'radio')
      void expect(forSomeoneElseRadio).toHaveAttribute('type', 'radio')
    })

  describe('Enablement settings', () => {
    it('disables fields when enablement settings are false', () => {
      const contextWithDisabledFields = {
        ...mockWorkflowContext,
        initialData: {
          ...mockWorkflowContext.initialData,
          enabilitySettings: {
            ...mockWorkflowContext.initialData.enabilitySettings,
            reqType: false,
            requestDate: false,
            locationId: false,
            stationeryRequestDetailDetails: false,
          },
        },
      }

      void mockUseWorkflow.mockReturnValue(contextWithDisabledFields)

      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>,
      )

      void expect(screen.getByDisplayValue('1')).toBeDisabled()
      void expect(screen.getByDisplayValue('2')).toBeDisabled()
      void expect(screen.getByTestId('datepicker-requestDate')).toBeDisabled()
      void expect(screen.getByTestId('selectbox-window.location')).toBeDisabled()
      void expect(screen.getByTestId('selectbox-materialGroup')).toBeDisabled()
    })

  describe('Package selection', () => {
    it('shows package selection section when appropriate', () => {
      render(
        <TestWrapper>
          <StationeryRequestScreen />
        </TestWrapper>
      )

      const forSomeoneElseRadio = screen.getByDisplayValue('2')
      void fireEvent.click(forSomeoneElseRadio)

      // Package section visibility depends on business logic
      // At minimum, we verify the radio button works
      void expect(forSomeoneElseRadio).toBeChecked()