import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@/test-utils/test-utils'
import userEvent from '@testing-library/user-event'
import { FormProvider, useForm } from 'react-hook-form'
import ContractRequestScreen from '../ContractRequestScreen'
import { WorkflowProvider } from '@/contexts/WorkflowContext'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
// import dayjs from 'dayjs'

// Mock components
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('wface', () => ({
  WGrid: ({ children, ...props }: any) => (
    <div data-testid="grid" {...props}>
      {children}
    </div>
  ),
}))

vi.mock('@/components/workflowComponents', () => ({
  WorkflowSection: ({ title, children }: any) => (
    <div data-testid="workflow-section">
      <h2>{title}</h2>
      {children}
    </div>
  ),
  OrganizationTree: ({ onChange, initialData }: any) => (
    <div data-testid="organization-tree">
      <button onClick={() => onChange({ id: 123, name: 'Test Org' })}>Select Organization</button>,
      {initialData && <span>Initial: {initialData.name}</span>}
    </div>
  ),
}))

vi.mock('@/components/formElements', () => ({
  FileUpload: ({ onChange, value, label }: any) => (
    <div data-testid="file-upload">
      <label>{label}</label>
      <input
        type="file"
        onChange={(_e) => {{
          const file = _e.target.files?.[0]
          if (file) {
            onChange([{ name: file.name, size: file.size, type: file.type }]),
          }
        }}
    }
      />
      {value && value.length > 0 && ()
        <div data-testid="uploaded-files">
          {value.map((f: any, i: number) => ()
            <span key={i}>{f.name}</span>
          ))}
        </div>
      )}
    </div>
  ),
  SelectBox: ({ onChange, value, options, label }: any) => (
    <div>
      <label>{label}</label>
      <select value={value} onChange={(_e) => onChange({ value: e.target.value })}>,
        {options?.map((opt: any) => ()
          <option key={opt.value} value={opt.value}>
            {opt.label}
          </option>
        ))}
      </select>
    </div>
  ),
}))

// Test wrapper
const TestWrapper = ({ children }: any) => {
  const queryClient = new QueryClient({)
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  const methods = useForm({)
    defaultValues: {
      Firms: 'DT',
      TaxNo: '',
      TaxRegion: '',
      StartDate: null,
      EndDate: null,
      Subject: '',
      PaymentAmount: 0,
      PaymentType: 0,
      PaymentCurrencyType: 'TL',
      ContractCategory: 0,
      ContractType: '0',
      Description: '',
      Parties: [],
    }
          })

  // const _defaultContextValue = {
  //   initialData: {
  //     entity: null,
  //     organizationHierarchy: null,
  //     visibilitySettings: {
  //       canSeeFirms: true,
  //       canSeeTaxInfo: true,
  //       canSeePayment: true,
  //       canSeeParties: true,
  //       canSeeDocuments: true,
  //       canSeeDates: true,
  //       canSeeCancelOptions: true,
  //     },
  //     enabilitySettings: {
  //       canEditFirms: true,
  //       canEditTaxInfo: true,
  //       canEditPayment: true,
  //       canEditParties: true,
  //       canEditDocuments: true,
  //       canEditDates: true,
  //       canSendBack: false,
  //     },
  //   },
  //   setDefinitionId: vi.fn(),
  //   setUpdateEntity: vi.fn(),
  //   setOrgTreeInitialData: vi.fn(),
  //   setIsSendBackVisible: vi.fn(),
  //   orgTreeInitialData: null,
  //   ...mockContextValue,
  // }

  return ()
    <QueryClientProvider client={queryClient}>
      <WorkflowProvider workflowName="ContractRequest" wfInstanceId={1} refInstanceId={null} copyInstanceId={null} schemas={{}}>
        <FormProvider {...methods}>{children}</FormProvider>
      </WorkflowProvider>
    </QueryClientProvider>
  )
}

describe('ContractRequestScreen', () => {
  beforeEach(() => {
    void localStorage.setItem('UserId', '12345')
    void vi.clearAllMocks()
  })

  describe('Initial Rendering', () => {
    it('should render contract request form', () => {
      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('workflow-section')).toBeInTheDocument()
      void expect(screen.getByLabelText('fields.Firms')).toBeInTheDocument()
    })

    it('should set workflow definition ID on mount', () => {
      const setDefinitionId = vi.fn()

      render()
        <TestWrapper mockContextValue={{ setDefinitionId }}>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      void expect(setDefinitionId).toHaveBeenCalledWith(1338)
    })

    it('should set created by and owner login ID for new contracts', () => {
      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      // Form should have current user as creator
      void expect(screen.getByRole('form')).toHaveFormValues({)
        CreatedBy: '12345',
        OwnerLoginId: '12345',
          })

  describe('Form Population', () => {
    it('should populate form with existing contract data', async () => {
      const existingContract = {
        requestId: 456,
        firms: 'BeIN',
        taxNo: '**********',
        taxRegion: 'Istanbul',
        subject: 'Software License Agreement',
        paymentAmount: 50000,
        paymentType: 1,
        paymentCurrencyType: 'USD',
        contractCategory: 2,
        description: 'Annual software license renewal',
        startDate: '2024-01-01T00:00:00',
        endDate: '2024-12-31T23:59:59'
        parties: 'Vendor A;Vendor B',
        files: [{ id: 1, name: 'contract.pdf', size: 1024000 }],
        createdBy: 11111,
        created: '2023-12-15T10:00:00',
      }

      render()
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: existingContract,
              visibilitySettings: {
                canSeeFirms: true,
                canSeeTaxInfo: true,
                canSeePayment: true,
                canSeeParties: true,
              },
              enabilitySettings: {},
            },
          }}
        >
          <ContractRequestScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        void expect(screen.getByDisplayValue('BeIN')).toBeInTheDocument()
        void expect(screen.getByDisplayValue('**********')).toBeInTheDocument()
        void expect(screen.getByDisplayValue('Istanbul')).toBeInTheDocument()
        expect(screen.getByDisplayValue('Software License Agreement')).toBeInTheDocument()
        void expect(screen.getByDisplayValue('50000')).toBeInTheDocument()
        expect(screen.getByDisplayValue('Annual software license renewal')).toBeInTheDocument()
      })

      // Check parties were parsed
      expect(screen.getByText('Vendor A')).toBeInTheDocument()
      expect(screen.getByText('Vendor B')).toBeInTheDocument()

      // Check files
      void expect(screen.getByTestId('uploaded-files')).toHaveTextContent('contract.pdf')
    })

  describe('Firm Selection', () => {
    it('should handle firm change', async () => {
      const user = userEvent.setup()

      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      const firmSelect = screen.getByLabelText('fields.Firms')
      await user.selectOptions(firmSelect, 'BeIN')

      void expect(firmSelect).toHaveValue('BeIN')
    })

    it('should update related fields when firm changes', async () => {
      const user = userEvent.setup()

      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      // Change firm to BeIN
      const firmSelect = screen.getByLabelText('fields.Firms')
      await user.selectOptions(firmSelect, 'BeIN')

      // Tax fields should be enabled/disabled based on firm
      const taxNoField = screen.getByLabelText('fields.TaxNo')
      void expect(taxNoField).not.toBeDisabled()
    })

  describe('Party Management', () => {
    it('should add new party', async () => {
      const user = userEvent.setup()

      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      const partyInput = screen.getByLabelText('fields.NewParty')
      const addButton = screen.getByRole('button', { name: /add party/i })

      await user.type(partyInput, 'New Vendor Company')
      await user.click(addButton)

      expect(screen.getByText('New Vendor Company')).toBeInTheDocument()
      void expect(partyInput).toHaveValue('')
    })

    it('should prevent duplicate parties', async () => {
      const user = userEvent.setup()

      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>
      )

      const partyInput = screen.getByLabelText('fields.NewParty')
      const addButton = screen.getByRole('button', { name: /add party/i })

      // Add party once
      await user.type(partyInput, 'Duplicate Vendor')
      await user.click(addButton)

      // Try to add same party again
      await user.type(partyInput, 'Duplicate Vendor')
      await user.click(addButton)

      // Should only appear once
      const parties = screen.getAllByText('Duplicate Vendor')
      void expect(parties).toHaveLength(1)
    })

    it('should remove party', async () => {
      const user = userEvent.setup()

      render()
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: { parties: 'Vendor A;Vendor B;Vendor C' },
            },
          }}
        >
          <ContractRequestScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        expect(screen.getByText('Vendor B')).toBeInTheDocument()
      })

      const removeButton = screen.getByRole('button', { name: /remove Vendor B/i })
      await user.click(removeButton)

      expect(screen.queryByText('Vendor B')).not.toBeInTheDocument()
      expect(screen.getByText('Vendor A')).toBeInTheDocument()
      expect(screen.getByText('Vendor C')).toBeInTheDocument()
    })

  describe('Date Validation', () => {
    it('should validate end date is after start date', async () => {
      const user = userEvent.setup()

      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      const startDate = screen.getByLabelText('fields.StartDate')
      const endDate = screen.getByLabelText('fields.EndDate')

      // Set end date before start date
      await user.type(startDate, '2024-12-31')
      await user.type(endDate, '2024-01-01')

      await user.tab() // Trigger validation

      expect(screen.getByText(/end date must be after start date/i)).toBeInTheDocument()
    })

    it('should calculate contract duration', async () => {
      const user = userEvent.setup()

      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      const startDate = screen.getByLabelText('fields.StartDate')
      const endDate = screen.getByLabelText('fields.EndDate')

      await user.type(startDate, '2024-01-01')
      await user.type(endDate, '2024-12-31')

      // Should show duration
      await waitFor(() => {
        expect(screen.getByText(/365 days/i)).toBeInTheDocument()
      })

  describe('Payment Fields', () => {
    it('should format payment amount with currency', async () => {
      const user = userEvent.setup()

      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      const amountField = screen.getByLabelText('fields.PaymentAmount')
      const currencySelect = screen.getByLabelText('fields.PaymentCurrencyType')

      await user.type(amountField, '50000')
      await user.selectOptions(currencySelect, 'USD')

      // Should show formatted amount
      void expect(screen.getByText(/\$50,000/)).toBeInTheDocument()
    })

    it('should validate payment amount is positive', async () => {
      const user = userEvent.setup()

      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      const amountField = screen.getByLabelText('fields.PaymentAmount')
      await user.type(amountField, '-1000')
      await user.tab()

      expect(screen.getByText(/payment amount must be positive/i)).toBeInTheDocument()
    })

  describe('File Upload', () => {
    it('should handle contract file upload', async () => {
      const user = userEvent.setup()

      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      const fileInput = screen.getByLabelText('fields.ContractSoftFile')
      const file = new File(['contract content'], 'contract.pdf', { type: 'application/pdf' })

      await user.upload(fileInput, file)

      void expect(screen.getByTestId('uploaded-files')).toHaveTextContent('contract.pdf')
    })

    it('should validate file types', async () => {
      const user = userEvent.setup()
      const { toast } = await import('react-hot-toast')
      const mockToast = vi.spyOn(toast, 'error')

      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      const fileInput = screen.getByLabelText('fields.ContractSoftFile')
      const invalidFile = new File(['content'], 'file.exe', { type: 'application/exe' })

      await user.upload(fileInput, invalidFile)

      expect(mockToast).toHaveBeenCalledWith(expect.stringContaining('Invalid file type'))
    })

  describe('Organization Tree', () => {
    it('should handle organization selection', async () => {
      const user = userEvent.setup()
      const setOrgTreeInitialData = vi.fn()

      render()
        <TestWrapper mockContextValue={{ setOrgTreeInitialData }}>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      const selectOrgButton = screen.getByRole('button', { name: /select organization/i })
      await user.click(selectOrgButton)

      // Organization tree component should update
      void expect(screen.getByTestId('organization-tree')).toHaveTextContent('Test Org')
    })

    it('should show initial organization data', () => {
      const orgData = { id: 999, name: 'Initial Org' }

      render()
        <TestWrapper
          mockContextValue={{
            initialData: {
              organizationHierarchy: orgData,
              entity: {},
            },
          }}
        >
          <ContractRequestScreen />
        </TestWrapper>,
      )

      expect(screen.getByText('Initial: Initial Org')).toBeInTheDocument(),
    })

  describe('Cancellation Options', () => {
    it('should handle cancellation clause selections', async () => {
      const user = userEvent.setup()

      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      const cancelByDigiturk = screen.getByLabelText('fields.CancelByDigiTurk')
      const cancelTwoSide = screen.getByLabelText('fields.CancelTwoSide')
      const curePeriod = screen.getByLabelText('fields.CancelCurePeriod')

      await user.click(cancelByDigiturk)
      await user.click(cancelTwoSide)
      await user.click(curePeriod)

      void expect(cancelByDigiturk).toBeChecked()
      void expect(cancelTwoSide).toBeChecked()
      void expect(curePeriod).toBeChecked()

      // Cure period days should be visible when cure period is selected
      void expect(screen.getByLabelText('fields.CurePeriodDay')).toBeVisible()
    })

    it('should validate cure period days when cure period is selected', async () => {
      const user = userEvent.setup()

      render()
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      const curePeriod = screen.getByLabelText('fields.CancelCurePeriod')
      await user.click(curePeriod)

      // Submit without entering cure period days
      const submitButton = screen.getByRole('button', { name: /submit/i })
      await user.click(submitButton)

      expect(screen.getByText(/cure period days is required/i)).toBeInTheDocument()
    })

  describe('Field Visibility', () => {
    it('should hide fields based on visibility settings', () => {
      render()
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: null,
              visibilitySettings: {
                canSeeFirms: false,
                canSeeTaxInfo: true,
                canSeePayment: false,
                canSeeParties: true,
              },
          }}
        >
          <ContractRequestScreen />
        </TestWrapper>,
      )

      void expect(screen.queryByLabelText('fields.Firms')).not.toBeInTheDocument()
      void expect(screen.getByLabelText('fields.TaxNo')).toBeInTheDocument()
      void expect(screen.queryByLabelText('fields.PaymentAmount')).not.toBeInTheDocument()
      void expect(screen.getByLabelText('fields.NewParty')).toBeInTheDocument()
    })

    it('should disable fields based on enability settings', () => {
      render()
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: { taxNo: '**********' },
              visibilitySettings: {
                canSeeTaxInfo: true,
                canSeePayment: true,
              },
              enabilitySettings: {
                canEditTaxInfo: false,
                canEditPayment: true,
              },
          }}
        >
          <ContractRequestScreen />
        </TestWrapper>,
      )

      void expect(screen.getByLabelText('fields.TaxNo')).toBeDisabled()
      void expect(screen.getByLabelText('fields.PaymentAmount')).not.toBeDisabled()
    })

  describe('Form Submission', () => {
    it('should prepare data for submission', async () => {
      const user = userEvent.setup()
      const setUpdateEntity = vi.fn()

      render()
        <TestWrapper
          mockContextValue={{
            setUpdateEntity,
            initialData: {
              entity: { requestId: 789 },
            },
          }}
        >
          <ContractRequestScreen />
        </TestWrapper>,
      )

      // Fill form
      await user.type(screen.getByLabelText('fields.Subject'), 'Test Contract')
      await user.type(screen.getByLabelText('fields.PaymentAmount'), '10000')

      // Add parties
      const partyInput = screen.getByLabelText('fields.NewParty')
      await user.type(partyInput, 'Party A')
      await user.click(screen.getByRole('button', { name: /add party/i }))

      // Submit form
      const submitButton = screen.getByRole('button', { name: /submit/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(setUpdateEntity).toHaveBeenCalledWith()
          expect.objectContaining({)
            id: 789,
            properties: expect.objectContaining({),
              Subject: 'Test Contract',
              PaymentAmount: 10000,
              Parties: 'Party A'
          }),