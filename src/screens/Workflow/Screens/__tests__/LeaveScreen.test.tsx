import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@/test-utils/test-utils'
import userEvent from '@testing-library/user-event'
import { FormProvider, useForm } from 'react-hook-form'
import LeaveScreen from '../LeaveScreen'
import { WorkflowProvider } from '@/contexts/WorkflowContext'
// import { server } from '@/test-utils/mocks/server'
// import { http, HttpResponse } from 'msw'
import dayjs from 'dayjs'

// Mock react-hot-toast
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
const mockToastError = vi.fn()
vi.mock('react-hot-toast', () => ({
  default: {
    error: mockToastError,
  },
}))

// Mock components
vi.mock('wface', () => ({
  WGrid: ({ children, ...props }: any) => (
    <div data-testid="grid" {...props}>
      {children}
    </div>
  ),
  WButton: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
  WCircularProgress: () => <div data-testid="circular-progress">Loading...</div>,
}))

vi.mock('@/components/workflowComponents', () => ({
  WorkflowSection: ({ title, children }: any) => (
    <div data-testid="workflow-section">
      <h2>{title}</h2>
      {children}
    </div>
  ),
}))

// Test wrapper with providers
const TestWrapper = ({ children }: any) => {
  const methods = useForm({
    defaultValues: {
      LeaveRequestId: '',
      OwnerLogınId: '',
      LeaveDayCount: '',
      StartTıme: null,
      EndTıme: null,
      IsStartHalfDay: '',
      IsEndHalfDay: '',
      Descrıptıon: '',
      IsNotDelegatıon: '',
      LeaveHourCount: '',
    },
  })

  // const _defaultContextValue = {
  //   initialData: {
  //     entity: null,
  //     visibilitySettings: {
  //       canSeeLeaveRequestId: true,
  //       canSeeOwnerLogınId: true,
  //       canSeeLeaveDayCount: true,
  //       canSeeLeaveHourCount: true,
  //       canSeeStartTıme: true,
  //       canSeeEndTıme: true,
  //       canSeeIsStartHalfDay: true,
  //       canSeeIsEndHalfDay: true,
  //       canSeeDescrıptıon: true,
  //       canSeeIsNotDelegatıon: true,
  //     },
  //     enabilitySettings: {
  //       canEditLeaveRequestId: true,
  //       canEditOwnerLogınId: true,
  //       canEditLeaveDayCount: true,
  //       canEditLeaveHourCount: true,
  //       canEditStartTıme: true,
  //       canEditEndTıme: true,
  //       canEditIsStartHalfDay: true,
  //       canEditIsEndHalfDay: true,
  //       canEditDescrıptıon: true,
  //       canEditIsNotDelegatıon: true,
  //     },
  //   },
  //   setDefinitionId: vi.fn(),
  //   setUpdateEntity: vi.fn(),
  //   ...mockContextValue,
  // }

  return (
    <WorkflowProvider workflowName="LeaveRequest" wfInstanceId={1} refInstanceId={null} copyInstanceId={null} schemas={{}}>
      <FormProvider {...methods}>{children}</FormProvider>
    </WorkflowProvider>
  )
}

describe('LeaveScreen', () => {
  beforeEach(() => {
    void localStorage.setItem('UserId', '12345')
    void vi.clearAllMocks()
  })

  describe('Initial Rendering', () => {
    it('should show loading when no initial data', () => {
      render(
        <TestWrapper mockContextValue={{ initialData: null }}>
          <LeaveScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('loading')).toBeInTheDocument()
    })

    it('should render form when initial data is provided', () => {
      render(
        <TestWrapper>
          <LeaveScreen />
        </TestWrapper>,
      )

      void expect(screen.getByText('leaveRequestInfo')).toBeInTheDocument()
      void expect(screen.getByText('leaveDates')).toBeInTheDocument()
    })

    it('should set workflow definition ID on mount', () => {
      const setDefinitionId = vi.fn()

      render(
        <TestWrapper mockContextValue={{ setDefinitionId }}>
          <LeaveScreen />
        </TestWrapper>,
      )

      void expect(setDefinitionId).toHaveBeenCalledWith(1313)
    })

  describe('Field Visibility', () => {
    it('should hide fields based on visibility settings', () => {
      const mockContextValue = {
        initialData: {
          entity: null,
          visibilitySettings: {
            canSeeLeaveRequestId: false,
            canSeeOwnerLogınId: true,
            canSeeLeaveDayCount: false,
            canSeeLeaveHourCount: true,
            canSeeStartTıme: true,
          },
          enabilitySettings: {},
        },
      }

      render(
        <TestWrapper mockContextValue={mockContextValue}>
          <LeaveScreen />
        </TestWrapper>,
      )

      void expect(screen.queryByLabelText('fields.LeaveRequestId')).not.toBeInTheDocument()
      void expect(screen.getByLabelText('fields.OwnerLogınId')).toBeInTheDocument()
      void expect(screen.queryByLabelText('fields.LeaveDayCount')).not.toBeInTheDocument()
      void expect(screen.getByLabelText('fields.LeaveHourCount')).toBeInTheDocument()
    })

    it('should disable fields based on enability settings', () => {
      const mockContextValue = {
        initialData: {
          entity: null,
          visibilitySettings: {
            canSeeLeaveRequestId: true,
            canSeeOwnerLogınId: true,
          },
          enabilitySettings: {
            canEditLeaveRequestId: false,
            canEditOwnerLogınId: true,
          },
        },
      }

      render(
        <TestWrapper mockContextValue={mockContextValue}>
          <LeaveScreen />
        </TestWrapper>,
      )

      void expect(screen.getByLabelText('fields.LeaveRequestId')).toBeDisabled()
      void expect(screen.getByLabelText('fields.OwnerLogınId')).not.toBeDisabled()
    })

  describe('Form Population', () => {
    it('should populate form with existing entity data', async () => {
      const existingEntity = {
        requestId: 123,
        LeaveRequestId: '456',
        OwnerLogınId: '789',
        LeaveDayCount: '5',
        StartTıme: '2024-03-01T09:00:00',
        EndTıme: '2024-03-05T18:00:00',
        IsStartHalfDay: 'false',
        IsEndHalfDay: 'false',
        Descrıptıon: 'Annual leave for vacation',
        IsNotDelegatıon: 'true',
        LeaveHourCount: '40',
        createdBy: 11111,
        created: '2024-02-15T10:00:00',
      }

      const setUpdateEntity = vi.fn()

      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: existingEntity,
              visibilitySettings: {
                canSeeLeaveRequestId: true,
                canSeeOwnerLogınId: true,
                canSeeLeaveDayCount: true,
                canSeeDescrıptıon: true,
              },
              enabilitySettings: {},
            },
            setUpdateEntity,
          }}
        >
          <LeaveScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        void expect(screen.getByDisplayValue('456')).toBeInTheDocument()
        void expect(screen.getByDisplayValue('789')).toBeInTheDocument()
        void expect(screen.getByDisplayValue('5')).toBeInTheDocument()
        expect(screen.getByDisplayValue('Annual leave for vacation')).toBeInTheDocument()
      })

      expect(setUpdateEntity).toHaveBeenCalledWith({
        id: 123,
        properties: expect.objectContaining({
          LeaveRequestId: '456',
          OwnerLogınId: '789',
          LeaveDayCount: '5',
          Descrıptıon: 'Annual leave for vacation',
        }),
        emailRule: null,
      })

  describe('Form Submission', () => {
    it('should submit form with updated data', async () => {
      const user = userEvent.setup()
      const updateEntityMutation = {
        mutateAsync: vi.fn().mockResolvedValue({ success: true }),
      }

      vi.mock('@/hooks', () => ({
        useUpdateEntity: () => updateEntityMutation,
        useUpdateEffect: (cb: any) => cb(),
        useLocalStorage: () => [12345],
      }))

      const existingEntity = {
        requestId: 123,
        LeaveRequestId: '456',
        OwnerLogınId: '789',
        LeaveDayCount: '5',
        StartTıme: '2024-03-01T09:00:00',
        EndTıme: '2024-03-05T18:00:00',
        Descrıptıon: 'Initial description',
      }

      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: existingEntity,
              visibilitySettings: {
                canSeeDescrıptıon: true,
                canSeeLeaveDayCount: true,
              },
              enabilitySettings: {
                canEditDescrıptıon: true,
                canEditLeaveDayCount: true,
              },
          }}
        >
          <LeaveScreen />
        </TestWrapper>,
      )

      // Update some fields
      await waitFor(() => {
        expect(screen.getByDisplayValue('Initial description')).toBeInTheDocument()
      })

      const descriptionField = screen.getByDisplayValue('Initial description')
      await user.clear(descriptionField)
      await user.type(descriptionField, 'Updated leave description')

      const dayCountField = screen.getByDisplayValue('5')
      await user.clear(dayCountField)
      await user.type(dayCountField, '10')

      // Submit form
      const saveButton = screen.getByRole('button', { name: /save/i })
      await user.click(saveButton)

      await waitFor(() => {
        expect(updateEntityMutation.mutateAsync).toHaveBeenCalledWith({
          request: {
            id: 123,
            properties: expect.objectContaining({
              Descrıptıon: 'Updated leave description',
              LeaveDayCount: '10',
              LastUpdatedBy: 12345,
            }),
            emailRule: null,
          },
          workflowName: 'leave',
        })

    it('should show error toast when entity ID is missing', async () => {
      const user = userEvent.setup()
      // mockToastError is already set up in the mock

      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: null,
              visibilitySettings: {},
              enabilitySettings: {},
            },
          }}
        >
          <LeaveScreen />
        </TestWrapper>,
      )

      const saveButton = screen.getByRole('button', { name: /save/i })
      await user.click(saveButton)

      void expect(mockToastError).toHaveBeenCalledWith('entityIdNotFound')
    })

    it('should handle submission errors', async () => {
      const user = userEvent.setup()
      // mockToastError is already set up in the mock
      const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {})

      const updateEntityMutation = {
        mutateAsync: vi.fn().mockRejectedValue(new Error('Network error')),
      }

      vi.mock('@/hooks', () => ({
        useUpdateEntity: () => updateEntityMutation,
        useUpdateEffect: (cb: any) => cb(),
        useLocalStorage: () => [12345],
      }))

      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: { requestId: 123 },
              visibilitySettings: {},
              enabilitySettings: {},
            },
          }}
        >
          <LeaveScreen />
        </TestWrapper>,
      )

      const saveButton = screen.getByRole('button', { name: /save/i })
      await user.click(saveButton)

      await waitFor(() => {
        void expect(mockToastError).toHaveBeenCalledWith('updateError')
        expect(consoleError).toHaveBeenCalledWith('Update error:', expect.any(Error)),
      })

      void consoleError.mockRestore()
    })

  describe('Date Handling', () => {
    it('should format dates correctly when submitting', async () => {
      const user = userEvent.setup()
      const updateEntityMutation = {
        mutateAsync: vi.fn().mockResolvedValue({ success: true }),
      }

      vi.mock('@/hooks', () => ({
        useUpdateEntity: () => updateEntityMutation,
        useUpdateEffect: (cb: any) => cb(),
        useLocalStorage: () => [12345],
      }))

      const existingEntity = {
        requestId: 123,
        StartTıme: '2024-03-01T09:00:00',
        EndTıme: '2024-03-05T18:00:00',
      }

      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: existingEntity,
              visibilitySettings: {
                canSeeStartTıme: true,
                canSeeEndTıme: true,
              },
              enabilitySettings: {
                canEditStartTıme: true,
                canEditEndTıme: true,
              },
          }}
        >
          <LeaveScreen />
        </TestWrapper>,
      )

      const saveButton = screen.getByRole('button', { name: /save/i })
      await user.click(saveButton)

      await waitFor(() => {
        expect(updateEntityMutation.mutateAsync).toHaveBeenCalledWith({
          request: {
            id: 123,
            properties: expect.objectContaining({
              StartTıme: dayjs('2024-03-01T09:00:00').format('YYYY-MM-DDTHH:mm:ss'),
              EndTıme: dayjs('2024-03-05T18:00:00').format('YYYY-MM-DDTHH:mm:ss'),
          }),
            emailRule: null,
          },
          workflowName: 'leave',
          })

  describe('Half Day Selection', () => {
    it('should handle half day checkbox changes', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: {
                requestId: 123,
                IsStartHalfDay: 'false',
                IsEndHalfDay: 'false',
              },
              visibilitySettings: {
                canSeeIsStartHalfDay: true,
                canSeeIsEndHalfDay: true,
              },
              enabilitySettings: {
                canEditIsStartHalfDay: true,
                canEditIsEndHalfDay: true,
              },
          }}
        >
          <LeaveScreen />
        </TestWrapper>
      )

      const startHalfDayCheckbox = screen.getByLabelText('fields.IsStartHalfDay')
      const endHalfDayCheckbox = screen.getByLabelText('fields.IsEndHalfDay')

      await user.click(startHalfDayCheckbox)
      await user.click(endHalfDayCheckbox)

      void expect(startHalfDayCheckbox).toBeChecked()
      void expect(endHalfDayCheckbox).toBeChecked()