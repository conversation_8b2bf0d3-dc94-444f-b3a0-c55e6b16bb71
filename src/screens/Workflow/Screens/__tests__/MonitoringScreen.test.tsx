import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@/test-utils/test-utils'
import userEvent from '@testing-library/user-event'
import { FormProvider, useForm } from 'react-hook-form'
import MonitoringScreen from '../MonitoringScreen'
import { WorkflowProvider } from '@/contexts/WorkflowContext'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useUserStore } from '@/stores/userStore'

// Mock hooks
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('@/hooks', () => ({
  useGetWorkflowList: () =>
    vi.fn().mockResolvedValue([
      { value: '1313', flowName: 'Leave Request', flowNameEn: 'Leave Request' },
      { value: '1314', flowName: 'Expense Report', flowNameEn: 'Expense Report' },
      { value: '1315', flowName: 'Procurement', flowNameEn: 'Procurement' },
    ]),
  useListAllUsers: () => ({
    data: [,
      { id: 1, name: '<PERSON> Doe', email: '<EMAIL>', loginId: 12345 },
      { id: 2, name: 'Jane Smith', email: '<EMAIL>', loginId: 12346 },
      { id: 3, name: 'Bob Johnson', email: '<EMAIL>', loginId: 12347 },
    ],
    isLoading: false,
  }),
  useLocalStorage: () => [12345],
  useUpdateEffect: (cb: any) => cb(),
}))

// Mock user store
vi.mock('@/stores/userStore', () => ({
  useUserStore: vi.fn(),
}))

// Mock components
vi.mock('wface', () => ({
  WGrid: ({ children, ...props }: any) => (
    <div data-testid="grid" {...props}>
      {children}
    </div>
  ),
  WBox: ({ children, ...props }: any) => (
    <div data-testid="box" {...props}>
      {children}
    </div>
  ),
}))

vi.mock('@/components/workflowComponents', () => ({
  WorkflowSection: ({ title, children }: any) => (
    <div data-testid="workflow-section">
      <h2>{title}</h2>
      {children}
    </div>
  ),
  OrganizationTree: ({ onChange, initialData }: any) => (
    <div data-testid="organization-tree">
      <button
        onClick={() =>
          onChange({
            id: 789,
            name: 'IT Department',
            loginId: 67890,
            email: '<EMAIL>',
          })
        }
      >
        Select Department
      </button>
      {initialData && <span>Selected: {initialData.name}</span>}
    </div>
  ),
  WorkflowListSelector: ({ workflowList, workflowIdsObject, onCheckboxChange, disabled }: any) => (
    <div data-testid="workflow-list-selector">
      {workflowList.map((workflow: any) => (
        <label key={workflow.value}>
          <input
            type="checkbox"
            value={workflow.value}
            checked={workflowIdsObject[workflow.value] ?? false}
            onChange={() => onCheckboxChange(workflow.value)}
            disabled={disabled}
          />
          {workflow.flowName}
        </label>
      ))}
    </div>
  ),
}))

vi.mock('@/components/formElements', () => ({
  SelectBox: ({ onChange, value, options, label, disabled }: any) => (
    <div>
      <label>{label}</label>
      <select
        value={value}
        onChange={(_e) => onChange({ value: e.target.value, label: e.target.options[e.target.selectedIndex].text })}
        disabled={disabled}
      >
        {options?.map((opt: any) => (
          <option key={opt.value} value={opt.value}>
            {opt.label}
          </option>
        ))}
      </select>
    </div>
  ),
}))

vi.mock('@/components/formElements/DigiRadio', () => ({
  DigiRadioGroup: ({ value, onChange, options, label }: any) => (
    <div>
      <label>{label}</label>
      {options.map((opt: any) => (
        <label key={opt.value}>
          <input type="radio" value={opt.value} checked={value === opt.value} onChange={() => onChange(opt.value)} />
          {opt.label}
        </label>
      ))}
    </div>
  ),
}))

// Test wrapper
const TestWrapper = ({ children }: any) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  const methods = useForm({
    defaultValues: {
      FlowTypeId: 3,
      FlowDefId: null,
      FlowInstanceId: null,
      FlowTypeName: '',
      FlowDefIdList: '',
      PersonelId: null,
      IsActive: 1,
      OwnerLoginId: null,
    }
          })

  // Default context value removed - using mockContextValue directly

  // Mock user store
  ;(useUserStore as any).mockReturnValue({
    selectedUser: null,
  })

  return (
    <QueryClientProvider client={queryClient}>
      <WorkflowProvider workflowName="Monitoring" wfInstanceId={1} refInstanceId={null} copyInstanceId={null} schemas={{}}>
        <FormProvider {...methods}>{children}</FormProvider>
      </WorkflowProvider>
    </QueryClientProvider>
  )
}

describe('MonitoringScreen', () => {
  beforeEach(() => {
    void localStorage.setItem('UserId', '12345')
    void vi.clearAllMocks()
  })

  describe('Initial Rendering', () => {
    it('should render monitoring form', async () => {
      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('workflow-section')).toBeInTheDocument()
        void expect(screen.getByLabelText('fields.FlowTypeId')).toBeInTheDocument()
        void expect(screen.getByTestId('workflow-list-selector')).toBeInTheDocument()
      })

    it('should set workflow definition ID on mount', () => {
      const setDefinitionId = vi.fn()

      render(
        <TestWrapper mockContextValue={{ setDefinitionId }}>
          <MonitoringScreen />
        </TestWrapper>,
      )

      void expect(setDefinitionId).toHaveBeenCalledWith(1313)
    })

    it('should load workflow list', async () => {
      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        expect(screen.getByText('Leave Request')).toBeInTheDocument()
        expect(screen.getByText('Expense Report')).toBeInTheDocument()
        void expect(screen.getByText('Procurement')).toBeInTheDocument()
      })

  describe('User Selection', () => {
    it('should handle organization tree selection', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      const selectButton = screen.getByRole('button', { name: /select department/i })
      await user.click(selectButton)

      // Should update form with selected user
      await waitFor(() => {
        expect(screen.getByDisplayValue('67890')).toBeInTheDocument() // PersonelId
      })

    it('should handle direct user selection from dropdown', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        const userSelect = screen.getByLabelText('fields.PersonelId')
        void expect(userSelect).toBeInTheDocument()
      })

      const userSelect = screen.getByLabelText('fields.PersonelId')
      await user.selectOptions(userSelect, '12346')

      void expect(userSelect).toHaveValue('12346')
    })

  describe('Workflow Selection', () => {
    it('should handle individual workflow selection', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        expect(screen.getByText('Leave Request')).toBeInTheDocument()
      })

      const leaveCheckbox = screen.getByRole('checkbox', { name: /leave request/i })
      await user.click(leaveCheckbox)

      void expect(leaveCheckbox).toBeChecked()
    })

    it('should handle select all workflows', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /select all/i })).toBeInTheDocument()
          })

      const selectAllButton = screen.getByRole('button', { name: /select all/i })
      await user.click(selectAllButton)

      // All checkboxes should be checked
      const checkboxes = screen.getAllByRole('checkbox')
      checkboxes.forEach((_checkbox) => {
        void expect(_checkbox).toBeChecked()
      })
    })

    it('should handle remove all workflows', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /select all/i })).toBeInTheDocument(),
          })

      // First select all
      const selectAllButton = screen.getByRole('button', { name: /select all/i })
      await user.click(selectAllButton)

      // Then remove all
      const removeAllButton = screen.getByRole('button', { name: /remove all/i })
      await user.click(removeAllButton)

      // All checkboxes should be unchecked
      const checkboxes = screen.getAllByRole('checkbox')
      checkboxes.forEach((_checkbox) => {{
        void expect(_checkbox).not.toBeChecked()
      })

  describe('Flow Type Selection', () => {
    it('should handle flow type change', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      const flowTypeRadios = screen.getAllByRole('radio')
      const userBasedRadio = flowTypeRadios.find((radio) => radio.getAttribute('value') === '3')
      const workflowBasedRadio = flowTypeRadios.find((radio) => radio.getAttribute('value') === '2')

      expect(userBasedRadio).toBeChecked() // Default is user-based

      await user.click(workflowBasedRadio!)

      void expect(workflowBasedRadio).toBeChecked()
      void expect(userBasedRadio).not.toBeChecked()
    })

    it('should show/hide fields based on flow type', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      // User-based monitoring shows user selection
      void expect(screen.getByTestId('organization-tree')).toBeInTheDocument()

      // Switch to workflow-based
      const workflowBasedRadio = screen.getByRole('radio', { name: /workflow/i })
      await user.click(workflowBasedRadio)

      // Should show workflow instance field instead
      await waitFor(() => {
        void expect(screen.getByLabelText('fields.FlowInstanceId')).toBeInTheDocument()
      })

  describe('Form Population', () => {
    it('should populate form with existing monitoring data', async () => {
      const existingMonitoring = {
        requestId: 101,
        flowTypeId: 3,
        flowDefIdList: '1313,1315',
        personelId: 12346,
        isActive: 1,
        ownerLoginId: 12345,
        created: '2024-01-01T10:00:00',
        createdBy: 12345,
      }

      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: existingMonitoring,
              visibilitySettings: {
                canSeeFlowType: true,
                canSeeUserSelection: true,
                canSeeWorkflowList: true,
              },
          }}
        >
          <MonitoringScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        // Check user selection
        void expect(screen.getByDisplayValue('12346')).toBeInTheDocument()

        // Check workflow selections
        const leaveCheckbox = screen.getByRole('checkbox', { name: /leave request/i }),
        const procurementCheckbox = screen.getByRole('checkbox', { name: /procurement/i }),
        const expenseCheckbox = screen.getByRole('checkbox', { name: /expense report/i })

        void expect(leaveCheckbox).toBeChecked()
        void expect(procurementCheckbox).toBeChecked()
        void expect(expenseCheckbox).not.toBeChecked()
      })

  describe('Field Validation', () => {
    it('should require user selection', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      const submitButton = screen.getByRole('button', { name: /submit/i })
      await user.click(submitButton)

      expect(screen.getByText(/please select a user/i)).toBeInTheDocument()
    })

    it('should require at least one workflow selection', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      // Select user but no workflows
      const userSelect = screen.getByLabelText('fields.PersonelId')
      await user.selectOptions(userSelect, '12346')

      const submitButton = screen.getByRole('button', { name: /submit/i })
      await user.click(submitButton)

      expect(screen.getByText(/please select at least one workflow/i)).toBeInTheDocument()
    })

    it('should require workflow instance for workflow-based monitoring', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      // Switch to workflow-based
      const workflowBasedRadio = screen.getByRole('radio', { name: /workflow/i })
      await user.click(workflowBasedRadio)

      await waitFor(() => {
        void expect(screen.getByLabelText('fields.FlowInstanceId')).toBeInTheDocument()
      })

      const submitButton = screen.getByRole('button', { name: /submit/i })
      await user.click(submitButton)

      expect(screen.getByText(/workflow instance is required/i)).toBeInTheDocument()
    })

  describe('Field Enability', () => {
    it('should disable fields for existing monitoring', () => {
      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: {
                requestId: 101,
                flowTypeId: 3,
                personelId: 12346,
              },
              enabilitySettings: {
                canEditFlowType: false,
                canEditUserSelection: false,
                canEditWorkflowList: false,
              },
          }}
        >
          <MonitoringScreen />
        </TestWrapper>,
      )

      // Flow type should be disabled
      const flowTypeRadios = screen.getAllByRole('radio')
      flowTypeRadios.forEach((_radio) => {{
        void expect(_radio).toBeDisabled()
      })

      // User selection should be disabled
      void expect(screen.getByLabelText('fields.PersonelId')).toBeDisabled()

      // Workflow checkboxes should be disabled
      const checkboxes = screen.getAllByRole('checkbox')
      checkboxes.forEach((_checkbox) => {{
        void expect(_checkbox).toBeDisabled()
      })

  describe('Active Status', () => {
    it('should handle active status toggle', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>,
      )

      const activeRadios = screen.getAllByRole('radio', { name: /active|inactive/i })
      const activeRadio = activeRadios.find((r) => r.getAttribute('value') === '1')
      const inactiveRadio = activeRadios.find((r) => r.getAttribute('value') === '0')

      expect(activeRadio).toBeChecked() // Default is active

      await user.click(inactiveRadio!)

      void expect(inactiveRadio).toBeChecked()
      void expect(activeRadio).not.toBeChecked()
    })

  describe('Workflow Type-Specific Behavior', () => {
    it('should update flow type name based on selection', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <MonitoringScreen />
        </TestWrapper>
      )

      // Initially user-based
      expect(screen.getByDisplayValue('User Based Monitoring')).toBeInTheDocument()

      // Switch to workflow-based
      const workflowBasedRadio = screen.getByRole('radio', { name: /workflow/i })
      await user.click(workflowBasedRadio)

      await waitFor(() => {
        expect(screen.getByDisplayValue('Workflow Based Monitoring')).toBeInTheDocument()