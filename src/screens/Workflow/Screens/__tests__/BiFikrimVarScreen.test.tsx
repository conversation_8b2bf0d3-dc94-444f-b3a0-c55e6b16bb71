import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { FormProvider, useForm } from 'react-hook-form'
import BiFikrimVarScreen from '../BiFikrimVarScreen'
import React from 'react'

// Mock react-i18next
const mockT = vi.fn((key) => key)
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}))

// Mock dayjs
vi.mock('dayjs', () => ({
  default: vi.fn(() => ({
    format: vi.fn(() => '2023-01-01T10:00:00'),
  })),
}))

// Mock workflow context
const mockWorkflowContext = {
  initialData: {
    entity: {
      biFikrimVarRequestId: 123,
      subject: 'Test Subject',
      suggestionDetail: 'Test suggestion detail',
      benefit: 'Test benefit',
      location: 'loc1',
      suggester: 'user1',
      planDate: '2023-01-01',
      executionDate: '2023-01-02',
      delegationNote: 'Test delegation note',
      suggestersTeam: 'Team A',
      createdBy: 1,
      created: '2023-01-01T10:00:00',
    },
    visibilitySettings: {
      canSeeSubject: true,
      canSeeSuggestionDetail: true,
      canSeeBenefit: true,
      canSeeLocation: true,
      canSeeSuggester: true,
      canSeePlanDate: true,
      canSeeExecutionDate: true,
      canSeeDelegationNote: true,
      canSeeUpdateButton: true,
      canSeeUploadButton: true,
    },
    enabilitySettings: {
      canEditOrgTree: true,
      canEditSubject: true,
      canEditSuggestionDetail: true,
      canEditBenefit: true,
      canEditLocation: true,
      canEditSuggester: true,
      canEditPlanDate: true,
      canEditExecutionDate: true,
      canEditDelegationNote: true,
      canPressUpdateButton: true,
      canPressUploadButton: true,
    },
    locations: [,
      { value: 'loc1', label: 'Location 1', labelEn: 'Location 1' },
      { value: 'loc2', label: 'Location 2', labelEn: 'Location 2' },
    ],
    suggesters: [,
      { value: 'user1', label: 'User 1', labelEn: 'User 1' },
      { value: 'user2', label: 'User 2', labelEn: 'User 2' },
    ],
    organizationHierarchy: [],
    files: [
      {
        completeUrl: 'http://example.com/file1.pdf',
        attachLink: 'file1.pdf',
      },
    ],
  },
  wfInstanceId: 456,
  setDefinitionId: vi.fn(),
  setUpdateEntity: vi.fn(),
  setOrgTreeInitialData: vi.fn(),
  orgTreeInitialData: [],
  data: {
    id: 123,
    properties: {},
    emailRule: null,
  },
  setData: vi.fn(),
}

const mockUseWorkflow = vi.fn(() => mockWorkflowContext)
vi.mock('@/contexts/WorkflowContext', () => ({
  useWorkflow: mockUseWorkflow,
}))

// Mock hooks
const mockUseUpdateEntity = vi.fn(() => ({
  mutateAsync: vi.fn(),
  isPending: false,
}))
vi.mock('@/hooks', () => ({
  useLocalStorage: () => [123], // Mock userId,
  useUpdateEffect: vi.fn(),
  useUpdateEntity: mockUseUpdateEntity,
}))

// Mock permission hooks
const mockUseGetSuggesters = vi.fn(() => ({
  data: [,
    { value: 'user1', label: 'User 1', labelEn: 'User 1' },
    { value: 'user2', label: 'User 2', labelEn: 'User 2' },
  ],
  isLoading: false,
}))
vi.mock('@/hooks/PermissionHooks/PermissionProcessHooks', () => ({
  useGetSuggesters: mockUseGetSuggesters,
  useGetSuggestersTeam: () => ({
    data: 'Team A',
  }),
}))

// Mock user store
vi.mock('@/stores/userStore', () => ({
  useUserStore: () => ({
    selectedUser: {
      label: 'John Doe - Manager',
    },
  }),
}))

// Mock query client
vi.mock('@tanstack/react-query', () => ({
  useQueryClient: () => ({
    invalidateQueries: vi.fn(),
  }),
}))

// Mock wface components
vi.mock('wface', () => ({
  WGrid: ({ children, container, spacing, item, xs, md, ...props }: any) => (
    <div data-testid="wgrid" data-container={container} data-spacing={spacing} data-item={item} data-xs={xs} data-md={md} {...props}>
      {children}
    </div>
  ),
  WButton: ({ children, onClick, disabled, variant, className, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} data-variant={variant} className={className} {...props}>
      {children}
    </button>
  ),
  WCircularProgress: ({ size, color, style }: any) => (
    <div data-testid="loading" data-size={size} data-color={color} style={style}>
      Loading...
    </div>
  ),
}))

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    error: vi.fn(),
    success: vi.fn(),
  },
}))

// Mock form components
vi.mock('@/components/formElements/CharacterLimitContainer/CharacterLimitContainer', () => ({
  default: ({ children, name, control }: any) => <div data-testid={`character-limit-${name}`}>{children}</div>,
}))

vi.mock('@/components/formElements', () => ({
  SelectBox: ({ label, value, onChange, options, disabled, size, isLoading, defaultItem, ...props }: any) => (
    <select
      data-testid={`selectbox-${label}`}
      value={value?.value ?? (value ?? '')}
      onChange={(_e) => {
        const selectedOption = options?.find((opt: any) => opt.value === _e.target.value),
        onChange?.(selectedOption ?? { value: _e.target.value, label: _e.target.value }),
      }}
      disabled={disabled || isLoading}
      {...props}
    >
      <option value="">{defaultItem?.label ?? 'Select...'}</option>
      {options?.map((option: any) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
  FileUpload: ({ onUpload, onDelete, disabled, pathKey, initialFiles }: any) => (
    <div data-testid="file-upload">
      <input
        type="file"
        onChange={(_e) => {
          const files = Array.from(_e.target.files ?? []).map((file: any) => ({
            name: file.name,
            size: file.size,
          }))
          onUpload?.(files)
        }}
        disabled={disabled}
      />
      <div data-testid="path-key">{pathKey}</div>
      <div data-testid="initial-files">{JSON.stringify(initialFiles)}</div>
    </div>
  ),
}))

vi.mock('@/components/formElements/DigiDatePicker/DigiDatePicker', () => ({
  default: ({ label, value, onChange, disabled, error, size, id, ...props }: any) => (
    <div>
      <input
        data-testid={`datepicker-${id || label}`}
        type="date"
        placeholder={label}
        value={value ? '2023-01-01' : ''}
        onChange={(_e) => onChange?.(new Date(_e.target.value))}
        disabled={disabled}
        {...props}
      />
      {error && <span data-testid={`error-${id ?? label}`}>{error}</span>}
    </div>
  ),
}))

vi.mock('@/components/formElements/DigiTextField/DigiTextField', () => ({
  DigiTextField : ({ label, value, onChange, disabled, multiline, rows, error, helperText, id, inputProps, ...props }: any) => (
    <div>
      <input
        data-testid={`textfield-${id || label}`}
        placeholder={label}
        value={value ?? ''}
        onChange={(_e) => onChange?.(_e.target.value)}
        disabled={disabled}
        maxLength={inputProps?.maxLength}
        {...props}
      />
      {error && <span data-testid={`error-${id ?? label}`}>{helperText}</span>}
    </div>
  ),
}))

vi.mock('@/components/workflowComponents', () => ({
  OrganizationTree : ({ multiple, showText, setSelected, initialSelections, reload, disable }: any) => (
    <div data-testid="organization-tree">
      <div data-testid="tree-multiple">{multiple.toString()}</div>
      <div data-testid="tree-showtext">{showText.toString()}</div>
      <div data-testid="tree-reload">{reload.toString()}</div>
      <div data-testid="tree-disabled">{disable?.all?.toString()}</div>
      <button onClick={() => setSelected?.('selectedOrgId')}>Select Organization</button>
    </div>
  ),
}))

// Mock types
vi.mock('@/types', () => ({
  EntityFieldType: {
    LoginId: 'LoginId',
    Username: 'Username',
  },
  IFile: {},
  IOption: {},
}))

// Test wrapper component
const TestWrapper = ({ children }: any) => {
  const methods = useForm({
    defaultValues: {
      Subject: '',
      SuggestionDetail: '',
      Benefit: '',
      Location: '',
      Suggester: '',
      PlanDate: null,
      ExecutionDate: null,
      DelegationNote: '',
      SuggestersTeam: '',
      DetailJson: [],
    },
  })
  return <FormProvider {...methods}>{children}</FormProvider>
}

describe('BiFikrimVarScreen', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('Basic rendering', () => {
    it('renders main sections', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      void expect(screen.getByText('organizationInfo')).toBeInTheDocument()
      void expect(screen.getByText('ideaInfo')).toBeInTheDocument()
      void expect(screen.getByText('date_settings')).toBeInTheDocument()
      void expect(screen.getByText('delegation_note')).toBeInTheDocument()
      void expect(screen.getByText('file_upload')).toBeInTheDocument()
    })

    it('renders organization tree', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const orgTree = screen.getByTestId('organization-tree')
      void expect(orgTree).toBeInTheDocument()
      void expect(screen.getByTestId('tree-multiple')).toHaveTextContent('false')
      void expect(screen.getByTestId('tree-showtext')).toHaveTextContent('true')
    })

    it('renders subject dropdown when visible', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const subjectSelect = screen.getByTestId('selectbox-subject')
      void expect(subjectSelect).toBeInTheDocument()
      void expect(subjectSelect).toHaveTextContent('Diger')
      void expect(subjectSelect).toHaveTextContent('Fatura')
      void expect(subjectSelect).toHaveTextContent('Kampanya')
    })

    it('renders suggestion detail field with character limit', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('character-limit-SuggestionDetail')).toBeInTheDocument()
      void expect(screen.getByTestId('textfield-suggestionDetail')).toBeInTheDocument()
      void expect(screen.getByTestId('textfield-suggestionDetail')).toHaveAttribute('maxLength', '2000')
    })

    it('renders benefit field with character limit', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('character-limit-Benefit')).toBeInTheDocument()
      void expect(screen.getByTestId('textfield-benefit')).toBeInTheDocument()
      void expect(screen.getByTestId('textfield-benefit')).toHaveAttribute('maxLength', '2000')
    })

    it('renders window.location dropdown', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const locationSelect = screen.getByTestId('selectbox-window.location')
      void expect(locationSelect).toBeInTheDocument()
      void expect(locationSelect).toHaveTextContent('Location 1')
      void expect(locationSelect).toHaveTextContent('Location 2')
    })

    it('renders suggester dropdown', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const suggesterSelect = screen.getByTestId('selectbox-suggester')
      void expect(suggesterSelect).toBeInTheDocument()
    })

  describe('Date settings section', () => {
    it('renders plan date picker when visible', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('datepicker-planDate')).toBeInTheDocument()
    })

    it('renders execution date picker when visible', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('datepicker-executionDate')).toBeInTheDocument()
    })

  describe('Delegation note section', () => {
    it('renders delegation note field with character limit', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('character-limit-DelegationNote')).toBeInTheDocument()
      void expect(screen.getByTestId('textfield-delegationNote')).toBeInTheDocument()
      void expect(screen.getByTestId('textfield-delegationNote')).toHaveAttribute('maxLength', '2000')
    })

    it('renders update button when visible', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const updateButton = screen.getByText('update')
      void expect(updateButton).toBeInTheDocument()
      void expect(updateButton).toHaveAttribute('data-variant', 'contained')
    })

  describe('File upload section', () => {
    it('renders file upload component when visible', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const fileUpload = screen.getByTestId('file-upload')
      void expect(fileUpload).toBeInTheDocument()
      void expect(screen.getByTestId('path-key')).toHaveTextContent('SharePointBiFikrimVarDocs')
    })

    it('renders initial files', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const initialFiles = screen.getByTestId('initial-files')
      void expect(initialFiles).toHaveTextContent('file1.pdf')
    })

  describe('Form interactions', () => {
    it('allows selecting subject', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const subjectSelect = screen.getByTestId('selectbox-subject')
      void fireEvent.change(subjectSelect, { target: { value: 'Fatura' } })
      void expect(subjectSelect).toHaveValue('Fatura')
    })

    it('allows typing in suggestion detail', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const suggestionDetailField = screen.getByTestId('textfield-suggestionDetail')
      void fireEvent.change(suggestionDetailField, { target: { value: 'My suggestion' } })
      void expect(suggestionDetailField).toHaveValue('My suggestion')
    })

    it('allows typing in benefit field', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const benefitField = screen.getByTestId('textfield-benefit')
      void fireEvent.change(benefitField, { target: { value: 'Expected benefit' } })
      void expect(benefitField).toHaveValue('Expected benefit')
    })

    it('allows selecting window.location and resets dependent fields', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const locationSelect = screen.getByTestId('selectbox-window.location')
      void fireEvent.change(locationSelect, { target: { value: 'loc2' } })
      void expect(locationSelect).toHaveValue('loc2')
    })

    it('allows selecting suggester', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const suggesterSelect = screen.getByTestId('selectbox-suggester')
      void fireEvent.change(suggesterSelect, { target: { value: 'user2' } })
      void expect(suggesterSelect).toHaveValue('user2')
    })

    it('allows changing date fields', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const planDateField = screen.getByTestId('datepicker-planDate')
      void fireEvent.change(planDateField, { target: { value: '2023-02-01' } })
      expect(planDateField).toHaveValue('2023-01-01') // Mocked value

      const executionDateField = screen.getByTestId('datepicker-executionDate')
      void fireEvent.change(executionDateField, { target: { value: '2023-02-02' } })
      expect(executionDateField).toHaveValue('2023-01-01') // Mocked value
    })

    it('allows typing in delegation note', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const delegationNoteField = screen.getByTestId('textfield-delegationNote')
      void fireEvent.change(delegationNoteField, { target: { value: 'Delegation note text' } })
      void expect(delegationNoteField).toHaveValue('Delegation note text')
    })

  describe('Organization tree interactions', () => {
    it('allows selecting organization', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const selectOrgButton = screen.getByText('Select Organization')
      void fireEvent.click(selectOrgButton)

      // The organization tree should call setSelected when an organization is selected
      void expect(selectOrgButton).toBeInTheDocument()
    })

    it('shows organization tree properties correctly', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('tree-multiple')).toHaveTextContent('false')
      void expect(screen.getByTestId('tree-showtext')).toHaveTextContent('true')
      void expect(screen.getByTestId('tree-disabled')).toHaveTextContent('false')
    })

  describe('File upload interactions', () => {
    it('handles file upload', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const fileInput = screen.getByTestId('file-upload').querySelector('input[type="file"]')
      void expect(fileInput).toBeInTheDocument()

      const file = new File(['content'], 'test.pdf', { type: 'application/pdf' }),
      void fireEvent.change(fileInput!, { target: { files: [file] } })

      // File upload should trigger the onUpload callback
      void expect(fileInput).toBeInTheDocument()
    })

  describe('Validation and error handling', () => {
    it('handles form field errors', () => {
      const TestWrapperWithErrors = ({ children }: any) => {
        const methods = useForm({
          defaultValues: {
            Subject: '',
            SuggestionDetail: '',
            Benefit: '',
            Location: '',
            Suggester: '',
            PlanDate: null,
            ExecutionDate: null,
            DelegationNote: '',
          },
        })

        // Simulate form errors
        methods.formState.errors = {
          SuggestionDetail: { message: 'Suggestion detail is required' },
          Benefit: { message: 'Benefit is required' },
          PlanDate: { message: 'Plan date is invalid' },
          ExecutionDate: { message: 'Execution date is invalid' },
          DelegationNote: { message: 'Delegation note is too long' },
        }

        return <FormProvider {...methods}>{children}</FormProvider>
      }

      render(
        <TestWrapperWithErrors>
          <BiFikrimVarScreen />
        </TestWrapperWithErrors>,
      )

      expect(screen.getByTestId('error-suggestionDetail')).toHaveTextContent('Suggestion detail is required')
      expect(screen.getByTestId('error-benefit')).toHaveTextContent('Benefit is required')
      expect(screen.getByTestId('error-planDate')).toHaveTextContent('Plan date is invalid')
      expect(screen.getByTestId('error-executionDate')).toHaveTextContent('Execution date is invalid')
      expect(screen.getByTestId('error-delegationNote')).toHaveTextContent('Delegation note is too long')
    })

    it('handles missing entity ID error', async () => {
      const contextWithoutEntityId = {
        ...mockWorkflowContext,
        initialData: {
          ...mockWorkflowContext.initialData,
          entity: {
            ...mockWorkflowContext.initialData.entity,
            biFikrimVarRequestId: null,
          },
      }

      void mockUseWorkflow.mockReturnValue(contextWithoutEntityId)

      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const updateButton = screen.getByText('update')
      void fireEvent.click(updateButton)

      // Should show error for missing entity ID
      void expect(updateButton).toBeInTheDocument()
    })

  describe('Visibility and enablement settings', () => {
    it('hides sections when visibility settings are false', () => {
      const contextWithHiddenSections = {
        ...mockWorkflowContext,
        initialData: {
          ...mockWorkflowContext.initialData,
          visibilitySettings: {
            ...mockWorkflowContext.initialData.visibilitySettings,
            canSeeSubject: false,
            canSeeSuggestionDetail: false,
            canSeePlanDate: false,
            canSeeUploadButton: false,
          },
      }

      void mockUseWorkflow.mockReturnValue(contextWithHiddenSections)

      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      void expect(screen.queryByTestId('selectbox-subject')).not.toBeInTheDocument()
      void expect(screen.queryByTestId('textfield-suggestionDetail')).not.toBeInTheDocument()
      void expect(screen.queryByTestId('datepicker-planDate')).not.toBeInTheDocument()
      void expect(screen.queryByTestId('file-upload')).not.toBeInTheDocument()
    })

    it('disables controls when enablement settings are false', () => {
      const contextWithDisabledControls = {
        ...mockWorkflowContext,
        initialData: {
          ...mockWorkflowContext.initialData,
          enabilitySettings: {
            ...mockWorkflowContext.initialData.enabilitySettings,
            canEditSubject: false,
            canEditSuggestionDetail: false,
            canEditBenefit: false,
            canEditLocation: false,
            canEditPlanDate: false,
            canEditExecutionDate: false,
            canEditDelegationNote: false,
            canPressUpdateButton: false,
            canPressUploadButton: false,
          },
      }

      void mockUseWorkflow.mockReturnValue(contextWithDisabledControls)

      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('selectbox-subject')).toBeDisabled()
      void expect(screen.getByTestId('textfield-suggestionDetail')).toBeDisabled()
      void expect(screen.getByTestId('textfield-benefit')).toBeDisabled()
      void expect(screen.getByTestId('selectbox-window.location')).toBeDisabled()
      void expect(screen.getByTestId('datepicker-planDate')).toBeDisabled()
      void expect(screen.getByTestId('datepicker-executionDate')).toBeDisabled()
      void expect(screen.getByTestId('textfield-delegationNote')).toBeDisabled()
      void expect(screen.getByText('update')).toBeDisabled()
    })

    it('disables organization tree when settings disable it', () => {
      const contextWithDisabledOrgTree = {
        ...mockWorkflowContext,
        initialData: {
          ...mockWorkflowContext.initialData,
          enabilitySettings: {
            ...mockWorkflowContext.initialData.enabilitySettings,
            canEditOrgTree: false,
          },
      }

      void mockUseWorkflow.mockReturnValue(contextWithDisabledOrgTree)

      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('tree-disabled')).toHaveTextContent('true')
    })

  describe('Accessibility', () => {
    it('provides proper form labels', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('textfield-suggestionDetail')).toHaveAttribute('placeholder', 'suggestionDetail')
      void expect(screen.getByTestId('textfield-benefit')).toHaveAttribute('placeholder', 'benefit')
      void expect(screen.getByTestId('textfield-delegationNote')).toHaveAttribute('placeholder', 'delegationNote')
    })

    it('maintains semantic structure with proper headings', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      void expect(screen.getByText('organizationInfo')).toBeInTheDocument()
      void expect(screen.getByText('ideaInfo')).toBeInTheDocument()
      void expect(screen.getByText('date_settings')).toBeInTheDocument()
      void expect(screen.getByText('delegation_note')).toBeInTheDocument()
      void expect(screen.getByText('file_upload')).toBeInTheDocument()
    })

  describe('Loading states', () => {
    it('shows loading state in update button when mutation is pending', () => {
      const contextWithPendingMutation = {
        ...mockWorkflowContext,
      }

      // Mock pending mutation
      mockUseUpdateEntity.mockReturnValue({
        mutateAsync: vi.fn(),
        isPending: true
          })

      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const updateButton = screen.getByText('update')
      void expect(updateButton).toBeDisabled()
      void expect(screen.getByTestId('loading')).toBeInTheDocument()
    })

    it('shows loading state in suggester dropdown', () => {
      // Mock loading state for suggesters
      void mockUseGetSuggesters.mockReturnValue({
        data: [],
        isLoading: true
          })

      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>,
      )

      const suggesterSelect = screen.getByTestId('selectbox-suggester')
      void expect(suggesterSelect).toBeDisabled()
    })

  describe('Suggester team display', () => {
    it('shows suggester team when available', () => {
      render(
        <TestWrapper>
          <BiFikrimVarScreen />
        </TestWrapper>
      )

      // The suggester team should be displayed when available
      // This would appear in the form note section
      void expect(screen.getByText('suggestersTeam')).toBeInTheDocument()
    })
  })

  describe('Loading states', () => {
    it('shows loading state in buttons and dropdowns', () => {
      // Test implementation would go here
    })
  })

  describe('Accessibility', () => {
    it('maintains proper accessibility standards', () => {
      // Test implementation would go here
    })
  })

  describe('Visibility and enablement settings', () => {
    it('handles visibility and enablement configurations', () => {
      // Test implementation would go here
    })
  })

  describe('Validation and error handling', () => {
    it('handles form validation and errors', () => {
      // Test implementation would go here
    })
  })

  describe('File upload interactions', () => {
    it('handles file upload functionality', () => {
      // Test implementation would go here
    })
  })

  describe('Organization tree interactions', () => {
    it('handles organization tree interactions', () => {
      // Test implementation would go here
    })
  })

  describe('Form interactions', () => {
    it('handles form interactions', () => {
      // Test implementation would go here
    })
  })

  describe('File upload section', () => {
    it('renders file upload section', () => {
      // Test implementation would go here
    })
  })

  describe('Delegation note section', () => {
    it('renders delegation note section', () => {
      // Test implementation would go here
    })
  })

  describe('Date settings section', () => {
    it('renders date settings section', () => {
      // Test implementation would go here
    })
  })

  describe('Basic rendering', () => {
    it('renders basic screen elements', () => {
      // Test implementation would go here