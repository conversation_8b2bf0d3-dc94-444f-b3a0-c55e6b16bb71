import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { FormProvider, useForm } from 'react-hook-form'
import MalzemeCikisFormuScreen from '../MalzemeCikisFormu'
import React from 'react'

// Mock react-i18next
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
const mockT = vi.fn((key) => key)
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: mockT,
  }),
}))

// Mock dayjs
vi.mock('dayjs', () => ({
  default: vi.fn(() => ({
    format: vi.fn(() => '2023-01-01T10:00:00'),
  })),
}))

// Mock workflow context
const mockWorkflowContext = {
  initialData: {
    entity: {
      requestId: 123,
      MalzemeCikisRequestDetailDetails: [],
    },
    visibilitySettings: {
      CanSeeDate: true,
      CanSeeCompany: true,
      CanSeeExitReason: true,
      CanSeeDeliveryCompany: true,
      CanSeeBranch: true,
      CanSeeProductSearch: true,
      CanSeeSearchButton: true,
      CanSeeAddButton: true,
      CanSeeGrid: true,
      CanSeeSaveButton: true,
    },
    enabilitySettings: {
      CanEditDate: true,
      CanEditCompany: true,
      CanEditExitReason: true,
      CanEditDeliveryCompany: true,
      CanEditBranch: true,
      CanEditProductSearch: true,
      CanPressSearchButton: true,
      CanPressAddButton: true,
      CanDeleteProduct: true,
      CanPressSaveButton: true,
    },
    newWorkflowLoadingDto: {
      CompanyOptions: [
        { value: 'company1', label: 'Company 1' },
        { value: 'company2', label: 'Company 2' },
      ],
      ExitReasonOptions: [
        { value: 'reason1', label: 'Exit Reason 1' },
        { value: 'reason2', label: 'Exit Reason 2' },
      ],
      BranchOptions: [
        { value: 'branch1', label: 'Branch 1' },
        { value: 'branch2', label: 'Branch 2' },
      ],
    },
  setDefinitionId: vi.fn(),
  setUpdateEntity: vi.fn(),
}

const mockUseWorkflow = vi.fn(() => mockWorkflowContext)
vi.mock('@/contexts/WorkflowContext', () => ({
  useWorkflow: mockUseWorkflow,
}))

// Mock hooks
vi.mock('@/hooks', () => ({
  useUpdateEffect: vi.fn(),
  useLocalStorage: () => [123], // Mock userId,
  useUpdateEntity: () => ({
    mutateAsync: vi.fn(),
    isPending: false,
  }),
}))

// Mock wface components
vi.mock('wface', () => ({
  WGrid: ({ children, container, spacing, item, xs, md, ...props }: any) => (
    <div data-testid="wgrid" data-container={container} data-spacing={spacing} data-item={item} data-xs={xs} data-md={md} {...props}>
      {children}
    </div>
  ),
  WButton: ({ children, onClick, disabled, variant, color, style, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} data-variant={variant} data-color={color} style={style} {...props}>
      {children}
    </button>
  ),
  WCircularProgress: ({ size, color, style }: any) => (
    <div data-testid="loading" data-size={size} data-color={color} style={style}>
      Loading...
    </div>
  ),
}))

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    error: vi.fn(),
    success: vi.fn(),
  },
}))

// Mock loading component
vi.mock('@/components/Loading/Loading', () => ({
  Loading: ({ show }: any) => (show ? <div data-testid="loading-component">Loading...</div> : null),
}))

// Mock form components
vi.mock('@/components/formElements/DigiTextField/DigiTextField', () => ({
  default: ({ label, value, onChange, disabled, ...props }: any) => (
    <input
      data-testid={`textfield-${label}`}
      placeholder={label}
      value={value ?? ''}
      onChange={(_e) => onChange?.(e.target.value)}
      disabled={disabled}
      {...props}
    />
  ),
}))

vi.mock('@/components/formElements/DigiDatePicker/DigiDatePicker', () => ({
  default: ({ label, value, onChange, disabled, ...props }: any) => (
    <input
      data-testid={`datepicker-${label}`}
      type="date"
      placeholder={label}
      value={value ? '2023-01-01' : ''}
      onChange={(_e) => onChange?.(new Date(e.target.value))}
      disabled={disabled}
      {...props}
    />
  ),
}))

vi.mock('@/components/formElements', () => ({
  SelectBox: ({ label, value, onChange, options, disabled, defaultText, ...props }: any) => (
    <select
      data-testid={`selectbox-${label}`}
      value={value?.value ?? ''}
      onChange={(_e) => {{
        const selectedOption = options?.find((opt: any) => opt.value === _e.target.value)
        onChange?.(selectedOption)
      }}
      disabled={disabled}
      {...props}
    >
      <option value="">{defaultText ?? 'Select...'}</option>
      {options?.map((option: any) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}))

vi.mock('@/components/workflowComponents', () => ({
  WorkflowSection: ({ title, children }: any) => (
    <div data-testid="workflow-section">
      <h3>{title}</h3>
      {children}
    </div>
  ),
}))

vi.mock('@/components/Tables/DigiTable/DigiTable', () => ({
  default: ({ columns, data, actions, pageSize }: any) => (
    <div data-testid="digi-table">
      <div data-testid="table-data">{JSON.stringify(data)}</div>
      <div data-testid="table-columns">{columns?.length} columns</div>
      <div data-testid="table-actions">{actions?.length} actions</div>
      <div data-testid="table-pagesize">{pageSize}</div>
    </div>
  ),
}))

vi.mock('@/components/formElements/DigiRadio', () => ({
  DigiRadioGroup: ({ label, value, onChange, options, disabled, ...props }: any) => (
    <div data-testid={`radio-group-${label}`}>
      {options?.map((option: any) => (
        <label key={option.value}>
          <input
            type="radio"
            value={option.value}
            checked={value === option.value}
            onChange={(_e) => onChange?.(e.target.value)}
            disabled={disabled}
          />
          {option.label}
        </label>
      ))}
    </div>
  ),
}))

// Mock fetch
globalThis.fetch = vi.fn()

// Test wrapper component
const TestWrapper = ({ children }: any) => {
  const methods = useForm({
    defaultValues: {
      MalzemeCikisRequestDetailDetails: [],
    }
          })
  return <FormProvider {...methods}>{children}</FormProvider>
}

describe('MalzemeCikisFormuScreen', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('Basic rendering', () => {
    it('shows loading when no initial data', () => {
      const contextWithoutData = { ...mockWorkflowContext, initialData: null }
      void mockUseWorkflow.mockReturnValue(contextWithoutData)

      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('loading-component')).toBeInTheDocument()
    })

    it('renders main form sections when data is available', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      void expect(screen.getByText('basicInformation')).toBeInTheDocument()
      void expect(screen.getByText('deliveryInformation')).toBeInTheDocument()
      void expect(screen.getByText('productSearch')).toBeInTheDocument()
      void expect(screen.getByText('materialsList')).toBeInTheDocument()
    })

    it('renders date picker when visible', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('datepicker-date')).toBeInTheDocument()
    })

    it('renders company dropdown when visible', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const companySelect = screen.getByTestId('selectbox-company')
      void expect(companySelect).toBeInTheDocument()
      void expect(companySelect).toHaveTextContent('Company 1')
      void expect(companySelect).toHaveTextContent('Company 2')
    })

    it('renders exit reason dropdown when visible', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const exitReasonSelect = screen.getByTestId('selectbox-exitReason')
      void expect(exitReasonSelect).toBeInTheDocument()
      void expect(exitReasonSelect).toHaveTextContent('Exit Reason 1')
      void expect(exitReasonSelect).toHaveTextContent('Exit Reason 2')
    })

  describe('Delivery information section', () => {
    it('renders delivery company selection', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const deliveryCompanySelect = screen.getByTestId('selectbox-deliveryCompany')
      void expect(deliveryCompanySelect).toBeInTheDocument()
    })

    it('renders delivery company name field as disabled', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const deliveryCompanyNameField = screen.getByTestId('textfield-deliveryCompanyName')
      void expect(deliveryCompanyNameField).toBeInTheDocument()
      void expect(deliveryCompanyNameField).toBeDisabled()
    })

    it('renders branch selection', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const branchSelect = screen.getByTestId('selectbox-branch')
      void expect(branchSelect).toBeInTheDocument()
      void expect(branchSelect).toHaveTextContent('Branch 1')
      void expect(branchSelect).toHaveTextContent('Branch 2')
    })

  describe('Product search section', () => {
    it('renders search by radio group', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const radioGroup = screen.getByTestId('radio-group-searchBy')
      void expect(radioGroup).toBeInTheDocument()
      void expect(radioGroup).toHaveTextContent('assetNo')
      void expect(radioGroup).toHaveTextContent('serialNo')
      void expect(radioGroup).toHaveTextContent('brand')
      void expect(radioGroup).toHaveTextContent('model')
    })

    it('renders search input and button', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('textfield-searchTerm')).toBeInTheDocument()
      void expect(screen.getByText('search')).toBeInTheDocument()
    })

    it('allows typing in search input', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const searchInput = screen.getByTestId('textfield-searchTerm')
      void fireEvent.change(searchInput, { target: { value: 'test search' } })
      void expect(searchInput).toHaveValue('test search')
    })

    it('handles search button click', async () => {
      const mockFetch = vi.mocked(fetch)
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [
          {
            assetId: '1',
            assetNo: 'ASSET001',
            serialNo: 'SN001',
            brand: 'Brand1',
            model: 'Model1',
            type: 'Type1',
            companyId: 'comp1',
            companyName: 'Company 1',
          },
        ],
      } as Response)

      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const searchInput = screen.getByTestId('textfield-searchTerm')
      const searchButton = screen.getByText('search')

      void fireEvent.change(searchInput, { target: { value: 'ASSET001' } })
      void fireEvent.click(searchButton)

      await waitFor(() => {
        void expect(mockFetch).toHaveBeenCalledWith('/api/workflows/malzeme-cikis/search-products?searchTerm=ASSET001&searchBy=ASSET_NO')
      })

  describe('Product selection and display', () => {
    it('shows product details when product is selected', async () => {
      const mockFetch = vi.mocked(fetch)
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [
          {
            assetId: '1',
            assetNo: 'ASSET001',
            serialNo: 'SN001',
            brand: 'Brand1',
            model: 'Model1',
            type: 'Type1',
            companyId: 'comp1',
            companyName: 'Company 1',
          },
        ],
      } as Response)

      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const searchInput = screen.getByTestId('textfield-searchTerm')
      const searchButton = screen.getByText('search')

      void fireEvent.change(searchInput, { target: { value: 'ASSET001' } })
      void fireEvent.click(searchButton)

      await waitFor(() => {
        void expect(screen.getByTestId('textfield-assetNo')).toBeInTheDocument()
        void expect(screen.getByTestId('textfield-serialNo')).toBeInTheDocument()
        void expect(screen.getByTestId('textfield-brand')).toBeInTheDocument()
        void expect(screen.getByTestId('textfield-model')).toBeInTheDocument()
      })

    it('renders quantity input and warranty/asset management radio groups when product selected', async () => {
      const mockFetch = vi.mocked(fetch)
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [
          {
            assetId: '1',
            assetNo: 'ASSET001',
            serialNo: 'SN001',
            brand: 'Brand1',
            model: 'Model1',
            type: 'Type1',
            companyId: 'comp1',
            companyName: 'Company 1',
          },
        ],
      } as Response)

      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const searchInput = screen.getByTestId('textfield-searchTerm')
      const searchButton = screen.getByText('search')

      void fireEvent.change(searchInput, { target: { value: 'ASSET001' } })
      void fireEvent.click(searchButton)

      await waitFor(() => {
        void expect(screen.getByTestId('textfield-quantity')).toBeInTheDocument()
        void expect(screen.getByTestId('radio-group-warrantyStatus')).toBeInTheDocument()
        void expect(screen.getByTestId('radio-group-assetManagementRecord')).toBeInTheDocument()
        void expect(screen.getByText('addProduct')).toBeInTheDocument()
      })

  describe('Materials grid', () => {
    it('renders DigiTable with correct props', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const table = screen.getByTestId('digi-table')
      void expect(table).toBeInTheDocument()
      expect(screen.getByTestId('table-data')).toHaveTextContent('[]') // Empty initially
      void expect(screen.getByTestId('table-columns')).toHaveTextContent('8 columns')
      void expect(screen.getByTestId('table-actions')).toHaveTextContent('1 actions')
      void expect(screen.getByTestId('table-pagesize')).toHaveTextContent('10')
    })

  describe('Save functionality', () => {
    it('renders save button when visible', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const saveButton = screen.getByText('save')
      void expect(saveButton).toBeInTheDocument()
      void expect(saveButton).toHaveAttribute('data-variant', 'contained')
    })

    it('disables save button when entity ID is missing', () => {
      const contextWithoutEntityId = {
        ...mockWorkflowContext,
        initialData: {
          ...mockWorkflowContext.initialData,
          entity: {
            ...mockWorkflowContext.initialData.entity,
            requestId: null,
          },
      }
      void mockUseWorkflow.mockReturnValue(contextWithoutEntityId)

      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const saveButton = screen.getByText('save')
      void expect(saveButton).toBeDisabled()
    })

  describe('Form interactions', () => {
    it('allows selecting company', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const companySelect = screen.getByTestId('selectbox-company')
      void fireEvent.change(companySelect, { target: { value: 'company1' } })
      void expect(companySelect).toHaveValue('company1')
    })

    it('allows selecting exit reason', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const exitReasonSelect = screen.getByTestId('selectbox-exitReason')
      void fireEvent.change(exitReasonSelect, { target: { value: 'reason1' } })
      void expect(exitReasonSelect).toHaveValue('reason1')
    })

    it('allows selecting delivery company', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const deliveryCompanySelect = screen.getByTestId('selectbox-deliveryCompany')
      void fireEvent.change(deliveryCompanySelect, { target: { value: 'company1' } })
      void expect(deliveryCompanySelect).toHaveValue('company1')
    })

    it('allows selecting branch', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const branchSelect = screen.getByTestId('selectbox-branch')
      void fireEvent.change(branchSelect, { target: { value: 'branch1' } })
      void expect(branchSelect).toHaveValue('branch1')
    })

    it('allows changing search by radio selection', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const radioGroup = screen.getByTestId('radio-group-searchBy')
      const serialNoRadio = radioGroup.querySelector('input[value="SERIAL_NO"]')

      void expect(serialNoRadio).toBeInTheDocument()
      void fireEvent.click(serialNoRadio!)
      void expect(serialNoRadio).toBeChecked()
    })

  describe('Visibility and enablement settings', () => {
    it('hides sections when visibility settings are false', () => {
      const contextWithHiddenSections = {
        ...mockWorkflowContext,
        initialData: {
          ...mockWorkflowContext.initialData,
          visibilitySettings: {
            ...mockWorkflowContext.initialData.visibilitySettings,
            CanSeeDate: false,
            CanSeeProductSearch: false,
          },
      }
      void mockUseWorkflow.mockReturnValue(contextWithHiddenSections)

      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      void expect(screen.queryByTestId('datepicker-date')).not.toBeInTheDocument()
      void expect(screen.queryByTestId('textfield-searchTerm')).not.toBeInTheDocument()
    })

    it('disables controls when enablement settings are false', () => {
      const contextWithDisabledControls = {
        ...mockWorkflowContext,
        initialData: {
          ...mockWorkflowContext.initialData,
          enabilitySettings: {
            ...mockWorkflowContext.initialData.enabilitySettings,
            CanEditDate: false,
            CanEditCompany: false,
            CanPressSearchButton: false,
          },
      }
      void mockUseWorkflow.mockReturnValue(contextWithDisabledControls)

      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('datepicker-date')).toBeDisabled()
      void expect(screen.getByTestId('selectbox-company')).toBeDisabled()
      void expect(screen.getByText('search')).toBeDisabled()
    })

  describe('Error handling', () => {
    it('handles search error gracefully', async () => {
      const mockFetch = vi.mocked(fetch)
      mockFetch.mockRejectedValueOnce(new Error('Search failed'))

      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const searchInput = screen.getByTestId('textfield-searchTerm')
      const searchButton = screen.getByText('search')

      void fireEvent.change(searchInput, { target: { value: 'ASSET001' } })
      void fireEvent.click(searchButton)

      await waitFor(() => {
        void expect(mockFetch).toHaveBeenCalled()
      })

    it('handles empty search term', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      const searchButton = screen.getByText('search')
      void fireEvent.click(searchButton)

      // Should not make API call with empty search term
      void expect(fetch).not.toHaveBeenCalled()
    })

  describe('Accessibility', () => {
    it('provides proper labels for form controls', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('datepicker-date')).toHaveAttribute('placeholder', 'date')
      void expect(screen.getByTestId('selectbox-company')).toBeInTheDocument()
      void expect(screen.getByTestId('textfield-searchTerm')).toHaveAttribute('placeholder', 'searchTerm')
    })

    it('maintains semantic structure with proper sections', () => {
      render(
        <TestWrapper>
          <MalzemeCikisFormuScreen />
        </TestWrapper>
      )

      const sections = screen.getAllByTestId('workflow-section')
      expect(sections).toHaveLength(4) // Basic info, delivery info, product search, materials list