import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@/test-utils/test-utils'
import userEvent from '@testing-library/user-event'
import { FormProvider, useForm } from 'react-hook-form'
import DelegationScreen from '../DelegationScreen'
import { WorkflowProvider } from '@/contexts/WorkflowContext'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useUserStore } from '@/stores/userStore'
// import dayjs from 'dayjs'

// Mock hooks
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('@/hooks', () => ({
  useGetWorkflowList: () =>
    vi.fn().mockResolvedValue([
      { value: '1313', flowName: 'Leave Request', flowNameEn: 'Leave Request' },
      { value: '1314', flowName: 'Expense Report', flowNameEn: 'Expense Report' },
      { value: '1315', flowName: 'Procurement', flowNameEn: 'Procurement' },
      { value: '1316', flowName: 'Contract Request', flowNameEn: 'Contract Request' },
    ]),
  useLocalStorage: () => [12345],
  useUpdateEffect: (cb: any) => cb(),
}))

// Mock user store
vi.mock('@/stores/userStore', () => ({
  useUserStore: vi.fn(),
}))

// Mock components
vi.mock('wface', () => ({
  WTextField: ({ value, onChange, label, ...props }: any) => (
    <div>
      <label>{label}</label>
      <input type="text" value={value} onChange={(_e) => onChange(e.target.value)} {...props} />
    </div>
  ),
  WGrid: ({ children, ...props }: any) => (
    <div data-testid="grid" {...props}>
      {children}
    </div>
  ),
}))

vi.mock('lucide-react', () => ({
  AlertTriangle: () => <span data-testid="alert-icon">⚠️</span>,
}))

vi.mock('@/components/Loading/Loading', () => ({
  Loading: ({ show }: any) => (show ? <div data-testid="loading">Loading...</div> : null),
}))

vi.mock('@/components/workflowComponents', () => ({
  OrganizationTree: ({ onChange, initialData, error }: any) => (
    <div data-testid="organization-tree">
      <button
        onClick={() =>
          onChange({
            id: 67890,
            name: 'John Manager',
            loginId: 67890,
            email: '<EMAIL>',
          })
        }
      >
        Select Delegate
      </button>
      {initialData && <span>Selected: {initialData.name}</span>}
      {error && <span role="alert">{error}</span>}
    </div>
  ),
  WorkflowListSelector: ({ workflowList, workflowIdsObject, onCheckboxChange, disabled }: any) => (
    <div data-testid="workflow-list-selector">
      {workflowList.map((workflow: any) => (
        <label key={workflow.value}>
          <input
            type="checkbox"
            name={`workflow-${workflow.value}`}
            value={workflow.value}
            checked={workflowIdsObject[workflow.value] ?? false}
            onChange={() => onCheckboxChange(workflow.value)}
            disabled={disabled}
          />
          {workflow.flowName}
        </label>
      ))}
    </div>
  ),
}))

// Test wrapper
const TestWrapper = ({ children }: any) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  const methods = useForm({
    defaultValues: {
      StartTime: null,
      EndTime: null,
      DelegatedLoginId: null,
      WorkFlowIds: '',
      DelegationComment: '',
      workflowIdsObject: {},
    }
          })

  // const defaultContextValue = {
  //   initialData: {
  //     entity: null,
  //     organizationHierarchy: null,
  //     visibilitySettings: {
  //       canSeeDelegatedUser: true,
  //       canSeeWorkflowList: true,
  //       canSeeDates: true,
  //       canSeeComment: true,
  //     },
  //     enabilitySettings: {
  //       canEditDelegatedUser: true,
  //       canEditWorkflowList: true,
  //       canEditDates: true,
  //       canEditComment: true,
  //     },
  //   },
  //   setDefinitionId: vi.fn(),
  //   setOrgTreeInitialData: vi.fn(),
  //   orgTreeInitialData: null,
  //   ...mockContextValue,
  // }

  // Mock user store
  ;(useUserStore as any).mockReturnValue({
    selectedUser: null,
  })

  return (
    <QueryClientProvider client={queryClient}>
      <WorkflowProvider workflowName="Delegation" wfInstanceId={1} refInstanceId={null} copyInstanceId={null} schemas={{}}>
        <FormProvider {...methods}>{children}</FormProvider>
      </WorkflowProvider>
    </QueryClientProvider>
  )
}

describe('DelegationScreen', () => {
  beforeEach(() => {
    void localStorage.setItem('UserId', '12345')
    void vi.clearAllMocks()
  })

  describe('Initial Rendering', () => {
    it('should render delegation form', async () => {
      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        void expect(screen.getByTestId('grid')).toBeInTheDocument()
        void expect(screen.getByTestId('organization-tree')).toBeInTheDocument()
        void expect(screen.getByTestId('workflow-list-selector')).toBeInTheDocument()
      })

    it('should set workflow definition ID on mount', () => {
      const setDefinitionId = vi.fn()

      render(
        <TestWrapper mockContextValue={{ setDefinitionId }}>
          <DelegationScreen />
        </TestWrapper>,
      )

      void expect(setDefinitionId).toHaveBeenCalledWith(1321)
    })

    it('should load workflow list', async () => {
      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        expect(screen.getByText('Leave Request')).toBeInTheDocument()
        expect(screen.getByText('Expense Report')).toBeInTheDocument()
        void expect(screen.getByText('Procurement')).toBeInTheDocument()
        expect(screen.getByText('Contract Request')).toBeInTheDocument()
      })

  describe('User Selection', () => {
    it('should handle delegate selection from organization tree', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>,
      )

      const selectButton = screen.getByRole('button', { name: /select delegate/i })
      await user.click(selectButton)

      await waitFor(() => {
        expect(screen.getByText('Selected: John Manager')).toBeInTheDocument()
      })
    })

    it('should show error for invalid delegate selection', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>,
      )

      // Submit without selecting delegate
      const submitButton = screen.getByRole('button', { name: /submit/i })
      await user.click(submitButton)

      await waitFor(() => {
        void expect(screen.getByRole('alert')).toHaveTextContent(/select a delegate/i)
      })

  describe('Workflow Selection', () => {
    it('should handle individual workflow selection', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        expect(screen.getByText('Leave Request')).toBeInTheDocument()
      })

      const leaveCheckbox = screen.getByRole('checkbox', { name: /leave request/i })
      await user.click(leaveCheckbox)

      void expect(leaveCheckbox).toBeChecked()
    })

    it('should select all workflows', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /select all/i })).toBeInTheDocument()
          })

      const selectAllButton = screen.getByRole('button', { name: /select all/i })
      await user.click(selectAllButton)

      const checkboxes = screen.getAllByRole('checkbox')
      checkboxes.forEach((_checkbox) => {{
        void expect(_checkbox).toBeChecked()
      });

    it('should remove all workflow selections', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /select all/i })).toBeInTheDocument(),
          })

      // First select all
      await user.click(screen.getByRole('button', { name: /select all/i }))

      // Then remove all
      await user.click(screen.getByRole('button', { name: /remove all/i }))

      const checkboxes = screen.getAllByRole('checkbox')
      checkboxes.forEach((_checkbox) => {{
        void expect(_checkbox).not.toBeChecked()
      })

    it('should require at least one workflow selection', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        expect(screen.getByText('Leave Request')).toBeInTheDocument()
      })

      // Select and then deselect
      const leaveCheckbox = screen.getByRole('checkbox', { name: /leave request/i })
      await user.click(leaveCheckbox)

      void expect(screen.getByText('atLeastOneWorkflow')).toBeInTheDocument()
    })

  describe('Date Selection', () => {
    it('should handle start and end date selection', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>,
      )

      const startDateInput = screen.getByLabelText('fields.StartTime')
      const endDateInput = screen.getByLabelText('fields.EndTime')

      await user.type(startDateInput, '2024-03-01')
      await user.type(endDateInput, '2024-03-31')

      void expect(startDateInput).toHaveValue('2024-03-01')
      void expect(endDateInput).toHaveValue('2024-03-31')
    })

    it('should validate end date is after start date', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>,
      )

      const startDateInput = screen.getByLabelText('fields.StartTime')
      const endDateInput = screen.getByLabelText('fields.EndTime')

      await user.type(startDateInput, '2024-03-31')
      await user.type(endDateInput, '2024-03-01')

      await user.tab() // Trigger validation

      expect(screen.getByText(/end date must be after start date/i)).toBeInTheDocument()
    })

  describe('Comment Field', () => {
    it('should handle delegation comment input', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>,
      )

      const commentInput = screen.getByLabelText('fields.DelegationComment')
      await user.type(commentInput, 'Out of office - Annual vacation')

      void expect(commentInput).toHaveValue('Out of office - Annual vacation')
    })

    it('should have default comment for existing delegations', async () => {
      const existingDelegation = {
        startTime: '2024-03-01T00:00:00',
        endTime: '2024-03-31T23:59:59',
        delegatedLoginId: 67890,
        workFlowIds: '1313,1314',
        delegationComment: 'Existing delegation comment',
      }

      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: existingDelegation,
            },
          }}
        >
          <DelegationScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        expect(screen.getByDisplayValue('Existing delegation comment')).toBeInTheDocument()
      })

  describe('Form Population', () => {
    it('should populate form with existing delegation data', async () => {
      const existingDelegation = {
        requestId: 123,
        startTime: '2024-03-01T00:00:00',
        endTime: '2024-03-31T23:59:59',
        delegatedLoginId: 67890,
        workFlowIds: '1313,1315',
        delegationComment: 'Annual leave delegation',
        createdBy: 12345,
        created: '2024-02-20T10:00:00',
      }

      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: existingDelegation,
              organizationHierarchy: {
                id: 67890,
                name: 'John Manager',
              },
          }}
        >
          <DelegationScreen />
        </TestWrapper>,
      )

      await waitFor(() => {
        // Check delegate is selected
        expect(screen.getByText('Selected: John Manager')).toBeInTheDocument()

        // Check workflows are selected
        const leaveCheckbox = screen.getByRole('checkbox', { name: /leave request/i }),
        const procurementCheckbox = screen.getByRole('checkbox', { name: /procurement/i })

        void expect(leaveCheckbox).toBeChecked()
        void expect(procurementCheckbox).toBeChecked()

        // Check comment
        expect(screen.getByDisplayValue('Annual leave delegation')).toBeInTheDocument()
      })

  describe('Copy Instance', () => {
    it('should handle copy instance from URL params', async () => {
      // Mock URL params
      delete (window as any).window.location
      window.location = {
        search: '?copyInstanceId=456',
      } as any

      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>,
      )

      // Should fetch and populate data from copied instance
      await waitFor(() => {
        void expect(screen.getByTestId('grid')).toBeInTheDocument()
      })

  describe('Field Visibility and Enability', () => {
    it('should hide fields based on visibility settings', () => {
      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: null,
              visibilitySettings: {
                canSeeDelegatedUser: false,
                canSeeWorkflowList: true,
                canSeeDates: false,
                canSeeComment: true,
              },
          }}
        >
          <DelegationScreen />
        </TestWrapper>,
      )

      void expect(screen.queryByTestId('organization-tree')).not.toBeInTheDocument()
      void expect(screen.getByTestId('workflow-list-selector')).toBeInTheDocument()
      void expect(screen.queryByLabelText('fields.StartTime')).not.toBeInTheDocument()
      void expect(screen.getByLabelText('fields.DelegationComment')).toBeInTheDocument()
    })

    it('should disable fields based on enability settings', () => {
      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: { delegatedLoginId: 67890 },
              enabilitySettings: {
                canEditDelegatedUser: false,
                canEditWorkflowList: false,
                canEditDates: true,
                canEditComment: true,
              },
          }}
        >
          <DelegationScreen />
        </TestWrapper>,
      )

      // Organization tree should be disabled
      void expect(screen.getByTestId('organization-tree')).toHaveAttribute('aria-disabled', 'true')

      // Workflow checkboxes should be disabled
      const checkboxes = screen.getAllByRole('checkbox')
      checkboxes.forEach((_checkbox) => {{
        void expect(_checkbox).toBeDisabled()
      })

      // Dates should be enabled
      void expect(screen.getByLabelText('fields.StartTime')).not.toBeDisabled()
      void expect(screen.getByLabelText('fields.EndTime')).not.toBeDisabled()
    })

  describe('Warning Messages', () => {
    it('should show warning for existing delegations', () => {
      render(
        <TestWrapper
          mockContextValue={{
            initialData: {
              entity: { requestId: 123 },
            },
          }}
        >
          <DelegationScreen />
        </TestWrapper>,
      )

      void expect(screen.getByTestId('alert-icon')).toBeInTheDocument()
      expect(screen.getByText(/existing delegation warning/i)).toBeInTheDocument()
    })

  describe('Submit Validation', () => {
    it('should validate all required fields before submission', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <DelegationScreen />
        </TestWrapper>
      )

      const submitButton = screen.getByRole('button', { name: /submit/i })
      await user.click(submitButton)

      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText(/select a delegate/i)).toBeInTheDocument()
        expect(screen.getByText(/at least one workflow/i)).toBeInTheDocument()
        expect(screen.getByText(/start date is required/i)).toBeInTheDocument()
        expect(screen.getByText(/end date is required/i)).toBeInTheDocument()
})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})})