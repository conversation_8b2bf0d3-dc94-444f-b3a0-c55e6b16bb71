import React, { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import { useFormContext, Controller } from 'react-hook-form'
import { useUpdateEffect } from '@/hooks'
import { useTranslation } from 'react-i18next'
import { useWorkflow } from '@/contexts/WorkflowContext'
import { WGrid, WButton, WCircularProgress } from 'wface'
import { useLocalStorage, useUpdateEntity } from '@/hooks'
import toast from 'react-hot-toast'
import { Loading } from '@/components/Loading/Loading'
import DigiTextField from '@/components/formElements/DigiTextField/DigiTextField'
import DigiDatePicker from '@/components/formElements/DigiDatePicker/DigiDatePicker'
import { SelectBox } from '@/components/formElements'
import { WorkflowSection } from '@/components/workflowComponents'
import DigiTable from '@/components/Tables/DigiTable/DigiTable'
import { DigiRadioGroup } from '@/components/formElements/DigiRadio'

interface MalzemeCikisFormuFormData {
  WorkflowDefId?: number
  CreatedBy?: number
  Created?: string
  LastUpdatedBy?: number
  LastUpdated?: string
  MalzemeCikisRequestDetailDetails?: MalzemeCikisRequestDetail[]
  [key: string]: any
}

interface MalzemeCikisRequestDetail {
  RequestId?: number
  RelatedRequestId?: number
  FirmaId?: string
  TeslimAlanSirketId?: string
  TeslimAlanSirketAd?: string
  VarlikYonetimiId?: string
  VarlikYonetimiKaydiVarMi?: string
  AssetNo?: string
  SeriNo?: string
  Adet?: string
  CikisNedeni?: string
  Tarih?: Date | string
  Tip?: string
  Marka?: string
  Model?: string
  Sube?: string
  GarantiKapsamindaMi?: string
}

interface SearchResult {
  ASSET_ID: string
  ASSET_NO: string
  SERIAL_NO: string
  BRAND: string
  MODEL: string
  TYPE: string
  COMPANY_ID: string
  COMPANY_NAME: string
}

const MalzemeCikisFormuScreen: React.FC = () => {
  const { t } = useTranslation(['malzemeCikisFormuSchema', 'common'])
  const { initialData, setDefinitionId, setUpdateEntity } = useWorkflow()
  const { control, reset, setValue, handleSubmit } = useFormContext<MalzemeCikisFormuFormData>()
  const [storedValue] = useLocalStorage<number>('UserId')
  const updateEntityMutation = useUpdateEntity()

  // State
  const [malzemeCikisRequestDetailDetails, setMalzemeCikisRequestDetailDetails] = useState<MalzemeCikisRequestDetail[]>([])
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date())
  const [selectedCompany, setSelectedCompany] = useState<string>('')
  const [selectedDeliveryCompany, setSelectedDeliveryCompany] = useState<string>('')
  const [deliveryCompanyName, setDeliveryCompanyName] = useState<string>('')
  const [selectedExitReason, setSelectedExitReason] = useState<string>('')
  const [selectedBranch, setSelectedBranch] = useState<string>('')
  const [searchBy, setSearchBy] = useState<string>('ASSET_NO')
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [selectedProduct, setSelectedProduct] = useState<SearchResult | null>(null)
  const [productQuantity, setProductQuantity] = useState<string>('1')
  const [warrantyStatus, setWarrantyStatus] = useState<string>('H')
  const [assetManagementRecord, setAssetManagementRecord] = useState<string>('H')

  // Grid columns definition
  const columns = [
    {
      field: 'AssetNo',
      headerName: t('assetNo'),
      flex: 1,
    },
    {
      field: 'SeriNo',
      headerName: t('serialNo'),
      flex: 1,
    },
    {
      field: 'Marka',
      headerName: t('brand'),
      flex: 1,
    },
    {
      field: 'Model',
      headerName: t('model'),
      flex: 1,
    },
    {
      field: 'Tip',
      headerName: t('type'),
      flex: 1,
    },
    {
      field: 'Adet',
      headerName: t('quantity'),
      flex: 1,
      type: 'numeric' as const,
    },
    {
      field: 'Sube',
      headerName: t('branch'),
      flex: 1.5,
    },
    {
      field: 'GarantiKapsamindaMi',
      headerName: t('warrantyStatus'),
      flex: 1.5,
      renderCell: (params: any) => (params.value === 'E' ? t('yes') : t('no')),
    },
  ]

  useUpdateEffect(() => {
    if (initialData?.entity) {
      reset({
        MalzemeCikisRequestDetailDetails: initialData.entity.MalzemeCikisRequestDetailDetails || [],
      })
      setUpdateEntity({
        id: initialData.entity.requestId,
        properties: {
          LastUpdated: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
          LastUpdatedBy: storedValue,
        },
        emailRule: null,
      })
      if (initialData?.loadEntityToControlsDto?.MalzemeCikisRequestDetailDetails) {
        setMalzemeCikisRequestDetailDetails(initialData.loadEntityToControlsDto.MalzemeCikisRequestDetailDetails)
      }
    }
  }, [initialData])

  useEffect(() => {
    setDefinitionId(2892)
    setValue('WorkflowDefId', 2892)
    if (!initialData?.entity) {
      setValue('CreatedBy', storedValue)
      setValue('Created', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
    } else {
      setValue('CreatedBy', initialData.entity.createdBy)
      setValue('Created', initialData.entity.created)
      setValue('LastUpdatedBy', storedValue)
      setValue('LastUpdated', dayjs().format('YYYY-MM-DDTHH:mm:ss'))
    }
  }, [setDefinitionId, setValue, initialData, storedValue])

  const handleSearch = async () => {
    if (!searchTerm) {
      toast.error(t('pleaseEnterSearchTerm'))
      return
    }

    try {
      const response = await fetch(`/api/workflows/malzeme-cikis/search-products?searchTerm=${encodeURIComponent(searchTerm)}&searchBy=${searchBy}`)
      if (!response.ok) {
        throw new Error('Search failed')
      }

      const results = await response.json()
      if (results && results.length > 0) {
        // For now, just select the first result
        const firstResult = results[0]
        setSelectedProduct({
          ASSET_ID: firstResult.assetId,
          ASSET_NO: firstResult.assetNo,
          SERIAL_NO: firstResult.serialNo,
          BRAND: firstResult.brand,
          MODEL: firstResult.model,
          TYPE: firstResult.type,
          COMPANY_ID: firstResult.companyId,
          COMPANY_NAME: firstResult.companyName,
        })
        toast.success(t('productFound'))
      } else {
        toast.error(t('noProductsFound'))
      }
    } catch (error) {
      console.error('Search error:', error)
      toast.error(t('searchError'))
    }
  }

  const handleAddProduct = () => {
    if (!selectedProduct) {
      toast.error(t('pleaseSelectProduct'))
      return
    }

    if (!selectedCompany || !selectedDeliveryCompany || !selectedExitReason || !selectedBranch) {
      toast.error(t('pleaseFillRequiredFields'))
      return
    }

    const newDetail: MalzemeCikisRequestDetail = {
      RelatedRequestId: initialData?.entity?.requestId || 0,
      FirmaId: selectedCompany,
      TeslimAlanSirketId: selectedDeliveryCompany,
      TeslimAlanSirketAd: deliveryCompanyName,
      VarlikYonetimiId: selectedProduct.ASSET_ID,
      VarlikYonetimiKaydiVarMi: assetManagementRecord,
      AssetNo: selectedProduct.ASSET_NO,
      SeriNo: selectedProduct.SERIAL_NO,
      Adet: productQuantity,
      CikisNedeni: selectedExitReason,
      Tarih: selectedDate || undefined,
      Tip: selectedProduct.TYPE,
      Marka: selectedProduct.BRAND,
      Model: selectedProduct.MODEL,
      Sube: selectedBranch,
      GarantiKapsamindaMi: warrantyStatus,
    }

    const updatedDetails = [...malzemeCikisRequestDetailDetails, newDetail]
    setMalzemeCikisRequestDetailDetails(updatedDetails)
    setValue('MalzemeCikisRequestDetailDetails', updatedDetails)

    // Reset product selection
    setSelectedProduct(null)
    setSearchTerm('')
    setProductQuantity('1')
    toast.success(t('productAddedSuccessfully'))
  }

  const handleDeleteProduct = (index: number) => {
    const updatedDetails = malzemeCikisRequestDetailDetails.filter((_, i) => i !== index)
    setMalzemeCikisRequestDetailDetails(updatedDetails)
    setValue('MalzemeCikisRequestDetailDetails', updatedDetails)
    toast.success(t('productDeletedSuccessfully'))
  }

  const onSubmit = async () => {
    if (!initialData?.entity?.requestId) {
      toast.error(t('entityIdNotFound'))
      return
    }

    try {
      const formData = {
        MalzemeCikisRequestDetailDetails: malzemeCikisRequestDetailDetails,
        LastUpdated: dayjs().format('YYYY-MM-DDTHH:mm:ss'),
        LastUpdatedBy: storedValue,
      }

      await updateEntityMutation.mutateAsync({
        request: {
          id: initialData.entity.requestId,
          properties: formData,
          emailRule: null,
        },
        workflowName: 'malzeme-cikis',
      })

      toast.success(t('common:workflow.updateSuccess'))
    } catch (error) {
      console.error('Update error:', error)
      toast.error(t('common:workflow.updateError'))
    }
  }

  const handleSave = () => {
    handleSubmit(onSubmit)()
  }

  return (
    <>
      {!initialData ? (
        <Loading show={true} />
      ) : (
        <WGrid container spacing={3}>
          {/* Basic Information Section */}
          <WorkflowSection title={t('basicInformation')}>
            {/* Date */}
            {initialData?.visibilitySettings?.CanSeeDate && (
              <WGrid item xs={12} md={4}>
                <DigiDatePicker
                  label={t('date')}
                  value={selectedDate}
                  size="small"
                  onChange={(value) => setSelectedDate(value)}
                  disabled={!initialData?.enabilitySettings?.CanEditDate}
                />
              </WGrid>
            )}

            {/* Company */}
            {initialData?.visibilitySettings?.CanSeeCompany && (
              <WGrid item xs={12} md={4}>
                <SelectBox
                  label={t('company')}
                  value={initialData?.newWorkflowLoadingDto?.CompanyOptions?.find((opt: any) => opt.value === selectedCompany) || null}
                  onChange={(option: any) => setSelectedCompany(option?.value || '')}
                  options={initialData?.newWorkflowLoadingDto?.CompanyOptions || []}
                  id="company"
                  name="company"
                  defaultText={t('selectCompany')}
                  disabled={!initialData?.enabilitySettings?.CanEditCompany}
                />
              </WGrid>
            )}

            {/* Exit Reason */}
            {initialData?.visibilitySettings?.CanSeeExitReason && (
              <WGrid item xs={12} md={4}>
                <SelectBox
                  label={t('exitReason')}
                  value={initialData?.newWorkflowLoadingDto?.ExitReasonOptions?.find((opt: any) => opt.value === selectedExitReason) || null}
                  onChange={(option: any) => setSelectedExitReason(option?.value || '')}
                  options={initialData?.newWorkflowLoadingDto?.ExitReasonOptions || []}
                  id="exitReason"
                  name="exitReason"
                  defaultText={t('selectExitReason')}
                  disabled={!initialData?.enabilitySettings?.CanEditExitReason}
                />
              </WGrid>
            )}
          </WorkflowSection>

          {/* Delivery Information Section */}
          <WorkflowSection title={t('deliveryInformation')}>
            {/* Delivery Company */}
            {initialData?.visibilitySettings?.CanSeeDeliveryCompany && (
              <>
                <WGrid item xs={12} md={6}>
                  <SelectBox
                    label={t('deliveryCompany')}
                    value={initialData?.newWorkflowLoadingDto?.CompanyOptions?.find((opt: any) => opt.value === selectedDeliveryCompany) || null}
                    onChange={(option: any) => {
                      setSelectedDeliveryCompany(option?.value || '')
                      const company = initialData?.newWorkflowLoadingDto?.CompanyOptions?.find((c: any) => c.value === option?.value)
                      setDeliveryCompanyName(company?.label || '')
                    }}
                    options={initialData?.newWorkflowLoadingDto?.CompanyOptions || []}
                    id="deliveryCompany"
                    name="deliveryCompany"
                    defaultText={t('selectDeliveryCompany')}
                    disabled={!initialData?.enabilitySettings?.CanEditDeliveryCompany}
                  />
                </WGrid>
                <WGrid item xs={12} md={6}>
                  <DigiTextField label={t('deliveryCompanyName')} value={deliveryCompanyName} fullWidth variant="outlined" disabled />
                </WGrid>
              </>
            )}

            {/* Branch */}
            {initialData?.visibilitySettings?.CanSeeBranch && (
              <WGrid item xs={12} md={6}>
                <SelectBox
                  label={t('branch')}
                  value={initialData?.newWorkflowLoadingDto?.BranchOptions?.find((opt: any) => opt.value === selectedBranch) || null}
                  onChange={(option: any) => setSelectedBranch(option?.value || '')}
                  options={initialData?.newWorkflowLoadingDto?.BranchOptions || []}
                  id="branch"
                  name="branch"
                  defaultText={t('selectBranch')}
                  disabled={!initialData?.enabilitySettings?.CanEditBranch}
                />
              </WGrid>
            )}
          </WorkflowSection>

          {/* Product Search Section */}
          {initialData?.visibilitySettings?.CanSeeProductSearch && (
            <WorkflowSection title={t('productSearch')}>
              {/* Search By Radio Group */}
              <WGrid item xs={12}>
                <Controller
                  name="searchBy"
                  control={control}
                  defaultValue="ASSET_NO"
                  render={({ field }) => (
                    <DigiRadioGroup
                      {...field}
                      axis="horizontal"
                      label={t('searchBy')}
                      value={searchBy}
                      onChange={(value) => setSearchBy(value)}
                      options={[
                        { label: t('assetNo'), value: 'ASSET_NO' },
                        { label: t('serialNo'), value: 'SERIAL_NO' },
                        { label: t('brand'), value: 'BRAND' },
                        { label: t('model'), value: 'MODEL' },
                      ]}
                      disabled={!initialData?.enabilitySettings?.CanEditProductSearch}
                    />
                  )}
                />
              </WGrid>

              {/* Search Input and Button */}
              <WGrid item xs={12} md={8}>
                <DigiTextField
                  label={t('searchTerm')}
                  value={searchTerm}
                  onChange={(value) => setSearchTerm(value)}
                  fullWidth
                  variant="outlined"
                  disabled={!initialData?.enabilitySettings?.CanEditProductSearch}
                />
              </WGrid>
              {initialData?.visibilitySettings?.CanSeeSearchButton && (
                <WGrid item xs={12} md={4}>
                  <WButton
                    variant="contained"
                    color="primary"
                    onClick={handleSearch}
                    disabled={!initialData?.enabilitySettings?.CanPressSearchButton}
                    fullWidth
                    style={{ height: '56px' }}
                  >
                    {t('search')}
                  </WButton>
                </WGrid>
              )}

              {/* Product Details */}
              {selectedProduct && (
                <>
                  <WGrid item xs={12} md={3}>
                    <DigiTextField label={t('assetNo')} value={selectedProduct.ASSET_NO} fullWidth variant="outlined" disabled />
                  </WGrid>
                  <WGrid item xs={12} md={3}>
                    <DigiTextField label={t('serialNo')} value={selectedProduct.SERIAL_NO} fullWidth variant="outlined" disabled />
                  </WGrid>
                  <WGrid item xs={12} md={3}>
                    <DigiTextField label={t('brand')} value={selectedProduct.BRAND} fullWidth variant="outlined" disabled />
                  </WGrid>
                  <WGrid item xs={12} md={3}>
                    <DigiTextField label={t('model')} value={selectedProduct.MODEL} fullWidth variant="outlined" disabled />
                  </WGrid>

                  {/* Additional Product Options */}
                  <WGrid item xs={12} md={3}>
                    <DigiTextField
                      label={t('quantity')}
                      value={productQuantity}
                      onChange={(value) => setProductQuantity(value)}
                      fullWidth
                      variant="outlined"
                      type="number"
                      disabled={!initialData?.enabilitySettings?.CanEditProductSearch}
                    />
                  </WGrid>
                  <WGrid item xs={12} md={3}>
                    <Controller
                      name="warrantyStatus"
                      control={control}
                      defaultValue="H"
                      render={({ field }) => (
                        <DigiRadioGroup
                          {...field}
                          axis="horizontal"
                          label={t('warrantyStatus')}
                          value={warrantyStatus}
                          onChange={(value) => setWarrantyStatus(value)}
                          options={[
                            { label: t('yes'), value: 'E' },
                            { label: t('no'), value: 'H' },
                          ]}
                          disabled={!initialData?.enabilitySettings?.CanEditProductSearch}
                        />
                      )}
                    />
                  </WGrid>
                  <WGrid item xs={12} md={3}>
                    <Controller
                      name="assetManagementRecord"
                      control={control}
                      defaultValue="H"
                      render={({ field }) => (
                        <DigiRadioGroup
                          {...field}
                          axis="horizontal"
                          label={t('assetManagementRecord')}
                          value={assetManagementRecord}
                          onChange={(value) => setAssetManagementRecord(value)}
                          options={[
                            { label: t('yes'), value: 'E' },
                            { label: t('no'), value: 'H' },
                          ]}
                          disabled={!initialData?.enabilitySettings?.CanEditProductSearch}
                        />
                      )}
                    />
                  </WGrid>

                  {/* Add Button */}
                  {initialData?.visibilitySettings?.CanSeeAddButton && (
                    <WGrid item xs={12} md={3}>
                      <WButton
                        variant="contained"
                        color="success"
                        onClick={handleAddProduct}
                        disabled={!initialData?.enabilitySettings?.CanPressAddButton}
                        fullWidth
                      >
                        {t('addProduct')}
                      </WButton>
                    </WGrid>
                  )}
                </>
              )}
            </WorkflowSection>
          )}

          {/* Materials Grid Section */}
          {initialData?.visibilitySettings?.CanSeeGrid && (
            <WorkflowSection title={t('materialsList')}>
              <WGrid item xs={12}>
                <DigiTable
                  columns={columns}
                  data={malzemeCikisRequestDetailDetails}
                  actions={[
                    {
                      icon: 'delete',
                      tooltip: t('delete'),
                      onClick: (_row: any, index: any) => handleDeleteProduct(index),
                      disabled: !initialData?.enabilitySettings?.CanDeleteProduct,
                    },
                  ]}
                  pageSize={10}
                />
              </WGrid>
            </WorkflowSection>
          )}

          {/* Save Button */}
          {initialData?.visibilitySettings?.CanSeeSaveButton && (
            <WGrid container alignItems="center" direction="row-reverse" style={{ marginTop: 14, marginLeft: 8 }}>
              <WButton
                variant="contained"
                onClick={handleSave}
                disabled={updateEntityMutation.isPending || !initialData?.entity?.requestId || !initialData?.enabilitySettings?.CanPressSaveButton}
                style={{ marginRight: '8px' }}
              >
                {updateEntityMutation.isPending && <WCircularProgress size={20} color="inherit" style={{ marginRight: 8 }} />}
                {t('save')}
              </WButton>
            </WGrid>
          )}

          <Controller
            name="MalzemeCikisRequestDetailDetails"
            control={control}
            render={({ field }) => <input type="hidden" {...field} value={JSON.stringify(field.value || [])} />}
          />
        </WGrid>
      )}
    </>
  )
}

export default MalzemeCikisFormuScreen
