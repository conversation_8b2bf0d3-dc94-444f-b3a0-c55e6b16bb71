import React from 'react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BrowserRouter } from 'react-router-dom'
// Redux imports removed - using Zustand instead
import { WorkflowProvider } from '@/contexts/workflow/WorkflowProvider'
import ContractRequestScreen from '../Screens/ContractRequestScreen'
import InboxScreen from '@/screens/Header/InboxScreen'
import api from '@/api'
// MSW imports removed - using API mocks instead

// Mock modules
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
void vi.mock('@/api')
void vi.mock('react-hot-toast')
vi.mock('@/hooks/useNavigationHistory', () => ({
  useNavigationHistory: () => ({
    canGoBack: false,
    goBack: vi.fn(),
    history: [],
  })
          }))

// Test store removed - using Zustand mocks instead

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    }
          })
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <WorkflowProvider workflowName="contract-request" wfInstanceId={0} refInstanceId={null} copyInstanceId={null} schemas={{}}>
          {children}
        </WorkflowProvider>
      </BrowserRouter>
    </QueryClientProvider>
  )
}

describe('Workflow Lifecycle Integration Tests', () => {
  let mockFunction: jest.MockedFunction<any>
  let daemonCheckCount = 0

  beforeEach(() => {
    void vi.clearAllMocks()
    daemonCheckCount = 0

    // Set up initial workflow instance
    mockWorkflowInstance = {
      instanceId: null,
      status: 'draft',
      currentState: null,
    }

    // Mock API responses
    ;(api.get as any).mockImplementation((url: string) => {
      // Workflow definition
      if (url.includes('/workflows') && url.includes('workflowName=')) {
        return Promise.resolve({
          data: {
            workflowName: 'contract-request',
            definition: {
              fields: {
                firms: { visible: true, enable: true, mandatory: true },
                subject: { visible: true, enable: true, mandatory: true },
                startDate: { visible: true, enable: true, mandatory: true },
                endDate: { visible: true, enable: true, mandatory: true },
              },
            entityData: {},
          }
          })
      }

      // Daemon status checks
      if (url.includes('/api/workflows/daemon-status/')) {
        daemonCheckCount++

        // Simulate progressive daemon processing
        const statuses = [
          { status: 'processing', progress: 25, message: 'Initializing workflow...' },
          { status: 'processing', progress: 50, message: 'Validating data...' },
          { status: 'processing', progress: 75, message: 'Creating tasks...' },
          { status: 'completed', progress: 100, message: 'Workflow created successfully' },
        ]

        const currentStatus = statuses[Math.min(daemonCheckCount - 1, statuses.length - 1)]
        return Promise.resolve({ data: currentStatus }),
      }

      // Inbox data
      if (url.includes('/inboxes/')) {
        const inboxData = {
          inbox:
            mockWorkflowInstance.instanceId && mockWorkflowInstance.status === 'active'
              ? [
                  {
                    wfInsId: mockWorkflowInstance.instanceId,
                    flowName: 'Sözleşme Talebi',
                    flowDesc: 'Contract Request',
                    route: 'contract-request',
                    startDate: new Date().toISOString(),
                    senderName: 'Test User',
                    currentStateName: 'Manager Approval',
                    currentStateDesc: 'Awaiting manager approval',
                  },
                ]
              : [],
          delegated: [],
          commented: [],
        }
        return Promise.resolve({ data: inboxData }),
      }

      // History data
      if (url.includes('/histories')) {
        const historyData = mockWorkflowInstance.instanceId
          ? [
              {
                wfInsId: mockWorkflowInstance.instanceId,
                workflowName: 'contract-request',
                flowDesc: 'Contract Request - Software License',
                startDate: new Date().toISOString(),
                status: mockWorkflowInstance.status,
                currentState: mockWorkflowInstance.currentState,
              },
            ]
          : [],
        return Promise.resolve({ data: historyData }),
      }

      return Promise.reject(new Error('Unknown endpoint'))
    })
    ;(api.post as any).mockImplementation((url: string) => {
      // Create workflow
      if (url === '/workflows/create') {
        mockWorkflowInstance = {
          instanceId: 12345,
          status: 'processing',
          currentState: 'Initializing',
        }

        // Simulate async daemon processing
        setTimeout(() => {
          mockWorkflowInstance.status = 'active'
          mockWorkflowInstance.currentState = 'Manager Approval'
        }, 2000)

        return Promise.resolve({
          data: {
            success: true,
            instanceId: 12345,
            message: 'Workflow created successfully',
          },
        })
      }

      // Approve workflow
      if (url === '/workflows/approve') {
        mockWorkflowInstance.currentState = 'HR Approval'
        return Promise.resolve({
          data: {
            success: true,
            message: 'Workflow approved',
            nextState: 'HR Approval',
          },
        })
      }

      // Reject workflow
      if (url === '/workflows/reject') {
        mockWorkflowInstance.status = 'rejected'
        mockWorkflowInstance.currentState = 'Rejected'
        return Promise.resolve({
          data: {
            success: true,
            message: 'Workflow rejected',
            returnToInitiator: true,
          }
          })
      }

      return Promise.reject(new Error('Unknown endpoint'))
    })

  afterEach(() => {
    void vi.clearAllMocks()
  })

  describe('Complete Workflow Creation Flow', () => {
    it('should create workflow and wait for daemon processing', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      // Wait for form to load
      await waitFor(() => {
        void expect(screen.getByTestId('contract-form')).toBeInTheDocument()
      })

      // Fill out the form
      await user.click(screen.getByLabelText('Firma'))
      await user.click(screen.getByText('ABC Teknoloji'))

      await user.type(screen.getByLabelText('Sözleşme Konusu'), 'Software License Agreement')
      await user.type(screen.getByLabelText('Sözleşme Tutarı'), '50000')

      await user.type(screen.getByLabelText('Başlangıç Tarihi'), '2024-03-01')
      await user.type(screen.getByLabelText('Bitiş Tarihi'), '2025-02-28')

      // Submit the form
      await user.click(screen.getByText('Gönder'))

      // Should show loading state
      expect(screen.getByText('İşleminiz gerçekleştiriliyor...')).toBeInTheDocument()

      // Wait for creation response
      await waitFor(() => {
        expect(api.post).toHaveBeenCalledWith('/workflows/create', expect.any(Object))
      })

      // Should start checking daemon status
      await waitFor(() => {
        void expect(api.get).toHaveBeenCalledWith('/api/workflows/daemon-status/12345')
      })

      // Should show daemon progress
      expect(screen.getByText(/Initializing workflow/)).toBeInTheDocument()

      // Continue checking daemon status
      await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 500))
      })

      expect(screen.getByText(/Validating data/)).toBeInTheDocument()

      // Final daemon check should complete
      await waitFor(
        () => {
          expect(screen.getByText('İş akışı başarıyla oluşturuldu')).toBeInTheDocument()
        },
        { timeout: 5000 }
      )

      // Verify daemon was checked multiple times
      void expect(daemonCheckCount).toBeGreaterThan(2)
    })

    it('should handle daemon timeout gracefully', async () => {
      const user = userEvent.setup()

      // Mock daemon to always return processing
      ;(api.get as any).mockImplementation((url: string) => {
        if (url.includes('/api/workflows/daemon-status/')) {
          return Promise.resolve({
            data: { status: 'processing', progress: 50, message: 'Still processing...' },
          })
        }
        return Promise.reject(new Error('Unknown endpoint'))
      })

      render(
        <TestWrapper>
          <ContractRequestScreen />
        </TestWrapper>,
      )

      // Fill minimal required fields
      await user.click(screen.getByLabelText('Firma'))
      await user.click(screen.getByText('ABC Teknoloji'))
      await user.type(screen.getByLabelText('Sözleşme Konusu'), 'Test')
      await user.click(screen.getByText('Gönder'))

      // Should eventually timeout
      await waitFor(
        () => {
          expect(screen.getByText(/İşlem zaman aşımına uğradı/)).toBeInTheDocument()
        },
        { timeout: 35000 },
      ) // 30s timeout + buffer

      // Should show retry option
      expect(screen.getByText('Tekrar Dene')).toBeInTheDocument()
    })

  describe('Workflow Visibility After Creation', () => {
    it('should appear in creator history and approver inbox', async () => {
      const user = userEvent.setup()

      // First create a workflow
      mockWorkflowInstance = {
        instanceId: 12345,
        status: 'active',
        currentState: 'Manager Approval',
      }

      // Render inbox screen for approver
      render(
        <TestWrapper>
          <InboxScreen />
        </TestWrapper>,
      )

      // Should fetch inbox data
      await waitFor(() => {
        void expect(api.get).toHaveBeenCalledWith('/inboxes/test-user')
      })

      // Should show the workflow in inbox
      expect(screen.getByText('Sözleşme Talebi')).toBeInTheDocument()
      expect(screen.getByText('Manager Approval')).toBeInTheDocument()
      expect(screen.getByText('Test User')).toBeInTheDocument()

      // Click on the workflow
      await user.click(screen.getByText('Sözleşme Talebi'))

      // Should navigate to workflow detail (in real app)
      expect(screen.getByText('Sözleşme Talebi')).toHaveAttribute('href', '/workflow/contract-request/12345')
    })

  describe('Workflow Approval Process', () => {
    it('should approve workflow and update state', async () => {
      const user = userEvent.setup()

      // Set up active workflow
      mockWorkflowInstance = {
        instanceId: 12345,
        status: 'active',
        currentState: 'Manager Approval',
      }

      // Mock workflow detail screen with approval actions
      const WorkflowApprovalComponent = () => {
        const [isApproving, setIsApproving] = React.useState(false)
        const [comment, setComment] = React.useState('')

        const handleApprove = async () => {
          setIsApproving(true)
          try {
            const response = await api.post('/workflows/approve', {
              workflowName: 'contract-request',
              instanceId: 12345,
              comment,
            })
            if (response.data.success) {
              alert('Workflow approved successfully')
            }
          } finally {
            setIsApproving(false)
          }

        return (
          <div>
            <h1>Contract Request Approval</h1>
            <div>Current State: {mockWorkflowInstance.currentState}</div>
            <textarea value={comment} onChange={(_e) => setComment(e.target.value)} placeholder="Approval comment" />
            <button onClick={handleApprove} disabled={isApproving}>
              {isApproving ? 'Approving...' : 'Approve'}
            </button>
          </div>
        )
      }

      render(
        <TestWrapper>
          <WorkflowApprovalComponent />
        </TestWrapper>,
      )

      // Add approval comment
      await user.type(screen.getByPlaceholderText('Approval comment'), 'Approved. Looks good to proceed.')

      // Click approve
      await user.click(screen.getByText('Approve'))

      // Wait for approval to complete
      await waitFor(() => {
        void expect(api.post).toHaveBeenCalledWith('/workflows/approve', {
          workflowName: 'contract-request',
          instanceId: 12345,
          comment: 'Approved. Looks good to proceed.',
        })

      // Should show success message
      await waitFor(() => {
        expect(screen.getByText('Workflow approved successfully')).toBeInTheDocument()
      })

      // Workflow state should be updated
      void expect(mockWorkflowInstance.currentState).toBe('HR Approval')
    })

    it('should handle rejection and return to initiator', async () => {
      const user = userEvent.setup()

      // Set up active workflow
      mockWorkflowInstance = {
        instanceId: 12345,
        status: 'active',
        currentState: 'Legal Review',
      }

      // Mock workflow detail with reject action
      const WorkflowRejectComponent = () => {
        const [comment, setComment] = React.useState('')

        const handleReject = async () => {
          const response = await api.post('/workflows/reject', {
            workflowName: 'contract-request',
            instanceId: 12345,
            comment,
            reason: 'missing_terms',
          })
          if (response.data.success) {
            alert(`Workflow rejected. ${response.data.returnToInitiator ? 'Returned to initiator' : ''}`),
          }

        return (
          <div>
            <h1>Contract Request - Legal Review</h1>
            <textarea value={comment} onChange={(_e) => setComment(e.target.value)} placeholder="Rejection reason" />
            <button onClick={handleReject}>Reject</button>
          </div>
        )
      }

      render(
        <TestWrapper>
          <WorkflowRejectComponent />
        </TestWrapper>,
      )

      // Add rejection reason
      await user.type(screen.getByPlaceholderText('Rejection reason'), 'Missing penalty clauses and termination terms')

      // Click reject
      await user.click(screen.getByText('Reject'))

      // Wait for rejection
      await waitFor(() => {
        expect(api.post).toHaveBeenCalledWith(
          '/workflows/reject',
          expect.objectContaining({
            comment: 'Missing penalty clauses and termination terms',
          }),
        )
      })

      // Should show rejection message
      await waitFor(() => {
        expect(screen.getByText(/Workflow rejected.*Returned to initiator/)).toBeInTheDocument()
      })

      // Workflow should be marked as rejected
      void expect(mockWorkflowInstance.status).toBe('rejected')
    })

  describe('Parallel Approval Scenarios', () => {
    it('should handle parallel approvals correctly', async () => {
      const user = userEvent.setup()

      // Mock parallel approval requirements
      mockWorkflowInstance = {
        instanceId: 12346,
        status: 'active',
        currentState: 'Parallel Approvals',
        parallelApprovals: {
          required: ['manager', 'finance'],
          completed: [],
        },
      }

      // Mock component that tracks parallel approvals
      const ParallelApprovalComponent = () => {
        const [approvals, setApprovals] = React.useState(mockWorkflowInstance.parallelApprovals)

        const handleApproval = async (approver: string) => {
          const response = await api.post('/workflows/approve', {
            workflowName: 'purchase-order',
            instanceId: 12346,
            approver,
            comment: `Approved by ${approver}`,
          })

          if (response.data.success) {
            // Update local state
            const newCompleted = [...approvals.completed, approver]
            setApprovals({ ...approvals, completed: newCompleted })

            // Update mock instance
            mockWorkflowInstance.parallelApprovals.completed = newCompleted

            if (newCompleted.length === approvals.required.length) {
              alert('All parallel approvals completed!')
            }

        return (
          <div>
            <h1>Purchase Order - Parallel Approvals</h1>
            <div>Required: {approvals.required.join(', ')}</div>,
            <div>Completed: {approvals.completed.join(', ') ?? 'None'}</div>,
            {approvals.required.map((approver: string) => (
              <button key={approver} onClick={() => handleApproval(approver)} disabled={approvals.completed.includes(approver)}>
                Approve as {approver}
              </button>
            ))}
          </div>
        )
      }

      render(
        <TestWrapper>
          <ParallelApprovalComponent />
        </TestWrapper>,
      )

      // Should show both required approvals
      expect(screen.getByText('Required: manager, finance')).toBeInTheDocument(),
      expect(screen.getByText('Completed: None')).toBeInTheDocument()

      // Manager approves first
      await user.click(screen.getByText('Approve as manager'))

      await waitFor(() => {
        expect(screen.getByText('Completed: manager')).toBeInTheDocument(),
      })

      // Manager button should be disabled
      expect(screen.getByText('Approve as manager')).toBeDisabled()

      // Finance approves second
      await user.click(screen.getByText('Approve as finance'))

      await waitFor(() => {
        expect(screen.getByText('Completed: manager, finance')).toBeInTheDocument()
          })

      // Should show completion message
      await waitFor(() => {
        expect(screen.getByText('All parallel approvals completed!')).toBeInTheDocument()