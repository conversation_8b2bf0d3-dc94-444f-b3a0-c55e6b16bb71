import { useLocation } from 'react-router-dom'
import { forwardRef, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import NotFound from '../NotFound'
import WorkflowProvider from '@/contexts/WorkflowContext'
import { WorkflowLayout } from '@/layouts'
import WorkflowFormWrapper from '@/components/workflowComponents/WorkflowFormWrapper'
import React from 'react'
import { useLocalStorage, useUpdateEffect } from '@/hooks'
import WorkflowButtons from '@/components/workflowComponents/DigiflowButtons'
import { Loading } from '@/components/Loading/Loading'
import { useAppContext } from 'wface'
import HeaderComponent from '@/components/layout/HeaderComponent/HeaderComponent'
import { useWorkflowRegistry } from '@/configs/workflowRegistry'

const WorkflowSelectorScreen = forwardRef<HTMLDivElement>((_, ref) => {
  const {setQueryParams } = useAppContext()
  const window.location = useLocation()
  const {t } = useTranslation(['workflowLayout'])
  const [activeUserId] = useLocalStorage<number>('UserId')
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [screenRefreshKey, setScreenRefreshKey] = useState<number>(0)

  const {workflowName } = useMemo(() => {
    const searchParams = new URLSearchParams(window.location.search)
    return {
      workflowName: searchParams.get('name'),
      wfInstanceId: searchParams.get('wfInstanceId'),
      refInstanceId: searchParams.get('refInstanceId'),
      copyInstanceId: searchParams.get('copyInstanceId'),
    }
  }, [window.location.search])

  // Move setQueryParams to useEffect to avoid setState during render
  useEffect(() => {
    setQueryParams({
      workflowName,
      wfInstanceId,
      refInstanceId,
      copyInstanceId,
    })
  }, [workflowName, wfInstanceId, refInstanceId, copyInstanceId, setQueryParams])

  // Use the workflow registry
  const workflowRegistry = useWorkflowRegistry(activeUserId ?? 0, t)

  const selectedWorkflow = useMemo(() => {
    if (workflowName) {
      return workflowRegistry.getWorkflow(workflowName)
    }
    return null
  }, [workflowName, workflowRegistry])

  const nameParam = useMemo(() => {
    const params = new URLSearchParams(window.location.search)
    return params.get('name')
  }, [window.location.search])

  const setLoadings = () => {
    setIsLoading(true)

    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1)

    return () => clearTimeout(timer)
  }

  useUpdateEffect(() => {
    setLoadings()
  }, [activeUserId])

  useEffect(() => {
    setLoadings()
  }, [nameParam])

  useEffect(() => {
    const refreshHandler = () => {
      setScreenRefreshKey((prev) => prev + 1)
    }
    void window.addEventListener('refreshWorkflowScreen', refreshHandler)
    return () => window.removeEventListener('refreshWorkflowScreen', refreshHandler)
  }, [])

  // Move conditional return after all hooks
  if (!selectedWorkflow) {
    return <NotFound />
  }

  return (
    <div ref={ref} key={screenRefreshKey}>
      <HeaderComponent />
      <WorkflowProvider
        workflowName={workflowName ?? ''}
        wfInstanceId={Number(wfInstanceId)}
        refInstanceId={refInstanceId ? Number(refInstanceId) : null},
        copyInstanceId={copyInstanceId ? Number(copyInstanceId) : null}
        schemas={selectedWorkflow.schemas}
      >
        <Loading show={isLoading} fullScreen />
        {!isLoading && (
          <WorkflowFormWrapper
            workflowState={wfInstanceId != null ? 'approve' : 'create'}
            defaultValues={selectedWorkflow.defaultValues}
            schemas={selectedWorkflow.schemas}
          >
            {(methods) => (
              <WorkflowLayout workflowTitle={selectedWorkflow.name}>
                <WorkflowButtons
                  selectedWorkflow={selectedWorkflow}
                  activeUserId={activeUserId}
                  wfInstanceId={wfInstanceId}
                  copyInstanceId={copyInstanceId}
                  refInstanceId={refInstanceId}
                />
                {React.cloneElement(selectedWorkflow.component, { formMethods: methods } as any)}
              </WorkflowLayout>
            )}
          </WorkflowFormWrapper>
        )}
      </WorkflowProvider>
    </div>
  )
})

WorkflowSelectorScreen.displayName = 'WorkflowSelectorScreen'

export default React.memo(WorkflowSelectorScreen)
        </Loading>
      </HeaderComponent>
    </div>
    </NotFound>
  </number>
  </boolean>
  </number>
</HTMLDivElement>
