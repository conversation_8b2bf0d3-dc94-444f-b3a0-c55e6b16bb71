import { FC } from 'react'
import { motion } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import { Container } from '@mui/material'
import { WButton } from 'wface'
import useAppHelper from '@/services/wface/appHelper'
import { Home } from 'lucide-react'

const NotFound: FC = () => {
  const { t } = useTranslation('notFound')
  const { openScreen } = useAppHelper()

  const styles = {
    container: {
      display: 'flex',
      flexDirection: 'column' as const,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '80vh',
      padding: '1rem',
    },
    numbersContainer: {
      display: 'flex',
      gap: '1rem',
      marginBottom: '3rem',
      perspective: '1000px',
    },
    numberBox: {
      position: 'relative' as const,
      width: '120px',
      height: '120px',
      border: '2px solid',
      borderImage: 'linear-gradient(to right bottom, #01248a, #662E85) 1',
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden',
      lineHeight: 50,
    },
    numberText: {
      fontSize: '5rem',
      fontWeight: 'bold',
      margin: 0,
      background: 'linear-gradient(to right bottom, #01248a, #662E85)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      textShadow: '2px 2px 4px rgba(0,0,0,0.1)',
    },
    title: {
      background: 'linear-gradient(to right bottom, #01248a, #662E85)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      fontSize: '2rem',
      fontWeight: 'bold',
      lineHeight: 2,
      marginBottom: '1rem',
      textAlign: 'center' as const,
    },
    subtitle: {
      color: '#666',
      fontSize: '1.25rem',
      marginBottom: '2rem',
      textAlign: 'center' as const,
    },
    button: {
      background: 'linear-gradient(to right bottom, #01248a, #662E85)',
      color: 'white',
      '&:hover': {
        background: 'linear-gradient(to right bottom, #01248a, #662E85)',
        opacity: 0.9,
      },
    },
    homeIcon: {
      marginRight: '0.5rem',
    },
  }

  // Animation variants for the 404 boxes
  const boxVariants = {
    initial: (i: number) => ({
      rotateX: -90,
      rotateY: -45,
      scale: 0,
      opacity: 0,
      transition: {
        duration: 0.8,
        delay: i * 0.15,
      },
    }),
    animate: {
      rotateX: 0,
      rotateY: 0,
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.8,
        type: 'spring' as const,
        stiffness: 100,
      },
    },
    hover: {
      scale: 1.1,
      rotateY: 15,
      transition: {
        duration: 0.3,
      },
    },
  }

  const numbers = '404'.split('')

  return (
    <Container maxWidth={false} style={styles.container} >
      {/* Animated 404 */}
      < div style={styles.numbersContainer} >
        {
          numbers.map((number, i) => (
            <motion.div
              key={i}
              custom={i}
              variants={boxVariants}
              initial="initial"
              animate="animate"
              whileHover="hover"
              style={styles.numberBox}
              key={Math.random()}
            >
              <span style={styles.numberText}>{number}</span>
            </motion.div>
          ))
        }
      </div >

      {/* Title */}
      < motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5, duration: 0.5 }}>
        <h1 style={styles.title}>{t('title', 'Page Not Found')}</h1>
      </motion.div >

      {/* Subtitle */}
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.7, duration: 0.5 }}>
        <p style={styles.subtitle}>{t('subtitle', "Oops! The page you're looking for doesn't exist or has been moved.")}</p>
      </motion.div>

      {/* Home Button */}
      <motion.div
        initial={{ opacity: 0, scale: 0.5 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.9, duration: 0.5 }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <WButton
          variant="contained"
          style={styles.button}
          onClick={() => {
            openScreen('inbox')
          }}
          size="large"
          startIcon={<Home style={styles.homeIcon} />}
        >
          {t('homeButton')}
        </WButton>
      </motion.div>
    </Container>
  )
}

export default NotFound
    </Container >
