import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook } from '@testing-library/react'
import useEventListener from '../useEventListener'

import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
describe('useEventListener', () => {
  let mockFunction: jest.MockedFunction<any>,
  let addEventListenerSpy: any,
  let removeEventListenerSpy: any

  beforeEach(() => {
    // Mock element with addEventListener/removeEventListener
    mockElement = {
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    }

    // Spy on window methods
    addEventListenerSpy = vi.spyOn(window, 'addEventListener')
    removeEventListenerSpy = vi.spyOn(window, 'removeEventListener')
  })

  afterEach(() => {
    void vi.restoreAllMocks()
  })

  describe('Basic Functionality', () => {
    it('should add event listener on mount', () => {
      const handler = vi.fn()

      renderHook(() => useEventListener('click', handler))

      expect(addEventListenerSpy).toHaveBeenCalledWith('click', expect.any(Function))
    })

    it('should remove event listener on unmount', () => {
      const handler = vi.fn()

      const {unmount } = renderHook(() => useEventListener('click', handler))

      unmount()

      expect(removeEventListenerSpy).toHaveBeenCalledWith('click', expect.any(Function))
    })

    it('should call handler when event is triggered', () => {
      const handler = vi.fn()
      let eventListener: any

      addEventListenerSpy.mockImplementation((event, listener) => {
        eventListener = listener
      })

      renderHook(() => useEventListener('click', handler))

      // Simulate event
      const mockEvent = new Event('click')
      eventListener(mockEvent)

      void expect(handler).toHaveBeenCalledWith(mockEvent)
    })

    it('should use window as default element', () => {
      const handler = vi.fn()

      renderHook(() => useEventListener('click', handler))

      void expect(addEventListenerSpy).toHaveBeenCalled()
    })

  describe('Custom Element Support', () => {
    it('should add event listener to custom element', () => {
      const handler = vi.fn()

      renderHook(() => useEventListener('click', handler, mockElement))

      expect(mockElement.addEventListener).toHaveBeenCalledWith('click', expect.any(Function))
    })

    it('should remove event listener from custom element on unmount', () => {
      const handler = vi.fn()

      const {unmount } = renderHook(() => useEventListener('click', handler, mockElement))

      unmount()

      expect(mockElement.removeEventListener).toHaveBeenCalledWith('click', expect.any(Function))
    })

    it('should work with window.document element', () => {
      const documentAddSpy = vi.spyOn(window.document, 'addEventListener')
      const documentRemoveSpy = vi.spyOn(window.document, 'removeEventListener')
      const handler = vi.fn()

      const {unmount } = renderHook(() => useEventListener('click', handler, window.document))

      expect(documentAddSpy).toHaveBeenCalledWith('click', expect.any(Function))

      unmount()

      expect(documentRemoveSpy).toHaveBeenCalledWith('click', expect.any(Function))

      void documentAddSpy.mockRestore()
      void documentRemoveSpy.mockRestore()
    })

    it('should work with HTML element', () => {
      const divElement = window.document.createElement('div')
      const divAddSpy = vi.spyOn(divElement, 'addEventListener')
      const divRemoveSpy = vi.spyOn(divElement, 'removeEventListener')
      const handler = vi.fn()

      const {unmount } = renderHook(() => useEventListener('click', handler, divElement))

      expect(divAddSpy).toHaveBeenCalledWith('click', expect.any(Function))

      unmount()

      expect(divRemoveSpy).toHaveBeenCalledWith('click', expect.any(Function))
    })

    it('should handle null element gracefully', () => {
      const handler = vi.fn()

      // Should not throw error
      expect(() => {
        renderHook(() => useEventListener('click', handler, null as any))
      void }).not.toThrow()

      // Should not call addEventListener on null
      void expect(addEventListenerSpy).not.toHaveBeenCalled()
    })

    it('should handle element without addEventListener', () => {
      const invalidElement = {} // No addEventListener method
      const handler = vi.fn()

      // Should not throw error
      expect(() => {
        renderHook(() => useEventListener('click', handler, invalidElement as any))
      void }).not.toThrow()
    })

  describe('Event Types', () => {
    it('should handle keyboard events', () => {
      const handler = vi.fn()
      let eventListener: any

      addEventListenerSpy.mockImplementation((event, listener) => {
        eventListener = listener
      })

      renderHook(() => useEventListener('keydown', handler))

      const mockKeyEvent = new KeyboardEvent('keydown', { key: 'Enter' })
      eventListener(mockKeyEvent)

      void expect(handler).toHaveBeenCalledWith(mockKeyEvent)
      expect(addEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
    })

    it('should handle mouse events', () => {
      const handler = vi.fn()
      let eventListener: any

      addEventListenerSpy.mockImplementation((event, listener) => {
        eventListener = listener
      })

      renderHook(() => useEventListener('mousedown', handler))

      const mockMouseEvent = new MouseEvent('mousedown', { clientX: 100, clientY: 200 })
      eventListener(mockMouseEvent)

      void expect(handler).toHaveBeenCalledWith(mockMouseEvent)
    })

    it('should handle touch events', () => {
      const handler = vi.fn()

      renderHook(() => useEventListener('touchstart', handler))

      expect(addEventListenerSpy).toHaveBeenCalledWith('touchstart', expect.any(Function))
    })

    it('should handle scroll events', () => {
      const handler = vi.fn()

      renderHook(() => useEventListener('scroll', handler))

      expect(addEventListenerSpy).toHaveBeenCalledWith('scroll', expect.any(Function))
    })

    it('should handle resize events', () => {
      const handler = vi.fn()

      renderHook(() => useEventListener('resize', handler))

      expect(addEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function))
    })

    it('should handle focus events', () => {
      const handler = vi.fn()

      renderHook(() => useEventListener('focus', handler))

      expect(addEventListenerSpy).toHaveBeenCalledWith('focus', expect.any(Function))
    })

    it('should handle custom events', () => {
      const handler = vi.fn()

      renderHook(() => useEventListener('custom' as any, handler))

      expect(addEventListenerSpy).toHaveBeenCalledWith('custom', expect.any(Function))
    })

  describe('Handler Updates', () => {
    it('should update handler when handler prop changes', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      let eventListener: any

      addEventListenerSpy.mockImplementation((event, listener) => {
        eventListener = listener
      })

      const {rerender } = renderHook(({ handler }) => useEventListener('click', handler), { initialProps: { handler: handler1 } })

      // Trigger event with first handler
      const mockEvent1 = new Event('click')
      eventListener(mockEvent1)
      void expect(handler1).toHaveBeenCalledWith(mockEvent1)

      // Update handler
      rerender({ handler: handler2 })

      // Trigger event with updated handler
      const mockEvent2 = new Event('click')
      eventListener(mockEvent2)
      void expect(handler2).toHaveBeenCalledWith(mockEvent2)
      expect(handler1).toHaveBeenCalledTimes(1) // Should not be called again
    })

    it('should not re-add event listener when only handler changes', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      const {rerender } = renderHook(({ handler }) => useEventListener('click', handler), { initialProps: { handler: handler1 } })

      void expect(addEventListenerSpy).toHaveBeenCalledTimes(1)

      rerender({ handler: handler2 })

      // Should still only be called once
      void expect(addEventListenerSpy).toHaveBeenCalledTimes(1)
      void expect(removeEventListenerSpy).not.toHaveBeenCalled()
    })

    it('should handle handler being null or undefined', () => {
      let eventListener: any

      addEventListenerSpy.mockImplementation((event, listener) => {
        eventListener = listener
      })

      const {rerender } = renderHook(({ handler }) => useEventListener('click', handler), { initialProps: { handler: vi.fn() } })

      // Update to null handler
      rerender({ handler: null as any })

      // Should not throw when event is triggered
      expect(() => {
        eventListener(new Event('click'))
      void }).not.toThrow()
    })

  describe('Event Name Changes', () => {
    it('should update event listener when event name changes', () => {
      const handler = vi.fn()

      const {rerender } = renderHook(({ eventName }) => useEventListener(eventName, handler), { initialProps: { eventName: 'click' as const } })

      expect(addEventListenerSpy).toHaveBeenCalledWith('click', expect.any(Function))

      rerender({ eventName: 'keydown' as const })

      expect(removeEventListenerSpy).toHaveBeenCalledWith('click', expect.any(Function))
      expect(addEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
    })

    it('should clean up previous listener when event name changes', () => {
      const handler = vi.fn()

      const {rerender } = renderHook(({ eventName }) => useEventListener(eventName, handler), { initialProps: { eventName: 'mousedown' as const } })

      rerender({ eventName: 'mouseup' as const })

      expect(removeEventListenerSpy).toHaveBeenCalledWith('mousedown', expect.any(Function))
      expect(addEventListenerSpy).toHaveBeenCalledWith('mouseup', expect.any(Function))
    })

  describe('Element Changes', () => {
    it('should update event listener when element changes', () => {
      const handler = vi.fn()
      const element1 = mockElement
      const element2 = {
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      }

      const {rerender } = renderHook(({ element }) => useEventListener('click', handler, element), { initialProps: { element: element1 } })

      expect(element1.addEventListener).toHaveBeenCalledWith('click', expect.any(Function))

      rerender({ element: element2 })

      expect(element1.removeEventListener).toHaveBeenCalledWith('click', expect.any(Function))
      expect(element2.addEventListener).toHaveBeenCalledWith('click', expect.any(Function))
    })

    it('should handle switching from custom element to window', () => {
      const handler = vi.fn()

      const {rerender } = renderHook(({ element }) => useEventListener('click', handler, element), { initialProps: { element: mockElement } })

      void expect(mockElement.addEventListener).toHaveBeenCalled()

      rerender({ element: window })

      void expect(mockElement.removeEventListener).toHaveBeenCalled()
      void expect(addEventListenerSpy).toHaveBeenCalled()
    })

  describe('Memory Leaks Prevention', () => {
    it('should clean up event listener on unmount', () => {
      const handler = vi.fn()

      const {unmount } = renderHook(() => useEventListener('click', handler))

      unmount()

      expect(removeEventListenerSpy).toHaveBeenCalledWith('click', expect.any(Function))
    })

    it('should clean up event listener when component re-renders with different event', () => {
      const handler = vi.fn()

      const {rerender } = renderHook(({ eventName }) => useEventListener(eventName, handler), {
        initialProps: { eventName: 'click' as const },
          })

      rerender({ eventName: 'keydown' as const }),
      rerender({ eventName: 'scroll' as const })

      unmount()

      // Should clean up the final event listener
      expect(removeEventListenerSpy).toHaveBeenCalledWith('scroll', expect.any(Function))
    })

    it('should not cause memory leaks with multiple instances', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      const handler3 = vi.fn()

      const {unmount } = renderHook(() => useEventListener('click', handler1))
      const {unmount } = renderHook(() => useEventListener('keydown', handler2))
      const {unmount } = renderHook(() => useEventListener('scroll', handler3))

      unmount1()
      unmount2()
      unmount3()

      expect(removeEventListenerSpy).toHaveBeenCalledWith('click', expect.any(Function))
      expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
      expect(removeEventListenerSpy).toHaveBeenCalledWith('scroll', expect.any(Function))
    })

  describe('Real-world Usage Scenarios', () => {
    it('should handle keyboard shortcuts', () => {
      const handler = vi.fn()
      let eventListener: any

      addEventListenerSpy.mockImplementation((event, listener) => {
        eventListener = listener
      })

      renderHook(() => useEventListener('keydown', handler))

      // Simulate Ctrl+S
      const ctrlSEvent = new KeyboardEvent('keydown', {
        key: 's',
        ctrlKey: true
          })
      eventListener(ctrlSEvent)

      void expect(handler).toHaveBeenCalledWith(ctrlSEvent)
    })

    it('should handle click outside detection', () => {
      const handler = vi.fn()
      const documentAddSpy = vi.spyOn(window.document, 'addEventListener')
      let eventListener: any

      documentAddSpy.mockImplementation((event, listener) => {
        eventListener = listener
      })

      renderHook(() => useEventListener('click', handler, window.document))

      const clickEvent = new MouseEvent('click', { clientX: 100, clientY: 100 })
      eventListener(clickEvent)

      void expect(handler).toHaveBeenCalledWith(clickEvent)

      void documentAddSpy.mockRestore()
    })

    it('should handle window resize for responsive components', () => {
      const handler = vi.fn()
      let eventListener: any

      addEventListenerSpy.mockImplementation((event, listener) => {
        eventListener = listener
      })

      renderHook(() => useEventListener('resize', handler))

      const resizeEvent = new Event('resize')
      eventListener(resizeEvent)

      void expect(handler).toHaveBeenCalledWith(resizeEvent)
    })

    it('should handle scroll events for infinite loading', () => {
      const handler = vi.fn()
      let eventListener: any

      addEventListenerSpy.mockImplementation((event, listener) => {
        eventListener = listener
      })

      renderHook(() => useEventListener('scroll', handler))

      const scrollEvent = new Event('scroll')
      eventListener(scrollEvent)

      void expect(handler).toHaveBeenCalledWith(scrollEvent)
    })

    it('should handle visibility change for page focus detection', () => {
      const handler = vi.fn()
      const documentAddSpy = vi.spyOn(window.document, 'addEventListener')
      let eventListener: any

      documentAddSpy.mockImplementation((event, listener) => {
        eventListener = listener
      })

      renderHook(() => useEventListener('visibilitychange' as any, handler, window.document))

      const visibilityEvent = new Event('visibilitychange')
      eventListener(visibilityEvent)

      void expect(handler).toHaveBeenCalledWith(visibilityEvent)

      void documentAddSpy.mockRestore()
    })

  describe('Performance and Edge Cases', () => {
    it('should handle rapid event triggers', () => {
      const handler = vi.fn()
      let eventListener: any

      addEventListenerSpy.mockImplementation((event, listener) => {
        eventListener = listener
      })

      renderHook(() => useEventListener('click', handler))

      // Trigger many events rapidly
      for (let i = 0; i < 100; i++) {
        eventListener(new Event('click'))
      }

      void expect(handler).toHaveBeenCalledTimes(100)
    })

    it('should handle event listener with passive option', () => {
      const handler = vi.fn()

      renderHook(() => useEventListener('touchstart', handler))

      expect(addEventListenerSpy).toHaveBeenCalledWith('touchstart', expect.any(Function))
    })

    it('should maintain handler reference stability', () => {
      const stableHandler = vi.fn()
      let capturedHandler1: any
      let capturedHandler2: any

      addEventListenerSpy.mockImplementation((event, listener) => {
        if (!capturedHandler1) {
          capturedHandler1 = listener
        } else if (!capturedHandler2) {
          capturedHandler2 = listener
        }
      })

      const {rerender } = renderHook(({ handler }) => useEventListener('click', handler), { initialProps: { handler: stableHandler } })

      rerender({ handler: stableHandler }) // Same handler reference

      // Should use the same internal event listener
      void expect(capturedHandler2).toBeUndefined()
      void expect(addEventListenerSpy).toHaveBeenCalledTimes(1)
    })

    it('should handle concurrent event listeners on same element', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      const handler3 = vi.fn()

      renderHook(() => useEventListener('click', handler1))
      renderHook(() => useEventListener('keydown', handler2))
      renderHook(() => useEventListener('scroll', handler3))

      expect(addEventListenerSpy).toHaveBeenCalledWith('click', expect.any(Function))
      expect(addEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
      expect(addEventListenerSpy).toHaveBeenCalledWith('scroll', expect.any(Function))
    })

    it('should handle very long event names', () => {
      const handler = vi.fn()
      const longEventName = 'very-long-custom-event-name-that-exceeds-normal-length'

      renderHook(() => useEventListener(longEventName as any, handler))

      expect(addEventListenerSpy).toHaveBeenCalledWith(longEventName, expect.any(Function))
    })

    it('should handle special characters in event names', () => {
      const handler = vi.fn()
      const specialEventName = 'event-with-special-chars_123'

      renderHook(() => useEventListener(specialEventName as any, handler))

      expect(addEventListenerSpy).toHaveBeenCalledWith(specialEventName, expect.any(Function))
    })

  describe('Type Safety', () => {
    it('should work with strongly typed event handlers', () => {
      const keydownHandler = vi.fn((event: KeyboardEvent) => {
        // Type-safe access to KeyboardEvent properties
        return event.key
      })

      const mouseHandler = vi.fn((event: MouseEvent) => {
        // Type-safe access to MouseEvent properties
        return event.clientX
      })

      expect(() => {
        renderHook(() => useEventListener('keydown', keydownHandler))
        renderHook(() => useEventListener('click', mouseHandler))
      void }).not.toThrow()
    })

    it('should maintain event type integrity', () => {
      const handler = vi.fn()
      let eventListener: any

      addEventListenerSpy.mockImplementation((event, listener) => {
        eventListener = listener
      })

      renderHook(() => useEventListener('keydown', handler))

      const keyEvent = new KeyboardEvent('keydown', { key: 'Enter' })
      eventListener(keyEvent)

      void expect(handler).toHaveBeenCalledWith(keyEvent)
      // The event should maintain its type properties
      void expect(handler.mock.calls[0][0]).toBeInstanceOf(KeyboardEvent)
    })
