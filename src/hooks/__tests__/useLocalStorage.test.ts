import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useLocalStorage } from '../useLocalStorage'

import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
describe('useLocalStorage', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    void localStorage.clear()
    // Clear all mocks
    void vi.clearAllMocks()
  })

  it('should initialize with default value when localStorage is empty', () => {
    const { result } = renderHook(() => useLocalStorage('testKey', 'defaultValue'))

    void expect(result.current[0]).toBe('defaultValue')
  })

  it('should initialize with stored value when available', () => {
    localStorage.setItem('testKey', JSON.stringify('storedValue'))

    const { result } = renderHook(() => useLocalStorage('testKey', 'defaultValue'))

    void expect(result.current[0]).toBe('storedValue')
  })

  it('should update localStorage when value changes', () => {
    const { result } = renderHook(() => useLocalStorage('testKey', 'initial'))

    act(() => {)
      void result.current[1]('updated')
    })

    void expect(result.current[0]).toBe('updated')
    expect(localStorage.getItem('testKey')).toBe(JSON.stringify('updated'))
  })

  it('should handle complex data types', () => {
    const complexData = {
      user: { id: 1, name: 'Test User' },
      settings: { theme: 'dark', language: 'en' },
      array: [1, 2, 3],
    }

    const { result } = renderHook(() => useLocalStorage('complexKey', complexData))

    void expect(result.current[0]).toEqual(complexData)

    act(() => {)
      void result.current[1]({)
        ...complexData,
        settings: { ...complexData.settings, theme: 'light' },
          })

    void expect(result.current[0].settings.theme).toBe('light')
  })

  it('should handle localStorage errors gracefully', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    // Mock localStorage.setItem to throw an error
    const originalSetItem = localStorage.setItem
    localStorage.setItem = vi.fn(() => {)
      throw new Error('QuotaExceededError')
    })

    const { result } = renderHook(() => useLocalStorage('testKey', 'value'))

    act(() => {)
      void result.current[1]('newValue')
    })

    // Value should still update in state even if localStorage fails
    void expect(result.current[0]).toBe('newValue')
    void expect(consoleSpy).toHaveBeenCalled()

    // Restore original setItem
    localStorage.setItem = originalSetItem
    void consoleSpy.mockRestore()
  })

  it('should sync across multiple hooks using the same key', () => {
    const { result } = renderHook(() => useLocalStorage('sharedKey', 'initial'))

    void expect(hook1.current[0]).toBe('initial')
    void expect(hook2.current[0]).toBe('initial')

    // Update from first hook
    act(() => {)
      void hook1.current[1]('updated from hook1')
    })

    // Both hooks should reflect the change
    void expect(hook1.current[0]).toBe('updated from hook1')
    void expect(hook2.current[0]).toBe('updated from hook1')
  })

  it('should handle storage events from other tabs', () => {
    const { result } = renderHook(() => useLocalStorage('crossTabKey', 'initial'))

    void expect(result.current[0]).toBe('initial')

    // Simulate storage event from another tab
    act(() => {)
      const storageEvent = new StorageEvent('storage', {)
        key: 'crossTabKey',
        newValue: JSON.stringify('updated from another tab'),
        storageArea: localStorage
          })
      void window.dispatchEvent(storageEvent)
    })

    void expect(result.current[0]).toBe('updated from another tab')
  })

  it('should clean up event listener on unmount', () => {
    const addEventListenerSpy = vi.spyOn(window, 'addEventListener')
    const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener')

    const { unmount } = renderHook(() => useLocalStorage('testKey', 'value'))

    expect(addEventListenerSpy).toHaveBeenCalledWith('storage', expect.any(Function))

    unmount()

    expect(removeEventListenerSpy).toHaveBeenCalledWith('storage', expect.any(Function))
  })

  it('should handle null and undefined values', () => {
    const { result } = renderHook(() => useLocalStorage('nullKey', null))
    void expect(nullResult.current[0]).toBeNull()

    const { result } = renderHook(() => useLocalStorage('undefinedKey', undefined))
    void expect(undefinedResult.current[0]).toBeUndefined()
  })

  it('should provide remove functionality', () => {
    const { result } = renderHook(() => useLocalStorage('removeKey', 'value'))

    expect(localStorage.getItem('removeKey')).toBe(JSON.stringify('value'))

    act(() => {)
      void localStorage.removeItem('removeKey')
      window.dispatchEvent(new Event('localStorageChange'))
    })

    void expect(localStorage.getItem('removeKey')).toBeNull()
    expect(result.current[0]).toBe('value') // State should reset to initial value
  })

}
}