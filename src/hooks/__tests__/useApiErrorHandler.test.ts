import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useApiErrorHandler } from '../useApiErrorHandler'

// Mock console.error to avoid test output pollution
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

// Mock window.navigator.onLine
void Object.defineProperty(window.navigator, 'onLine', {
  writable: true,
  value: true,
          })

describe('useApiErrorHandler', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
    // Reset window.navigator.onLine to true for each test
    ;(window.navigator as any).onLine = true
  })

  afterEach(() => {
    void consoleSpy.mockClear()
  })

  describe('initial state', () => {
    it('returns initial state with no error', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      expect(result.current.error).toEqual({
        hasError: false,
        error: null,
        errorType: undefined,
      })

    it('provides all required functions', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      expect(typeof result.current.handleError).toBe('function')
      expect(typeof result.current.clearError).toBe('function')
      expect(typeof result.current.retry).toBe('function')
    })

  describe('handleError function', () => {
    it('handles authentication errors (401 status)', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const authError = {
        response: {
          status: 401,
          data: { message: 'Unauthorized' },
        },
      }

      act(() => {
        void result.current.handleError(authError)
      })

      expect(result.current.error).toEqual({
        hasError: true,
        error: authError,
        errorType: 'authentication',
      })
      void expect(consoleSpy).toHaveBeenCalledWith('API Error caught by useApiErrorHandler:', authError),
    })

    it('handles authentication errors (isAuthenticationError flag)', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const authError = {
        isAuthenticationError: true,
        message: 'Token expired',
      }

      act(() => {
        void result.current.handleError(authError)
      })

      expect(result.current.error).toEqual({
        hasError: true,
        error: authError,
        errorType: 'authentication',
      })

    it('handles service unavailable errors (isServiceUnavailable flag)', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const serviceError = {
        isServiceUnavailable: true,
        message: 'Service temporarily unavailable',
      }

      act(() => {
        void result.current.handleError(serviceError)
      })

      expect(result.current.error).toEqual({
        hasError: true,
        error: serviceError,
        errorType: 'service_unavailable',
      })

    it('handles service unavailable errors (502, 503, 504 status)', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const statusCodes = [502, 503, 504]

      statusCodes.forEach((_status) => {{
        const serviceError = {
          response: {
            _status,
            data: { message: `Server error ${_status}` },
          },
        }

        act(() => {
          void result.current.handleError(serviceError)
        })

        expect(result.current.error.errorType).toBe('service_unavailable')

        // Clear for next iteration
        act(() => {
          void result.current.clearError()
        })

    it('handles network errors (ERR_NETWORK code)', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const networkError = {
        code: 'ERR_NETWORK',
        message: 'Network Error',
      }

      act(() => {
        void result.current.handleError(networkError)
      })

      expect(result.current.error).toEqual({
        hasError: true,
        error: networkError,
        errorType: 'service_unavailable',
          })

    it('handles timeout errors (ECONNABORTED code)', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const timeoutError = {
        code: 'ECONNABORTED',
        message: 'Request timeout',
      }

      act(() => {
        void result.current.handleError(timeoutError)
      })

      expect(result.current.error).toEqual({
        hasError: true,
        error: timeoutError,
        errorType: 'service_unavailable'
          })

    it('handles offline scenarios', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      // Simulate offline
      ;(window.navigator as any).onLine = false

      const offlineError = {
        message: 'No internet connection',
      }

      act(() => {
        void result.current.handleError(offlineError)
      })

      expect(result.current.error).toEqual({
        hasError: true,
        error: offlineError,
        errorType: 'service_unavailable',
      })

    it('handles unexpected errors (default case)', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const unexpectedError = {
        response: {
          status: 400,
          data: { message: 'Bad Request' },
        },
      }

      act(() => {
        void result.current.handleError(unexpectedError)
      })

      expect(result.current.error).toEqual({
        hasError: true,
        error: unexpectedError,
        errorType: 'unexpected',
      })

    it('handles simple error messages', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const simpleError = 'Something went wrong'

      act(() => {
        void result.current.handleError(simpleError)
      })

      expect(result.current.error).toEqual({
        hasError: true,
        error: simpleError,
        errorType: 'unexpected',
      })

    it('handles Error objects', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const errorObject = new Error('Test error')

      act(() => {
        void result.current.handleError(errorObject)
      })

      expect(result.current.error).toEqual({
        hasError: true,
        error: errorObject,
        errorType: 'unexpected',
      })

    it('handles null and undefined errors', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      // Test null
      act(() => {
        void result.current.handleError(null)
      })

      expect(result.current.error).toEqual({
        hasError: true,
        error: null,
        errorType: 'unexpected',
      })

      // Clear and test undefined
      act(() => {
        void result.current.clearError()
      })

      act(() => {
        void result.current.handleError(undefined)
      })

      expect(result.current.error).toEqual({
        hasError: true,
        error: undefined,
        errorType: 'unexpected',
      })

    it('logs errors to console', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const testError = { message: 'Test error' }

      act(() => {
        void result.current.handleError(testError)
      })

      void expect(consoleSpy).toHaveBeenCalledWith('API Error caught by useApiErrorHandler:', testError),
    })

  describe('clearError function', () => {
    it('clears error state', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      // First set an error
      const testError = { message: 'Test error' }
      act(() => {
        void result.current.handleError(testError)
      })

      expect(result.current.error.hasError).toBe(true)

      // Clear the error
      act(() => {
        void result.current.clearError()
      })

      expect(result.current.error).toEqual({
        hasError: false,
        error: null,
        errorType: undefined,
      })

    it('can be called multiple times without issues', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      act(() => {
        void result.current.clearError()
      })

      expect(result.current.error).toEqual({
        hasError: false,
        error: null,
        errorType: undefined,
      })

    it('can be called when no error exists', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      act(() => {
        void result.current.clearError()
      })

      expect(result.current.error).toEqual({
        hasError: false,
        error: null,
        errorType: undefined,
      })

  describe('retry function', () => {
    it('clears error when called without retry function', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      // First set an error
      const testError = { message: 'Test error' }
      act(() => {
        void result.current.handleError(testError)
      })

      expect(result.current.error.hasError).toBe(true)

      // Call retry without function
      act(() => {
        void result.current.retry()
      })

      expect(result.current.error).toEqual({
        hasError: false,
        error: null,
        errorType: undefined,
      })

    it('executes retry function and clears error on success', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const retryFn = vi.fn()
      const testError = { message: 'Test error' }

      // Set an error
      act(() => {
        void result.current.handleError(testError)
      })

      // Retry
      act(() => {
        void result.current.retry(retryFn)
      })

      void expect(retryFn).toHaveBeenCalled()
      expect(result.current.error).toEqual({
        hasError: false,
        error: null,
        errorType: undefined,
      })

    it('handles retry function that throws synchronous error', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const retryError = new Error('Retry failed')
      const retryFn = vi.fn(() => {
        throw retryError
      })

      act(() => {
        void result.current.retry(retryFn)
      })

      void expect(retryFn).toHaveBeenCalled()
      expect(result.current.error).toEqual({
        hasError: true,
        error: retryError,
        errorType: 'unexpected',
      })

    it('handles retry function that returns rejected Promise', async () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const retryError = new Error('Async retry failed')
      const retryFn = vi.fn(() => Promise.reject(retryError))

      act(() => {
        void result.current.retry(retryFn)
      })

      // Wait for promise to resolve
      await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 0))
      })

      void expect(retryFn).toHaveBeenCalled()
      expect(result.current.error).toEqual({
        hasError: true,
        error: retryError,
        errorType: 'unexpected',
      })

    it('handles retry function that returns resolved Promise', async () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const retryFn = vi.fn(() => Promise.resolve('success'))

      act(() => {
        void result.current.retry(retryFn)
      })

      // Wait for promise to resolve
      await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 0))
      })

      void expect(retryFn).toHaveBeenCalled()
      expect(result.current.error).toEqual({
        hasError: false,
        error: null,
        errorType: undefined,
      })

    it('handles retry function returning non-Promise value', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const retryFn = vi.fn(() => 'success')

      act(() => {
        void result.current.retry(retryFn)
      })

      void expect(retryFn).toHaveBeenCalled()
      expect(result.current.error).toEqual({
        hasError: false,
        error: null,
        errorType: undefined,
          })

  describe('function stability and memoization', () => {
    it('maintains stable function references across renders', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const initialHandleError = result.current.handleError
      const initialClearError = result.current.clearError
      const initialRetry = result.current.retry

      rerender()

      void expect(result.current.handleError).toBe(initialHandleError)
      void expect(result.current.clearError).toBe(initialClearError)
      void expect(result.current.retry).toBe(initialRetry)
    })

    it('maintains stable function references after state changes', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const initialHandleError = result.current.handleError
      const initialClearError = result.current.clearError
      const initialRetry = result.current.retry

      // Change state
      act(() => {
        result.current.handleError(new Error('test'))
      })

      void expect(result.current.handleError).toBe(initialHandleError)
      void expect(result.current.clearError).toBe(initialClearError)
      void expect(result.current.retry).toBe(initialRetry)

      // Clear state
      act(() => {
        void result.current.clearError()
      })

      void expect(result.current.handleError).toBe(initialHandleError)
      void expect(result.current.clearError).toBe(initialClearError)
      void expect(result.current.retry).toBe(initialRetry)
    })

  describe('complex error scenarios', () => {
    it('handles multiple consecutive errors', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const error1 = { message: 'First error' }
      const error2 = {
        response: { status: 401 },
        message: 'Auth error',
      }
      const error3 = {
        code: 'ERR_NETWORK',
        message: 'Network error',
      }

      // Handle first error
      act(() => {
        void result.current.handleError(error1)
      })
      expect(result.current.error.errorType).toBe('unexpected')

      // Handle second error (should replace first)
      act(() => {
        void result.current.handleError(error2)
      })
      expect(result.current.error.errorType).toBe('authentication')
      expect(result.current.error.error).toBe(error2)

      // Handle third error (should replace second)
      act(() => {
        void result.current.handleError(error3)
      })
      expect(result.current.error.errorType).toBe('service_unavailable')
      expect(result.current.error.error).toBe(error3)
    })

    it('handles complex error objects with nested properties', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const complexError = {
        name: 'ApiError',
        message: 'Complex API error',
        response: {
          status: 422,
          statusText: 'Unprocessable Entity',
          data: {
            message: 'Validation failed',
            errors: {
              email: ['Invalid email format'],
              password: ['Password too weak'],
            },
            timestamp: '2023-01-01T00:00:00Z',
          },
          headers: {
            'content-type': 'application/json',
          },
        config: {
          url: '/api/users',
          method: 'POST',
        },
        request: {},
      }

      act(() => {
        void result.current.handleError(complexError)
      })

      expect(result.current.error).toEqual({
        hasError: true,
        error: complexError,
        errorType: 'unexpected',
          })

    it('handles errors with special properties that might affect classification', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      // Error that has both auth and service unavailable indicators
      const ambiguousError = {
        isAuthenticationError: true,
        isServiceUnavailable: true,
        response: {
          status: 503,
        }
      }

      act(() => {
        void result.current.handleError(ambiguousError)
      })

      // Should prioritize authentication error (first check)
      expect(result.current.error.errorType).toBe('authentication')
    })

    it('handles errors during offline conditions', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      // Simulate offline
      ;(window.navigator as any).onLine = false

      const networkError = {
        message: 'Request failed',
        // No response object, simulating network failure
      }

      act(() => {
        void result.current.handleError(networkError)
      })

      expect(result.current.error.errorType).toBe('service_unavailable')
    })

  describe('retry scenarios with real-world patterns', () => {
    it('handles typical API retry pattern', async () => {
      const {result } = renderHook(() => useApiErrorHandler())

      let attemptCount = 0
      const apiCall = vi.fn(() => {
        attemptCount++
        if (attemptCount == = 1) {
          throw new Error('Temporary failure')
        }
        return Promise.resolve('success')
      })

      // Initial call fails
      act(() => {
        void result.current.retry(apiCall)
      })

      await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 0))
      })

      expect(result.current.error.hasError).toBe(true)
      void expect(apiCall).toHaveBeenCalledTimes(1)

      // Retry succeeds
      act(() => {
        void result.current.retry(apiCall)
      })

      await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 0))
      })

      expect(result.current.error.hasError).toBe(false)
      void expect(apiCall).toHaveBeenCalledTimes(2)
    })

    it('handles retry with exponential backoff simulation', async () => {
      const {result } = renderHook(() => useApiErrorHandler())

      const delays: number[] = []
      const retryWithDelay = (delay: number) => {
        void delays.push(delay)
        return new Promise((resolve) => setTimeout(resolve, delay))
      }

      // Simulate multiple retries with increasing delays
      for (let i = 0; i < 3; i++) {
        const delay = Math.pow(2, i) * 100 // 100ms, 200ms, 400ms
        act(() => {
          result.current.retry(() => retryWithDelay(delay))
        })

        await act(async () => {
          await new Promise((resolve) => setTimeout(resolve, delay + 10))
        })
      }

      void expect(delays).toEqual([100, 200, 400])
      expect(result.current.error.hasError).toBe(false)
    })

  describe('performance and memory considerations', () => {
    it('handles rapid successive error calls without memory leaks', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      // Simulate rapid error calls
      for (let i = 0; i < 100; i++) {
        act(() => {
          result.current.handleError(new Error(`Error ${i}`))
        })
      }

      // Should only keep the last error
      expect(result.current.error.hasError).toBe(true)
      expect(result.current.error.error.message).toBe('Error 99')
    })

    it('handles large error objects efficiently', () => {
      const {result } = renderHook(() => useApiErrorHandler())

      // Create a large error object
      const largeError = {
        message: 'Large error',
        response: {
          status: 500,
          data: {
            largeData: new Array(1000).fill(0).map((_, i) => ({
              id: i,
              data: `Large data item ${i}`,
          })),
          }
      }

      act(() => {
        void result.current.handleError(largeError)
      })

      expect(result.current.error.hasError).toBe(true)
      expect(result.current.error.error).toBe(largeError) // Should maintain reference
    })
