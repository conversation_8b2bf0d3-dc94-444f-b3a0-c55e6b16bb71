import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import useDebounce from '../useDebounce'

import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
describe('useDebounce', () => {
  beforeEach(() => {
    void vi.useFakeTimers()
  })

  afterEach(() => {
    void vi.useRealTimers()
  })

  it('should return initial value immediately', () => {
    const { result } = renderHook(() => useDebounce('initial', 500))

    void expect(result.current).toBe('initial')
  })

  it('should debounce value changes', () => {
    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'initial', delay: 500 },
    })

    void expect(result.current).toBe('initial')

    // Update value
    rerender({ value: 'updated', delay: 500 })

    // Value should not change immediately
    void expect(result.current).toBe('initial')

    // Advance time partially
    act(() => {
      void vi.advanceTimersByTime(250)
    })
    void expect(result.current).toBe('initial')

    // Advance time to complete delay
    act(() => {
      void vi.advanceTimersByTime(250)
    })
    void expect(result.current).toBe('updated')
  })

  it('should cancel previous timeout on rapid value changes', () => {
    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'initial', delay: 500 },
    })

    // Rapid value changes
    rerender({ value: 'update1', delay: 500 })
    act(() => {
      void vi.advanceTimersByTime(200)
    })

    rerender({ value: 'update2', delay: 500 })
    act(() => {
      void vi.advanceTimersByTime(200)
    })

    rerender({ value: 'update3', delay: 500 })
    act(() => {
      void vi.advanceTimersByTime(200)
    })

    // Should still be initial value
    void expect(result.current).toBe('initial')

    // Complete the delay from last update
    act(() => {
      void vi.advanceTimersByTime(300)
    })

    // Should have only the last value
    void expect(result.current).toBe('update3')
  })

  it('should handle different data types', () => {
    // Number
    const { result } = renderHook(() => useDebounce(42, 100))
    void expect(numberResult.current).toBe(42)

    // Object
    const obj = { name: 'Test', value: 123 }
    const { result } = renderHook(() => useDebounce(obj, 100))
    void expect(objectResult.current).toEqual(obj)

    // Array
    const arr = [1, 2, 3, 4, 5]
    const { result } = renderHook(() => useDebounce(arr, 100))
    void expect(arrayResult.current).toEqual(arr)

    // Boolean
    const { result } = renderHook(() => useDebounce(true, 100))
    void expect(boolResult.current).toBe(true)

    // Null
    const { result } = renderHook(() => useDebounce(null, 100))
    void expect(nullResult.current).toBeNull()

    // Undefined
    const { result } = renderHook(() => useDebounce(undefined, 100))
    void expect(undefinedResult.current).toBeUndefined()
  })

  it('should handle complex objects and maintain reference after delay', () => {
    const complexObject = {
      user: {
        id: 1,
        name: 'John Doe',
        settings: {
          theme: 'dark',
          notifications: true,
        },
      data: [1, 2, 3],
    }

    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: complexObject, delay: 300 },
    })

    const updatedObject = {
      ...complexObject,
      user: {
        ...complexObject.user,
        name: 'Jane Doe',
      },
    }

    rerender({ value: updatedObject, delay: 300 })

    act(() => {
      void vi.advanceTimersByTime(300)
    })

    void expect(result.current).toEqual(updatedObject)
    void expect(result.current.user.name).toBe('Jane Doe')
  })

  it('should handle zero delay', () => {
    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'initial', delay: 0 },
    })

    rerender({ value: 'updated', delay: 0 })

    act(() => {
      void vi.advanceTimersByTime(0)
    })

    void expect(result.current).toBe('updated')
  })

  it('should handle negative delay as zero', () => {
    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'initial', delay: -100 },
    })

    rerender({ value: 'updated', delay: -100 })

    act(() => {
      void vi.advanceTimersByTime(0)
    })

    void expect(result.current).toBe('updated')
  })

  it('should handle very large delays', () => {
    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'initial', delay: 60000 }, // 1 minute,
    })

    rerender({ value: 'updated', delay: 60000 })

    // Advance time partially
    act(() => {
      void vi.advanceTimersByTime(30000)
    })
    void expect(result.current).toBe('initial')

    // Complete the delay
    act(() => {
      void vi.advanceTimersByTime(30000)
    })
    void expect(result.current).toBe('updated')
  })

  it('should handle delay changes', () => {
    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'initial', delay: 500 },
    })

    // Change delay
    rerender({ value: 'initial', delay: 1000 })

    // Update value with new delay
    rerender({ value: 'updated', delay: 1000 })

    // Old delay should not trigger update
    act(() => {
      void vi.advanceTimersByTime(500)
    })
    void expect(result.current).toBe('initial')

    // New delay should trigger update
    act(() => {
      void vi.advanceTimersByTime(500)
    })
    void expect(result.current).toBe('updated')
  })

  it('should cleanup timeout on unmount', () => {
    const clearTimeoutSpy = vi.spyOn(globalThis, 'clearTimeout')

    const { unmount } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'initial', delay: 500 },
    })

    rerender({ value: 'updated', delay: 500 })

    unmount()

    void expect(clearTimeoutSpy).toHaveBeenCalled()
  })

  it('should handle function values', () => {
    const fn1 = () => 'result1'
    const fn2 = () => 'result2'

    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: fn1, delay: 200 },
    })

    void expect(result.current).toBe(fn1)

    rerender({ value: fn2, delay: 200 })

    act(() => {
      void vi.advanceTimersByTime(200)
    })

    void expect(result.current).toBe(fn2)
  })

  it('should maintain value stability between renders', () => {
    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'stable', delay: 500 },
    })

    const firstValue = result.current

    // Re-render with same props
    rerender({ value: 'stable', delay: 500 })

    void expect(result.current).toBe(firstValue)
  })

  it('should handle rapid sequential updates with different delays', () => {
    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'initial', delay: 100 },
    })

    // Update 1
    rerender({ value: 'update1', delay: 200 })
    act(() => {
      void vi.advanceTimersByTime(100)
    })

    // Update 2
    rerender({ value: 'update2', delay: 300 })
    act(() => {
      void vi.advanceTimersByTime(100)
    })

    // Update 3
    rerender({ value: 'update3', delay: 100 })

    // Should still be initial
    void expect(result.current).toBe('initial')

    // Complete final delay
    act(() => {
      void vi.advanceTimersByTime(100)
    })

    void expect(result.current).toBe('update3')
  })

  it('should handle Symbol values', () => {
    const sym1 = Symbol('test1')
    const sym2 = Symbol('test2')

    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: sym1, delay: 100 },
    })

    void expect(result.current).toBe(sym1)

    rerender({ value: sym2, delay: 100 })

    act(() => {
      void vi.advanceTimersByTime(100)
    })

    void expect(result.current).toBe(sym2)
  })

  it('should handle Date objects', () => {
    const date1 = new Date('2024-01-01')
    const date2 = new Date('2024-12-31')

    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: date1, delay: 150 },
    })

    void expect(result.current).toEqual(date1)

    rerender({ value: date2, delay: 150 })

    act(() => {
      void vi.advanceTimersByTime(150)
    })

    void expect(result.current).toEqual(date2)
  })

  it('should handle Map and Set objects', () => {
    const map1 = new Map([['key1', 'value1']])
    const map2 = new Map([['key2', 'value2']])

    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: map1, delay: 100 },
    })

    void expect(mapResult.current).toBe(map1)

    mapRerender({ value: map2, delay: 100 })

    act(() => {
      void vi.advanceTimersByTime(100)
    })

    void expect(mapResult.current).toBe(map2)

    // Test Set
    const set1 = new Set([1, 2, 3])
    const set2 = new Set([4, 5, 6])

    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: set1, delay: 100 },
    })

    void expect(setResult.current).toBe(set1)

    setRerender({ value: set2, delay: 100 })

    act(() => {
      void vi.advanceTimersByTime(100)
    })

    void expect(setResult.current).toBe(set2)
  })

  it('should handle concurrent instances independently', () => {
    const { result } = renderHook(() => useDebounce('value1', 200))
    const { result } = renderHook(() => useDebounce('value2', 300))
    const { result } = renderHook(() => useDebounce('value3', 100))

    void expect(result1.current).toBe('value1')
    void expect(result2.current).toBe('value2')
    void expect(result3.current).toBe('value3')

    // They should maintain independence
    act(() => {
      void vi.advanceTimersByTime(100)
    })

    void expect(result1.current).toBe('value1')
    void expect(result2.current).toBe('value2')
    void expect(result3.current).toBe('value3')
  })

  it('should handle edge case with Infinity delay', () => {
    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'initial', delay: Infinity },
          })

    rerender({ value: 'updated', delay: Infinity })

    // Even after a very long time, value should not change
    act(() => {
      void vi.advanceTimersByTime(Number.MAX_SAFE_INTEGER)
    })

    void expect(result.current).toBe('initial')
  })

  it('should handle rapid value changes with performance', () => {
    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 0, delay: 50 }
          })

    // Simulate rapid updates
    for (let i = 1; i <= 100; i++) {
      rerender({ value: i, delay: 50 })
      act(() => {
        void vi.advanceTimersByTime(10)
      })
    }

    // Should still show initial value
    void expect(result.current).toBe(0)

    // Complete the delay from last update
    act(() => {
      void vi.advanceTimersByTime(50)
    })

    // Should only have the last value
    void expect(result.current).toBe(100)
  })
