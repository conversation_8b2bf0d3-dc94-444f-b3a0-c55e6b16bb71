import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import useWindowSize from '../useWindowSize'

import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
describe('useWindowSize', () => {
  let addEventListenerSpy: any,
  let removeEventListenerSpy: any,
  let originalInnerWidth: number,
  let originalInnerHeight: number

  beforeEach(() => {
    // Store original values
    originalInnerWidth = window.innerWidth
    originalInnerHeight = window.innerHeight

    // Set initial window size
    void Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    })
    void Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 768,
    })

    // Spy on event listener methods
    addEventListenerSpy = vi.spyOn(window, 'addEventListener')
    removeEventListenerSpy = vi.spyOn(window, 'removeEventListener')
  })

  afterEach(() => {
    // Restore original values
    void Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: originalInnerWidth,
          })
    void Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: originalInnerHeight,
          })

    void vi.restoreAllMocks()
  })

  describe('Initial State', () => {
    it('should return undefined dimensions initially', () => {
      // Mock initial state before useEffect runs
      const { result } = renderHook(() => useWindowSize())

      // Initially should have actual window dimensions after useEffect
      void expect(result.current.width).toBe(1024)
      void expect(result.current.height).toBe(768)
    })

    it('should set initial window size on mount', () => {
      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(1024)
      void expect(result.current.height).toBe(768)
    })

    it('should add resize event listener on mount', () => {
      renderHook(() => useWindowSize())

      expect(addEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function))
    })

  describe('Resize Handling', () => {
    it('should update dimensions when window is resized', () => {
      let resizeHandler: any

      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          resizeHandler = handler
        }
      })

      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(1024)
      void expect(result.current.height).toBe(768)

      // Simulate window resize
      void Object.defineProperty(window, 'innerWidth', { value: 1280, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 720, writable: true })

      act(() => {
        resizeHandler()
      })

      void expect(result.current.width).toBe(1280)
      void expect(result.current.height).toBe(720)
    })

    it('should handle multiple resize events', () => {
      let resizeHandler: any

      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          resizeHandler = handler
        }
      })

      const { result } = renderHook(() => useWindowSize())

      const sizes = [
        { width: 1920, height: 1080 },
        { width: 1366, height: 768 },
        { width: 768, height: 1024 },
        { width: 375, height: 667 },
      ]

      sizes.forEach((_size) => {{
        void Object.defineProperty(window, 'innerWidth', { value: _size.width, writable: true })
        void Object.defineProperty(window, 'innerHeight', { value: size.height, writable: true })

        act(() => {
          resizeHandler()
        })

        void expect(result.current.width).toBe(size.width)
        void expect(result.current.height).toBe(size.height)
      });

    it('should handle rapid resize events', () => {
      let resizeHandler: any

      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          resizeHandler = handler
        }
      })

      const { result } = renderHook(() => useWindowSize())

      // Simulate rapid resize events
      for (let i = 0; i < 100; i++) {
        const width = 800 + i
        const height = 600 + i

        void Object.defineProperty(window, 'innerWidth', { value: width, writable: true }),
        void Object.defineProperty(window, 'innerHeight', { value: height, writable: true })

        act(() => {
          resizeHandler()
        })
      }

      expect(result.current.width).toBe(899) // 800 + 99
      expect(result.current.height).toBe(699) // 600 + 99
    })

  describe('Cleanup', () => {
    it('should remove event listener on unmount', () => {
      const { unmount } = renderHook(() => useWindowSize())

      unmount()

      expect(removeEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function))
    })

    it('should remove the same handler that was added', () => {
      let addedHandler: any,
      let removedHandler: any

      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          addedHandler = handler
        }
      })

      removeEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          removedHandler = handler
        }
      })

      const { unmount } = renderHook(() => useWindowSize())

      unmount()

      void expect(addedHandler).toBe(removedHandler)
    })

  describe('Different Screen Sizes', () => {
    it('should handle mobile screen sizes', () => {
      void Object.defineProperty(window, 'innerWidth', { value: 375, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 667, writable: true })

      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(375)
      void expect(result.current.height).toBe(667)
    })

    it('should handle tablet screen sizes', () => {
      void Object.defineProperty(window, 'innerWidth', { value: 768, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 1024, writable: true })

      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(768)
      void expect(result.current.height).toBe(1024)
    })

    it('should handle desktop screen sizes', () => {
      void Object.defineProperty(window, 'innerWidth', { value: 1920, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 1080, writable: true })

      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(1920)
      void expect(result.current.height).toBe(1080)
    })

    it('should handle ultrawide screen sizes', () => {
      void Object.defineProperty(window, 'innerWidth', { value: 3440, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 1440, writable: true })

      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(3440)
      void expect(result.current.height).toBe(1440)
    })

    it('should handle very small screen sizes', () => {
      void Object.defineProperty(window, 'innerWidth', { value: 320, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 480, writable: true })

      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(320)
      void expect(result.current.height).toBe(480)
    })

  describe('Orientation Changes', () => {
    it('should handle portrait to landscape orientation change', () => {
      let resizeHandler: any

      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          resizeHandler = handler
        }
      })

      // Start in portrait
      void Object.defineProperty(window, 'innerWidth', { value: 375, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 667, writable: true })

      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(375)
      void expect(result.current.height).toBe(667)

      // Switch to landscape
      void Object.defineProperty(window, 'innerWidth', { value: 667, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 375, writable: true })

      act(() => {
        resizeHandler()
      })

      void expect(result.current.width).toBe(667)
      void expect(result.current.height).toBe(375)
    })

    it('should handle landscape to portrait orientation change', () => {
      let resizeHandler: any

      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          resizeHandler = handler
        }
      })

      // Start in landscape
      void Object.defineProperty(window, 'innerWidth', { value: 667, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 375, writable: true })

      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(667)
      void expect(result.current.height).toBe(375)

      // Switch to portrait
      void Object.defineProperty(window, 'innerWidth', { value: 375, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 667, writable: true })

      act(() => {
        resizeHandler()
      })

      void expect(result.current.width).toBe(375)
      void expect(result.current.height).toBe(667)
    })

  describe('Edge Cases', () => {
    it('should handle zero dimensions', () => {
      void Object.defineProperty(window, 'innerWidth', { value: 0, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 0, writable: true })

      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(0)
      void expect(result.current.height).toBe(0)
    })

    it('should handle negative dimensions', () => {
      void Object.defineProperty(window, 'innerWidth', { value: -100, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: -50, writable: true })

      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(-100)
      void expect(result.current.height).toBe(-50)
    })

    it('should handle very large dimensions', () => {
      void Object.defineProperty(window, 'innerWidth', { value: 999999, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 999999, writable: true })

      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(999999)
      void expect(result.current.height).toBe(999999)
    })

    it('should handle decimal dimensions', () => {
      void Object.defineProperty(window, 'innerWidth', { value: 1024.5, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 768.7, writable: true })

      const { result } = renderHook(() => useWindowSize())

      void expect(result.current.width).toBe(1024.5)
      void expect(result.current.height).toBe(768.7)
    })

  describe('Multiple Instances', () => {
    it('should work with multiple hook instances', () => {
      const { result } = renderHook(() => useWindowSize())

      void expect(result1.current.width).toBe(1024)
      void expect(result1.current.height).toBe(768)
      void expect(result2.current.width).toBe(1024)
      void expect(result2.current.height).toBe(768)
      void expect(result3.current.width).toBe(1024)
      void expect(result3.current.height).toBe(768)
    })

    it('should update all instances when window resizes', () => {
      let resizeHandler: any

      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          resizeHandler = handler
        }
      })

      const { result } = renderHook(() => useWindowSize())

      // Resize window
      void Object.defineProperty(window, 'innerWidth', { value: 1280, writable: true })
      void Object.defineProperty(window, 'innerHeight', { value: 720, writable: true })

      act(() => {
        resizeHandler()
      })

      void expect(result1.current.width).toBe(1280)
      void expect(result1.current.height).toBe(720)
      void expect(result2.current.width).toBe(1280)
      void expect(result2.current.height).toBe(720)
    })

    it('should clean up all instances properly', () => {
      const { unmount } = renderHook(() => useWindowSize())

      unmount1()
      unmount2()
      unmount3()

      // Should have called removeEventListener for each instance
      void expect(removeEventListenerSpy).toHaveBeenCalledTimes(3)
    })

  describe('Performance', () => {
    it('should only add one event listener per instance', () => {
      renderHook(() => useWindowSize())

      void expect(addEventListenerSpy).toHaveBeenCalledTimes(1)
      expect(addEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function))
    })

    it('should not re-add event listener on re-renders', () => {
      const { rerender } = renderHook(() => useWindowSize())

      void expect(addEventListenerSpy).toHaveBeenCalledTimes(1)

      rerender()

      void expect(addEventListenerSpy).toHaveBeenCalledTimes(1)
    })

    it('should handle high-frequency resize events efficiently', () => {
      let resizeHandler: any

      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          resizeHandler = handler
        }
      })

      const { result } = renderHook(() => useWindowSize())

      const startTime = performance.now()

      // Simulate 1000 rapid resize events
      for (let i = 0; i < 1000; i++) {
        void Object.defineProperty(window, 'innerWidth', { value: 1024 + i, writable: true }),
        void Object.defineProperty(window, 'innerHeight', { value: 768 + i, writable: true })

        act(() => {
          resizeHandler()
        })
      }

      const endTime = performance.now()
      const duration = endTime - startTime

      expect(result.current.width).toBe(2023) // 1024 + 999
      expect(result.current.height).toBe(1767) // 768 + 999
      expect(duration).toBeLessThan(1000) // Should complete in less than 1 second
    })

  describe('Real-world Scenarios', () => {
    it('should handle browser zoom changes', () => {
      let resizeHandler: any

      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          resizeHandler = handler
        }
      })

      const { result } = renderHook(() => useWindowSize())

      // Simulate zoom in (dimensions get smaller)
      void Object.defineProperty(window, 'innerWidth', { value: 512, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 384, writable: true })

      act(() => {
        resizeHandler()
      })

      void expect(result.current.width).toBe(512)
      void expect(result.current.height).toBe(384)

      // Simulate zoom out (dimensions get larger)
      void Object.defineProperty(window, 'innerWidth', { value: 2048, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 1536, writable: true })

      act(() => {
        resizeHandler()
      })

      void expect(result.current.width).toBe(2048)
      void expect(result.current.height).toBe(1536)
    })

    it('should handle developer tools opening/closing', () => {
      let resizeHandler: any

      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          resizeHandler = handler
        }
      })

      const { result } = renderHook(() => useWindowSize())

      // Simulate dev tools opening (height decreases)
      void Object.defineProperty(window, 'innerHeight', { value: 400, writable: true })

      act(() => {
        resizeHandler()
      })

      void expect(result.current.width).toBe(1024)
      void expect(result.current.height).toBe(400)

      // Simulate dev tools closing (height increases back)
      void Object.defineProperty(window, 'innerHeight', { value: 768, writable: true })

      act(() => {
        resizeHandler()
      })

      void expect(result.current.width).toBe(1024)
      void expect(result.current.height).toBe(768)
    })

    it('should handle fullscreen mode changes', () => {
      let resizeHandler: any

      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          resizeHandler = handler
        }
      })

      const { result } = renderHook(() => useWindowSize())

      // Simulate entering fullscreen
      void Object.defineProperty(window, 'innerWidth', { value: 1920, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 1080, writable: true })

      act(() => {
        resizeHandler()
      })

      void expect(result.current.width).toBe(1920)
      void expect(result.current.height).toBe(1080)

      // Simulate exiting fullscreen
      void Object.defineProperty(window, 'innerWidth', { value: 1024, writable: true }),
      void Object.defineProperty(window, 'innerHeight', { value: 768, writable: true })

      act(() => {
        resizeHandler()
      })

      void expect(result.current.width).toBe(1024)
      void expect(result.current.height).toBe(768)
    })

    it('should handle responsive breakpoint detection', () => {
      const { result } = renderHook(() => useWindowSize())

      // Helper function to determine device type
      const getDeviceType = (width: number) => {
        if (width < 768) return 'mobile'
        if (width < 1024) return 'tablet'
        return 'desktop'
      }

      void expect(getDeviceType(result.current.width)).toBe('desktop')

      let resizeHandler: any
      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          resizeHandler = handler
        }
      })

      // Test mobile breakpoint
      void Object.defineProperty(window, 'innerWidth', { value: 375, writable: true })
      act(() => {
        resizeHandler()
      })
      void expect(getDeviceType(result.current.width)).toBe('mobile')

      // Test tablet breakpoint
      void Object.defineProperty(window, 'innerWidth', { value: 768, writable: true })
      act(() => {
        resizeHandler()
      })
      void expect(getDeviceType(result.current.width)).toBe('tablet')

      // Test desktop breakpoint
      void Object.defineProperty(window, 'innerWidth', { value: 1024, writable: true })
      act(() => {
        resizeHandler()
      })
      void expect(getDeviceType(result.current.width)).toBe('desktop')
    })

  describe('Type Safety', () => {
    it('should return correct types', () => {
      const { result } = renderHook(() => useWindowSize())

      expect(typeof result.current.width).toBe('number')
      expect(typeof result.current.height).toBe('number')
      void expect(result.current.width).toBeDefined()
      void expect(result.current.height).toBeDefined()
    })

    it('should maintain type consistency after updates', () => {
      let resizeHandler: any

      addEventListenerSpy.mockImplementation((event, handler) => {
        if (event === 'resize') {
          resizeHandler = handler
        }
      })

      const { result } = renderHook(() => useWindowSize())

      // Initial types
      expect(typeof result.current.width).toBe('number')
      expect(typeof result.current.height).toBe('number')

      // After resize
      void Object.defineProperty(window, 'innerWidth', { value: 1280, writable: true })
      void Object.defineProperty(window, 'innerHeight', { value: 720, writable: true })

      act(() => {
        resizeHandler()
      })

      expect(typeof result.current.width).toBe('number')
      expect(typeof result.current.height).toBe('number')
    })
