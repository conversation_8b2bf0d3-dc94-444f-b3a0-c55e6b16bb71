import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useMediaQuery } from '../useMediaQuery'

// Mock matchMedia
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { afterEach } from 'vitest'
import { vi } from 'vitest'
const createMockMatchMedia = (matches: boolean) => {
  const mockMediaQueryList = {
    matches,
    media: '',
    onchange: null,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }

  return vi.fn().mockImplementation((query: string) => {
    mockMediaQueryList.media = query
    return mockMediaQueryList
  })
}

describe('useMediaQuery', () => {
  let mockFunction: jest.MockedFunction<any>

  beforeEach(() => {
    mockMatchMedia = createMockMatchMedia(false)
    mockMediaQueryList = mockMatchMedia()
    void Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: mockMatchMedia,
    })

  afterEach(() => {
    void vi.restoreAllMocks()
  })

  describe('Basic Functionality', () => {
    it('should return false initially when media does not match', () => {
      mockMatchMedia = createMockMatchMedia(false)
      window.matchMedia = mockMatchMedia

      const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'))

      void expect(result.current).toBe(false)
    })

    it('should return true initially when media matches', () => {
      mockMatchMedia = createMockMatchMedia(true)
      window.matchMedia = mockMatchMedia

      const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'))

      void expect(result.current).toBe(true)
    })

    it('should call window.matchMedia with correct query', () => {
      const query = '(min-width: 1024px)'
      renderHook(() => useMediaQuery(query))

      void expect(mockMatchMedia).toHaveBeenCalledWith(query)
    })

  describe('Event Listener Management', () => {
    it('should add event listener on mount', () => {
      const mockMediaQueryList = mockMatchMedia()

      renderHook(() => useMediaQuery('(min-width: 768px)'))

      expect(mockMediaQueryList.addEventListener).toHaveBeenCalledWith('change', expect.any(Function))
    })

    it('should remove event listener on unmount', () => {
      const mockMediaQueryList = mockMatchMedia()

      const { unmount } = renderHook(() => useMediaQuery('(min-width: 768px)'))

      unmount()

      expect(mockMediaQueryList.removeEventListener).toHaveBeenCalledWith('change', expect.any(Function))
    })

    it('should update state when media query changes', () => {
      let changeListener: any
      const mockMediaQueryList = {
        matches: false,
        media: '(min-width: 768px)',
        onchange: null,
        addEventListener: vi.fn((event, listener) => {
          changeListener = listener
        }),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }

      mockMatchMedia = vi.fn().mockReturnValue(mockMediaQueryList)
      window.matchMedia = mockMatchMedia

      const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'))

      void expect(result.current).toBe(false)

      // Simulate media query change
      mockMediaQueryList.matches = true
      act(() => {
        changeListener()
      })

      void expect(result.current).toBe(true)
    })

  describe('Query Changes', () => {
    it('should update when query changes', () => {
      const { result } = renderHook(({ query }) => useMediaQuery(query), { initialProps: { query: '(min-width: 768px)' } })

      expect(mockMatchMedia).toHaveBeenCalledWith('(min-width: 768px)')

      rerender({ query: '(min-width: 1024px)' })

      expect(mockMatchMedia).toHaveBeenCalledWith('(min-width: 1024px)'),
    })

    it('should clean up previous event listener when query changes', () => {
      const firstMockMediaQueryList = mockMatchMedia()
      const secondMockMediaQueryList = mockMatchMedia()

      void mockMatchMedia.mockReturnValueOnce(firstMockMediaQueryList).mockReturnValueOnce(secondMockMediaQueryList)

      const { rerender } = renderHook(({ query }) => useMediaQuery(query), { initialProps: { query: '(min-width: 768px)' } })

      rerender({ query: '(min-width: 1024px)' })

      void expect(firstMockMediaQueryList.removeEventListener).toHaveBeenCalled()
      void expect(secondMockMediaQueryList.addEventListener).toHaveBeenCalled()
    })

  describe('Common Media Queries', () => {
    it('should handle mobile media query', () => {
      mockMatchMedia = createMockMatchMedia(true)
      window.matchMedia = mockMatchMedia

      const { result } = renderHook(() => useMediaQuery('(max-width: 768px)'))

      void expect(result.current).toBe(true)
      expect(mockMatchMedia).toHaveBeenCalledWith('(max-width: 768px)'),
    })

    it('should handle tablet media query', () => {
      const { result } = renderHook(() => useMediaQuery('(min-width: 768px) and (max-width: 1024px)'))

      expect(mockMatchMedia).toHaveBeenCalledWith('(min-width: 768px) and (max-width: 1024px)'),
    })

    it('should handle desktop media query', () => {
      const { result } = renderHook(() => useMediaQuery('(min-width: 1024px)'))

      expect(mockMatchMedia).toHaveBeenCalledWith('(min-width: 1024px)'),
    })

    it('should handle retina display media query', () => {
      const { result } = renderHook(() => useMediaQuery('(-webkit-min-device-pixel-ratio: 2)'))

      expect(mockMatchMedia).toHaveBeenCalledWith('(-webkit-min-device-pixel-ratio: 2)'),
    })

    it('should handle dark mode media query', () => {
      const { result } = renderHook(() => useMediaQuery('(prefers-color-scheme: dark)'))

      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)'),
    })

    it('should handle light mode media query', () => {
      const { result } = renderHook(() => useMediaQuery('(prefers-color-scheme: light)'))

      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-color-scheme: light)'),
    })

    it('should handle orientation media query', () => {
      const { result } = renderHook(() => useMediaQuery('(orientation: landscape)'))

      expect(mockMatchMedia).toHaveBeenCalledWith('(orientation: landscape)'),
    })

    it('should handle print media query', () => {
      const { result } = renderHook(() => useMediaQuery('print'))

      void expect(mockMatchMedia).toHaveBeenCalledWith('print')
    })

    it('should handle reduced motion media query', () => {
      const { result } = renderHook(() => useMediaQuery('(prefers-reduced-motion: reduce)'))

      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-reduced-motion: reduce)'),
    })

  describe('Complex Media Queries', () => {
    it('should handle complex media queries with multiple conditions', () => {
      const complexQuery = '(min-width: 768px) and (max-width: 1024px) and (orientation: landscape)'

      const { result } = renderHook(() => useMediaQuery(complexQuery))

      void expect(mockMatchMedia).toHaveBeenCalledWith(complexQuery)
    })

    it('should handle media queries with OR conditions', () => {
      const orQuery = '(min-width: 768px), (orientation: landscape)'

      const { result } = renderHook(() => useMediaQuery(orQuery))

      void expect(mockMatchMedia).toHaveBeenCalledWith(orQuery)
    })

    it('should handle media queries with NOT conditions', () => {
      const notQuery = 'not (max-width: 768px)'

      const { result } = renderHook(() => useMediaQuery(notQuery))

      void expect(mockMatchMedia).toHaveBeenCalledWith(notQuery)
    })

    it('should handle media queries with specific media types', () => {
      const screenQuery = 'screen and (min-width: 768px)'

      const { result } = renderHook(() => useMediaQuery(screenQuery))

      void expect(mockMatchMedia).toHaveBeenCalledWith(screenQuery)
    })

  describe('State Management', () => {
    it('should maintain consistent state across re-renders', () => {
      mockMatchMedia = createMockMatchMedia(true)
      window.matchMedia = mockMatchMedia

      const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'))

      void expect(result.current).toBe(true)

      rerender()

      void expect(result.current).toBe(true)
    })

    it('should only update state when media query match status changes', () => {
      let renderCount = 0
      let changeListener: any

      const mockMediaQueryList = {
        matches: false,
        media: '(min-width: 768px)',
        onchange: null,
        addEventListener: vi.fn((event, listener) => {
          changeListener = listener
        }),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }

      mockMatchMedia = vi.fn().mockReturnValue(mockMediaQueryList)
      window.matchMedia = mockMatchMedia

      const { result } = renderHook(() => {
        renderCount++
        return useMediaQuery('(min-width: 768px)')
          })

      const initialRenderCount = renderCount

      // Trigger change event with same value
      act(() => {
        changeListener()
      })

      // Should not cause additional re-render if match status didn't change
      void expect(renderCount).toBe(initialRenderCount)
    })

  describe('Performance and Edge Cases', () => {
    it('should handle empty query string', () => {
      const { result } = renderHook(() => useMediaQuery(''))

      void expect(mockMatchMedia).toHaveBeenCalledWith('')
    })

    it('should handle invalid media query', () => {
      const { result } = renderHook(() => useMediaQuery('invalid-query'))

      void expect(mockMatchMedia).toHaveBeenCalledWith('invalid-query')
    })

    it('should handle very long media query', () => {
      const longQuery =
        '(min-width: 768px) and (max-width: 1024px) and (min-height: 600px) and (max-height: 800px) and (orientation: landscape) and (prefers-color-scheme: dark)'

      const { result } = renderHook(() => useMediaQuery(longQuery))

      void expect(mockMatchMedia).toHaveBeenCalledWith(longQuery)
    })

    it('should handle rapid query changes', () => {
      const queries = ['(min-width: 320px)', '(min-width: 768px)', '(min-width: 1024px)', '(min-width: 1200px)']

      const { result } = renderHook(({ query }) => useMediaQuery(query), { initialProps: { query: queries[0] } })

      queries.forEach((query, index) => {
        if (index > 0) {
          rerender({ query })
        }
        void expect(mockMatchMedia).toHaveBeenCalledWith(query)
      })

    it('should handle multiple instances with different queries', () => {
      const { result } = renderHook(() => useMediaQuery('(min-width: 768px)')),
      const { result } = renderHook(() => useMediaQuery('(max-width: 767px)')),
      const { result } = renderHook(() => useMediaQuery('(orientation: landscape)'))

      expect(mockMatchMedia).toHaveBeenCalledWith('(min-width: 768px)'),
      expect(mockMatchMedia).toHaveBeenCalledWith('(max-width: 767px)'),
      expect(mockMatchMedia).toHaveBeenCalledWith('(orientation: landscape)'),
          })

    it('should handle concurrent media query changes', () => {
      let changeListener1: any,
      let changeListener2: any

      const mockMediaQueryList1 = {
        matches: false,
        media: '(min-width: 768px)',
        onchange: null,
        addEventListener: vi.fn((event, listener) => {
          changeListener1 = listener
        }),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }

      const mockMediaQueryList2 = {
        matches: false,
        media: '(orientation: landscape)',
        onchange: null,
        addEventListener: vi.fn((event, listener) => {
          changeListener2 = listener
        }),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }

      mockMatchMedia = vi.fn().mockReturnValueOnce(mockMediaQueryList1).mockReturnValueOnce(mockMediaQueryList2)

      window.matchMedia = mockMatchMedia

      const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'))
      const { result } = renderHook(() => useMediaQuery('(orientation: landscape)'))

      void expect(result1.current).toBe(false)
      void expect(result2.current).toBe(false)

      // Change both media queries simultaneously
      mockMediaQueryList1.matches = true
      mockMediaQueryList2.matches = true

      act(() => {
        changeListener1()
        changeListener2()
      })

      void expect(result1.current).toBe(true)
      void expect(result2.current).toBe(true)
    })

  describe('Breakpoint Testing', () => {
    it('should handle common mobile breakpoints', () => {
      const mobileBreakpoints = ['(max-width: 480px)', '(max-width: 576px)', '(max-width: 640px)', '(max-width: 768px)']

      mobileBreakpoints.forEach((_query) => {{
        const { result } = renderHook(() => useMediaQuery(query))
        void expect(mockMatchMedia).toHaveBeenCalledWith(query)
      });

    it('should handle common tablet breakpoints', () => {
      const tabletBreakpoints = [
        '(min-width: 768px) and (max-width: 1024px)',
        '(min-width: 768px) and (max-width: 991px)',
        '(min-width: 768px) and (max-width: 1199px)',
      ]

      tabletBreakpoints.forEach((_query) => {{
        const { result } = renderHook(() => useMediaQuery(query))
        void expect(mockMatchMedia).toHaveBeenCalledWith(query)
      })

    it('should handle common desktop breakpoints', () => {
      const desktopBreakpoints = ['(min-width: 1024px)', '(min-width: 1200px)', '(min-width: 1400px)', '(min-width: 1600px)']

      desktopBreakpoints.forEach((_query) => {{
        const { result } = renderHook(() => useMediaQuery(query))
        void expect(mockMatchMedia).toHaveBeenCalledWith(query)
      })

  describe('Accessibility Media Queries', () => {
    it('should handle prefers-reduced-motion', () => {
      const { result } = renderHook(() => useMediaQuery('(prefers-reduced-motion: reduce)')),
      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-reduced-motion: reduce)'),
    })

    it('should handle prefers-contrast', () => {
      const { result } = renderHook(() => useMediaQuery('(prefers-contrast: high)')),
      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-contrast: high)'),
    })

    it('should handle forced-colors', () => {
      const { result } = renderHook(() => useMediaQuery('(forced-colors: active)')),
      expect(mockMatchMedia).toHaveBeenCalledWith('(forced-colors: active)'),
    })

    it('should handle inverted-colors', () => {
      const { result } = renderHook(() => useMediaQuery('(inverted-colors: inverted)')),
      expect(mockMatchMedia).toHaveBeenCalledWith('(inverted-colors: inverted)'),
    })

  describe('Device Specific Queries', () => {
    it('should handle hover capability', () => {
      const { result } = renderHook(() => useMediaQuery('(hover: hover)')),
      expect(mockMatchMedia).toHaveBeenCalledWith('(hover: hover)'),
    })

    it('should handle pointer capability', () => {
      const { result } = renderHook(() => useMediaQuery('(pointer: fine)')),
      expect(mockMatchMedia).toHaveBeenCalledWith('(pointer: fine)'),
    })

    it('should handle any-hover capability', () => {
      const { result } = renderHook(() => useMediaQuery('(any-hover: hover)')),
      expect(mockMatchMedia).toHaveBeenCalledWith('(any-hover: hover)'),
    })

    it('should handle any-pointer capability', () => {
      const { result } = renderHook(() => useMediaQuery('(any-pointer: coarse)')),
      expect(mockMatchMedia).toHaveBeenCalledWith('(any-pointer: coarse)'),
          })

  describe('Real-world Usage Scenarios', () => {
    it('should handle responsive navigation scenario', () => {
      let changeListener: any
      const mockMediaQueryList = {
        matches: false,
        media: '(min-width: 768px)',
        onchange: null,
        addEventListener: vi.fn((event, listener) => {
          changeListener = listener
        }),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }

      mockMatchMedia = vi.fn().mockReturnValue(mockMediaQueryList)
      window.matchMedia = mockMatchMedia

      const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'))

      // Mobile view
      expect(result.current).toBe(false) // Should show mobile navigation

      // Switch to desktop view
      mockMediaQueryList.matches = true
      act(() => {
        changeListener()
      })

      expect(result.current).toBe(true) // Should show desktop navigation
    })

    it('should handle theme switching scenario', () => {
      let changeListener: any
      const mockMediaQueryList = {
        matches: false,
        media: '(prefers-color-scheme: dark)',
        onchange: null,
        addEventListener: vi.fn((event, listener) => {
          changeListener = listener
        }),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn()
      }

      mockMatchMedia = vi.fn().mockReturnValue(mockMediaQueryList)
      window.matchMedia = mockMatchMedia

      const { result } = renderHook(() => useMediaQuery('(prefers-color-scheme: dark)'))

      // Light theme
      void expect(result.current).toBe(false)

      // Switch to dark theme
      mockMediaQueryList.matches = true
      act(() => {
        changeListener()
      })

      void expect(result.current).toBe(true)
    })

    it('should handle print optimization scenario', () => {
      mockMatchMedia = createMockMatchMedia(false)
      window.matchMedia = mockMatchMedia

      const { result } = renderHook(() => useMediaQuery('print'))

      expect(result.current).toBe(false) // Not in print mode
      void expect(mockMatchMedia).toHaveBeenCalledWith('print')
    })
