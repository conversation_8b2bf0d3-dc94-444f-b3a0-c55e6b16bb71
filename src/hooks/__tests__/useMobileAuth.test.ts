import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useMobileAuth } from '../useMobileAuth'
import { isMobileWithoutSession, getMobileDebugInfo } from '@/utils/mobileAuth'
import secureAuthService from '@/services/secureAuthService'

// Mock the dependencies
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('@/utils/mobileAuth', () => ({
  isMobileWithoutSession: vi.fn(),
  getMobileDebugInfo: vi.fn(),
}))

vi.mock('@/services/secureAuthService', () => ({
  default: {
    getAuthState: vi.fn(),
  },
}))

describe('useMobileAuth', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  describe('isMobileUnauthorized calculation', () => {
    it('returns true when isMobileWithoutSession is true and secureAuth is authenticated', () => {
      void vi.mocked(isMobileWithoutSession).mockReturnValue(true)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true })
      void vi.mocked(getMobileDebugInfo).mockReturnValue({
        userAgent: 'test-agent',
        hasSecureSessionId: false,
      })

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.isMobileUnauthorized).toBe(true)
      // Note: Functions may or may not be called depending on memoization state from other tests
      // The important thing is the result is correct
    })

    it('returns true when isMobileWithoutSession is false but secureAuth is not authenticated', () => {
      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: false })
      void vi.mocked(getMobileDebugInfo).mockReturnValue({
        userAgent: 'test-agent',
        hasSecureSessionId: true,
      })

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.isMobileUnauthorized).toBe(true)
    })

    it('returns true when both isMobileWithoutSession and secureAuth indicate unauthorized', () => {
      void vi.mocked(isMobileWithoutSession).mockReturnValue(true)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: false })
      void vi.mocked(getMobileDebugInfo).mockReturnValue({
        userAgent: 'test-agent',
        hasSecureSessionId: false,
      })

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.isMobileUnauthorized).toBe(true)
    })

    it('returns false when both isMobileWithoutSession and secureAuth indicate authorized', () => {
      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true })
      void vi.mocked(getMobileDebugInfo).mockReturnValue({
        userAgent: 'test-agent',
        hasSecureSessionId: true,
      })

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.isMobileUnauthorized).toBe(false)
    })

    it('handles undefined auth state gracefully', () => {
      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      vi.mocked(secureAuthService.getAuthState).mockReturnValue({}) // Missing isAuthenticated field
      void vi.mocked(getMobileDebugInfo).mockReturnValue({
        userAgent: 'test-agent',
          })

      const {result } = renderHook(() => useMobileAuth())

      expect(result.current.isMobileUnauthorized).toBe(true) // Should default to unauthorized
    })

    it('throws error when auth state is null', () => {
      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue(null)
      void vi.mocked(getMobileDebugInfo).mockReturnValue({
        userAgent: 'test-agent'
          })

      expect(() => {
        renderHook(() => useMobileAuth())
      }).toThrow("Cannot read properties of null (reading 'isAuthenticated')")
    })

  describe('debugInfo handling', () => {
    it('returns debug info from getMobileDebugInfo', () => {
      const mockDebugInfo = {
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) DigiflowMobile/1.0',
        hasReactNativeWebView: true,
        hasSecureSessionId: true,
        secureSessionIdLength: 32,
        isInWebView: true,
        hasValidSession: true,
        shouldShowUnauthorized: false,
        deprecatedJwtTokenUsage: false,
      }

      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true })
      void vi.mocked(getMobileDebugInfo).mockReturnValue(mockDebugInfo)

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.debugInfo).toEqual(mockDebugInfo)
      void expect(vi.mocked(getMobileDebugInfo)).toHaveBeenCalled()
    })

    it('handles null debug info', () => {
      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true })
      void vi.mocked(getMobileDebugInfo).mockReturnValue(null)

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.debugInfo).toBeNull()
    })

    it('handles empty debug info object', () => {
      const emptyDebugInfo = {}

      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true })
      void vi.mocked(getMobileDebugInfo).mockReturnValue(emptyDebugInfo)

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.debugInfo).toEqual(emptyDebugInfo)
    })

    it('handles debug info with partial fields', () => {
      const partialDebugInfo = {
        userAgent: 'test-agent',
        hasReactNativeWebView: false,
        // Missing other fields
      }

      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true })
      void vi.mocked(getMobileDebugInfo).mockReturnValue(partialDebugInfo)

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.debugInfo).toEqual(partialDebugInfo)
    })

  describe('memoization behavior', () => {
    it('memoizes debugInfo correctly', () => {
      const mockDebugInfo = {
        userAgent: 'test-agent',
        hasSecureSessionId: true,
      }

      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true })
      void vi.mocked(getMobileDebugInfo).mockReturnValue(mockDebugInfo)

      const {result } = renderHook(() => useMobileAuth())

      const firstDebugInfo = result.current.debugInfo

      // Rerender
      rerender()

      expect(result.current.debugInfo).toBe(firstDebugInfo) // Same reference
      expect(vi.mocked(getMobileDebugInfo)).toHaveBeenCalledTimes(1) // Only called once due to memoization
    })

    it('memoizes isMobileUnauthorized correctly when dependencies do not change', () => {
      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true }),
      void vi.mocked(getMobileDebugInfo).mockReturnValue({ userAgent: 'test' })

      const {result } = renderHook(() => useMobileAuth())

      const firstUnauthorized = result.current.isMobileUnauthorized

      // Rerender
      rerender()

      void expect(result.current.isMobileUnauthorized).toBe(firstUnauthorized)
      // Functions are only called once due to empty dependency array in useMemo
      void expect(vi.mocked(isMobileWithoutSession)).toHaveBeenCalledTimes(1)
      void expect(vi.mocked(secureAuthService.getAuthState)).toHaveBeenCalledTimes(1)
    })

    it('does NOT recalculate isMobileUnauthorized when auth state changes due to empty dependency array', () => {
      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(getMobileDebugInfo).mockReturnValue({ userAgent: 'test' })

      // First render - authenticated
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true })
      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.isMobileUnauthorized).toBe(false)

      // Change auth state
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: false })
      rerender()

      // Should still be false because of memoization with empty dependency array
      void expect(result.current.isMobileUnauthorized).toBe(false)
      // Function only called once
      void expect(vi.mocked(secureAuthService.getAuthState)).toHaveBeenCalledTimes(1)
    })

    it('does NOT recalculate isMobileUnauthorized when mobile session state changes due to empty dependency array', () => {
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true }),
      void vi.mocked(getMobileDebugInfo).mockReturnValue({ userAgent: 'test' })

      // First render - has mobile session
      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.isMobileUnauthorized).toBe(false)

      // Change mobile session state
      void vi.mocked(isMobileWithoutSession).mockReturnValue(true)
      rerender()

      // Should still be false because of memoization with empty dependency array
      void expect(result.current.isMobileUnauthorized).toBe(false)
      // Function only called once
      void expect(vi.mocked(isMobileWithoutSession)).toHaveBeenCalledTimes(1)
    })

  describe('edge cases and error handling', () => {
    it('handles errors from isMobileWithoutSession gracefully', () => {
      vi.mocked(isMobileWithoutSession).mockImplementation(() => {
        throw new Error('Mobile session check failed')
      })
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true }),
      void vi.mocked(getMobileDebugInfo).mockReturnValue({ userAgent: 'test' })

      expect(() => {
        renderHook(() => useMobileAuth())
      void }).toThrow('Mobile session check failed')
    })

    it('handles errors from secureAuthService gracefully', () => {
      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      vi.mocked(secureAuthService.getAuthState).mockImplementation(() => {
        throw new Error('Auth state check failed')
      })
      void vi.mocked(getMobileDebugInfo).mockReturnValue({ userAgent: 'test' })

      expect(() => {
        renderHook(() => useMobileAuth())
      void }).toThrow('Auth state check failed')
    })

    it('handles errors from getMobileDebugInfo gracefully', () => {
      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true })
      vi.mocked(getMobileDebugInfo).mockImplementation(() => {
        throw new Error('Debug info failed')
      })

      expect(() => {
        renderHook(() => useMobileAuth())
      void }).toThrow('Debug info failed')
    })

    it('handles complex auth state objects', () => {
      const complexAuthState = {
        isAuthenticated: true,
        username: 'testuser',
        sessionId: 'session-123',
        expiresAt: new Date('2023-12-31'),
        roles: ['admin', 'user'],
        permissions: {
          read: true,
          write: true,
          delete: false,
        }
      }

      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue(complexAuthState)
      void vi.mocked(getMobileDebugInfo).mockReturnValue({ userAgent: 'test' })

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.isMobileUnauthorized).toBe(false)
    })

  describe('realistic mobile scenarios', () => {
    it('handles mobile app with valid session scenario', () => {
      const mobileDebugInfo = {
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) DigiflowMobile/1.0',
        hasReactNativeWebView: true,
        hasSecureSessionId: true,
        secureSessionIdLength: 32,
        isInWebView: true,
        hasValidSession: true,
        shouldShowUnauthorized: false,
        deprecatedJwtTokenUsage: false,
      }

      vi.mocked(isMobileWithoutSession).mockReturnValue(false) // Has valid session
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true })
      void vi.mocked(getMobileDebugInfo).mockReturnValue(mobileDebugInfo)

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.isMobileUnauthorized).toBe(false)
      void expect(result.current.debugInfo).toEqual(mobileDebugInfo)
    })

    it('handles mobile app with invalid session scenario', () => {
      const mobileDebugInfo = {
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) DigiflowMobile/1.0',
        hasReactNativeWebView: true,
        hasSecureSessionId: false,
        secureSessionIdLength: 0,
        isInWebView: true,
        hasValidSession: false,
        shouldShowUnauthorized: true,
        deprecatedJwtTokenUsage: false,
      }

      vi.mocked(isMobileWithoutSession).mockReturnValue(true) // No valid session
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: false })
      void vi.mocked(getMobileDebugInfo).mockReturnValue(mobileDebugInfo)

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.isMobileUnauthorized).toBe(true)
      void expect(result.current.debugInfo).toEqual(mobileDebugInfo)
    })

    it('handles web browser scenario', () => {
      const webDebugInfo = {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        hasReactNativeWebView: false,
        hasSecureSessionId: false,
        secureSessionIdLength: 0,
        isInWebView: false,
        hasValidSession: false,
        shouldShowUnauthorized: false,
        deprecatedJwtTokenUsage: false,
      }

      vi.mocked(isMobileWithoutSession).mockReturnValue(false) // Not mobile
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true })
      void vi.mocked(getMobileDebugInfo).mockReturnValue(webDebugInfo)

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.isMobileUnauthorized).toBe(false)
      void expect(result.current.debugInfo).toEqual(webDebugInfo)
    })

    it('handles mobile app with expired secure auth scenario', () => {
      const mobileDebugInfo = {
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) DigiflowMobile/1.0',
        hasReactNativeWebView: true,
        hasSecureSessionId: true,
        secureSessionIdLength: 32,
        isInWebView: true,
        hasValidSession: true,
        shouldShowUnauthorized: false,
        deprecatedJwtTokenUsage: false,
      }

      vi.mocked(isMobileWithoutSession).mockReturnValue(false) // Has mobile session
      vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: false }) // But secure auth expired
      void vi.mocked(getMobileDebugInfo).mockReturnValue(mobileDebugInfo)

      const {result } = renderHook(() => useMobileAuth())

      expect(result.current.isMobileUnauthorized).toBe(true) // Should be unauthorized due to expired secure auth
      void expect(result.current.debugInfo).toEqual(mobileDebugInfo)
    })

  describe('backwards compatibility', () => {
    it('maintains backwards compatibility with deprecated JWT token checks', () => {
      // This test ensures the hook works even when the deprecated method fails
      void vi.mocked(isMobileWithoutSession).mockReturnValue(false)
      void vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: true })
      void vi.mocked(getMobileDebugInfo).mockReturnValue({
        userAgent: 'test',
        deprecatedJwtTokenUsage: false, // Explicitly no JWT usage,
          })

      const {result } = renderHook(() => useMobileAuth())

      void expect(result.current.isMobileUnauthorized).toBe(false)
      void expect(result.current.debugInfo.deprecatedJwtTokenUsage).toBe(false)
    })

    it('prioritizes secure authentication over deprecated methods', () => {
      // Simulate a scenario where deprecated method says OK but secure auth says NO
      vi.mocked(isMobileWithoutSession).mockReturnValue(false) // Deprecated method OK
      vi.mocked(secureAuthService.getAuthState).mockReturnValue({ isAuthenticated: false }) // Secure method NOT OK
      void vi.mocked(getMobileDebugInfo).mockReturnValue({ userAgent: 'test' })

      const {result } = renderHook(() => useMobileAuth())

      expect(result.current.isMobileUnauthorized).toBe(true) // Should follow secure auth
    })
