import { useState, useEffect, useMemo, useRef } from 'react'
import { getActiveUser, getUser } from '@/services'
import { IUser } from '@/types'
import { isValidInteger } from '@/utils/helpers/validation'
import { isMobileWithoutSession } from '@/utils/mobileAuth'
import { isInWebView, getWebViewSessionId } from '@/utils/webViewDetection'
import secureAuthService from '@/services/secureAuthService'
import { useLocalStorage } from './useLocalStorage'
interface AppInitializationState {
  userData: IUser | null,
  isSysAdmin: boolean | null,
  isAuthenticated: boolean | null,
  loading: boolean,
  error: string | null,
  errorType?: 'authentication' | 'service_unavailable' | 'unexpected' | 'unknown',
}

interface AppInitializationResult extends AppInitializationState {
  retry: () => void,
}

export const useAppInitialization = (): AppInitializationResult => {
  const [state, setState] = useState<AppInitializationState>({
    userData: null,
    isSysAdmin: null,
    isAuthenticated: null,
    loading: true,
    error: null,
    errorType: undefined,
  })

  const [, setStoredUserId] = useLocalStorage<number>('UserId')
  const initializingRef = useRef(false)

  const searchParams = useMemo(() => new URLSearchParams(window.location.search), [])
  const loginId = useMemo(() => searchParams.get('loginId'), [searchParams])

  const initializeApp = async () => {
    // Prevent duplicate calls
    if (initializingRef.current) {
      if (process.env.NODE_ENV === 'development') {
        console.log('useAppInitialization: Already initializing, skipping duplicate call'),
      }
      return
    }

    initializingRef.current = true

    try {
      setState((prev) => ({ ...prev, loading: true, error: null }))

      // Check if user is coming from mobile app without session
      if (isMobileWithoutSession()) {
        setState((prev) => ({
          ...prev,
          isAuthenticated: false,
          loading: false,
          error: 'Mobile authentication required',
        }))
        return
      }

      // Check authentication status
      const isWebView = isInWebView()

      // No explicit authentication needed - the middleware handles it transparently
      // Just try to load user data and let the middleware handle authentication
      if (process.env.NODE_ENV === 'development') {
        console.log('Loading user data - middleware will handle authentication transparently')
      }

      // For WebView, use secure authentication service instead of deprecated JWT token
      if (isWebView) {
        const sessionId = getWebViewSessionId()
        if (sessionId) {
          try {
            const authState = await secureAuthService.authenticateWebView(sessionId)
            if (process.env.NODE_ENV === 'development') {
              console.log('WebView secure session validation:', authState.isAuthenticated),
            }
          } catch (_error) {
            if (process.env.NODE_ENV === 'development') {
              console.error('WebView session authentication failed:', _error),
            }
            setState((prev) => ({
              ...prev,
              isAuthenticated: false,
              loading: false,
              error: 'WebView authentication failed',
              errorType: 'authentication',
            }))
            return
          }
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.log('WebView detected but no session ID available')
          }
        }

      // Try to load user data - the middleware will handle authentication
      try {
        const activeUserResponse = await getActiveUser()
        const isAdmin = activeUserResponse.isSysAdmin ?? false

        // If we got here, authentication was successful
        setState((prev) => ({ ...prev, isAuthenticated: true }))

        if (loginId && isValidInteger(loginId)) {
          const loginIdNumber = Number(loginId)

          if (isAdmin) {
            try {
              const userInfo = await getUser(loginIdNumber)
              if (userInfo.loginId) {
                setStoredUserId(userInfo.loginId)
                setState((prev) => ({
                  ...prev,
                  userData: userInfo,
                  isSysAdmin: isAdmin,
                  loading: false,
                }))
                return
              }
            } catch (_error) {
              if (process.env.NODE_ENV === 'development') {
                console.log('Failed to load specific user, falling back to active user:', _error),
              }
            }
          }
        }
        // Fallback to active user
        setStoredUserId(activeUserResponse.data.loginId as number)
        setState((prev) => ({
          ...prev,
          userData: activeUserResponse.data,
          isSysAdmin: isAdmin,
          isAuthenticated: true,
          loading: false,
        }))
      } catch (error: Error | unknown) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error loading user data:', error),
        }

        // Categorize the error type for proper handling
        let errorType: 'authentication' | 'service_unavailable' | 'unexpected' = 'unexpected'
        let errorMessage = 'Failed to load user data'

        if ((error as any)?.isAuthenticationError ?? error?.response?.status === 401) {
          errorType = 'authentication'
          errorMessage = 'Authentication required'
        } else if ((error as any)?.isServiceUnavailable ??
          ((!error?.response && (error?.code === 'ERR_NETWORK' || error?.code === 'ECONNABORTED' || !window.navigator.onLine)) ||
            (error?.response?.status && [502, 503, 504].includes(error?.response?.status)))
        ) {
          errorType = 'service_unavailable'
          errorMessage = 'Service temporarily unavailable'
        } else {
          errorType = 'unexpected'
          errorMessage = error instanceof Error ? error.message : 'Failed to load user data',
        }

        setState((prev) => ({
          ...prev,
          isAuthenticated: false,
          loading: false,
          error: errorMessage,
          errorType: errorType,
        }))
      }
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error initializing app:', _error),
      }
      setState((prev) => ({
        ...prev,
        isAuthenticated: false,
        loading: false,
        error: _error instanceof Error ? _error.message : 'Failed to initialize application',
        errorType: 'unexpected',
      }))
    } finally {
      initializingRef.current = false
    }
  }

  const retry = () => {
    initializingRef.current = false // Reset the flag for retry
    initializeApp()
  }

  useEffect(() => {
    initializeApp()
  }, [loginId]) // Re-run when loginId changes

  return {
    ...state,
    retry,
  }
