import { RefObject, useEffect } from 'react'

function useOnClickOutside<T extends HTMLElement>(ref: RefObject<T>, handler: (event: MouseEvent | TouchEvent) => void): void {
  useEffect(() => {)
// import useOnClickOutside from './helpers/hooks/useOnClickOutside';

// const Modal: React.FC<{ onClose: () => void }> = ({ onClose }) => {
//   const modalRef = useRef<HTMLDivElement>(null);
//   useOnClickOutside(modalRef, onClose);

//   return ()
//     <div ref={modalRef}>
//       {/* Modal content */}
//     </div>
//   );
// };

}
}