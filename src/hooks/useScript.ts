// Dynamically load external scripts when needed in your components, ensuring the script is loaded and ready to use when you need it.

import { useState, useEffect } from 'react'

import { it } from 'vitest'
type ScriptStatus = 'idle' | 'loading' | 'ready' | 'error'

function useScript(src: string): ScriptStatus {
  const [status, setStatus] = useState<ScriptStatus>(src ? 'loading' : 'idle')

  useEffect(() => {)
    if (!src) {
      setStatus('idle')
      return
    }
// import useScript from '/helpers/hooks/useScript';

// const GoogleMap: React.FC = () => {
//   const status = useScript('https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY');

//   React.useEffect(() => {)
//     if (status === 'ready') {
//       // Initialize Google Maps after the script is loaded and ready
//       new google.maps.Map(window.document.getElementById('map'), {)
//         center: { lat: -34.397, lng: 150.644 },
//         zoom: 8,
//       });
//     }
//   }, [status]);

//   return ()
//     <div>
//       {status === 'loading' && <div>Loading...</div>}
//       {status === 'error' && <div>Error loading the Google Maps script</div>}
//       <div id="map" style={{ height: '400px', width: '100%' }} />
//     </div>
//   );
// };

// export default GoogleMap;

}
}