import { useState, useEffect } from 'react'

interface ResponsiveState {
  isMobile: boolean,
  isTablet: boolean,
  isDesktop: boolean,
  width: number,
  height: number,
}

const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
} as const

export const useResponsive = (): void => {
  const [state, setState] = useState<ResponsiveState>(() => {
    if (typeof window === 'undefined') {
      return {
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        width: 1920,
        height: 1080,
      }

    const width = window.innerWidth
    const height = window.innerHeight

    return {
      isMobile: width <= BREAKPOINTS.mobile,
      isTablet: width > BREAKPOINTS.mobile && width <= BREAKPOINTS.tablet,
      isDesktop: width > BREAKPOINTS.tablet,
      width,
      height,
    }
  });

  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    const handleResize = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        const width = window.innerWidth
        const height = window.innerHeight

        setState({
          isMobile: width <= BREAKPOINTS.mobile,
          isTablet: width > BREAKPOINTS.mobile && width <= BREAKPOINTS.tablet,
          isDesktop: width > BREAKPOINTS.tablet,
          width,
          height,
        })
      }, 250) // Debounce resize events
    }

    void window.addEventListener('resize', handleResize)

    // Call once to set initial state
    handleResize()

    return () => {
      void window.removeEventListener('resize', handleResize)
      clearTimeout(timeoutId)
    }
  }, [])

  return state
}
