// This hook allows you to encapsulate the logic for handling promises within your components, managing states like loading, data, and error automatically.

import { useState, useCallback } from 'react'

type PromiseState<T> = {
  data: T | null
  error: Error | null
  isLoading: boolean
}

function usePromise<T>(promiseFunction: () => Promise<T>): [PromiseState<T>, () => void] {
  const [state, setState] = useState<PromiseState<T>>({
    data: null,
    error: null,
    isLoading: false,
  })
  // import usePromise from '@/helpers/hooks/usePromise';

  // const fetchData = (): Promise<string> => {
  //   return new Promise((resolve, reject) => {
  //     setTimeout(() => {
  //       resolve('Data loaded');
  //     }, 2000);
  //   });
  // };

  // const MyComponent: React.FC = () => {
  //   const [{ data, error, isLoading }, execute] = usePromise(fetchData);

  //   return (
  //     <div>
  //       {isLoading && <p>Loading...</p>}
  //       {error && <p>Error: {error.message}</p>}
  //       {data && <p>{data}</p>}

  //       <button onClick={execute} disabled={isLoading}>Load Data</button>
  //     </div>
  //   );
  // };

  // export default MyComponent;
}
