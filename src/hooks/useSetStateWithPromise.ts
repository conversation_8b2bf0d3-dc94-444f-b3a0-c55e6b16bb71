import { useState } from 'react'

/**
 * Custom hook to set state with a promise in React.
 * This ensures that the state is updated before proceeding with further actions.
 *
 * @returns [state, setStateWithPromise] - The current state and a function to set the state that returns a promise.
 *
 * Example usage:
 * const [currentAction, setCurrentAction] = useSetStateWithPromise<string>();
 *
 * const handleButtonClick = () => {
 *   setCurrentAction('create')
 *     .then(() => handleSubmit(onSubmit)())
 *     .catch((error) => console.error('Error:', error));
 * };
 */
// eslint-disable-next-line no-unused-vars
export function useSetStateWithPromise<T>(): [T | null, (newState: T) => Promise<T>] {
  const [state, setState] = useState<T>(null as T)

  const setStateWithPromise = (newState: T): Promise<T> => {
    return new Promise((resolve) => {
      setState(newState)
      resolve(newState)
    })
  }

  return [state, setStateWithPromise]
}
