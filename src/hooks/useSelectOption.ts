import { useState } from 'react'

// T is the type of items in the list, S is the type of the transformed selected item, and defaults to T when not explicitly provided
export const useSelectOption = <T, S = T>(
  items: T[] = [],
  defaultItem: T | null | undefined = null,
  transform?: (item: T) => S, // Optional transformation function
) => {
  const [selectedItem, setSelectedItem] = useState<S | null>(defaultItem && transform ? transform(defaultItem) : (defaultItem as S | null))
  const [itemList, setItemList] = useState<T[]>(items)

  // Function to update the selected item, with conditional transformation
  const updateSelectedItem = (item: T | null) => {
    if (item === null || item === undefined) {
      setSelectedItem(null)
    } else if (transform) {
      setSelectedItem(transform(item))
    } else {
      setSelectedItem(item as S | null)
    }

    return [selectedItem, updateSelectedItem, itemList, setItemList] as const
  }
}
