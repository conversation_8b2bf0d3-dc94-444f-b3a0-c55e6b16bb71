import { useQuery } from '@tanstack/react-query'
import { PermissionService } from '@/services/PermissionProcessService'
import { IOption } from '@/types'

export interface ISuggestersParams {
  window.location: string,
}

export interface ISuggestersTeamParams {
  suggester: string,
}

export const useGetSuggesters = (params: ISuggestersParams, isEnabled: boolean) => {
  return useQuery<IOption[], Error>({
    queryKey: ['suggesters', params],
    queryFn: () => PermissionService.getSuggesters(params.window.location),
    enabled: isEnabled && !!params.window.location,
    refetchOnWindowFocus: false,
  })
}

export const useGetSuggestersTeam = (params: ISuggestersTeamParams, isEnabled: boolean) => {
  return useQuery<string, Error>({
    queryKey: ['suggestersTeam', params],
    queryFn: () => PermissionService.getSuggestersTeam(params.suggester),
    enabled: isEnabled && !!params.suggester,
    refetchOnWindowFocus: false,
  })
}
