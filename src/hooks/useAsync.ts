// This hook extends the usePromise idea, providing more control over the asynchronous execution, such as auto-executing the promise on mount or handling dependencies.

import { useEffect, useState, useCallback } from 'react'

type AsyncState<T> = {
  data: T | null
  error: Error | null
  isLoading: boolean
}

function useAsync<T>(asyncFunction: () => Promise<T>, immediate = true): AsyncState<T> {
  const [state, setState] = useState<AsyncState<T>>({
    data: null,
    error: null,
    isLoading: false,
  })
  // import useAsync from './helpers/hooks/useAsync';

  // const fetchData = (): Promise<string> => {
  //   return new Promise((resolve, reject) => {
  //     setTimeout(() => {
  //       resolve('Data loaded');
  //     }, 2000);
  //   });
  // };

  // const MyComponent: React.FC = () => {
  //   const {data } = useAsync(fetchData);

  //   return (
  //     <div>
  //       {isLoading && <p>Loading...</p>}
  //       {error && <p>Error: {error.message}</p>}
  //       {data && <p>{data}</p>}
  //     </div>
  //   );
  // };

  // export default MyComponent;
}
