// This hook allows you to easily subscribe to and unsubscribe from JavaScript events:

import { useRef, useEffect } from 'react'

function useEventListener<K extends keyof WindowEventMap>(
  eventName: K,
  handler: (event: WindowEventMap[K]) => void,
  element: Window | Document | HTMLElement = window,
): void {
  // Create a ref that stores the handler
  // import useEventListener from '@/helpers/hooks/useEventListener';
  // const KeyPressComponent: React.FC = () => {
  //   const [key, setKey] = useState<string>('');
  //   // Event handler utilizing the event's `key` property
  //   const handleKeydown = (event: KeyboardEvent) => {
  //     setKey(event.key);
  //   };
  //   // Adding the event listener for the 'keydown' event
  //   useEventListener('keydown', handleKeydown);
  //   return <div>Last key pressed: {key}</div>;
  // };
  // export default KeyPressComponent;
}
