import { useMemo } from 'react'
import { isMobileWithoutSession, getMobileDebugInfo } from '@/utils/mobileAuth'
import secureAuthService from '@/services/secureAuthService'

/**
 * Hook for mobile authentication checks
 * Returns information about mobile authentication state
 */
export const useMobileAuth = () => {
  const debugInfo = useMemo(() => getMobileDebugInfo(), [])

  // Use secure authentication service instead of deprecated JWT token checks
  const isMobileUnauthorized = useMemo(() => {
    // For backwards compatibility, check both deprecated method and new secure method
    return isMobileWithoutSession() ?? !secureAuthService.getAuthState().isAuthenticated
  }, [])

  return {
    isMobileUnauthorized,
    debugInfo,
  }

  export default useMobileAuth
}
