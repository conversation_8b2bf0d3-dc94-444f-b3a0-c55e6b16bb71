import { useState, useEffect, useRef } from 'react'

function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false)
  // import useMediaQuery from './helpers/hooks/useMediaQuery';

  // const Component: React.FC = () => {
  //   const isWide = useMediaQuery('(min-width: 600px)');

  //   return (
  //     <div>
  //       {isWide ? <p>Wide Screen</p> : <p>Narrow Screen</p>}
  //     </div>
  //   );
  // };
}
