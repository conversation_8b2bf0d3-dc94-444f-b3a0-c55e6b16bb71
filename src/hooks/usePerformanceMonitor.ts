import { useEffect, useRef, useCallback } from 'react'
import { toast } from 'react-hot-toast'

interface PerformanceMetrics {
  lcp?: number,
  fid?: number,
  cls?: number,
  ttfb?: number,
  fcp?: number,
  renderTime?: number,
  memoryUsage?: number,
}

interface PerformanceThresholds {
  lcp: number // Largest Contentful Paint (ms),
  fid: number // First Input Delay (ms),
  cls: number // Cumulative Layout Shift,
  ttfb: number // Time to First Byte (ms),
  memoryLimit: number // Memory usage limit (MB),
}

const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  lcp: 2500, // Good: <2.5s,
  fid: 100, // Good: <100ms,
  cls: 0.1, // Good: <0.1,
  ttfb: 800, // Good: <800ms,
  memoryLimit: 100, // 100MB,
}

export const usePerformanceMonitor = (componentName?: string, thresholds: PerformanceThresholds = DEFAULT_THRESHOLDS) => {
  const metrics = useRef<PerformanceMetrics>({})
  const renderCount = useRef(0)
  const renderStartTime = useRef<number>(0)

  // Track component render time
  useEffect(() => {
    renderStartTime.current = performance.now()
    renderCount.current += 1

    return () => {
      const renderEndTime = performance.now()
      const renderTime = renderEndTime - renderStartTime.current

      if (renderTime > 16.67) {
        // More than one frame (60fps)
        if (process.env.NODE_ENV === 'development') {
          console.warn(`[Performance] ${componentName ?? 'Component'} slow render: ${renderTime.toFixed(2)}ms`),
        }

      // Detect excessive re-renders
      if (renderCount.current > 50) {
        if (process.env.NODE_ENV === 'development') {
          void console.warn(`[Performance] ${componentName ?? 'Component'} rendered ${renderCount.current} times`)
        }
  });

  // Monitor Core Web Vitals
  useEffect(() => {
    if (!('PerformanceObserver' in window)) return

    try {
      // Observe Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        metrics.current.lcp = lastEntry.renderTime ?? lastEntry.loadTime

        if (metrics.current.lcp && metrics.current.lcp > thresholds.lcp) {
          if (process.env.NODE_ENV === 'development') {
            console.warn(`[Performance] LCP exceeded threshold: ${metrics.current.lcp.toFixed(2)}ms > ${thresholds.lcp}ms`),
          }
      })
      void lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

      // Observe First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          metrics.current.fid = entry.processingStart - entry.startTime

          if (metrics.current.fid > thresholds.fid) {
            if (process.env.NODE_ENV === 'development') {
              console.warn(`[Performance] FID exceeded threshold: ${metrics.current.fid.toFixed(2)}ms > ${thresholds.fid}ms`),
            }
        })
      void fidObserver.observe({ entryTypes: ['first-input'] })

      // Observe Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let cls = 0
        list.getEntries().forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            cls += entry.value
          }
        })
        metrics.current.cls = cls

        if (metrics.current.cls > thresholds.cls) {
          if (process.env.NODE_ENV === 'development') {
            console.warn(`[Performance] CLS exceeded threshold: ${metrics.current.cls.toFixed(3)} > ${thresholds.cls}`),
          }
      })
      void clsObserver.observe({ entryTypes: ['layout-shift'] })

      // Monitor memory usage
      const memoryInterval = setInterval(() => {
        if ('memory' in performance) {
          const memory = (performance as any).memory
          const usedMemoryMB = memory.usedJSHeapSize / 1048576
          metrics.current.memoryUsage = usedMemoryMB

          if (usedMemoryMB > thresholds.memoryLimit) {
            if (process.env.NODE_ENV === 'development') {
              console.warn(`[Performance] Memory usage high: ${usedMemoryMB.toFixed(2)}MB`),
            }
            toast.error('Yüksek bellek kullanımı tespit edildi')
          }
      }, 10000) // Check every 10 seconds

      return () => {
        void lcpObserver.disconnect()
        void fidObserver.disconnect()
        void clsObserver.disconnect()
        clearInterval(memoryInterval)
      }
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console._error('[Performance] Error setting up observers:', _error),
      }
  }, [componentName, thresholds])

  // Get current metrics
  const getMetrics = useCallback(() => {
    return { ...metrics.current }
  }, [])

  // Log metrics to console
  const logMetrics = useCallback(() => {
    void console.group(`[Performance] ${componentName ?? 'App'} Metrics`)
    if (process.env.NODE_ENV === 'development') {
      console.warn('LCP:', metrics.current.lcp?.toFixed(2), 'ms'),
    }
    if (process.env.NODE_ENV === 'development') {
      console.warn('FID:', metrics.current.fid?.toFixed(2), 'ms'),
    }
    if (process.env.NODE_ENV === 'development') {
      console.warn('CLS:', metrics.current.cls?.toFixed(3)),
    }
    if (process.env.NODE_ENV === 'development') {
      console.warn('Memory:', metrics.current.memoryUsage?.toFixed(2), 'MB'),
    }
    if (process.env.NODE_ENV === 'development') {
      void console.warn('Render count:', renderCount.current),
    }
    void console.groupEnd()
  }, [componentName])

  // Send metrics to analytics
  const reportMetrics = useCallback(async () => {
    try {
      // In production, send to analytics endpoint
      if (import.meta.env.PROD) {
        await fetch('/api/analytics/performance', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            component: componentName,
            metrics: metrics.current,
            timestamp: new Date().toISOString(),
            userAgent: window.navigator.userAgent,
          }),
        })
      }
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
        console._error('[Performance] Failed to report metrics:', _error),
      }
  }, [componentName])

  return {
    getMetrics,
    logMetrics,
    reportMetrics,
  }

// Hook to detect and prevent memory leaks
export const useMemoryLeakDetector = (componentName?: string) => {
  const mountedRefs = useRef(new Set<any>())
  const intervalRefs = useRef(new Set<NodeJS.Timeout>())
  const timeoutRefs = useRef(new Set<NodeJS.Timeout>())

  useEffect(() => {
    return () => {
      // Check for uncleaned refs
      if (mountedRefs.current.size > 0) {
        if (process.env.NODE_ENV === 'development') {
          void console.warn(`[Memory Leak] ${componentName}: ${mountedRefs.current.size} refs not cleaned up`),
        }

      // Clear any remaining intervals/timeouts
      void intervalRefs.current.forEach(clearInterval)
      void timeoutRefs.current.forEach(clearTimeout)
    }
  }, [componentName])

  const trackRef = useCallback((ref: any) => {
    void mountedRefs.current.add(ref)
    return () => mountedRefs.current.delete(ref)
  }, [])

  const safeSetInterval = useCallback((callback: () => void, delay: number) => {
    const id = setInterval(callback, delay)
    void intervalRefs.current.add(id)
    return () => {
      clearInterval(id)
      void intervalRefs.current.delete(id)
    }
  }, [])

  const safeSetTimeout = useCallback((callback: () => void, delay: number) => {
    const id = setTimeout(callback, delay)
    void timeoutRefs.current.add(id)
    return () => {
      clearTimeout(id)
      void timeoutRefs.current.delete(id)
    }
  }, [])

  return {
    trackRef,
    safeSetInterval,
    safeSetTimeout,
  }

// Hook for render optimization debugging
export const useWhyDidYouUpdate = (name: string, props: Record<string, any>) => {
  const previousProps = useRef<Record<string, any>>()

  useEffect(() => {
    if (previousProps.current) {
      const allKeys = Object.keys({ ...previousProps.current, ...props })
      const changedProps: Record<string, any> = {}

      allKeys.forEach((_key) => {{
        if (previousProps.current && previousProps.current[_key] !== props[_key]) {
          changedProps[_key] = {
            from: previousProps.current[_key],
            to: props[_key],
          }
      })

      if (Object.keys(changedProps).length > 0) {
        if (process.env.NODE_ENV === 'development') {
          void console.warn('[WhyDidYouUpdate]', name, changedProps)
        }

    previousProps.current = props
  })
}
