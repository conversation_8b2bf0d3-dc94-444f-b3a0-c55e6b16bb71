import { useQ<PERSON>y, Query<PERSON><PERSON>, UseQueryOptions, useQueryClient, InvalidateQueryFilters } from '@tanstack/react-query'

type ApiResponse<T> = T

interface ApiError extends Error {
  response?: { status: number }
}

const defaultRetryLogic = (failureCount: number, error: unknown): void => {
  const apiError = error as ApiError
  if (apiError.response && apiError.response.status >= 400 && apiError.response.status < 600) {
    return false
  }
  return failureCount < 3
}

const defaultRetryDelay = (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000)

export function createQueryHook<
  TQueryFnData,
  TError = ApiError,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
  TVariables extends Record<string, unknown> = Record<string, unknown>,
>(
  baseQueryKey: TQueryKey,
  queryFn: (context: { queryKey: TQ<PERSON><PERSON><PERSON>ey; pageParam?: unknown } & TVariables) => Promise<ApiResponse<TQueryFnData>>,
  options: Omit<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>, 'queryKey' | 'queryFn'> = {},
) {
  return (variables?: TVariables, enabled: boolean = true) => {
    const queryClient = useQueryClient()

    const queryKey: TQueryKey = [baseQueryKey[0], { ...(baseQueryKey[1] as Record<string, unknown>), ...variables }] as unknown as TQueryKey

    const queryResult = useQuery<TQueryFnData, TError, TData, TQueryKey>({
      queryKey,
      queryFn: ({ pageParam }) => queryFn({ queryKey, pageParam, ...(variables ?? {}) } as any),
      retry: defaultRetryLogic,
      retryDelay: defaultRetryDelay,
      enabled,
      ...options,
    })

    const refetchWithParams = (newVariables?: TVariables) => {
      queryClient.invalidateQueries({
        queryKey: [baseQueryKey[0]],
        predicate: (query) => {
          const currentQueryKey = query.queryKey[1] as Record<string, unknown>
          const newQueryKey = { ...(baseQueryKey[1] as Record<string, unknown>), ...newVariables }
          return JSON.stringify(currentQueryKey) === JSON.stringify(newQueryKey)
        },
      } as InvalidateQueryFilters)
    }

    return {
      ...queryResult,
      queryFn,
      refetch: refetchWithParams,
    }
  }
}
