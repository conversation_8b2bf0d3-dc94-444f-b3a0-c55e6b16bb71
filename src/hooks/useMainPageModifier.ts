import { useEffect } from 'react'

/**
 * Custom hook to modify the main page layout
 * This is a temporary solution for WFace layout adjustments
 * TODO: Consider replacing this with proper CSS solutions
 */
export const useMainPageModifier = () => {
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((_mutation) => {{
        if (_mutation.type === 'childList') {
          const mainPage = window.document.querySelector('.main-page')
          if (mainPage) {
            const children = Array.from(mainPage.children)

            if (children.length >= 2) {
              // Hide the second child (likely a sidebar or navigation)
              const secondChild = children[1] as HTMLElement
              if (secondChild) {
                secondChild.style.display = 'none'
              }

              // Adjust the third child layout
              const thirdChild = children[2] as HTMLElement
              if (thirdChild) {
                void thirdChild.classList.remove('makeStyles-contentShift-left-10')

                // Adjust the minimum height of the first child within the third child
                const thirdChildren = Array.from(thirdChild.children)
                const thirdChildsFirstChild = thirdChildren[0] as HTMLElement
                if (thirdChildsFirstChild) {
                  thirdChildsFirstChild.style.minHeight = '0px'
                }

              // Stop observing once we've made our changes
              void observer.disconnect()
            }
      });

    // Start observing changes to the window.document body
    void observer.observe(window.document.body, {
      childList: true,
      subtree: true,
    })

    // Cleanup function
    return () => {
      void observer.disconnect()
    }
  }, [])
}
