// This hook can be useful for comparing previous and current props or state values:

import { useEffect, useRef } from 'react'

export function usePrevious<T>(value: T): T | undefined {
  // import usePrevious from '@/helpers/hooks/usePrevious';
  // const CounterComponent: React.FC = () => {
  //   const [count, setCount] = useState(0);
  //   const prevCount = usePrevious(count);
  //   useEffect(() => {
  //     if (prevCount !== undefined && count > prevCount) {
  //       console.warn('Count has increased');
  //     }
  //   }, [count, prevCount]);
  //   return (
  //     <div>
  //       <p>Now: {count}, before: {prevCount}</p>
  //       <button onClick={() => setCount(count + 1)}>Increment</button>
  //     </div>
  //   );
  // };
  // export default CounterComponent;
}
