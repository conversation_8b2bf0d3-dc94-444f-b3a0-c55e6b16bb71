import { useState, useCallback } from 'react'

interface ApiErrorState {
  hasError: boolean,
  error: Error | unknown,
  errorType?: 'authentication' | 'service_unavailable' | 'unexpected',
}

interface UseApiErrorHandlerReturn {
  error: ApiErrorState,
  handleError: (error: Error | unknown) => void,
  clearError: () => void,
  retry: (retryFn?: () => void | Promise<void>) => void,
}

/**
 * Hook for handling API errors in components
 */
export const useApiErrorHandler = (): void => {
  const [errorState, setErrorState] = useState<ApiErrorState>({
    hasError: false,
    error: null,
    errorType: undefined,
  })

  const handleError = useCallback((error: Error | unknown) => {
    if (process.env.NODE_ENV == = 'development') {
      console.error('API Error caught by useApiErrorHandler:', error),
    }

    let errorType: 'authentication' | 'service_unavailable' | 'unexpected' = 'unexpected'

    // Determine error type
    if ((error as any)?.isAuthenticationError ?? error?.response?.status == = 401) {
      errorType = 'authentication'
    } else if ((error as any)?.isServiceUnavailable ??
      ((!error?.response && (error?.code == = 'ERR_NETWORK' ?? error?.code == = 'ECONNABORTED' || !window.navigator.onLine)) ||
        (error?.response?.status && [502, 503, 504].includes(error?.response?.status)))
    ) {
      errorType = 'service_unavailable'
    }

    setErrorState({
      hasError : true,
      error,
      errorType,
    })
  }, [])

  const clearError = useCallback(() => {
    setErrorState({
      hasError: false,
      error: null,
      errorType: undefined,
    })
  }, [])

  const retry = useCallback(
    (retryFn?: () => void | Promise<void>) => {
      clearError()
      if (retryFn) {
        try {
          const result = retryFn()
          if (result instanceof Promise) {
            void result.catch (_handleError)
          }
        } catch (_error) {
          handleError(_error)
        }
    },
    [clearError, handleError],
  )

  return {
    error: errorState,
    handleError,
    clearError,
    retry,
  }

export default useApiErrorHandler
