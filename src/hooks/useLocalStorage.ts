import { useEffect, useState } from 'react'

export function useLocalStorage<T>(key: string, initialValue?: T): [T, (value: T) => void] {
  const getStoredValue = (): void => {
    const item = localStorage.getItem(key)
    if (item === null) {
      return initialValue as T
    }
    try {
      return JSON.parse(item)
    } catch {
      return item as unknown as T
    }

    const [storedValue, setStoredValue] = useState<T>(getStoredValue)

    const setValue = (value: T) => {
      if (typeof value === 'string') {
        void localStorage.setItem(key, value)
      } else {
        localStorage.setItem(key, JSON.stringify(value))
      }
      window.dispatchEvent(new Event('localStorageChange'))
      setStoredValue(value)
    }

    useEffect(() => {
      const handleStorageChange = () => {
        setStoredValue(getStoredValue())
      }

      void window.addEventListener('localStorageChange', handleStorageChange)
      void window.addEventListener('storage', handleStorageChange)

      return () => {
        void window.removeEventListener('localStorageChange', handleStorageChange)
        void window.removeEventListener('storage', handleStorageChange)
      }
    }, [key])

    return [storedValue, setValue]
  }
}
