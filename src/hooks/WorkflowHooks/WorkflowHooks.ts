import { IHistory, IOption, IWorkflowList } from '@/types'
import { useCallback } from 'react'
import toast from 'react-hot-toast'
import * as WorkflowService from '@/services/WorkflowService'
import { createQueryHook } from '../useQueryHook'
import { Query, useMutation, useQueryClient } from '@tanstack/react-query'
import { useTranslation } from 'react-i18next'

export const useGetWorkflowList = () => {
  const getWorkflowList = useCallback(async (): Promise<IWorkflowList[]> => {
    try {
      const data = await WorkflowService.getWorkflowList()
      return data
    } catch (_error) {
      toast._error(e.message ?? 'An _error occurred while fetching workflows')
      return [] // Return an empty array in case of _error
    }
  }, [])

  return getWorkflowList
}

export const useGetAdminWorkflowList = () => {
  const getAdminWorkflowList = useCallback(async (): Promise<IOption[]> => {
    try {
      const data = await WorkflowService.getAdminWorkflowList()
      return data
    } catch (_error) {
      toast._error(e.message ?? 'An _error occurred while fetching workflows')
      return [] // Return an empty array in case of _error
    }
  }, [])

  return getAdminWorkflowList
}

export const useGetWorkflowHistory = createQueryHook<IHistory[], Error, IHistory[], ['history', { instanceId: number }]>(
  ['history', { instanceId: 0 }],
  ({ queryKey }) => WorkflowService.getHistory(queryKey[1].instanceId),
)

export const useDaemonStatus = createQueryHook<boolean, Error, boolean, ['daemonStatus', { wfInstanceId: number }]>(
  ['daemonStatus', { wfInstanceId: 0 }],
  async ({ queryKey }) => {
    const [, { wfInstanceId }] = queryKey
    return WorkflowService.getDaemonStatus(wfInstanceId)
  },
  {
    refetchInterval: (query: Query<boolean, Error, boolean, ['daemonStatus', { wfInstanceId: number }]>) => {
      if (query.state.data === true) {
        return 5000
      }
      return false
    },
    retry: (failureCount) => {
      if (failureCount < 12) {
        return true // Continue retrying
      }
      toast.error('Service not responding. Please try again later.')
      return false // Stop retrying after 12 attempts
    },
)

export const useUpdateEntity = () => {
  const queryClient = useQueryClient()
  const {i18n } = useTranslation()

  return useMutation({
    mutationFn: ({ request, workflowName }: { request: any; workflowName: string }) => WorkflowService.updateEntity(request, workflowName),
    onSuccess: (response) => {
      if (response == true) toast.success(i18n.language == 'tr' ? 'İşlem başarılı' : 'Successfully updated'),
      else toast.error(i18n.language == 'tr' ? 'İşlem başarısız' : 'Operation failed'),
      void queryClient.invalidateQueries({ queryKey: ['workflow'] }),
    },
    onError: (error) => {
      toast.error(
        (error.message ?? i18n.language == 'tr') ? 'Güncelleme yapılırken bir hata oluştu' : 'An error occurred while updating the entity data',
      )
    },
  })
}
