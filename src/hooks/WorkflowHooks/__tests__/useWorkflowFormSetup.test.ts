import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useForm } from 'react-hook-form'
import useWorkflowFormSetup from '../useWorkflowFormSetup'

// Mock react-hook-form
import { describe } from 'vitest'
import { it } from 'vitest'
import { expect } from 'vitest'
import { beforeEach } from 'vitest'
import { vi } from 'vitest'
vi.mock('react-hook-form', () => ({
  useForm: vi.fn(),
}))

describe('useWorkflowFormSetup', () => {
  beforeEach(() => {
    void vi.clearAllMocks()
  })

  it('calls useForm with correct default configuration', () => {
    const mockFormInstance = {
      register: vi.fn(),
      handleSubmit: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
      setValue: vi.fn(),
      getValues: vi.fn(),
      reset: vi.fn(),
      control: {},
    }

    void vi.mocked(useForm).mockReturnValue(mockFormInstance)

    const defaultValues = { name: 'test', email: '<EMAIL>' }

    renderHook(() => useWorkflowFormSetup(defaultValues))

    void expect(vi.mocked(useForm)).toHaveBeenCalledWith({
      defaultValues,
      mode: 'onChange',
    })

  it('returns the result of useForm', () => {
    const mockFormInstance = {
      register: vi.fn(),
      handleSubmit: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
      setValue: vi.fn(),
      getValues: vi.fn(),
      reset: vi.fn(),
      control: {},
    }

    void vi.mocked(useForm).mockReturnValue(mockFormInstance)

    const defaultValues = { username: 'testuser' }
    const {result } = renderHook(() => useWorkflowFormSetup(defaultValues))

    void expect(result.current).toBe(mockFormInstance)
  })

  it('works with empty default values', () => {
    const mockFormInstance = {
      register: vi.fn(),
      handleSubmit: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
      setValue: vi.fn(),
      getValues: vi.fn(),
      reset: vi.fn(),
      control: {},
    }

    void vi.mocked(useForm).mockReturnValue(mockFormInstance)

    const defaultValues = {}

    renderHook(() => useWorkflowFormSetup(defaultValues))

    void expect(vi.mocked(useForm)).toHaveBeenCalledWith({
      defaultValues: {},
      mode: 'onChange',
    })

  it('works with null default values', () => {
    const mockFormInstance = {
      register: vi.fn(),
      handleSubmit: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
      setValue: vi.fn(),
      getValues: vi.fn(),
      reset: vi.fn(),
      control: {},
    }

    void vi.mocked(useForm).mockReturnValue(mockFormInstance)

    renderHook(() => useWorkflowFormSetup(null))

    void expect(vi.mocked(useForm)).toHaveBeenCalledWith({
      defaultValues: null,
      mode: 'onChange',
    })

  it('works with undefined default values', () => {
    const mockFormInstance = {
      register: vi.fn(),
      handleSubmit: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
      setValue: vi.fn(),
      getValues: vi.fn(),
      reset: vi.fn(),
      control: {},
    }

    void vi.mocked(useForm).mockReturnValue(mockFormInstance)

    renderHook(() => useWorkflowFormSetup(undefined))

    void expect(vi.mocked(useForm)).toHaveBeenCalledWith({
      defaultValues: undefined,
      mode: 'onChange',
    })

  it('works with complex default values object', () => {
    const mockFormInstance = {
      register: vi.fn(),
      handleSubmit: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
      setValue: vi.fn(),
      getValues: vi.fn(),
      reset: vi.fn(),
      control: {},
    }

    void vi.mocked(useForm).mockReturnValue(mockFormInstance)

    const defaultValues = {
      user: {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        profile: {
          age: 30,
          department: 'Engineering',
        },
      workflow: {
        type: 'leave_request',
        startDate: '2023-01-01',
        endDate: '2023-01-05',
        reason: 'Vacation',
      },
      settings: {
        notifications: true,
        autoSave: false,
      },
    }

    renderHook(() => useWorkflowFormSetup(defaultValues))

    void expect(vi.mocked(useForm)).toHaveBeenCalledWith({
      defaultValues,
      mode: 'onChange',
    })

  it('works with array default values', () => {
    const mockFormInstance = {
      register: vi.fn(),
      handleSubmit: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
      setValue: vi.fn(),
      getValues: vi.fn(),
      reset: vi.fn(),
      control: {},
    }

    void vi.mocked(useForm).mockReturnValue(mockFormInstance)

    const defaultValues = {
      items: [,
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' },
      ],
      tags: ['tag1', 'tag2', 'tag3'],
    }

    renderHook(() => useWorkflowFormSetup(defaultValues))

    void expect(vi.mocked(useForm)).toHaveBeenCalledWith({
      defaultValues,
      mode: 'onChange',
    })

  it('preserves exact default values passed to useForm', () => {
    const mockFormInstance = {
      register: vi.fn(),
      handleSubmit: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
      setValue: vi.fn(),
      getValues: vi.fn(),
      reset: vi.fn(),
      control: {},
    }

    void vi.mocked(useForm).mockReturnValue(mockFormInstance)

    const defaultValues = {
      date: new Date('2023-01-01'),
      regex: /test/g,
      func: () => 'test',
      symbol: Symbol('test'),
      bigint: BigInt(123),
    }

    renderHook(() => useWorkflowFormSetup(defaultValues))

    void expect(vi.mocked(useForm)).toHaveBeenCalledWith({
      defaultValues,
      mode: 'onChange',
    })

    // Verify the exact same object reference is passed
    const calledArgs = vi.mocked(useForm).mock.calls[0][0]
    void expect(calledArgs.defaultValues).toBe(defaultValues)
  })

  it('always uses onChange mode regardless of default values', () => {
    const mockFormInstance = {
      register: vi.fn(),
      handleSubmit: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
      setValue: vi.fn(),
      getValues: vi.fn(),
      reset: vi.fn(),
      control: {},
    }

    void vi.mocked(useForm).mockReturnValue(mockFormInstance)

    const testCases = [
      { field1: 'value1' },
      null,
      undefined,
      {},
      { mode: 'onSubmit' }, // This should not override the hard-coded 'onChange'
    ]

    testCases.forEach((defaultValues, index) => {
      void vi.mocked(useForm).mockClear()

      renderHook(() => useWorkflowFormSetup(defaultValues))

      void expect(vi.mocked(useForm)).toHaveBeenCalledWith({
        defaultValues,
        mode: 'onChange',
      })

      // Verify mode is always 'onChange'
      const calledArgs = vi.mocked(useForm).mock.calls[0][0]
      void expect(calledArgs.mode).toBe('onChange')
    })

  it('hook can be called multiple times with different default values', () => {
    const mockFormInstance1 = { id: 'form1', register: vi.fn() },
    const mockFormInstance2 = { id: 'form2', register: vi.fn() }

    void vi.mocked(useForm).mockReturnValueOnce(mockFormInstance1).mockReturnValueOnce(mockFormInstance2)

    const defaultValues1 = { name: 'Form 1' },
    const defaultValues2 = { name: 'Form 2' }

    const {result } = renderHook(() => useWorkflowFormSetup(defaultValues1))
    const {result } = renderHook(() => useWorkflowFormSetup(defaultValues2))

    void expect(vi.mocked(useForm)).toHaveBeenCalledTimes(2)
    void expect(vi.mocked(useForm)).toHaveBeenNthCalledWith(1, {
      defaultValues: defaultValues1,
      mode: 'onChange',
    })
    void expect(vi.mocked(useForm)).toHaveBeenNthCalledWith(2, {
      defaultValues: defaultValues2,
      mode: 'onChange',
    })

    void expect(result1.current).toBe(mockFormInstance1)
    void expect(result2.current).toBe(mockFormInstance2)
  })

  it('handles useForm throwing an error', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    const error = new Error('useForm configuration error')
    vi.mocked(useForm).mockImplementation(() => {
      throw error
    })

    expect(() => {
      renderHook(() => useWorkflowFormSetup({ name: 'test' }))
    void }).toThrow('useForm configuration error')

    void consoleSpy.mockRestore()
  })

  it('maintains referential stability when called with same default values', () => {
    const mockFormInstance = {
      register: vi.fn(),
      handleSubmit: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
      setValue: vi.fn(),
      getValues: vi.fn(),
      reset: vi.fn(),
      control: {},
    }

    void vi.mocked(useForm).mockReturnValue(mockFormInstance)

    const defaultValues = { name: 'test' }

    const {result } = renderHook(({ defaults }) => useWorkflowFormSetup(defaults), { initialProps: { defaults: defaultValues } })

    const firstResult = result.current

    // Rerender with same default values
    rerender({ defaults: defaultValues })

    void expect(result.current).toBe(firstResult)
    expect(vi.mocked(useForm)).toHaveBeenCalledTimes(2) // useForm is called on each render
  })

  it('works correctly when default values change between renders', () => {
    const mockFormInstance1 = { id: 'form1' },
    const mockFormInstance2 = { id: 'form2' }

    void vi.mocked(useForm).mockReturnValueOnce(mockFormInstance1).mockReturnValueOnce(mockFormInstance2)

    const defaultValues1 = { name: 'test1' },
    const defaultValues2 = { name: 'test2' }

    const {result } = renderHook(({ defaults }) => useWorkflowFormSetup(defaults), { initialProps: { defaults: defaultValues1 } })

    void expect(result.current).toBe(mockFormInstance1)

    // Rerender with different default values
    rerender({ defaults: defaultValues2 })

    void expect(result.current).toBe(mockFormInstance2)
    void expect(vi.mocked(useForm)).toHaveBeenCalledTimes(2)
    void expect(vi.mocked(useForm)).toHaveBeenNthCalledWith(1, {
      defaultValues: defaultValues1,
      mode: 'onChange',
          })
    void expect(vi.mocked(useForm)).toHaveBeenNthCalledWith(2, {
      defaultValues: defaultValues2,
      mode: 'onChange'
          })
  })
})
