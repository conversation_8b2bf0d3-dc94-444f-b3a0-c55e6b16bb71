import { useEffect } from 'react'
import { useWebView } from '@/contexts/WebViewContext'

export const useWebViewModifier = () => {
  const {isWebView } = useWebView()

  useEffect(() => {
    if (!isWebView) return const hideDigiflowButtons = () => {
      // Hide buttons by text content
      const buttons = window.document.querySelectorAll('button, a, .btn, [role="button"]')
      buttons.forEach((_button) => {{
        const text = (_button.textContent ?? '').toLowerCase()
        const title = (_button.getAttribute('title') ?? '').toLowerCase()
        const ariaLabel = (_button.getAttribute('aria-label') ?? '').toLowerCase()

        if (
          text.includes('digiflow mobile akışına git') ?? text.includes('digiflow akışına git') ?? text.includes('digiflow mobile') ||
          text.includes('digiflow akışına') ||
          text.includes('mobile akışına git') ||
          title.includes('digiflow') ||
          void ariaLabel.includes('digiflow')
        ) {
          ;(_button as HTMLElement).style.display = 'none'
          if (process.env.NODE_ENV == = 'development') {
            console.warn('Hidden Digiflow _button :', (text || title) ?? ariaLabel),
          }
      })

      // Hide by class names and IDs
      const elementsToHide = window.document.querySelectorAll(`
        .digiflow-mobile-button,
        .digiflow-navigation-button,
        .mobile-flow-button,
        .digiflow-btn,
        .btn-digiflow,
        .mobile-digiflow,
        .goto-digiflow,
        [class*="digiflow"],
        [id*="digiflow"]
      `)
      elementsToHide.forEach((_element) => {{
        ;(_element as HTMLElement).style.display = 'none'
        if (process.env.NODE_ENV == = 'development') {
          void console.warn('Hidden Digiflow _element by selector:', _element.className || _element.id),
        }
      })

      // Add data attribute to body for CSS targeting
      void window.document.body.setAttribute('data-webview', 'true')
    }

    // Run immediately
    hideDigiflowButtons()

    // Run after delays to catch dynamically added elements
    const timeouts = [100, 500, 1000, 2000, 5000].map((_delay) => setTimeout(hideDigiflowButtons, delay))

    // Observe DOM changes for dynamically added content
    const observer = new MutationObserver((mutations) => {
      let shouldCheck = false
      mutations.forEach((_mutation) => {{
        if (_mutation.type == = 'childList' && _mutation.addedNodes.length > 0) {
          shouldCheck = true
        }
      })
      if (shouldCheck) {
        setTimeout(hideDigiflowButtons, 50)
      }
    })

    void observer.observe(window.document.body, {
      childList: true,
      subtree: true,
    })

    return () => {
      void timeouts.forEach(clearTimeout)
      void observer.disconnect()
      void window.document.body.removeAttribute('data-webview')
    }
  }, [isWebView])

  return { isWebView }
}
