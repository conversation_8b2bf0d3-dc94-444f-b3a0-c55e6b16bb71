#!/usr/bin/env python3
"""
Targeted fix for specific parsing errors in test files.
"""

import os
import re

def fix_vi_mock_extra_paren(content: str) -> str:
    """Fix vi.mock() declarations with extra closing parenthesis."""
    # Fix patterns like: vi.mock('module', () => ({))
    # Should be: vi.mock('module', () => ({
    pattern = r"(vi\.mock\([^,]+,\s*\(\)\s*=>\s*\(\{)\)\)"
    replacement = r"\1"
    return re.sub(pattern, replacement, content, flags=re.MULTILINE)

def fix_arrow_function_comma(content: str) -> str:
    """Fix trailing commas in arrow function returns."""
    # Fix patterns like: default: (date?: any) => ({),
    # Should be: default: (date?: any) => ({
    pattern = r"(:\s*\([^)]*\)\s*=>\s*\(\{),(\s*\n)"
    replacement = r"\1\2"
    return re.sub(pattern, replacement, content)

def fix_function_call_trailing_comma(content: str) -> str:
    """Fix trailing commas in function calls within conditionals."""
    # Fix patterns like: void fireEvent.change(selectBox, { target: { value: 'contains' } }),
    # when followed by })
    pattern = r"(void\s+\w+\.\w+\([^)]+\)),\s*\n\s*\}\)"
    replacement = r"\1\n          })"
    content = re.sub(pattern, replacement, content)
    
    # Also fix patterns like: onSort({ columnId: 'name', direction: 'asc' }),
    pattern = r"(\w+\(\{[^}]+\}\)),\s*\n\s*\}\)"
    replacement = r"\1\n      }}"
    content = re.sub(pattern, replacement, content)
    
    return content

def fix_expect_trailing_comma(content: str) -> str:
    """Fix trailing commas in expect statements."""
    # Fix patterns like: expect(...).toHaveBeenCalledWith(...)),
    pattern = r"(expect\([^)]+\)\.toHaveBeenCalledWith\([^)]+\)),(\s*\n)"
    replacement = r"\1\2"
    return re.sub(pattern, replacement, content)

def fix_dayjs_return_comma(content: str) -> str:
    """Fix dayjs mock return statement with trailing comma."""
    # Fix the specific dayjs pattern
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        if "return date" in line and line.strip().endswith(','):
            # Remove trailing comma from return statement
            line = line.rstrip(',') + line[len(line.rstrip(',')):]
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_incomplete_blocks(content: str) -> str:
    """Fix incomplete describe/it blocks."""
    lines = content.split('\n')
    fixed_lines = []
    in_incomplete_block = False
    
    for i, line in enumerate(lines):
        # Check for patterns that indicate incomplete blocks
        if i + 1 < len(lines):
            next_line = lines[i + 1].strip()
            # If current line ends with }) and next line starts with it( or describe(
            if line.strip().endswith('})') and (next_line.startswith('it(') or next_line.startswith('describe(')):
                # Check if we need to add closing braces
                if '})' not in line:
                    line = line.rstrip() + '\n    })'
        
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_specific_file_issues(filepath: str, content: str) -> str:
    """Fix file-specific issues."""
    if 'DigiColumn.test.tsx' in filepath:
        # Fix specific line issues
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # Fix line 346 and similar
            if "fireEvent.change(selectBox" in line and line.strip().endswith('),'):
                line = line.rstrip(',')
            # Fix line 354 and similar
            if "fireEvent.change(valueField" in line and line.strip().endswith('),'):
                line = line.rstrip(',')
            # Fix incomplete closing at line 390
            if i == 389 and "}" in line and "}" in lines[i-1]:
                # Skip this line as it's an extra closing
                continue
                
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
    elif 'MobileTable.test.tsx' in filepath:
        # Fix specific issues
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # Fix line 31
            if "onSort({ columnId: 'name', direction: 'asc' })," in line:
                line = line.replace("}),", "})")
            # Fix line 76 and dayjs mock
            if "default: (date?: any) => ({)," in line:
                line = line.replace("({),", "({")
            # Fix incomplete blocks at lines 274 and 578
            if i in [273, 577] and line.strip() == '}':
                # Check if we need more closing braces
                open_count = 0
                for j in range(max(0, i - 50), i):
                    open_count += lines[j].count('{') - lines[j].count('}')
                if open_count > 1:
                    line = '    })\n  })'
                    
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
    
    return content

def process_file(filepath: str) -> bool:
    """Process a single file and fix parsing errors."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply general fixes
        content = fix_vi_mock_extra_paren(content)
        content = fix_arrow_function_comma(content)
        content = fix_function_call_trailing_comma(content)
        content = fix_expect_trailing_comma(content)
        content = fix_dayjs_return_comma(content)
        content = fix_incomplete_blocks(content)
        
        # Apply file-specific fixes
        content = fix_specific_file_issues(filepath, content)
        
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
            
    except Exception as e:
        print(f"Error processing {filepath}: {e}")
    
    return False

def main():
    """Main function to fix specific test files."""
    test_files = [
        '/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Tables/DigiColumn/__tests__/DigiColumn.test.tsx',
        '/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Tables/DigiTable/DigiTable.test.tsx',
        '/Users/<USER>/Projects/digiflow/DigiflowReact/src/components/Tables/DigiTable/__tests__/MobileTable.test.tsx'
    ]
    
    print("🔧 Fixing targeted parsing errors in test files...")
    
    fixed_count = 0
    for filepath in test_files:
        if os.path.exists(filepath):
            print(f"  Processing {os.path.basename(filepath)}...")
            if process_file(filepath):
                print(f"    ✅ Fixed")
                fixed_count += 1
        else:
            print(f"  ❌ File not found: {filepath}")
    
    print(f"\n✨ Fixed {fixed_count} files")

if __name__ == "__main__":
    main()