// IE Polyfills and Compatibility Fixes

// Detect IE version
var isIE = /*@cc_on!@*/false || !!document.documentMode;
var isEdge = !isIE && !!window.StyleMedia;

// String.includes polyfill for IE
if (!String.prototype.includes) {
    String.prototype.includes = function(search, start) {
        'use strict';
        if (typeof start !== 'number') {
            start = 0;
        }
        if (start + search.length > this.length) {
            return false;
        } else {
            return this.indexOf(search, start) !== -1;
        }
    };
}

// Array.includes polyfill for IE
if (!Array.prototype.includes) {
    Array.prototype.includes = function(searchElement, fromIndex) {
        'use strict';
        var O = Object(this);
        var len = parseInt(O.length) || 0;
        if (len === 0) {
            return false;
        }
        var n = parseInt(fromIndex) || 0;
        var k;
        if (n >= 0) {
            k = n;
        } else {
            k = len + n;
            if (k < 0) {
                k = 0;
            }
        }
        while (k < len) {
            if (searchElement === O[k]) {
                return true;
            }
            k++;
        }
        return false;
    };
}

// Object.assign polyfill for IE
if (typeof Object.assign !== 'function') {
    Object.assign = function(target) {
        'use strict';
        if (target === null || target === undefined) {
            throw new TypeError('Cannot convert undefined or null to object');
        }
        var to = Object(target);
        for (var index = 1; index < arguments.length; index++) {
            var nextSource = arguments[index];
            if (nextSource !== null && nextSource !== undefined) {
                for (var nextKey in nextSource) {
                    if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                        to[nextKey] = nextSource[nextKey];
                    }
                }
            }
        }
        return to;
    };
}

// Fix for transform in IE9
if (isIE && document.documentMode < 10) {
    $(document).ready(function() {
        // Add IE9 class for specific CSS handling
        $('html').addClass('ie9');
    });
}

// Fix for CSS transitions in IE
if (isIE) {
    $(document).ready(function() {
        // Force repaint for transitions
        $('.slider, .slide').each(function() {
            this.style.zoom = '1';
        });
    });
}

// Swipe event polyfill for IE (if jQuery Mobile not available)
if (isIE && !$.mobile) {
    (function($) {
        var touchStartX = 0;
        var touchStartY = 0;
        var touchEndX = 0;
        var touchEndY = 0;
        
        $(document).on('mousedown touchstart', '.slider-container', function(e) {
            var touch = e.originalEvent.touches ? e.originalEvent.touches[0] : e;
            touchStartX = touch.pageX;
            touchStartY = touch.pageY;
        });
        
        $(document).on('mouseup touchend', '.slider-container', function(e) {
            var touch = e.originalEvent.changedTouches ? e.originalEvent.changedTouches[0] : e;
            touchEndX = touch.pageX;
            touchEndY = touch.pageY;
            
            var deltaX = touchEndX - touchStartX;
            var deltaY = touchEndY - touchStartY;
            
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                    $(this).trigger('swiperight');
                } else {
                    $(this).trigger('swipeleft');
                }
            }
        });
    })(jQuery);
}

// RequestAnimationFrame polyfill for IE9
(function() {
    var lastTime = 0;
    var vendors = ['ms', 'moz', 'webkit', 'o'];
    for(var x = 0; x < vendors.length && !window.requestAnimationFrame; ++x) {
        window.requestAnimationFrame = window[vendors[x]+'RequestAnimationFrame'];
        window.cancelAnimationFrame = window[vendors[x]+'CancelAnimationFrame'] || 
                                      window[vendors[x]+'CancelRequestAnimationFrame'];
    }

    if (!window.requestAnimationFrame) {
        window.requestAnimationFrame = function(callback) {
            var currTime = new Date().getTime();
            var timeToCall = Math.max(0, 16 - (currTime - lastTime));
            var id = window.setTimeout(function() { 
                callback(currTime + timeToCall); 
            }, timeToCall);
            lastTime = currTime + timeToCall;
            return id;
        };
    }

    if (!window.cancelAnimationFrame) {
        window.cancelAnimationFrame = function(id) {
            clearTimeout(id);
        };
    }
}());

// Console polyfill for IE8
if (!window.console) {
    window.console = {
        log: function() {},
        error: function() {},
        warn: function() {},
        info: function() {}
    };
}