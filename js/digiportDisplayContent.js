﻿
$(document).ready(function () {
    // Tüm query string parametrelerini al
    const domain = 'http://digiflowtest/';
    const params = new URLSearchParams(window.location.search);
    const contentId = params.get("content-id");
    var componentType = params.get("component-type");
    var componentTitle = params.get("component-title");
    var componentTypeString = params.get("component-type-string");
    const pageSize = params.get("page-size");
    const pageSizeGrid = params.get("page-size-grid");
    if (componentType == null || componentType == undefined)
        componentType = '';
    if (componentTitle == null || componentTitle == undefined)
        componentTitle = '';
    if (componentTypeString == null || componentTypeString == undefined)
        componentTypeString = '';
    var iframeSrc = '';

    // Eğer her iki parametre de varsa, iframe src’sini güncelle
    if (contentId && componentType) {
        iframeSrc = domain + 'Digiport/DisplayContent.aspx?content-id=' + contentId + '&component-type=' + componentType;
    } else if (componentTypeString == 'DiscountOpportunity') {
        iframeSrc = domain + 'Digiport/IndirimFirsati.aspx?view-mode=all&page-size1=' + pageSize + '&component-type=' + componentType + '&component-type-string=' + componentTypeString + '&component-title=' + componentTitle;
    }
    else if (componentTypeString == 'Links') {
        iframeSrc = domain + 'Digiport/Linkler.aspx?view-mode=grid&page-size-grid=' + pageSizeGrid + '&component-type=' + componentType + '&component-type-string=' + componentTypeString + '&component-title=' + componentTitle;
    }
    $('#iframe1').css('height', parseFloat($('#MiddleMainDV').height()) - 15);
    $("#iframe1").attr("src", iframeSrc);
});
