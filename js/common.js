﻿var popupWindow = null;
function openNewWindow(popUpWindowWidth, popUpWindowHeight, targetLink) {
    if (popupWindow && !popupWindow.closed) {
        popupWindow.close();
    }
    popupWindow = window.open(targetLink, 'popup', 'width=' + popUpWindowWidth + ',height=' + popUpWindowHeight);
}
function openNewTab(targetLink) {
    var link = $('<a>', {
        href: targetLink,
        target: '_blank'
    });
    $('body').append(link);
    link[0].click();
    link.remove();
}