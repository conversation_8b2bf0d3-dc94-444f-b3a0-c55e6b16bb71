#!/usr/bin/env python3
"""
Comprehensive script to fix all parsing errors in the codebase.
This script analyzes common patterns and fixes them systematically.
"""

import os
import re
import subprocess
from typing import List, Tuple, Dict

def get_parsing_errors() -> List[Tuple[str, int, str]]:
    """Get all parsing errors from ESLint."""
    cmd = ["yarn", "lint"]
    result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.path.dirname(os.path.abspath(__file__)))
    
    errors = []
    all_output = result.stdout + '\n' + result.stderr
    
    current_file = None
    for line in all_output.split('\n'):
        # Check if this is a file path line
        if line.strip() and '/src/' in line and (line.endswith('.ts') or line.endswith('.tsx')):
            current_file = line.strip()
        # Check if this is an error line
        elif current_file and 'error' in line and 'Parsing error' in line:
            # Extract line number and error message
            match = re.search(r'(\d+):\d+\s+error\s+Parsing error:\s*(.+)', line)
            if match:
                line_num = int(match.group(1))
                error_msg = match.group(2).strip()
                errors.append((current_file, line_num, error_msg))
    
    return errors

def count_brackets(content: str) -> Dict[str, int]:
    """Count opening and closing brackets."""
    return {
        'open_curly': content.count('{'),
        'close_curly': content.count('}'),
        'open_paren': content.count('('),
        'close_paren': content.count(')'),
        'open_square': content.count('['),
        'close_square': content.count(']'),
        'open_angle': len(re.findall(r'<(?![/=])', content)),
        'close_angle': len(re.findall(r'(?<![=/])>', content))
    }

def fix_missing_closing_brackets(filepath: str, content: str) -> str:
    """Fix missing closing brackets based on analysis."""
    lines = content.split('\n')
    counts = count_brackets(content)
    
    # Fix missing closing curly braces
    if counts['open_curly'] > counts['close_curly']:
        diff = counts['open_curly'] - counts['close_curly']
        # Add missing closing braces at the end
        for _ in range(diff):
            lines.append('}')
    
    # Fix missing closing parentheses
    if counts['open_paren'] > counts['close_paren']:
        # Try to find lines with unclosed parentheses
        for i, line in enumerate(lines):
            open_count = line.count('(')
            close_count = line.count(')')
            if open_count > close_count:
                # Add closing parentheses at the end of the line
                lines[i] = line.rstrip() + ')' * (open_count - close_count)
    
    return '\n'.join(lines)

def fix_jsx_closing_tags(filepath: str, content: str) -> str:
    """Fix missing JSX closing tags."""
    if not filepath.endswith('.tsx'):
        return content
    
    lines = content.split('\n')
    
    # Track open JSX tags
    tag_stack = []
    modified = False
    
    for i, line in enumerate(lines):
        # Find opening tags
        opening_tags = re.findall(r'<(\w+)(?:\s[^>]*)?>(?!.*</\1>)', line)
        for tag in opening_tags:
            if tag.lower() not in ['br', 'hr', 'img', 'input', 'meta', 'link']:
                tag_stack.append((tag, i))
        
        # Find closing tags
        closing_tags = re.findall(r'</(\w+)>', line)
        for tag in closing_tags:
            if tag_stack and tag_stack[-1][0] == tag:
                tag_stack.pop()
    
    # Add missing closing tags
    if tag_stack:
        # Add closing tags in reverse order
        for tag, line_num in reversed(tag_stack):
            # Find appropriate place to add closing tag
            insert_line = len(lines) - 1
            for j in range(len(lines) - 1, line_num, -1):
                if lines[j].strip() and not lines[j].strip().startswith('//'):
                    insert_line = j + 1
                    break
            
            # Insert closing tag
            indent = len(lines[line_num]) - len(lines[line_num].lstrip())
            lines.insert(insert_line, ' ' * indent + f'</{tag}>')
            modified = True
    
    return '\n'.join(lines) if modified else content

def fix_unexpected_tokens(filepath: str, content: str, error_msg: str) -> str:
    """Fix unexpected token errors."""
    lines = content.split('\n')
    
    # Fix common patterns
    if "Did you mean `{'}'}` or `&rbrace;`?" in error_msg:
        # Replace standalone } in JSX context
        for i, line in enumerate(lines):
            if '}' in line and filepath.endswith('.tsx'):
                # Check if it's in JSX context
                if re.search(r'>\s*}\s*<', line):
                    lines[i] = re.sub(r'>\s*}\s*<', '>{"}"}<', line)
    
    return '\n'.join(lines)

def fix_expression_expected(filepath: str, content: str, line_num: int) -> str:
    """Fix 'Expression expected' errors."""
    lines = content.split('\n')
    if 0 <= line_num - 1 < len(lines):
        line = lines[line_num - 1]
        
        # Fix empty expressions
        line = re.sub(r'\(\s*\)', '()', line)
        line = re.sub(r'\{\s*\}', '{}', line)
        line = re.sub(r'\[\s*\]', '[]', line)
        
        # Fix trailing commas in wrong places
        line = re.sub(r',\s*\)', ')', line)
        line = re.sub(r',\s*\]', ']', line)
        line = re.sub(r',\s*\}', '}', line)
        
        lines[line_num - 1] = line
    
    return '\n'.join(lines)

def fix_declaration_expected(filepath: str, content: str, line_num: int) -> str:
    """Fix 'Declaration or statement expected' errors."""
    lines = content.split('\n')
    if 0 <= line_num - 1 < len(lines):
        line = lines[line_num - 1]
        
        # Remove extra semicolons
        if line.strip() == ';':
            lines[line_num - 1] = ''
        
        # Fix dangling commas
        if line.strip() == ',':
            lines[line_num - 1] = ''
    
    return '\n'.join(lines)

def main():
    print("🔍 Analyzing parsing errors...")
    errors = get_parsing_errors()
    print(f"Found {len(errors)} parsing errors")
    
    # Group errors by file
    errors_by_file = {}
    for filepath, line_num, error_msg in errors:
        if filepath not in errors_by_file:
            errors_by_file[filepath] = []
        errors_by_file[filepath].append((line_num, error_msg))
    
    # Process each file
    fixed_count = 0
    for filepath, file_errors in errors_by_file.items():
        if not os.path.exists(filepath):
            continue
        
        print(f"\n📄 Processing {filepath} ({len(file_errors)} errors)...")
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Apply fixes based on error types
            for line_num, error_msg in sorted(file_errors, reverse=True):
                if "'}' expected" in error_msg:
                    content = fix_missing_closing_brackets(filepath, content)
                elif 'Unexpected token' in error_msg:
                    content = fix_unexpected_tokens(filepath, content, error_msg)
                elif 'Expression expected' in error_msg:
                    content = fix_expression_expected(filepath, content, line_num)
                elif 'Declaration or statement expected' in error_msg:
                    content = fix_declaration_expected(filepath, content, line_num)
            
            # Always check for missing JSX closing tags in TSX files
            if filepath.endswith('.tsx'):
                content = fix_jsx_closing_tags(filepath, content)
            
            # Write back if changed
            if content != original_content:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ Fixed {filepath}")
                fixed_count += 1
            else:
                print(f"  ⚠️  Could not auto-fix {filepath}")
        
        except Exception as e:
            print(f"  ❌ Error processing {filepath}: {e}")
    
    print(f"\n✨ Fixed {fixed_count} files")
    
    # Run lint again to check remaining errors
    print("\n🔍 Checking remaining errors...")
    remaining_errors = get_parsing_errors()
    print(f"Remaining parsing errors: {len(remaining_errors)}")

if __name__ == "__main__":
    main()