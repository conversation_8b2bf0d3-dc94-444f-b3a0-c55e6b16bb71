﻿.dxgvTable .dxgvDataRow:hover {
    background-color: #f3eef9 !important;
}

.clamp {
    /* Webkit-specific clamp not supported in IE - use height-based truncation */
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 5px;
    text-indent: 3px;
    max-height: 3em; /* Approximately 2 lines */
    line-height: 1.5em;
    /* Webkit specific for modern browsers */
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 2 lines */
    -webkit-box-orient: vertical;
}

.dxgvDataRow td.dxgv {
    padding: 5px 5px 5px 3px;
}

.dxic > input[type="text"]:focus, .dxic > textarea:focus, .dxic > select:focus {
    border-color: #5c2d91 !important; /* IE doesn't support CSS variables */
    box-shadow: 0 0 0 3px rgba(92, 45, 145, 0.25) !important;
    outline: none !important;
}

td.dxic {
    padding: 5px !important;
}

.dxeEditArea {
    font-size: 11px !important;
}

.dxgvControl a {
    margin: 0 !important;
}

.underline_cursor_color1 {
    cursor: pointer;
}

    .underline_cursor_color1:hover {
        color: #e47902;
        text-decoration: underline;
    }

.underline_cursor_color2 {
    cursor: pointer;
}

    .underline_cursor_color2:hover {
        text-decoration: underline;
    }

.ContentHeadline {
    color: #770cb7;
    font-size: 20px;
    font-weight: bold;
    border-bottom: groove 2px #770cb7;
    padding-bottom: 2px;
    margin-bottom: 8px;
}

    .ContentHeadline a {
        text-decoration: none;
        color: #770cb7;
        cursor: pointer;
    }

.component-caption {
    padding: 5px;
    font-size: 15px;
    color: #6615bd;
    border-bottom: solid 3px #6615bd;
    font-weight: bold;
}

.txtMultilineGrid {
    height: 70px;
    width: 95%;
    border: none;
}

.txtMultilineGridEmpty {
    height: 20px;
    width: 95%;
    border: none;
    margin-top: 5px;
}

.ul1 {
    margin: 0 !important;
    /* margin-block not supported in IE */
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    /* padding-inline not supported in IE */
    padding-left: 15px;
    padding-right: 15px;
}

    .ul1 li {
        color: #2248a9;
        list-style-type: square;
        padding: 1px 0px 1px 0px;
        font-size: 14px;
    }


.ulAnaSayfaSolMenu {
    margin: 0 !important;
    /* margin-block not supported in IE */
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    /* padding-inline not supported in IE */
    padding-right: 15px;
    padding-left: 0;
    margin-left: 25px !important;
}

    .ulAnaSayfaSolMenu li {
        color: #662e85;
        list-style-type: square;
        padding: 1px 0px 1px 0px;
        font-size: 14px;
    }

        .ulAnaSayfaSolMenu li p {
            /* margin-block not supported in IE */
            margin-top: 7px;
            margin-bottom: 7px;
        }

.AnaSayfaSolMenuCaption {
    background-color: #662e85;
    color: #fff;
    height: 20px;
    padding-left: 4px;
    font-weight: bold;
    font-family: Calibri;
    font-size: 15px;
}

.AnaSayfaSolMenuWrapper {
    border: solid 1px #662e85;
}
