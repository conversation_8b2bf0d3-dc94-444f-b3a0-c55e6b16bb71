﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Linkler.aspx.cs" Inherits="Digiport_Linkler" %>


<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
</head>
<body>
    <link href="/Digiport/css/common.css?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>" rel="stylesheet" />
    <form id="form1" runat="server">
        <div>
            <div class="ContentHeadline">
                <asp:HyperLink runat="server" ID="hyperLinkler"  Target="_blank" Visible="false"></asp:HyperLink>
                <asp:Label ID="lblLinkler" runat="server" Visible="false"></asp:Label>
            </div>
            <div id="divContainer_Grid" runat="server" visible="false">
                <dx:ASPxGridView ID="gridViewLinkler" runat="server" SettingsBehavior-AllowGroup="true" KeyFieldName="ID" Width="100%">
                    <Columns>
                        <dx:GridViewDataColumn Caption="Link Adı" FieldName="LINK_ADI" VisibleIndex="1" CellStyle-HorizontalAlign="Left">
                            <DataItemTemplate>
                                <asp:Label runat="server" ID="lblLinkAdi" Text='<%# Eval("LINK_ADI") %>' ToolTip='<%# Eval("LINK_ADI") %>' CssClass='<%# SatirKlas(Convert.ToInt32(Eval("CLICK_ACTION").ToString())) %>' onclick='<%# LinkClickEvent(Eval("ID").ToString(),Convert.ToInt32(Eval("CLICK_ACTION").ToString()),Eval("TARGET_LINK").ToString(),Eval("POPUP_WIDTH").ToString(),Eval("POPUP_HEIGHT").ToString()) %>'></asp:Label>
                            </DataItemTemplate>
                        </dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="Notlar" FieldName="NOTLAR" VisibleIndex="2" CellStyle-HorizontalAlign="Left">
                        </dx:GridViewDataColumn>
                        <%--<dx:GridViewDataColumn Caption="Notlar" FieldName="NOTLAR" VisibleIndex="2" CellStyle-HorizontalAlign="Left">
                            <DataItemTemplate>
                                <asp:TextBox runat="server" ID="txtNotlarGrid" ReadOnly="true" autocomplete="off" Text='<%# Eval("NOTLAR").ToString() %>' ToolTip='<%# Eval("NOTLAR").ToString() %>' TextMode="MultiLine" CssClass='<%# Eval("NOTLAR").ToString()==string.Empty?"txtMultilineGridEmpty":"txtMultilineGrid" %>'></asp:TextBox>
                            </DataItemTemplate>
                        </dx:GridViewDataColumn>--%>
                    </Columns>
                    <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}" NumericButtonCount="5" ShowNumericButtons="true">
                        <NextPageButton Text="İleri">
                        </NextPageButton>
                        <PrevPageButton Text="Geri">
                        </PrevPageButton>
                        <FirstPageButton Text="İlk Sayfa">
                        </FirstPageButton>
                        <LastPageButton Text="Son Sayfa">
                        </LastPageButton>
                        <Summary Text="" />
                    </SettingsPager>
                    <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" ShowGroupPanel="false" />
                    <SettingsText EmptyDataRow="Kayıt bulunmadı" FilterBarClear="Filtreleri Temizle" />
                    <Styles>
                        <Header BackColor="#5c2d91" ForeColor="White" Paddings-Padding="3px"></Header>
                        <Cell>
                            <Border BorderColor="#5c2d91" />
                            <Paddings PaddingBottom="8" PaddingLeft="3" PaddingRight="3" PaddingTop="8" />
                        </Cell>
                    </Styles>
                </dx:ASPxGridView>
            </div>
            <div id="divContainer_Repeater" runat="server" visible="false">
                <ul class="ul1">
                    <asp:Repeater runat="server" ID="repeaterLinkler">
                        <ItemTemplate>
                            <li title='<%# Eval("LINK_ADI") %>' runat="server" class='<%# LiKlas(Convert.ToInt32(Eval("CLICK_ACTION").ToString())) %>' onclick='<%# LinkClickEvent(Eval("ID").ToString(),Convert.ToInt32(Eval("CLICK_ACTION").ToString()),Eval("TARGET_LINK").ToString(),Eval("POPUP_WIDTH").ToString(),Eval("POPUP_HEIGHT").ToString()) %>'>
                                <asp:Literal runat="server" Text='<%# Eval("LINK_ADI") %>'></asp:Literal></li>
                        </ItemTemplate>
                    </asp:Repeater>
                </ul>
            </div>
        </div>
        <script src="/Digiport/js/jquery-3.6.0.min.js"></script>
        <script src="/Digiport/js/common.js?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>"></script>
    </form>
</body>
</html>
