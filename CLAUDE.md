# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an ASP.NET Web Forms application (.NET Framework 4.5.2) serving as an admin panel for various corporate management systems. The application uses Oracle database and is tightly integrated with Active Directory for authentication.

## Key Technologies

- **Framework**: ASP.NET Web Forms (.NET Framework 4.5.2)
- **Language**: C#
- **Database**: Oracle (Oracle.DataAccess)
- **UI Libraries**: DevExpress v16.2, jQuery, Select2, SummerNote
- **Authentication**: Windows Authentication with AD integration

## Build & Development Commands

Since this is a Visual Studio solution without modern build tools:

```bash
# Open solution in Visual Studio
# File: AdminYonetimSistemi.sln

# Build from command line (requires MSBuild)
msbuild AdminYonetimSistemi.sln /p:Configuration=Debug
msbuild AdminYonetimSistemi.sln /p:Configuration=Release

# Run IIS Express locally (from Visual Studio)
# Press F5 or Ctrl+F5 in Visual Studio
```

## High-Level Architecture

### Solution Structure
```
AdminYönetimSistemi (Main Web Project)
├── AdminPages/          # Feature-specific admin pages
├── Handlers/            # File upload/download handlers  
├── UserControl/         # Reusable WebForm controls
└── Global.asax.cs       # Application lifecycle & auth

FormHelper (Business Layer)
└── Various helper classes for business logic

Entities (Domain Models)
└── Entity classes representing business objects

DataAccessLayer (Data Access - mostly stub)
└── Minimal implementation, most DB access in pages
```

### Key Architectural Patterns

1. **Authentication Flow**: 
   - Global.asax.cs handles Windows auth and AD group validation
   - DigiportAuthorizationModule.cs provides custom authorization
   - User impersonation supported via query parameters

2. **Page Organization**:
   - Feature modules under AdminPages/ (e.g., AracTakipSistemi, DigiportAdmin)
   - Each module typically has List/Detail/Edit pages
   - Heavy use of code-behind files with mixed concerns

3. **Data Access**:
   - Direct Oracle queries in page code-behind
   - Connection strings in Web.config
   - No repository pattern or proper data layer abstraction

4. **External Dependencies**:
   - DLLs referenced from network shares (\\dtl1iis3\Deployment\)
   - AD Portal web services for user/group management
   - SharePoint integration for document storage

### Critical Configuration

**Web.config Key Settings**:
- `debugMode`: Controls debug/production behavior
- `ESSBDurum`: E/H flag for various features
- `AdPortalServices`: AD management service endpoints
- Oracle connection strings (SUBSET15, DBSLIVE)
- SMTP settings for email functionality

### Development Considerations

1. **Network Dependencies**: Application requires access to network shares for DLL references
2. **AD Integration**: Must be on domain or have proper AD connectivity
3. **Oracle Client**: Requires Oracle client installation for database access
4. **IIS Configuration**: Requires Windows Authentication enabled in IIS
5. **File Paths**: Many hardcoded paths (e.g., LogicalGroupDefinition) may need adjustment

### Common Tasks

1. **Adding New Admin Page**:
   - Create new .aspx page under appropriate AdminPages subfolder
   - Inherit from System.Web.UI.Page
   - Add authentication check in Page_Load
   - Register in navigation menus (Site.Master or module-specific menus)

2. **Database Operations**:
   - Use existing connection string helpers
   - Follow pattern of direct SQL execution in code-behind
   - Handle Oracle-specific syntax

3. **File Upload/Download**:
   - Use existing handlers in Handlers/ folder
   - Follow security patterns for file type validation

4. **AD Group Access**:
   - Define groups in Web.config appSettings
   - Use Global.asax authentication methods
   - Check group membership in page authorization

### Important Notes

- Legacy application with tightly coupled architecture
- No unit tests or modern development practices
- Heavy reliance on server-side controls and postbacks
- Mixed Turkish/English in codebase
- Production deployment requires specific IIS and network setup

### Localization & Resources

**CRITICAL**: This application uses ASP.NET resource files for all user-facing text. Never use hardcoded strings directly in the code.

1. **Resource Files Location**:
   - Global resources: `App_GlobalResources/` folder
   - Page-specific resources: `App_LocalResources/` folders

2. **Using Resources in ASPX Pages**:
   ```aspx
   <%-- Use resource expressions for server controls --%>
   <asp:Literal Text="<%$ Resources:DigiportAdminResource, UserAssignment_PageTitle %>" />
   
   <%-- For client-side binding in GridView/Repeater --%>
   <%# HttpContext.GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_StatusActive") %>
   ```

3. **Using Resources in Code-Behind**:
   ```csharp
   // Get resource string with fallback
   string message = GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_SaveSuccess")?.ToString() 
                    ?? "Default fallback message";
   
   // Direct resource access (when sure it exists)
   string title = DigiportAdminResource.UserAssignment_PageTitle;
   ```

4. **Resource Naming Convention**:
   - Format: `PageName_ElementDescription`
   - Examples: `UserAssignment_PageTitle`, `UserAssignment_SaveButton`, `UserAssignment_DeleteConfirmation`

5. **Adding New Resources**:
   - Add entries to appropriate .resx files (both Turkish and English versions)
   - Use descriptive keys that indicate the page and element
   - Always provide fallback values when using GetGlobalResourceObject