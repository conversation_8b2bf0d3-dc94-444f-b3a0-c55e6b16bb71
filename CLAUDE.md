# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a legacy ASP.NET Web Forms application (Digiport) that appears to be a content display module for a larger portal system. The codebase uses traditional Web Forms with code-behind pattern and DevExpress UI components.

## Architecture & Key Patterns

### Component-Based Content Display
The application follows a modular component architecture where each ASPX page represents a self-contained UI component designed to be embedded within a larger system:

- **DisplayContent.aspx**: Central content router that handles all content requests via query strings
- Each component type (slide, menu, announcements) has its own ASPX page and corresponding helper class
- Components are loaded based on `component-type` and `content-id` query parameters

### Helper Class Pattern
All content retrieval logic is delegated to helper classes in the `DigiportMenuDisplayHelpers` namespace:
- `AnaSayfaSolSagSlideHelper`
- `AnaSayfaUstSlideHelper`
- `HrMediaSlideHelper`
- `IndirimFirsatiHelper`
- `LinklerHelper`
- `AnaSayfaSolMenuHelper`

### Configuration Management
- Environment configuration via `ConfigurationManager.AppSettings`
- Domain switching based on debug mode: `http://digiflowtest` (test) vs `http://digiflow` (production)
- Configuration key: `Workflow.Mail.IsMailDebugMode`

## Technology Stack

- **Backend**: ASP.NET Web Forms, C#, .NET Framework
- **Frontend**: jQuery 3.6.0, custom JavaScript modules
- **UI Components**: DevExpress Web Controls
- **CSS**: Custom styles with DevExpress grid customizations

## Code Conventions

### Naming Patterns
- **Turkish terminology**: AnaSayfa (Homepage), Sol (Left), Sag (Right), Ust (Top)
- **C# conventions**: PascalCase for classes and methods
- **JavaScript conventions**: camelCase for functions and variables
- **Component types**: Lowercase in query strings (e.g., "anasayfasolmenu")

### Error Handling
The codebase uses basic try-catch blocks with empty catch statements. When modifying error handling:
- Preserve the existing pattern unless specifically asked to improve error handling
- Content display failures should fail silently (as per current implementation)

### Frontend Architecture
- jQuery-based event handling and DOM manipulation
- Slider components with configurable parameters
- Popup and tab management functions in common.js

## Important Context

This appears to be a partial module from a larger system. Missing elements that may exist in the parent project:
- web.config file with full configuration
- .csproj project file
- packages.config for NuGet dependencies
- DigiportMenuDisplayHelpers implementation
- Main application that consumes these components

When working with this codebase:
1. Assume DevExpress components are available and properly configured
2. Maintain compatibility with the existing Web Forms pattern
3. Preserve the Turkish naming conventions
4. Keep the modular component structure intact
5. Respect the query string-based routing pattern