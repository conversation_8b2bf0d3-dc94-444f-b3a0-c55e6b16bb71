﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="IndirimFirsati.aspx.cs" Inherits="Digiport_IndirimFirsati" %>


<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
</head>
<body>
    <link href="/Digiport/css/common.css?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>" rel="stylesheet" />
    <form id="form1" runat="server">
        <div>
            <div class="ContentHeadline">
                <asp:HyperLink runat="server" ID="hyperIndirimFirsatiFullView" Text="" Target="_blank" Visible="false"></asp:HyperLink>
                <asp:Label ID="lblLinkler" runat="server" Text="" Visible="false"></asp:Label>
            </div>
            <dx:ASPxGridView ID="gridViewIndirimFirsati" runat="server" SettingsBehavior-AllowGroup="true" KeyFieldName="ID" Width="100%">
                <Columns>
                    <dx:GridViewDataColumn Caption="Başlık" FieldName="BASLIK" VisibleIndex="1" CellStyle-HorizontalAlign="Left">
                        <DataItemTemplate>
                            <asp:Label runat="server" ID="lblBaslik" Text='<%# Eval("BASLIK") %>' ToolTip='<%# Eval("BASLIK") %>' CssClass='<%# BaslikKlas(Convert.ToInt32(Eval("CLICK_ACTION").ToString())) %>' onclick='<%# IndirimFirsatiClickEvent(Eval("ID").ToString(),Convert.ToInt32(Eval("CLICK_ACTION").ToString()),Eval("TARGET_LINK").ToString(),Eval("POPUP_WIDTH").ToString(),Eval("POPUP_HEIGHT").ToString()) %>'></asp:Label>
                        </DataItemTemplate>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="Kategori" FieldName="KATEGORI" VisibleIndex="2" CellStyle-HorizontalAlign="Left" Width="80px" />
                    <dx:GridViewDataColumn Caption="Kurum" FieldName="KURUM_ADI" VisibleIndex="3" CellStyle-HorizontalAlign="Left" Width="120px" />
                    <dx:GridViewDataColumn Caption="Başlangıç Tarihi" FieldName="BASLANGIC_TARIHI" VisibleIndex="4" Width="80px">
                        <DataItemTemplate>
                            <asp:Label ID="lblTarihBaslangic" runat="server" Text='<%# Eval("BASLANGIC_TARIHI", "{0:dd.MM.yyyy}") %>'></asp:Label>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="Bitiş Tarihi" FieldName="BITIS_TARIHI" VisibleIndex="5" Width="80px">
                        <DataItemTemplate>
                            <asp:Label ID="lblTarihBitis" runat="server" Text='<%# Eval("BITIS_TARIHI", "{0:dd.MM.yyyy}") %>'></asp:Label>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                </Columns>
                <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}" NumericButtonCount="5" ShowNumericButtons="true">
                    <NextPageButton Text="İleri">
                    </NextPageButton>
                    <PrevPageButton Text="Geri">
                    </PrevPageButton>
                    <FirstPageButton Text="İlk Sayfa">
                    </FirstPageButton>
                    <LastPageButton Text="Son Sayfa">
                    </LastPageButton>
                    <Summary Text="" />
                </SettingsPager>
                <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" ShowGroupPanel="false" />
                <SettingsText EmptyDataRow="Kayıt bulunmadı" FilterBarClear="Filtreleri Temizle" />
                <Styles>
                    <Header BackColor="#5c2d91" ForeColor="White" Paddings-Padding="3px"></Header>
                    <Cell>
                        <Border BorderColor="#5c2d91" />
                        <Paddings PaddingBottom="8" PaddingLeft="3" PaddingRight="3" PaddingTop="8"/>
                    </Cell>
                </Styles>
            </dx:ASPxGridView>
        </div>
        <script src="/Digiport/js/jquery-3.6.0.min.js"></script>
        <script src="/Digiport/js/common.js?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>"></script>
    </form>
</body>
</html>
