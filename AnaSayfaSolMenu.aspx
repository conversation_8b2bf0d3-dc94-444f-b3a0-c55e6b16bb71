﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="AnaSayfaSolMenu.aspx.cs" Inherits="Digiport_AnaSayfaSolMenu" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
</head>
<body>
    <link href="/Digiport/css/common.css?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>" rel="stylesheet" />
    <form id="form1" runat="server">
        <div id="divContainer_Repeater" runat="server" visible="false">
            <div class="AnaSayfaSolMenuWrapper">
                <div class="AnaSayfaSolMenuCaption" id="divAnaSayfaSolMenuCaption" runat="server"></div>
                <ul class="ulAnaSayfaSolMenu">
                    <asp:Repeater runat="server" ID="repeaterSolMenu">
                        <ItemTemplate>
                            <li runat="server" class='<%# LiKlas(Convert.ToInt32(Eval("CLICK_ACTION").ToString())) %>' onclick='<%# LinkClickEvent(Eval("ID").ToString(),Convert.ToInt32(Eval("CLICK_ACTION").ToString()),Eval("TARGET_LINK").ToString(),Eval("POPUP_WIDTH").ToString(),Eval("POPUP_HEIGHT").ToString()) %>'>
                                <asp:Literal runat="server" Text='<%# Eval("CONTENT") %>'></asp:Literal></li>
                        </ItemTemplate>
                    </asp:Repeater>
                </ul>
            </div>
        </div>
        <script src="/Digiport/js/jquery-3.6.0.min.js"></script>
        <script src="/Digiport/js/common.js?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>"></script>
    </form>
</body>
</html>
