﻿using FormHelper.DigiportAdmin;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.SessionState;

namespace AracTakipSistemi.Handlers
{
    /// <summary>
    /// Summary description for UploadMediaHandlerHrApp
    /// </summary>
    public class UploadMediaHandlerHrApp : IHttpHandler, IRequiresSessionState
    {

        public void ProcessRequest(HttpContext context)
        {
            try
            {
                HttpPostedFile file = context.Request.Files["file"];
                string componentName = context.Request["componentName"];
                if (file != null && file.ContentLength > 0)
                {
                    string[] allowedExtensionsResim = ConfigurationManager.AppSettings["DigiportAdminHtmlEditorIzinliResimHrApp"].Split(',');
                    string[] allowedExtensionsVideo = ConfigurationManager.AppSettings["DigiportAdminHtmlEditorIzinliVideoHrApp"].Split(',');
                    string[] allowedExtensionsDiger = ConfigurationManager.AppSettings["DigiportAdminHtmlEditorIzinliDigerDosyaHrApp"].Split(',');
                    string extension = Path.GetExtension(file.FileName).ToLower();

                    if (Array.IndexOf(allowedExtensionsResim, extension.Replace(".", "")) < 0 && Array.IndexOf(allowedExtensionsVideo, extension.Replace(".", "")) < 0 && Array.IndexOf(allowedExtensionsDiger, extension.Replace(".", "")) < 0)
                    {
                        context.Response.StatusCode = 415;
                        string allowed = string.Join(",", allowedExtensionsResim) + "," + string.Join(",", allowedExtensionsVideo) + "," + string.Join(",", allowedExtensionsDiger);
                        context.Response.TrySkipIisCustomErrors = true;
                        context.Response.Write(Resources.DigiportAdminResource.SadeceResimVideo + allowed);
                        return;
                    }
                    if (Array.IndexOf(allowedExtensionsVideo, extension.Replace(".", "")) >= 0)
                    {
                        decimal allowedMaxBytes = Convert.ToDecimal(ConfigurationManager.AppSettings["DigiportAdminHtmlEditorIzinliVideoFileSizeInBytesHrApp"]);
                        if (file.ContentLength > allowedMaxBytes)
                        {
                            string maks = Math.Round((allowedMaxBytes / 1000000), 0).ToString();
                            context.Response.StatusCode = 413;
                            context.Response.TrySkipIisCustomErrors = true;
                            context.Response.Write(Resources.DigiportAdminResource.VideoIzinVerilenMaxBoyut + "[ Max " + maks + " MB ]");
                            return;
                        }
                    }
                    string virtualPath = "/Content/SummerNoteTempUploads/HrApp/" + componentName + "/" + DateTime.Today.Year.ToString() + "/" + DateTime.Today.Month.ToString() + "/";
                    string uploadPath = context.Server.MapPath("~" + virtualPath);
                    if (!Directory.Exists(uploadPath))
                        Directory.CreateDirectory(uploadPath);

                    string fileName = Guid.NewGuid().ToString() + extension;
                    string filePath = Path.Combine(uploadPath, fileName);
                    file.SaveAs(filePath);

                    string fileUrl = virtualPath + fileName;

                    context.Response.TrySkipIisCustomErrors = false;
                    context.Response.Write(fileUrl);
                    DigiportMediaHrAppHelper helper = new DigiportMediaHrAppHelper(componentName);
                    helper.AddUploadedMediaToSession(filePath);
                }
                else
                {
                    context.Response.StatusCode = 400;
                    context.Response.TrySkipIisCustomErrors = true;
                    context.Response.Write(Resources.DigiportAdminResource.DosyaYuklenemedi);
                }
            }
            catch (Exception ex)
            {
                context.Response.StatusCode = 400;
                context.Response.TrySkipIisCustomErrors = true;
                context.Response.Write(Resources.DigiportAdminResource.DosyaYuklenemedi + "." + ex.Message);
            }
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}