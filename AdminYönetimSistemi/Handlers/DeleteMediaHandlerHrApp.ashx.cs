﻿using FormHelper.DigiportAdmin;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.SessionState;

namespace AracTakipSistemi.Handlers
{
    /// <summary>
    /// Summary description for DeleteMediaHandlerHrApp
    /// </summary>
    public class DeleteMediaHandlerHrApp : I<PERSON>ttp<PERSON><PERSON><PERSON>, IRequiresSessionState
    {

        public void ProcessRequest(HttpContext context)
        {
            string url = context.Request["fileUrl"];
            string componentName = context.Request["componentName"];
            if (!string.IsNullOrEmpty(url))
            {
                string path = context.Server.MapPath(url);

                if (path.Contains(@"\Content\SummerNoteTempUploads\HrApp\" + componentName) && File.Exists(path))
                {
                    File.Delete(path);
                }
                if (path.Contains(@"\Content\SummerNoteUploads\HrApp\" + componentName) && File.Exists(path))
                {
                    DigiportMediaHrAppHelper helper = new DigiportMediaHrAppHelper(componentName);
                    helper.AddRemovedMediaToSession(path);
                }
            }
            context.Response.Write("OK");
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}