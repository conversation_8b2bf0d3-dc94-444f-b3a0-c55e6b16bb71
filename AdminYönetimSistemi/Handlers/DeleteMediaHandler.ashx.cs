﻿using FormHelper.DigiportAdmin;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.SessionState;

namespace AracTakipSistemi.Handlers
{
    /// <summary>
    /// Summary description for DeleteMediaHandler
    /// </summary>
    public class DeleteMediaHandler : IHttpHand<PERSON>, IRequiresSessionState
    {

        public void ProcessRequest(HttpContext context)
        {
            string url = context.Request["fileUrl"];
            string componentName = context.Request["componentName"];
            if (!string.IsNullOrEmpty(url))
            {
                string path = context.Server.MapPath(url);

                if (path.Contains(@"\Content\SummerNoteTempUploads\Digiport\"+ componentName) && File.Exists(path))
                {
                    File.Delete(path);
                }
                if(path.Contains(@"\Content\SummerNoteUploads\Digiport\" + componentName) && File.Exists(path))
                {
                    DigiportHtmlEditorMediaHelper helper = new DigiportHtmlEditorMediaHelper(componentName);
                    helper.AddRemovedMediaToSession(path);
                }
            }
            context.Response.Write("OK");
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}