﻿using System;
using System.Configuration;
using System.Diagnostics;
using System.Globalization;
using System.Text;
using System.Web;
using AracTakipSistemi.Authentication;

namespace AracTakipSistemi
{
    public class Global : HttpApplication
    {
        protected void Application_Start(object sender, EventArgs e)
        {
            #region TEST - LIVE Connection String Atamaları

            var DT_WORKFLOW_settings = ConfigurationManager.ConnectionStrings["DT_WORKFLOW"];
            var DBSConnection_settings = ConfigurationManager.ConnectionStrings["DBSConnection"];

            var Default_settings = ConfigurationManager.ConnectionStrings["DefaultConnection"];
            var fi3 = typeof(ConfigurationElement).GetField("_bReadOnly", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            fi3.SetValue(DT_WORKFLOW_settings, false);
            fi3.SetValue(Default_settings, false);

            var fi = typeof(ConfigurationElement).GetField("_bReadOnly", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            fi.SetValue(DBSConnection_settings, false);




            if (ConfigurationManager.AppSettings["debugMode"].ToString() == "true")
            {
                DT_WORKFLOW_settings.ConnectionString = ConfigurationManager.ConnectionStrings["DT_WORKFLOW_TEST"].ToString();
                DBSConnection_settings.ConnectionString = Digiturk.Workflow.Digiflow.DataAccessLayer.PasswordBoxConnectionString.WebConfigConnectionString("SUBSET15", "INQUIRY", "TEST");
            }
            else
            {
                DT_WORKFLOW_settings.ConnectionString = ConfigurationManager.ConnectionStrings["DT_WORKFLOW_LIVE"].ToString();
                DBSConnection_settings.ConnectionString = Digiturk.Workflow.Digiflow.DataAccessLayer.PasswordBoxConnectionString.WebConfigConnectionString("DBSLIVE", "INQUIRY", "LIVE");
            }
            Default_settings.ConnectionString = DT_WORKFLOW_settings.ConnectionString;

            #endregion TEST - LIVE Connection String Atamaları
        }

        protected void Session_Start(object sender, EventArgs e)
        {
            Debug.WriteLine("Session_Start - Attempting to authenticate user");
            AuthenticationManager.AuthenticateUser(Context);
        }

        protected void Application_PreRequestHandlerExecute(object sender, EventArgs e)
        {
            try
            {
                // Attempt to authenticate on each request
                if (HttpContext.Current.Session["LoginId"] == null)
                {
                    Debug.WriteLine("Application_PreRequestHandlerExecute - No user session, attempting to authenticate");
                    AuthenticationManager.AuthenticateUser(HttpContext.Current);
                }
                else if (HttpContext.Current.Request.QueryString["LoginId"] != null)
                {
                    // Handle user switching/impersonation
                    Debug.WriteLine("LoginId found in query string, handling user impersonation");
                    AuthenticationManager.AuthenticateUser(HttpContext.Current);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Application_PreRequestHandlerExecute: {ex.Message}");
            }

            // Set culture for localization
            string langstr = string.Empty;
            try
            {
                langstr = Session["AdminYonetimSistemiSecilenDil" + Session.SessionID] != null
                    ? Session["AdminYonetimSistemiSecilenDil" + Session.SessionID] as string
                    : "Turkish";
            }
            catch
            {
                langstr = "Turkish";
            }

            string cultureLang = langstr == "English" ? "en-US" : "tr-TR";
            System.Threading.Thread.CurrentThread.CurrentCulture = new CultureInfo(cultureLang);
            System.Threading.Thread.CurrentThread.CurrentUICulture = new CultureInfo(cultureLang);
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            // Global error handling
            Exception ex = Server.GetLastError();
            if (ex != null)
            {
                Debug.WriteLine($"Application Error: {ex.Message}");
            }
        }

        // Debug helper method
        public static string GetSessionDebugInfo()
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine("<h3>Windows Identity Information</h3>");

                try { sb.AppendLine($"WindowsIdentity.GetCurrent: {System.Security.Principal.WindowsIdentity.GetCurrent().Name}<br/>"); }
                catch (Exception ex) { sb.AppendLine($"WindowsIdentity.GetCurrent error: {ex.Message}<br/>"); }

                try { sb.AppendLine($"Request.LogonUserIdentity: {HttpContext.Current?.Request?.LogonUserIdentity?.Name ?? "null"}<br/>"); }
                catch (Exception ex) { sb.AppendLine($"Request.LogonUserIdentity error: {ex.Message}<br/>"); }

                try { sb.AppendLine($"Thread.CurrentPrincipal: {System.Threading.Thread.CurrentPrincipal?.Identity?.Name ?? "null"}<br/>"); }
                catch (Exception ex) { sb.AppendLine($"Thread.CurrentPrincipal error: {ex.Message}<br/>"); }

                sb.AppendLine("<h3>Session Information</h3>");
                sb.AppendLine($"LoginId: {HttpContext.Current?.Session?["LoginId"] ?? "null"}<br/>");
                sb.AppendLine($"Username: {HttpContext.Current?.Session?["Username"] ?? "null"}<br/>");
                sb.AppendLine($"UserFullName: {HttpContext.Current?.Session?["UserFullName"] ?? "null"}<br/>");
                sb.AppendLine($"IsAdmin: {HttpContext.Current?.Session?["IsAdmin"] ?? "null"}<br/>");
                sb.AppendLine($"IsImpersonating: {HttpContext.Current?.Session?["IsImpersonating"] ?? "false"}<br/>");

                if (HttpContext.Current?.Session?["IsImpersonating"] != null &&
                    (bool)HttpContext.Current.Session["IsImpersonating"])
                {
                    sb.AppendLine("<h3>Original User Information</h3>");
                    sb.AppendLine($"OriginalLoginId: {HttpContext.Current?.Session?["OriginalLoginId"] ?? "null"}<br/>");
                    sb.AppendLine($"OriginalUserName: {HttpContext.Current?.Session?["OriginalUserName"] ?? "null"}<br/>");
                    sb.AppendLine($"OriginalUserFullName: {HttpContext.Current?.Session?["OriginalUserFullName"] ?? "null"}<br/>");
                    sb.AppendLine($"OriginalWindowsUsername: {HttpContext.Current?.Session?["OriginalWindowsUsername"] ?? "null"}<br/>");
                }

                sb.AppendLine("<h3>Request Information</h3>");
                sb.AppendLine($"Current URL: {HttpContext.Current?.Request?.Url?.ToString() ?? "null"}<br/>");
                sb.AppendLine($"LoginId in QueryString: {HttpContext.Current?.Request?.QueryString["LoginId"] ?? "null"}<br/>");
                sb.AppendLine($"Is Debug Mode: {ConfigurationManager.AppSettings["debugMode"] ?? "null"}<br/>");

                return sb.ToString();
            }
            catch (Exception ex)
            {
                return $"Error generating debug info: {ex.Message}";
            }
        }
    }
}