﻿using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using DevExpress.Web;
using Entities;
using FormHelper.YetkiHelper;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.TestMate
{
    public partial class TmateDomainsAdmins : AdminAbstract
    {
        protected void Page_Load(object sender, EventArgs e) //KAYITTA HATA ALIYOR AMA AD PORTAL WEB SERVİCE CALISIYO REBUILD ALINIYO
        {
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            ((Button)AdminKaydet1.FindControl("btnKaydet")).CausesValidation = false;
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            this.Master.SayfaBaslikAt("Test Mate Domain Entry");
            if (!IsPostBack)
            {
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
            }
            Grid_Doldur();
            if (FormHelper.CoreHelper.isEnglish())
            {
                ((Button)AdminKaydet1.FindControl("btnKaydet")).Text = "Add";
                ((Button)AdminKaydet1.FindControl("btnSil")).Text = "Delete";
                ((Button)AdminKaydet1.FindControl("btnGuncelle")).Text = "Update";
                ((Button)AdminKaydet1.FindControl("btnTemizle")).Text = "Cancel";
            }
        }

        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(grdDomains.GetRowValues(index, "ID"));
            Kayit_Getir(deger);
        }
        public override void Grid_Doldur()
        {
            grdDomains.DataSource = FormHelper.TestMate.DomainKaydetHelper.grdListe();
            grdDomains.DataBind();
        }
        public override void Kaydet()
        {
            Page.Validate();
            if (Page.IsValid)
            {
                if (!FormHelper.TestMate.DomainKaydetHelper.mukerrerKayitVarMi(txtDomain.Text, 0))
                {
                    Entities.TMATE_DOMAINS_ADMINS nesnem = new Entities.TMATE_DOMAINS_ADMINS();
                    Esitle(nesnem);
                    nesnem.CREATED = DateTime.Now;
                    nesnem.CREATED_BY = LoginId;
                    string YeniID = PRepository<TMATE_DOMAINS_ADMINS>.EntityKaydet("DT_WORKFLOW", nesnem);
                    Temizle();
                    nesnem = null;
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Info", "Entry is successful!", true);
                    }
                    if (!FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Bilgi", "Kayıt Edildi", false);
                    }
                }
                else
                {
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Fail", "Dublicate record entry!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Hata", "Aynı Kayıt İkinci Kez Girilemez", true);
                    }
                }
            }
        }
        private void Esitle(Entities.TMATE_DOMAINS_ADMINS nesnem)
        {
            nesnem.DOMAIN_NAME = txtDomain.Text;
            nesnem.AD_GROUP_TYPE = drpAdDomainType.SelectedValue;
            nesnem.AD_GROUP_NAME = drp_AdPortalGroups.SelectedValue;
            nesnem.AKTIF = ConvertionHelper.ConvertValue<string>(ConvertionHelper.ConvertValue<int>(chkAktif.Checked));
        }
        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                if (!FormHelper.TestMate.DomainKaydetHelper.mukerrerKayitVarMi(txtDomain.Text, ID))
                {
                    Entities.TMATE_DOMAINS_ADMINS nesnem = PRepository<TMATE_DOMAINS_ADMINS>.EntityGetir("DT_WORKFLOW", ID);
                    Esitle(nesnem);
                    nesnem.LAST_UPDATED = DateTime.Now;
                    nesnem.LAST_UPDATED_BY = LoginId;
                    PRepository<TMATE_DOMAINS_ADMINS>.EntityUpdateEt("DT_WORKFLOW", nesnem);
                    Temizle();
                    nesnem = null;
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Info", "Update is successful!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Bilgi", "Kayıt Güncellendi", false);
                    }
                }
                else
                {
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Fail", "Dublicate record entry!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Hata", "Aynı Kayıt İkinci Kez Girilemez", true);
                    }
                }
            }
        }

        public override void Kayit_Getir(int ID)
        {
            Entities.TMATE_DOMAINS_ADMINS nesnem = PRepository<TMATE_DOMAINS_ADMINS>.EntityGetir("DT_WORKFLOW", ID);
            if (nesnem != null)
            {
                txtDomain.Text = nesnem.DOMAIN_NAME;
                drpAdDomainType.SelectedValue = nesnem.AD_GROUP_TYPE;
                drpAdDomainType_SelectedIndexChanged(this, null);
                drp_AdPortalGroups.SelectedValue = nesnem.AD_GROUP_NAME;
                chkAktif.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.AKTIF));
                AdminKaydet1.RecordId = nesnem.ID;
                AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
                AdminKaydet1.btnSilButtonGizle();
            }
            nesnem = null;
        }
        public override void Sil(int ID)
        {
            try
            {
                PRepository<TMATE_DOMAINS_ADMINS>.EntitySil("DT_WORKFLOW", ID);
                Temizle();
                this.Master.PopupGoster("Bilgi", "Kayıt Silindi", false);
            }
            catch (Exception)
            {
                this.Master.PopupGoster("Bilgi", "Kayıt silinemedi. Alt kırılım kayıtları mevcut olabilir.<br>Aktif seçeneğini kullanabilirsiniz.", true);
            }
        }
        public override void Temizle()
        {
            txtDomain.Text = "";
            drp_AdPortalGroups.SelectedValue = "0";
            drpAdDomainType.SelectedValue = "0";
            chkAktif.Checked = true;
            AdminKaydet1.RecordId = 0;
            Grid_Doldur();
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
        }

        protected void drpAdDomainType_SelectedIndexChanged(object sender, EventArgs e)
        {
            BuildAdPortalGrupList(drpAdDomainType.SelectedValue);
        }

        public void BuildAdPortalGrupList(string Domain)
        {
            try
            {
                var gruplist = FormHelper.TestMate.DomainKaydetHelper.GetAdGroupList(Domain);
                drp_AdPortalGroups.Items.Clear();
                drp_AdPortalGroups.Items.Add(new ListItem("---Seçiniz---", "0"));
                foreach (var item in gruplist)
                {
                    drp_AdPortalGroups.Items.Add(new ListItem(item.GroupName, item.GroupName));
                }
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }
    }
}