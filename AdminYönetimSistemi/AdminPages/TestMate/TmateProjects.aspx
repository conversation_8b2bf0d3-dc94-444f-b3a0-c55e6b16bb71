﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="TmateProjects.aspx.cs" Inherits="AracTakipSistemi.AdminPages.TestMate.TmateProjects" %>

<%@ MasterType VirtualPath="~/Site.Master" %>
<%--<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>--%>
<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>


<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>
        table#tblProjeKaydet {
            width: max-content;
        }

            table#tblProjeKaydet td {
                padding: 5px 20px;
            }

                table#tblProjeKaydet td input {
                    margin: 0;
                }

                table#tblProjeKaydet td select {
                    width: 200px;
                }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:Panel ID="pnlAll" Style="width: max-content" runat="server">
        <asp:Panel ID="pnlProjeKaydet" runat="server">
            <table id="tblProjeKaydet">
                <tr>
                    <td>
                        <asp:Literal Text="<%$ Resources:TmateResource, Domain %>" runat="server" />
                    </td>
                    <td>
                        <asp:DropDownList ID="drpDomains" DataValueField="ID" DataTextField="DOMAIN_NAME" runat="server">
                        </asp:DropDownList>
                        <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" InitialValue="0" Display="Dynamic" ForeColor="Red" ValidationGroup="vgMate" ControlToValidate="drpDomains" runat="server" />
                    </td>

                </tr>
                <tr>
                    <td>
                        <asp:Literal Text="<%$ Resources:TmateResource, Proje %>" runat="server" />
                    </td>
                    <td>
                        <asp:TextBox ID="txtProje" Width="192px" runat="server" MaxLength="70"/>
                        <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" Display="Dynamic" ForeColor="Red" ControlToValidate="txtProje" ValidationGroup="vgMate" runat="server" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <asp:CheckBox ID="chckAktif" Text="<%$ Resources:TmateResource, Aktif %>" runat="server" />
                    </td>
                </tr>
            </table>
            <table id="tblAdminUserCtrl">
                <tr>
                    <td>
                        <uc1:AdminKaydet runat="server" ID="AdminKaydet1" guncellemeUyarisi="Güncellemek istediğinize emin misiniz?" ValidationGroup="vgMate" />
                    </td>
                </tr>
            </table>
        </asp:Panel>
        <asp:Panel ID="pnlGrid" runat="server">
            <table id="tblGrid">
                <tr>
                    <td>
                        <dx:ASPxGridView ID="grdProjects" KeyFieldName="ID" Width="100%" runat="server">
                            <Columns>
                                <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, Domain %>" FieldName="DOMAIN_NAME" VisibleIndex="2"></dx:GridViewDataColumn>
                                <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, Proje %>" FieldName="PROJECT_NAME" VisibleIndex="3"></dx:GridViewDataColumn>
                                <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, Aktif %>" FieldName="AKTIF" VisibleIndex="5"></dx:GridViewDataColumn>
                                <dx:GridViewDataColumn Caption="" FieldName="" VisibleIndex="10">
                                    <DataItemTemplate>
                                        <asp:LinkButton ID="lnkGuncelle" Text="<%$ Resources:TmateResource, Duzenle %>" OnClick="lnkduzenle_Click" CausesValidation="false" runat="server" />
                                    </DataItemTemplate>
                                </dx:GridViewDataColumn>
                            </Columns>
                            <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" />
                            <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                            <SettingsBehavior AllowSelectSingleRowOnly="true" ConfirmDelete="true" />
                            <Styles>
                                <Header BackColor="#C60C30" ForeColor="White"></Header>
                            </Styles>
                        </dx:ASPxGridView>
                    </td>
                </tr>
            </table>
        </asp:Panel>
    </asp:Panel>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>
