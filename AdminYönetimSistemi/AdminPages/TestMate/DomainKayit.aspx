﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="DomainKayit.aspx.cs" Inherits="AracTakipSistemi.AdminPages.AlmTools.DomainKayit" %>
<%@ MasterType VirtualPath="~/Site.Master" %>
<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v10.2" Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:Panel ID="pnlDomainKaydet" runat="server">  
        <table>
            <tr>
                <td>
                    <asp:Label ID="lblDomain" Text="Domain" runat="server" />
                </td>
                <td>
                    <asp:TextBox ID="txtDomain" runat="server" />
                </td>
            </tr>
            <tr>
                <td>
                    <asp:Label ID="lblUsers" Text="Kullanıcı" runat="server" />
                </td>
                <td>
                    <asp:DropDownList ID="drpUsers" DataValueField="F_LOGIN_ID" DataTextField="NAME_SURNAME" runat="server">
                        <asp:ListItem Text="text1" />
                        <asp:ListItem Text="text2" />
                    </asp:DropDownList>
                </td>
            </tr>
            <tr>
                <td>
                    <uc1:AdminKaydet runat="server" ID="AdminKaydet1" />
                </td>
            </tr>
        </table>
    </asp:Panel>
    <%--<asp:Panel ID="pnlGrdDomain" runat="server">  
        <table>
            <tr>
                <td>    
                    <dx:aspxgridview id="grdDomains" keyfieldname="ID" width="100%" runat="server">
                        <columns>
                            <dx:gridviewdatacolumn caption="ID" fieldname="ID" visibleindex="1"></dx:gridviewdatacolumn>
                            <dx:gridviewdatacolumn caption="DOMAIN" fieldname="DOMAINS" visibleindex="2"></dx:gridviewdatacolumn>
                            <dx:gridviewdatacolumn caption="AD SOYAD" fieldname="NAME_SURNAME" visibleindex="3"></dx:gridviewdatacolumn>
                            <dx:gridviewdatacolumn caption="" fieldname="" visibleindex="10">
                                <dataitemtemplate>
                                    <asp:LinkButton ID="lnkGuncelle" Text="Güncelle" OnClick="" runat="server" />
                                </dataitemtemplate>
                            </dx:gridviewdatacolumn>
                        </columns>
                        <settings showfilterrow="true" showfilterbar="Auto" showfilterrowmenu="true" />
                        <settingstext groupcontinuedonnextpage="(Devamı sonraki sayfada)" grouppanel="Gruplamak istediğiniz alanları buraya sürükleyin" emptydatarow="Herhangi bir kayıt bulunmamaktadır" filterbarclear="Temizle" commandselect="İçerir" />
                        <settingsbehavior allowselectsinglerowonly="true" confirmdelete="true" />
                        <styles>
                            <header backcolor="#C60C30" forecolor="White"></header>
                        </styles>
                    </dx:aspxgridview>
                </td>
            </tr>
        </table>
    </asp:Panel>--%>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>
