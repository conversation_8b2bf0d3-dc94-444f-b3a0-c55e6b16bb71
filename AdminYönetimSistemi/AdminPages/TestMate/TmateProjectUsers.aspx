﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="TmateProjectUsers.aspx.cs" Inherits="AracTakipSistemi.AdminPages.TestMate.TmateProjectUsers" %>

<%@ MasterType VirtualPath="~/Site.Master" %>
<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>

<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>
<%@ Register Src="~/UserControl/MsgBoxCtrl.ascx" TagPrefix="uc1" TagName="MsgBoxCtrl" %>


<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>
        #pnlProjeKullaniciKayit {
            max-width: 350px;
        }

        table#tblProjeKullaniciKayit td {
            padding: 2px;
        }

        table.tblProjeKayit {
            width: 100%;
            padding: 0 15%;
            position: relative;
            margin-left: 5%;
        }

        .tblAktifVeChck {
            width: 100%;
            padding: 0 15%;
            margin: 10px 5%;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <link rel="stylesheet" href="../../css/select2.min.css" />
    <script type="text/javascript" src="../../Scripts/select2.min.js"></script>

    <script type="text/javascript">
        $(function () {
            $('.drpSelect2').select2();
        });
    </script>
    <asp:Panel ID="pnlProjeKullaniciKayit" runat="server">
        <table id="tblProjeKullaniciKayit" class="tblProjeKayit">
            <tr>
                <td>
                    <asp:Literal Text="<%$ Resources:TmateResource,Domain%>" runat="server" />
                </td>
                <td>
                    <asp:DropDownList ID="drpDomains" CssClass="drpSelect2" DataValueField="ID" DataTextField="DOMAIN_NAME" AutoPostBack="true" OnSelectedIndexChanged="drpDomains_SelectedIndexChanged" Width="208px" runat="server">
                    </asp:DropDownList>
                    <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" ValidationGroup="vgMate" ControlToValidate="drpDomains" ForeColor="Red" InitialValue="0" runat="server" />
                </td>
            </tr>
            <tr>
                <td>
                    <asp:Literal Text="<%$ Resources:TmateResource, Proje %>" runat="server" />
                </td>
                <td>
                    <asp:DropDownList ID="drpProje" CssClass="drpSelect2" DataValueField="ID" Style="min-width: 162px; width: 208px" DataTextField="PROJECT_NAME" AutoPostBack="false" OnSelectedIndexChanged="drpProje_SelectedIndexChanged" runat="server">
                    </asp:DropDownList>
                    <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" ValidationGroup="vgMate" ControlToValidate="drpProje" ForeColor="Red" InitialValue="0" runat="server" />
                </td>
            </tr>
            <tr>
                <td>
                    <asp:Literal Text="<%$ Resources:TmateResource, Kullanici %>" runat="server" />
                </td>
                <td>
                    <asp:Panel ID="pnlAdGroups" runat="server">
                        <fieldset style="width:90%">
                            <legend>
                                <asp:Literal Text="<%$ Resources:TmateResource, MailGrubu %>" runat="server" /></legend>
                            <table>
                                <tr>
                                    <td style="width: 107px;">
                                        <asp:DropDownList ID="drpAdDomainType" OnSelectedIndexChanged="drpAdDomainType_SelectedIndexChanged" AutoPostBack="true" runat="server">
                                            <asp:ListItem Text="Seçiniz..." Value="0" />
                                            <asp:ListItem Text="DIGITURK" Value="DIGITURK" />
                                            <asp:ListItem Text="DIGITURKCC" Value="DIGITURKCC" />
                                        </asp:DropDownList>
                                    </td>
                                    <td>
                                        <asp:DropDownList ID="drp_AdPortalGroups" CssClass="drpAdPort drpSelect2" runat="server">
                                        </asp:DropDownList>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" ValidationGroup="vgMate" ControlToValidate="drpAdDomainType" ForeColor="Red" InitialValue="0" runat="server" />
                                    </td>
                                    <td>
                                        <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" ValidationGroup="vgMate" ControlToValidate="drp_AdPortalGroups" ForeColor="Red" InitialValue="0" runat="server" />
                                    </td>
                                </tr>
                            </table>
                        </fieldset>
                    </asp:Panel>
                </td>
            </tr>
        </table>
        <table id="tblAktifVeChck" class="tblAktifVeChck">
            <tr>
                <td>
                    <asp:CheckBox ID="chckAktif" Text="<%$ Resources:TmateResource, Aktif %>" runat="server" />
                </td>
            </tr>
            <tr>
                <td>
                    <asp:CheckBoxList ID="chckYazmaVeRapor" RepeatDirection="Horizontal" runat="server">
                        <asp:ListItem Text="<%$ Resources:TmateResource, YazmaYetkisi %>" Value="1" />
                        <asp:ListItem Text="<%$ Resources:TmateResource, RaporlamaYetkisi %>" Value="2" />
                    </asp:CheckBoxList>
                </td>
            </tr>
        </table>       
        <div id="divAdminKaydet" style="padding: 0 35%;">
            <uc1:AdminKaydet runat="server" ValidationGroup="vgMate" guncellemeUyarisi="Güncellemek istediğinize emin misiniz?" ID="AdminKaydet1" />
            <uc1:MsgBoxCtrl runat="server" ID="MsgBoxCtrl1" />
        </div>
    </asp:Panel>
    <asp:Panel ID="pnlGrid" runat="server">
        <table id="tblGrid" width="100%">
            <tr>
                <td>
                    <dx:ASPxGridView ID="grdProjectUsers" KeyFieldName="ID" Width="100%" runat="server">
                        <Columns>
                            <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, Domain %>" FieldName="DOMAIN_NAME" VisibleIndex="2"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, Proje %>" FieldName="PROJECT_NAME" VisibleIndex="3"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, AdGroupTipi %>" FieldName="AD_GROUP_TYPE" VisibleIndex="4"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, AdGroup %>" FieldName="AD_GROUP" VisibleIndex="5"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, YazmaYetkisi %>" FieldName="YAZMA_YETKISI" VisibleIndex="5"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, RaporlamaYetkisi %>" FieldName="RAPORLAMA_YETKISI" VisibleIndex="5"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, Duzenle %>" FieldName="" VisibleIndex="10">
                                <DataItemTemplate>
                                    <asp:LinkButton ID="lnkGuncelle" Text="<%$ Resources:TmateResource, Duzenle %>" OnClick="lnkduzenle_Click" CausesValidation="false" runat="server" />
                                </DataItemTemplate>
                            </dx:GridViewDataColumn>
                        </Columns>
                        <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" />
                        <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                        <SettingsBehavior AllowSelectSingleRowOnly="true" ConfirmDelete="true" />
                        <Styles>
                            <Header BackColor="#C60C30" ForeColor="White"></Header>
                        </Styles>
                    </dx:ASPxGridView>
                </td>
            </tr>
        </table>
    </asp:Panel>
</asp:Content>
<%--<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>--%>

