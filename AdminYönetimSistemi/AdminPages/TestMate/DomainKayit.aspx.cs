﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using AdminPages;
using CoreHelpers;
using Entities;
using WebCore;

namespace AracTakipSistemi.AdminPages.AlmTools
{
    public partial class DomainKayit : AdminAbstract
    {
        public string Username
        {
            get
            {
                return HttpContext.Current.Request.LogonUserIdentity.Name.Replace("DIGITURK\\", ""); // DTBGUNAY
            }
        }
        public long LoginId
        {
            get
            {
                return FormHelper.CoreHelper.GetKullaniciLoginId(Username);
            }
        }


        protected void Page_Load(object sender, EventArgs e)
        {
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            this.Master.SayfaBaslikAt("Domain Kaydet");
            if (!IsPostBack)
            {
                drpUsers.DataSource = FormHelper.AlmTools.DomainKaydetHelper.GetKullaniciList();
                drpUsers.DataBind();
            }            
        }
        public override void Grid_Doldur()
        {
            throw new NotImplementedException();
        }

        public override void Guncelle(int ID)
        {
            throw new NotImplementedException();
        }

        public override void Kaydet()
        {           
            Entities.ALM_DOMAINS nesnem = new Entities.ALM_DOMAINS();
            nesnem.DOMAIN = txtDomain.Text;
            nesnem.NAME_SURNAME = drpUsers.SelectedItem.Text;
            nesnem.CREATED = DateTime.Now;
            nesnem.CREATED_BY = ConvertionHelper.ConvertValue<int>(drpUsers.SelectedValue);
            
            string YeniID = PRepository<ALM_DOMAINS>.EntityKaydet("DT_WORKFLOW", nesnem);

        }

        public override void Kayit_Getir(int ID)
        {
            throw new NotImplementedException();
        }

        public override void Sil(int ID)
        {
            throw new NotImplementedException();
        }

        public override void Temizle()
        {
            throw new NotImplementedException();
        }
    }
}