﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="TmateStatusEntry.aspx.cs" Inherits="AracTakipSistemi.AdminPages.TestMate.TmateStatusEntry" %>

<%@ MasterType VirtualPath="~/Site.Master" %>
<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>

<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script src="../../Scripts/jquery-1.9.min.js"></script>
    <script src="../../Scripts/select2-4.1.0.min.js"></script>
    <link href="../../css/select2-4.1.0.min.css" rel="stylesheet" />

    <script type="text/javascript">
        $(document).ready(function () {
            $('.drp').select2()
        });


    </script>
    <style>
        table.clsStatusEntry {
            width: 100%;
        }

            table.clsStatusEntry th {
                width: 40%;
                text-align: left;
                padding-left: 150px;
            }

            table.clsStatusEntry td {
                /*width: 50%;*/
                text-align: left;
            }

            table.clsStatusEntry td select {
                width:175px;
            }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:Panel ID="pnlAll" runat="server">
        <asp:Panel ID="pnlStatusEntry" runat="server">
            <table id="tblStatusEntry" class="clsStatusEntry">
                <tr>
                    <th>
                        <asp:Literal Text="<%$ Resources:TmateResource, Domain %>" runat="server" />

                    </th>
                    <td>
                        <asp:DropDownList ID="drpDomain" DataValueField="ID" DataTextField="DOMAIN_NAME" CssClass="drp" OnSelectedIndexChanged="drpDomain_SelectedIndexChanged" AutoPostBack="true" runat="server">
                        </asp:DropDownList>
                        <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" ValidationGroup="vgMate" ControlToValidate="drpDomain" ForeColor="Red" InitialValue="0" Display="Dynamic" runat="server" />
                    </td>
                </tr>
                <tr>
                    <th>
                        <asp:Literal Text="<%$ Resources:TmateResource, Proje %>" runat="server" />
                    </th>
                    <td>
                        <asp:DropDownList ID="drpProject" DataValueField="ID" DataTextField="PROJECT_NAME" CssClass="drp" runat="server">
                        </asp:DropDownList>
                        <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" ValidationGroup="vgMate" ControlToValidate="drpProject" ForeColor="Red" InitialValue="0" Display="Dynamic" runat="server" />
                    </td>
                </tr>
                <tr>
                    <th>
                        <asp:Literal Text="<%$ Resources:TmateResource, StatusType %>" runat="server" />
                    </th>
                    <td>
                        <asp:DropDownList ID="drpStatusType" DataValueField="ID" DataTextField="STATUS_TYPE_NAME" CssClass="drp" runat="server">
                        </asp:DropDownList>
                        <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" ValidationGroup="vgMate" ControlToValidate="drpStatusType" ForeColor="Red" InitialValue="0" Display="Dynamic" runat="server" />
                    </td>
                </tr>
                <tr>
                    <th>
                        <asp:Literal Text="<%$ Resources:TmateResource, StatusNameTr %>" runat="server" />
                    </th>
                    <td>
                        <asp:TextBox ID="txtStatusValueTr" runat="server" />
                        <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" ValidationGroup="vgMate" ControlToValidate="txtStatusValueTr" ForeColor="Red" Display="Dynamic" runat="server" />
                    </td>
                </tr>
                <tr>
                    <th>
                        <asp:Literal Text="<%$ Resources:TmateResource, StatusNameEn %>" runat="server" />
                    </th>
                    <td>
                        <asp:TextBox ID="txtStatusValueEn" runat="server" />
                        <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" ValidationGroup="vgMate" ControlToValidate="txtStatusValueEn" ForeColor="Red" Display="Dynamic" runat="server" />
                    </td>
                </tr>
                <tr>
                    <th>
                        <asp:CheckBox ID="chckAktif" Text="<%$ Resources:TmateResource, Aktif %>" runat="server" />
                    </th>
                </tr>
            </table>
        </asp:Panel>
        <asp:Panel ID="pnlAdminKaydet" runat="server">
            <table id="tblAdminKaydet">
                <tr>
                    <td>
                        <uc1:AdminKaydet runat="server" guncellemeUyarisi="Güncellemek istediğinize emin misiniz?" ID="AdminKaydet1" />
                    </td>
                </tr>
            </table>
        </asp:Panel>
        <asp:Panel ID="pnlGrid" runat="server">
            <table id="tblGrid">
                <tr>
                    <td>
                        <dx:ASPxGridView ID="grdStatus" KeyFieldName="ID" Width="100%" runat="server">
                            <Columns>
                                <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, StatusType %>" FieldName="STATUS_TYPE_NAME" VisibleIndex="2"></dx:GridViewDataColumn>
                                <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, StatusNameTr %>" FieldName="STATUS_NAME_TR" VisibleIndex="3"></dx:GridViewDataColumn>
                                <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, StatusNameEn %>" FieldName="STATUS_NAME_EN" VisibleIndex="4"></dx:GridViewDataColumn>
                                <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, Proje %>" FieldName="PROJECT_NAME" VisibleIndex="5"></dx:GridViewDataColumn>
                                <dx:GridViewDataColumn Caption="" FieldName="DOMAIN_ID" Visible="false" VisibleIndex="6">
                                    <DataItemTemplate>
                                        <asp:HiddenField ID="hdnDomain" runat="server" />
                                    </DataItemTemplate>
                                </dx:GridViewDataColumn>
                                <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, Duzenle %>" FieldName="" VisibleIndex="10">
                                    <DataItemTemplate>
                                        <asp:LinkButton ID="lnkGuncelle" Text="<%$ Resources:TmateResource, Duzenle %>" OnClick="lnkduzenle_Click" Visible='<%# EditLinkVisible(Eval("STATUS_TYPE_NAME").ToString(),Eval("STATUS_NAME_EN").ToString(),Eval("STATUS_NAME_TR").ToString()) %>' CausesValidation="false" runat="server" />
                                    </DataItemTemplate>
                                </dx:GridViewDataColumn>
                            </Columns>
                            <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" />
                            <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                            <SettingsBehavior AllowSelectSingleRowOnly="true" ConfirmDelete="true" />
                            <Styles>
                                <Header BackColor="#C60C30" ForeColor="White"></Header>
                            </Styles>
                        </dx:ASPxGridView>
                    </td>
                </tr>
            </table>
        </asp:Panel>
    </asp:Panel>

</asp:Content>
<%--<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>--%>

