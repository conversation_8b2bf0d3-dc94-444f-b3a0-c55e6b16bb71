﻿using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using DevExpress.Web;
using Entities;
using FormHelper.YetkiHelper;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.TestMate
{
    public partial class TmateProjectUsers : AdminAbstract
    {

        public string Username
        {
            get
            {
                return HttpContext.Current.Request.LogonUserIdentity.Name.Replace("DIGITURK\\", ""); // DTBGUNAY
            }
        }

        public long LoginId
        {
            get
            {
                return FormHelper.CoreHelper.GetKullaniciLoginId(Username);
            }
        }
        protected void Page_Load(object sender, EventArgs e)
        {
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            this.Master.SayfaBaslikAt("Test Mate Project Users Entry");
            if (!IsPostBack)
            {
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
                //drpDomains.DataSource = FormHelper.TestMate.ProjeKullaniciKayitHelper.GetDomainsList();
                //drpDomains.DataBind();
                //drpDomains.SelectedValue = "0";
            }
            //else
            //{
            //    if ((Request.Form[txt_CREATED.UniqueID] != null) && (Request.Form[txt_CREATED.UniqueID] != ""))
            //    {
            //        try
            //        {
            //            cld_CREATED.SelectedDate = Convert.ToDateTime(Request.Form[txt_CREATED.UniqueID]);
            //            txt_CREATED.Text = Request.Form[txt_CREATED.UniqueID];
            //        }
            //        catch (Exception) { }
            //    }
            //    if ((Request.Form[txt_LAST_UPDATED.UniqueID] != null) && (Request.Form[txt_LAST_UPDATED.UniqueID] != ""))
            //    {
            //        try
            //        {
            //            cld_LAST_UPDATED.SelectedDate = Convert.ToDateTime(Request.Form[txt_LAST_UPDATED.UniqueID]);
            //            txt_LAST_UPDATED.Text = Request.Form[txt_LAST_UPDATED.UniqueID];
            //        }
            //        catch (Exception) { }
            //    }
            //}
            Grid_Doldur();
            if (FormHelper.CoreHelper.isEnglish())
            {
                ((Button)AdminKaydet1.FindControl("btnKaydet")).Text = "Add";
                ((Button)AdminKaydet1.FindControl("btnSil")).Text = "Delete";
                ((Button)AdminKaydet1.FindControl("btnGuncelle")).Text = "Update";
                ((Button)AdminKaydet1.FindControl("btnTemizle")).Text = "Cancel";
            }
        }

        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int IDdeger = ConvertionHelper.ConvertValue<int>(grdProjectUsers.GetRowValues(index, "ID"));           
            Kayit_Getir(IDdeger);
        }
        public override void Grid_Doldur()
        {
            grdProjectUsers.DataSource = FormHelper.TestMate.ProjeKullaniciKayitHelper.grdListe();
            grdProjectUsers.DataBind();
        }
        public override void Kaydet()
        {            
            Page.Validate();
            if (Page.IsValid)
            {
                if (FormHelper.TestMate.ProjeKullaniciKayitHelper.mukerrerKayitVarMi(drpDomains.SelectedValue, drpProje.SelectedValue, drpAdDomainType.SelectedItem.Text, drp_AdPortalGroups.SelectedItem.Text,0))
                {
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Fail", "Dublicate record entry!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Hata", "Aynı Kayıt İkinci Kez Girilemez", true);
                    }
                }
                else
                {
                    Entities.TMATE_PROJECT_USERS nesnem = new Entities.TMATE_PROJECT_USERS();
                    Esitle(nesnem);
                    string YeniID = PRepository<TMATE_PROJECT_USERS>.EntityKaydet("DT_WORKFLOW", nesnem);
                    nesnem = null;
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Info", "Entry is successful!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Bilgi", "Kayıt Edildi", false);
                    }

                }
                Temizle();               
            }


        }
        
        private void Esitle(Entities.TMATE_PROJECT_USERS nesnem)
        {
            nesnem.DOMAIN_ID = ConvertionHelper.ConvertValue<int>(drpDomains.SelectedValue);
            nesnem.PROJECT_ID = ConvertionHelper.ConvertValue<int>(drpProje.SelectedValue);
            nesnem.AD_GROUP = drp_AdPortalGroups.SelectedItem.Text;
            nesnem.AD_GROUP_TYPE = drpAdDomainType.SelectedItem.Text;           
            nesnem.AKTIF = chckAktif.Checked == true ? "1" : "0";
            nesnem.YAZMA_YETKISI = chckYazmaVeRapor.Items.FindByValue("1").Selected == true ? "1" : "0";
            nesnem.RAPORLAMA_YETKISI = chckYazmaVeRapor.Items.FindByValue("2").Selected == true ? "1" : "0";
            nesnem.CREATED = DateTime.Now;
            nesnem.CREATED_BY = LoginId;

        }
        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                if (FormHelper.TestMate.ProjeKullaniciKayitHelper.mukerrerKayitVarMi(drpDomains.SelectedValue, drpProje.SelectedValue, drpAdDomainType.SelectedItem.Text, drp_AdPortalGroups.SelectedItem.Text,ID))
                {
                    if(FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Fail", "Dublicate record entry!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Hata", "Aynı Kayıt İkinci Kez Girilemez", true);
                    }
                }
                else
                {
                    Entities.TMATE_PROJECT_USERS nesnem = PRepository<TMATE_PROJECT_USERS>.EntityGetir("DT_WORKFLOW", ID);
                    Esitle(nesnem);
                    nesnem.LAST_UPDATED = DateTime.Now;
                    nesnem.LAST_UPDATED_BY = LoginId;
                    PRepository<TMATE_PROJECT_USERS>.EntityUpdateEt("DT_WORKFLOW", nesnem);                    
                    nesnem = null;
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Info", "Update is successful!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Bilgi", "Kayıt Güncellendi", false);
                    }
                }
                Temizle();

            }
        }

        public override void Kayit_Getir(int ID)
        {
            Entities.TMATE_PROJECT_USERS nesnem = PRepository<TMATE_PROJECT_USERS>.EntityGetir("DT_WORKFLOW", ID);
            if (nesnem != null)
            {
                drpAdDomainType_SelectedIndexChanged(this, null);                          
                GenericIslemler.KutuEsitle(drpDomains, nesnem.DOMAIN_ID.ToString());
                int domainId = Convert.ToInt32(drpDomains.SelectedValue);
                drpProje.DataSource = FormHelper.TestMate.ProjeKullaniciKayitHelper.GetProjectsList(domainId);
                drpProje.DataBind();
                GenericIslemler.KutuEsitle(drpProje, nesnem.PROJECT_ID.ToString());
                GenericIslemler.KutuEsitle(drpAdDomainType, nesnem.AD_GROUP_TYPE.ToString());                
                GenericIslemler.KutuEsitle(drp_AdPortalGroups, nesnem.AD_GROUP.ToString());                
                chckYazmaVeRapor.Items.FindByValue("1").Selected = nesnem.YAZMA_YETKISI == "1" ? true : false;
                chckYazmaVeRapor.Items.FindByValue("2").Selected = nesnem.RAPORLAMA_YETKISI == "1" ? true : false;
                AdminKaydet1.RecordId = nesnem.ID;
                AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
                AdminKaydet1.btnSilButtonGizle();
            }
            nesnem = null;
        }
        public override void Sil(int ID)
        {
            try
            {
                PRepository<TMATE_PROJECT_USERS>.EntitySil("DT_WORKFLOW", ID);
                Temizle();
                this.Master.PopupGoster("Bilgi", "Kayıt Silindi", false);
            }
            catch (Exception)
            {
                this.Master.PopupGoster("Bilgi", "Kayıt silinemedi. Alt kırılım kayıtları mevcut olabilir.<br>Aktif seçeneğini kullanabilirsiniz.", true);
            }
        }
        public override void Temizle()
        {
            drpDomains.DataSource = FormHelper.TestMate.ProjeKullaniciKayitHelper.GetDomainsList();
            drpDomains.DataBind();
            drpDomains.SelectedValue = "0";
            drpProje.DataSource = FormHelper.TestMate.ProjeKullaniciKayitHelper.GetProjectsListAll();
            drpProje.DataBind();
            drpAdDomainType.SelectedValue = "0";
            drp_AdPortalGroups.SelectedValue = "0";
            chckYazmaVeRapor.ClearSelection();
            chckAktif.Checked = true;
            AdminKaydet1.RecordId = 0;
            Grid_Doldur();
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
        }

        protected void drpDomains_SelectedIndexChanged(object sender, EventArgs e)
        {
            int domainId = Convert.ToInt32(drpDomains.SelectedValue);
            drpProje.DataSource = FormHelper.TestMate.ProjeKullaniciKayitHelper.GetProjectsList(domainId);
            drpProje.DataBind();
        }

        protected void drpProje_SelectedIndexChanged(object sender, EventArgs e)
        {
            
        }

        protected void drpAdDomainType_SelectedIndexChanged(object sender, EventArgs e)
        {
            BuildAdPortalGrupList(drpAdDomainType.SelectedValue);
        }

        public void BuildAdPortalGrupList(string Domain)
        {
            try
            {
                var gruplist = FormHelper.TestMate.DomainKaydetHelper.GetAdGroupList(Domain);
                drp_AdPortalGroups.Items.Clear();
                drp_AdPortalGroups.Items.Add(new ListItem("---Seçiniz---", "0"));
                foreach (var item in gruplist)
                {
                    drp_AdPortalGroups.Items.Add(new ListItem(item.GroupName, item.GroupName));
                }
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }
    }
}




