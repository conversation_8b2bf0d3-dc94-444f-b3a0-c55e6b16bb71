﻿using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using DevExpress.Web;
using Entities;
using FormHelper.TestMate;
using FormHelper.YetkiHelper;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.TestMate
{
    public partial class TmateStatusEntry : AdminAbstract
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            this.Master.SayfaBaslikAt("Test Mate Status Entry");
            if (!IsPostBack)
            {
                Temizle();
                
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
            }
            //else
            //{
            //    if ((Request.Form[txt_CREATED.UniqueID] != null) && (Request.Form[txt_CREATED.UniqueID] != ""))
            //    {
            //        try
            //        {
            //            cld_CREATED.SelectedDate = Convert.ToDateTime(Request.Form[txt_CREATED.UniqueID]);
            //            txt_CREATED.Text = Request.Form[txt_CREATED.UniqueID];
            //        }
            //        catch (Exception) { }
            //    }
            //    if ((Request.Form[txt_UPDATED.UniqueID] != null) && (Request.Form[txt_UPDATED.UniqueID] != ""))
            //    {
            //        try
            //        {
            //            cld_UPDATED.SelectedDate = Convert.ToDateTime(Request.Form[txt_UPDATED.UniqueID]);
            //            txt_UPDATED.Text = Request.Form[txt_UPDATED.UniqueID];
            //        }
            //        catch (Exception) { }
            //    }
            //}
            Grid_Doldur();
            if (FormHelper.CoreHelper.isEnglish())
            {
                ((Button)AdminKaydet1.FindControl("btnKaydet")).Text = "Add";
                ((Button)AdminKaydet1.FindControl("btnSil")).Text = "Delete";
                ((Button)AdminKaydet1.FindControl("btnGuncelle")).Text = "Update";
                ((Button)AdminKaydet1.FindControl("btnTemizle")).Text = "Cancel";
            }            
        }

        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(grdStatus.GetRowValues(index, "ID"));
            int deger2 = ConvertionHelper.ConvertValue<int>(grdStatus.GetRowValues(index, "DOMAIN_ID"));
            GenericIslemler.KutuEsitle(drpDomain, deger2.ToString());
            Kayit_Getir(deger);
        }
        public override void Grid_Doldur()
        {
            grdStatus.DataSource = FormHelper.TestMate.StatusHelper.GetGridInfos();
            grdStatus.DataBind();
        }
        public override void Kaydet()
        {
            Page.Validate();
            if (Page.IsValid)
            {
                if (FormHelper.TestMate.StatusHelper.mukerrerKayitVarMi(drpProject.SelectedValue,drpStatusType.SelectedValue,txtStatusValueTr.Text,txtStatusValueEn.Text,0))
                {
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Fail", "Dublicate record entry!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Hata", "Aynı Kayıt İkinci Kez Girilemez", true);
                    }
                }
                else
                {
                    Entities.TMATE_STATUS nesnem = new Entities.TMATE_STATUS();
                    Esitle(nesnem);
                    nesnem.CREATED = DateTime.Now;
                    nesnem.CREATED_BY = LoginId;
                    string YeniID = PRepository<TMATE_STATUS>.EntityKaydet("DT_WORKFLOW", nesnem);
                    nesnem = null;                    
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Info", "Entry is successful!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Bilgi", "Kayıt Edildi", false);
                    }
                }
                Temizle();
            }
        }
        private void Esitle(Entities.TMATE_STATUS nesnem)
        {
            nesnem.STATUS_TYPE = ConvertionHelper.ConvertValue<int>(drpStatusType.SelectedValue);
            nesnem.STATUS_NAME_TR = txtStatusValueTr.Text;
            nesnem.STATUS_NAME_EN = txtStatusValueEn.Text;
            nesnem.PROJECT_ID = ConvertionHelper.ConvertValue<int>(drpProject.SelectedValue);
            nesnem.AKTIF = ConvertionHelper.ConvertValue<int>(chckAktif.Checked).ToString();            
        }
        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                if (FormHelper.TestMate.StatusHelper.mukerrerKayitVarMi(drpProject.SelectedValue, drpStatusType.SelectedValue, txtStatusValueTr.Text, txtStatusValueEn.Text, ID))
                {
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Fail", "Dublicate record entry!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Hata", "Aynı Kayıt İkinci Kez Girilemez", true);
                    }
                }
                else
                {
                    Entities.TMATE_STATUS nesnem = PRepository<TMATE_STATUS>.EntityGetir("DT_WORKFLOW", ID);
                    Esitle(nesnem);
                    nesnem.UPDATED = DateTime.Now;
                    nesnem.UPDATED_BY = LoginId;
                    PRepository<TMATE_STATUS>.EntityUpdateEt("DT_WORKFLOW", nesnem);                    
                    nesnem = null;
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Info", "Update is successful!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Bilgi", "Kayıt Güncellendi", false);
                    }
                }
                Temizle();

            }
        }

        public override void Kayit_Getir(int ID)
        {
            Entities.TMATE_STATUS nesnem = PRepository<TMATE_STATUS>.EntityGetir("DT_WORKFLOW", ID);
            if (nesnem != null)
            {
                GenericIslemler.KutuEsitle(drpStatusType, nesnem.STATUS_TYPE.ToString());
                txtStatusValueTr.Text = nesnem.STATUS_NAME_TR;
                txtStatusValueEn.Text = nesnem.STATUS_NAME_EN;
                GenericIslemler.KutuEsitle(drpProject, nesnem.PROJECT_ID.ToString());
                chckAktif.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.AKTIF));                                               
                AdminKaydet1.RecordId = nesnem.ID;
                AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
                AdminKaydet1.btnSilButtonGizle();
            }
            nesnem = null;
        }
        public override void Sil(int ID)
        {
            try
            {
                PRepository<TMATE_STATUS>.EntitySil("DT_WORKFLOW", ID);
                Temizle();
                this.Master.PopupGoster("Bilgi", "Kayıt Silindi", false);
            }
            catch (Exception)
            {
                this.Master.PopupGoster("Bilgi", "Kayıt silinemedi. Alt kırılım kayıtları mevcut olabilir.<br>Aktif seçeneğini kullanabilirsiniz.", true);
            }
        }
        public override void Temizle()
        {
            drpStatusType.DataSource = FormHelper.TestMate.StatusHelper.GetStatusTypes();
            drpStatusType.DataBind();
            drpDomain.DataSource = FormHelper.TestMate.StatusHelper.GetDomainsList();
            drpDomain.DataBind();            
            txtStatusValueTr.Text = "";
            txtStatusValueEn.Text = "";
            chckAktif.Checked = true;
            drpProject.DataSource = FormHelper.TestMate.StatusHelper.GetProjectsListAll();
            drpProject.DataBind();
            drpProject.SelectedValue = "0";         
            AdminKaydet1.RecordId = 0;
            Grid_Doldur();
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
        }

        protected void drpDomain_SelectedIndexChanged(object sender, EventArgs e)
        {
            drpProject.DataSource = FormHelper.TestMate.StatusHelper.GetProjectsList(drpDomain.SelectedValue);
            drpProject.DataBind();
        }
        public bool EditLinkVisible(string statusType,string nameEn,string nameTr)
        {
            bool donen = true;
            switch (statusType)
            {
                case "TEST":
                    if (nameEn == SpecialStatusNames.Repair.ToString() && nameTr == SpecialStatusNames.Onarım.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.Ready.ToString() && nameTr == SpecialStatusNames.Hazır.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.Imported.ToString() && nameTr == SpecialStatusNames.İçeri_Alınmış.ToString().Replace("_"," "))
                        donen = false;
                    else if (nameEn == SpecialStatusNames.Design.ToString() && nameTr == SpecialStatusNames.Tasarım.ToString())
                        donen = false;
                    break;
                case "TEST SET":
                    if (nameEn == SpecialStatusNames.Open.ToString() && nameTr == SpecialStatusNames.Açık.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.Closed.ToString() && nameTr == SpecialStatusNames.Kapalı.ToString())
                        donen = false;
                    break;
                case "TEST INSTANCE":
                    if (nameEn == SpecialStatusNames.Not_Completed.ToString().Replace("_", " ") && nameTr == SpecialStatusNames.Tamamlanmamış.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.No_Run.ToString().Replace("_", " ") && nameTr == SpecialStatusNames.Koşulmamış.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.Failed.ToString() && nameTr == SpecialStatusNames.Hatalı.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.Blocked.ToString() && nameTr == SpecialStatusNames.Engellenmiş.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.Passed.ToString() && nameTr == SpecialStatusNames.Geçti.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.N_A.ToString().Replace("_","/") && nameTr == SpecialStatusNames.Uygun_Değil.ToString().Replace("_"," "))
                        donen = false;
                    break;
                case "TEST STEP RUN":
                    if (nameEn == SpecialStatusNames.Not_Completed.ToString().Replace("_", " ") && nameTr == SpecialStatusNames.Tamamlanmamış.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.No_Run.ToString().Replace("_", " ") && nameTr == SpecialStatusNames.Koşulmamış.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.Failed.ToString() && nameTr == SpecialStatusNames.Hatalı.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.Blocked.ToString() && nameTr == SpecialStatusNames.Engellenmiş.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.Passed.ToString() && nameTr == SpecialStatusNames.Geçti.ToString())
                        donen = false;
                    else if (nameEn == SpecialStatusNames.N_A.ToString().Replace("_", "/") && nameTr == SpecialStatusNames.Uygun_Değil.ToString().Replace("_", " "))
                        donen = false;
                    break;
            }
            return donen;
        }
    }
}