﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="TmateDomainsAdmins.aspx.cs" Inherits="AracTakipSistemi.AdminPages.TestMate.TmateDomainsAdmins" %>

<%@ MasterType VirtualPath="~/Site.Master" %>
<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="ajaxToolkit" %>


<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <link rel="stylesheet" href="../../css/select2.min.css" />
    <script type="text/javascript" src="../../Scripts/select2.min.js"></script>

    <script type="text/javascript">
        $(function () {
            $('.drpSelect2').select2();
        });
    </script>
    <asp:Panel ID="pnlAll" runat="server">
        <asp:Panel ID="pnlDomainKaydet" runat="server">
            <table id="tblDomainKaydet" style="width:100%">
                <colgroup>
                    <col span="1" />
                    <col span="1" />
                    <col span="1" />
                </colgroup>
                <tr>
                    <td>
                        <asp:Literal Text="<%$ Resources:TmateResource, Domain %>" runat="server" />
                    </td>
                    <td style="max-width:max-content; padding-left:25px;">
                        <asp:TextBox ID="txtDomain" MaxLength="240" runat="server" />
                        <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" ForeColor="Red" ValidationGroup="vgMate" ControlToValidate="txtDomain" Display="Dynamic" runat="server" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <asp:Literal Text="<%$ Resources:TmateResource, Kullanici %>" runat="server" />
                    </td>
                    <td style="max-width:max-content; padding-left:25px;">
                        <asp:Panel ID="pnlAdGroups" runat="server">
                            <fieldset style="width:90%">
                                <legend>
                                    <asp:Literal Text="<%$ Resources:TmateResource, MailGrubu %>" runat="server" />
                                </legend>
                                <table>
                                    <tr>
                                        <td>
                                            <asp:DropDownList ID="drpAdDomainType" Width="107px" OnSelectedIndexChanged="drpAdDomainType_SelectedIndexChanged" AutoPostBack="true" runat="server">
                                                <asp:ListItem Text="Seçiniz..." Value="0" />
                                                <asp:ListItem Text="DIGITURK" Value="DIGITURK" />
                                                <asp:ListItem Text="DIGITURKCC" Value="DIGITURKCC" />
                                            </asp:DropDownList>
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="drp_AdPortalGroups" CssClass="drpAdPort drpSelect2" runat="server"></asp:DropDownList>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="max-width: 107px">
                                            <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" ForeColor="Red" ControlToValidate="drpAdDomainType" Display="Dynamic" InitialValue="0" ValidationGroup="vgMate" runat="server" />
                                        </td>
                                        <td>
                                            <asp:RequiredFieldValidator ErrorMessage="<%$ Resources:TmateResource, ReqValError %>" ForeColor="Red" ControlToValidate="drp_AdPortalGroups" Display="Dynamic" InitialValue="0" ValidationGroup="vgMate" runat="server" />
                                        </td>
                                    </tr>
                                </table>
                            </fieldset>
                        </asp:Panel>
                    </td>
                </tr>
                <tr>
                    <td>
                        <asp:CheckBox ID="chkAktif" Text="<%$ Resources:TmateResource, Aktif %>" runat="server" />
                    </td>
                </tr>


            </table>
            <table>
                <tr>
                    <td>
                        <uc1:AdminKaydet runat="server" ID="AdminKaydet1" guncellemeUyarisi="Güncellemek istediğinize emin misiniz?" validasyonGrubu="vgMate" />
                    </td>
                </tr>
            </table>
        </asp:Panel>
        <asp:Panel ID="pnlGrdDomain" runat="server">
            <table style="width: 100%">
                <tr>
                    <td>
                        <dx:ASPxGridView ID="grdDomains" KeyFieldName="ID" Width="100%" runat="server">
                            <Columns>
                                <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, Domain %>" FieldName="DOMAIN_NAME" VisibleIndex="2"></dx:GridViewDataColumn>
                                <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, AdGroupType %>" FieldName="AD_GROUP_TYPE" VisibleIndex="3"></dx:GridViewDataColumn>
                                <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, AdGroup %>" FieldName="AD_GROUP_NAME" VisibleIndex="4"></dx:GridViewDataColumn>
                                <dx:GridViewDataColumn Caption="<%$ Resources:TmateResource, Aktif %>" FieldName="AKTIF" VisibleIndex="5"></dx:GridViewDataColumn>
                                <dx:GridViewDataColumn Caption="" FieldName="" VisibleIndex="10">
                                    <DataItemTemplate>
                                        <asp:LinkButton ID="lnkGuncelle" Text="<%$ Resources:TmateResource, Duzenle %>" OnClick="lnkduzenle_Click" runat="server" />
                                    </DataItemTemplate>
                                </dx:GridViewDataColumn>
                            </Columns>
                            <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" />
                            <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                            <SettingsBehavior AllowSelectSingleRowOnly="true" ConfirmDelete="true" />
                            <Styles>
                                <Header BackColor="#C60C30" ForeColor="White"></Header>
                            </Styles>
                        </dx:ASPxGridView>
                    </td>
                </tr>
            </table>
        </asp:Panel>
    </asp:Panel>

</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>
