﻿using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using DevExpress.Web;
using Entities;
using FormHelper.TestMate;
using FormHelper.YetkiHelper;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.TestMate
{
    public partial class TmateProjects : AdminAbstract
    {
        public string Username
        {
            get
            {
                return HttpContext.Current.Request.LogonUserIdentity.Name.Replace("DIGITURK\\", ""); // DTBGUNAY
            }
        }
        public long LoginId
        {
            get
            {
                return FormHelper.CoreHelper.GetKullaniciLoginId(Username);
            }
        }
        protected void Page_Load(object sender, EventArgs e)
        {
           
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            this.Master.SayfaBaslikAt("Test Mate Project Entry");
            if (!IsPostBack)
            {
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
                drpDomains.DataSource = FormHelper.TestMate.ProjeKayitHelper.GetDomainsList();
                drpDomains.DataBind();
            }
            //else
            //{
            //    if ((Request.Form[txt_CREATED.UniqueID] != null) && (Request.Form[txt_CREATED.UniqueID] != ""))
            //    {
            //        try
            //        {
            //            cld_CREATED.SelectedDate = Convert.ToDateTime(Request.Form[txt_CREATED.UniqueID]);
            //            txt_CREATED.Text = Request.Form[txt_CREATED.UniqueID];
            //        }
            //        catch (Exception) { }
            //    }
            //    if ((Request.Form[txt_LAST_UPDATED.UniqueID] != null) && (Request.Form[txt_LAST_UPDATED.UniqueID] != ""))
            //    {
            //        try
            //        {
            //            cld_LAST_UPDATED.SelectedDate = Convert.ToDateTime(Request.Form[txt_LAST_UPDATED.UniqueID]);
            //            txt_LAST_UPDATED.Text = Request.Form[txt_LAST_UPDATED.UniqueID];
            //        }
            //        catch (Exception) { }
            //    }
            //}
            Grid_Doldur();
            if (FormHelper.CoreHelper.isEnglish())
            {
                ((Button)AdminKaydet1.FindControl("btnKaydet")).Text = "Add";
                ((Button)AdminKaydet1.FindControl("btnSil")).Text = "Delete";
                ((Button)AdminKaydet1.FindControl("btnGuncelle")).Text = "Update";
                ((Button)AdminKaydet1.FindControl("btnTemizle")).Text = "Cancel";
            }
        }

        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(grdProjects.GetRowValues(index, "ID"));
            Kayit_Getir(deger);
        }
        public override void Grid_Doldur()
        {
            grdProjects.DataSource = ProjeKayitHelper.grdListe();
            grdProjects.DataBind();
        }
        public override void Kaydet()
        {
            
            Page.Validate();
            if (Page.IsValid)
            {
                if(FormHelper.TestMate.ProjeKayitHelper.mukerrerKayitVarMi(drpDomains.SelectedValue, txtProje.Text,0))
                {
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Fail", "Dublicate record entry!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Hata", "Aynı Kayıt İkinci Kez Girilemez", true);
                    }
                }
                else
                {
                    Entities.TMATE_PROJECTS nesnem = new Entities.TMATE_PROJECTS();
                    Esitle(nesnem);
                    string YeniID = PRepository<TMATE_PROJECTS>.EntityKaydet("DT_WORKFLOW", nesnem);                    
                    nesnem = null;
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Info", "Entry is successful!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Bilgi", "Kayıt Edildi", false);
                    }
                    if (!string.IsNullOrEmpty(YeniID))
                        ProjeKayitHelper.ProjeDurumlariKaydet(ConvertionHelper.ConvertValue<int>(YeniID),LoginId);
                }
                Temizle();
            }
            
        }
        private void Esitle(Entities.TMATE_PROJECTS nesnem)
        {
            nesnem.DOMAIN_ID = ConvertionHelper.ConvertValue<int>(drpDomains.SelectedValue);
            nesnem.PROJECT_NAME = txtProje.Text;
            nesnem.NAME_SURNAME = FormHelper.CoreHelper.GetKullaniciNameSurname(LoginId);
            nesnem.AKTIF = chckAktif.Checked == true ? "1" : "0";
            nesnem.CREATED = DateTime.Now;
            nesnem.CREATED_BY = LoginId;
        }
        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                if (FormHelper.TestMate.ProjeKayitHelper.mukerrerKayitVarMi(drpDomains.SelectedValue, txtProje.Text,ID))
                {
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Fail", "Dublicate record entry!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Hata", "Aynı Kayıt İkinci Kez Girilemez", true);
                    }
                }
                else
                {
                    Entities.TMATE_PROJECTS nesnem = PRepository<TMATE_PROJECTS>.EntityGetir("DT_WORKFLOW", ID);
                    Esitle(nesnem);
                    nesnem.LAST_UPDATED = DateTime.Now;
                    nesnem.LAST_UPDATED_BY = LoginId;
                    PRepository<TMATE_PROJECTS>.EntityUpdateEt("DT_WORKFLOW", nesnem);                   
                    nesnem = null;
                    if (FormHelper.CoreHelper.isEnglish())
                    {
                        this.Master.PopupGoster("Info", "Update is successful!", true);
                    }
                    else
                    {
                        this.Master.PopupGoster("Bilgi", "Kayıt Güncellendi", false);
                    }
                }
                Temizle();

            }
        }

        public override void Kayit_Getir(int ID)
        {
            Entities.TMATE_PROJECTS nesnem = PRepository<TMATE_PROJECTS>.EntityGetir("DT_WORKFLOW", ID);
            if (nesnem != null)
            {
                GenericIslemler.KutuEsitle(drpDomains, nesnem.DOMAIN_ID.ToString());
                txtProje.Text = nesnem.PROJECT_NAME;
                chckAktif.Checked = nesnem.AKTIF == "1" ? true : false;                                
                AdminKaydet1.RecordId = nesnem.ID;
                AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
                AdminKaydet1.btnSilButtonGizle();
            }
            nesnem = null;
        }
        public override void Sil(int ID)
        {
            try
            {
                PRepository<TMATE_PROJECTS>.EntitySil("DT_WORKFLOW", ID);
                Temizle();
                this.Master.PopupGoster("Bilgi", "Kayıt Silindi", false);
            }
            catch (Exception)
            {
                this.Master.PopupGoster("Bilgi", "Kayıt silinemedi. Alt kırılım kayıtları mevcut olabilir.<br>Aktif seçeneğini kullanabilirsiniz.", true);
            }
        }
        public override void Temizle()
        {
            drpDomains.DataSource = FormHelper.TestMate.ProjeKayitHelper.GetDomainsList();
            drpDomains.DataBind();
            txtProje.Text = "";
            chckAktif.Checked = true;
            AdminKaydet1.RecordId = 0;
            Grid_Doldur();
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
        }

        


    }
}