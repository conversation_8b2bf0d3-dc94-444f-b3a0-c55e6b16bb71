﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Hata.aspx.cs" Inherits="AracTakipSistemi.AdminPages.Hata" %>
<%@ Import Namespace="Resources" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title><%=DigiportAdminResource.AccessDeniedTitle %></title>
    
    <link rel="stylesheet" type="text/css" href="/ddlevelsfiles/ddlevelsmenu-base.css" />
    <link rel="stylesheet" type="text/css" href="/ddlevelsfiles/ddlevelsmenu-topbar.css" />
    <%--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />--%>
    <link href="/css/fontawesome_5.14.15.css" rel="stylesheet" />
    <style>
        :root {
            --primary-purple: #5c2d91;
            --light-purple: #7b4eaf;
            --dark-purple: #4b2173;
            --purple-bg: #f9f7fc;
        }

        .hatalbl {
            font-size: 28px;
            color: var(--primary-purple);
            font-weight: 600;
            margin-bottom: 20px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .error-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 20px auto;
            max-width: 600px;
            border-top: 5px solid var(--primary-purple);
            animation: fadeIn 0.5s ease-in-out;
        }

        .error-icon {
            font-size: 60px;
            color: var(--primary-purple);
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .error-message {
            color: #555;
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .btn-primary {
            background-color: var(--primary-purple);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: var(--dark-purple);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .footer {
            background-color: var(--primary-purple);
            color: white;
            padding: 15px;
            border-radius: 0 0 8px 8px;
            font-weight: 500;
            margin-top: 20px;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 768px) {
            .error-container {
                margin: 10px;
                padding: 20px;
            }
        }
    </style>
</head>
<body style="background-color: var(--purple-bg);">
    <link rel="stylesheet" href="/css/style.css?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>" type="text/css" />
    <form id="form1" runat="server" style="height: 100vh; display: flex; justify-content:center; align-items:center">
        <div>
            <table border="0" width="100%">
                <tr>
                    <td>
                        <div class="error-container">
                            <div align="center">
                                <i class="fas fa-exclamation-circle error-icon"></i>
                                <asp:Label ID="Label1" CssClass="hatalbl" runat="server" Text="<%$ Resources:DigiportAdminResource, AccessDeniedMessage %>"></asp:Label>

                                <div class="error-message">
                                    <p><%=DigiportAdminResource.AccessDeniedHelp %></p>
                                    <p><%=DigiportAdminResource.AccessDeniedBySystem %></p>
                                </div>

                                <asp:Button ID="btnAnaSayfa" runat="server" Text="<%$ Resources:DigiportAdminResource, ReturnToHomePage %>" OnClick="btnAnaSayfa_Click" CssClass="btn-primary" />

                                <div class="footer">
                                    <%=DigiportAdminResource.Copyright %>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </form>
</body>
</html>
