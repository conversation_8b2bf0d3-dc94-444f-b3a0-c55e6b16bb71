﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Resources;

namespace AracTakipSistemi.AdminPages
{
    public partial class Hata : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Set default error message
                string errorMessage = DigiportAdminResource.AccessDeniedMessage;

                // Get custom error message from session if available
                if (Session["ErrorPageMessage"] != null)
                {
                    errorMessage = Session["ErrorPageMessage"].ToString();
                    Session["ErrorPageMessage"] = null;
                }

                // Set error message
                Label1.Text = errorMessage;

                // Log error
                LogError(errorMessage);
            }
        }

        /// <summary>
        /// Logs error information
        /// </summary>
        private void LogError(string errorMessage)
        {
            try
            {
                // Get information about the error
                string username = HttpContext.Current?.User?.Identity?.Name ?? DigiportAdminResource.Unknown;
                string requestedUrl = HttpContext.Current?.Request?.UrlReferrer?.ToString() ?? DigiportAdminResource.Unknown;
                string ipAddress = HttpContext.Current?.Request?.UserHostAddress ?? DigiportAdminResource.Unknown;

                // Log to application event log
                string logMessage = string.Format(DigiportAdminResource.AccessDeniedLogMessage, username, requestedUrl, ipAddress, errorMessage);
                Debug.WriteLine(logMessage);

                // TODO: Add database logging if needed
            }
            catch (Exception ex)
            {
                Debug.WriteLine(string.Format(DigiportAdminResource.LogErrorMessage, ex.Message));
            }
        }

        /// <summary>
        /// Redirects to the home page
        /// </summary>
        protected void btnAnaSayfa_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/Anasayfa.aspx");
        }
    }
}