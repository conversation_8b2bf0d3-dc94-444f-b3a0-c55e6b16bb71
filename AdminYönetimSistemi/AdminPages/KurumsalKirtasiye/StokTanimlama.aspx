﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="StokTanimlama.aspx.cs" Inherits="AracTakipSistemi.AdminPages.KurumsalKirtasiye.StokTanimlama" %>


<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="ajaxToolkit" %>

<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>
<%@ MasterType VirtualPath="~/Site.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <link rel="stylesheet" href="../../css/select2.min.css" />
    <script type="text/javascript" src="../../Scripts/select2.min.js"></script>

    <script type="text/javascript">
        $(function () {
            $("[id*=drpStokGrubu]").select2();
            $(".drop").select2();
        });
    </script>
    <style>
        .dxeTextBoxDefaultWidthSys {
            border: none;
            background-color: transparent;
            width: 178px !important;
            margin-left: -6px;
        }

        .select2-container {
            width: 171px !important;
        }
    </style>
    <div style="margin: auto;">
        <asp:Panel ID="Pnl1" GroupingText=" " runat="server" Width="100%" Style="border-style: none; text-align: left;">
            <table style="width: 100%; margin-left: 15px;">
                <tr>
                    <td>Sene</td>
                    <td class="alignLeft" style="padding: 5px 0 5px 0;">
                        <asp:DropDownList ID="drp_Sene" Width="155" runat="server" CssClass="drop" AutoPostBack="true" OnSelectedIndexChanged="drp_Sene_SelectedIndexChanged"></asp:DropDownList>
                    </td>
                </tr>
                <tr>
                    <td>Stok Grubu</td>
                    <td class="alignLeft" style="padding: 5px 0 5px 0;">
                        <asp:DropDownList ID="drpStokGrubu" AutoPostBack="true" Width="155px" CssClass="drop" runat="server" OnSelectedIndexChanged="drpStokGrubu_SelectedIndexChanged"></asp:DropDownList>
                        <asp:RequiredFieldValidator ID="rfv_StokGrupAdı" runat="server" ErrorMessage="Stok Grubu Seçiniz" ValidationGroup="vg0" ControlToValidate="drpStokGrubu" Display="Dynamic" InitialValue="0" ForeColor="#C60C30"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td>Stok Kodu</td>
                    <td class="alignLeft" style="padding: 5px 0 5px 0;">
                        <asp:TextBox ID="txt_STOK_KODU" runat="server" Width="150" autocomplete="off" CssClass="textbox" MaxLength="100"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfv_STOK_KODU" runat="server" ControlToValidate="txt_STOK_KODU" Display="Dynamic" ErrorMessage="Stok Kodu Alanı Boş Bırakılamaz" ForeColor="#C60C30" ValidationGroup="vg0"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td>Stok Adı</td>
                    <td class="alignLeft" style="padding: 5px 0 5px 0;">
                        <asp:TextBox ID="txt_STOK_ADI" runat="server" Width="150" autocomplete="off" CssClass="textbox" MaxLength="300"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfv_STOK_ADI" runat="server" ControlToValidate="txt_STOK_ADI" Display="Dynamic" ErrorMessage="Stok Adı Alanı Boş Bırakılamaz" ForeColor="#C60C30" ValidationGroup="vg0"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td>Birim Fiyatı</td>
                    <td class="alignLeft" style="padding: 5px 0 5px 0;">
                        <table style="width: 100%;">
                            <tr>
                                <td class="alignLeft" style="width: 150px;">
                                    <dx:ASPxTextBox ID="txt_BIRIM_FIYATI" autocomplete="off" CssClass="textbox" ValidationSettings-ErrorDisplayMode="None" Width="155" Height="20" MaxLength="20" runat="server">
                                        <MaskSettings Mask="<0..999999999g>.<00..99>" />
                                    </dx:ASPxTextBox>
                                </td>
                                <td class="alignLeft">
                                    <asp:RegularExpressionValidator ID="regexval_BIRIM_FIYATI" runat="server" ControlToValidate="txt_BIRIM_FIYATI" ValidationGroup="vg0" ForeColor="#C60C30" ErrorMessage="Birim Fiyatı Boş Bırakılamaz" ValidationExpression="^(?!0,00)[0-9]+(.\d{3})*?(\,[0-9][0-9]?)?$"></asp:RegularExpressionValidator>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>Para Birimi</td>
                    <td class="alignLeft" style="padding: 5px 0 5px 0;">
                        <asp:DropDownList ID="drpParaBirimi" Width="150" runat="server" CssClass="drop"></asp:DropDownList>
                    </td>
                </tr>
                <tr>
                    <td>Aktif</td>
                    <td style="padding: 5px 0 5px 0;">
                        <asp:CheckBox ID="chkAktif" runat="server" /></td>
                </tr>
                <tr>
                    <td colspan="2">
                        <div style="max-width: 500px;">
                            <uc1:AdminKaydet runat="server" ID="AdminKaydet1" silmeUyarisi="Pasif etmek istediğinize emin misiniz ?" validasyonGrubu="vg0" UyariGoster="true" guncellemeUyarisi="Güncellemek istediğinize emin misiniz ?" />
                        </div>
                    </td>
                </tr>
            </table>
        </asp:Panel>
        <div style="margin-top: 5px;">
            <dx:ASPxGridView ID="gridViewStok" runat="server" SettingsBehavior-AllowGroup="true" KeyFieldName="ID;SENE" Width="100%" OnAfterPerformCallback="ASPxGridView_AfterPerformCallback">
                <Columns>
                    <dx:GridViewDataColumn Caption="Stok Grup Adı" FieldName="STOK_GRUP_ADI" VisibleIndex="2" />
                    <dx:GridViewDataColumn Caption="Stok Kodu" FieldName="STOK_KODU" VisibleIndex="3" />
                    <dx:GridViewDataColumn Caption="Stok Adı" FieldName="STOK_ADI" VisibleIndex="4" />
                    <dx:GridViewDataColumn Caption="Birim Fiyatı" FieldName="BIRIM_FIYAT" VisibleIndex="5" Width="150" />
                    <dx:GridViewDataColumn Caption="Para Birimi" FieldName="PARA_BIRIMI" VisibleIndex="6" Width="50" />
                    <dx:GridViewDataColumn Caption="Aktif" FieldName="AKTIF_DURUM" VisibleIndex="7" Width="50" />
                    <dx:GridViewDataTextColumn FieldName="Düzenle" VisibleIndex="8" Width="50">
                        <DataItemTemplate>
                            <asp:LinkButton ID="lnkduzenle" runat="server" Text="Düzenle" OnClick="lnkduzenle_Click"></asp:LinkButton>
                        </DataItemTemplate>
                    </dx:GridViewDataTextColumn>
                </Columns>
                <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" />
                <SettingsPager PageSize="50"></SettingsPager>
                <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                <Styles>
                    <Header BackColor="#C60C30" ForeColor="White"></Header>
                    <Cell>
                        <border bordercolor="#42145F" />
                    </Cell>
                </Styles>
            </dx:ASPxGridView>
        </div>
    </div>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>

