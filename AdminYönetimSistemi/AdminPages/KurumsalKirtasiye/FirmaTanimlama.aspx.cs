﻿using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using DevExpress.Web;
using Entities;
using FormHelper.KurumsalKirtasiye;
using FormHelper.YetkiHelper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.KurumsalKirtasiye
{
    public partial class FirmaTanimlama : AdminAbstract
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            ((Button)AdminKaydet1.FindControl("btnSil")).Text = "Pasif Et";
            if (!IsPostBack)
            {
                ((SiteMaster)Master).SayfaBaslikAt("Kırtasiye Firma Tanımlama");
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
            }
        }
        protected void ASPxGridView_AfterPerformCallback(object sender, ASPxGridViewAfterPerformCallbackEventArgs e)
        {
            Grid_Doldur();
        }

        public override void Grid_Doldur()
        {
            try
            {
                gridViewFirma.DataSource = FirmaHelper.GridListele();
                gridViewFirma.DataBind();
            }
            catch
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Tablo Sorgu Hata Verdi.", true);
            }
        }
        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(gridViewFirma.GetRowValues(index, "ID"));
            Kayit_Getir(deger);
        }
        public override void Kaydet()
        {
            Page.Validate();
            if (Page.IsValid)
            {
                try
                {
                    KRT_FIRMA nesnem = new KRT_FIRMA();
                    Esitle(nesnem, true);
                    if (FirmaHelper.KayitEklenebilirmi(nesnem, ref ((SiteMaster)Master).UyariMesaji))
                    {
                        string YeniID = PRepository<KRT_FIRMA>.EntityKaydet("DT_WORKFLOW", nesnem);
                        Temizle();
                        nesnem = null;
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Edildi", false);
                    }
                    else
                        throw new CustomException(((SiteMaster)Master).UyariMesaji);
                }
                catch (CustomException ex)
                {
                    ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
                }
                catch
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Kayıt Hata Verdi.", true);
                }
            }
        }
        private void Esitle(KRT_FIRMA nesnem, bool IsInsert)
        {
            nesnem.FIRMA_ADI = txt_FIRMA_ADI.Text.Trim();
            nesnem.AKTIF = ConvertionHelper.ConvertValue<int>(chkAktif.Checked).ToString();
            if (IsInsert)
            {
                nesnem.CREATED_BY = this.LoginId;
                nesnem.CREATED = DateTime.Now;
            }
            else
            {
                nesnem.UPDATED_BY = this.LoginId;
                nesnem.UPDATED = DateTime.Now;
            }
        }
        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                try
                {
                    KRT_FIRMA nesnem = PRepository<KRT_FIRMA>.EntityGetir("DT_WORKFLOW", ID);
                    Esitle(nesnem, false);
                    if (FirmaHelper.KayitGuncellenebilirmi(nesnem, ref ((SiteMaster)Master).UyariMesaji))
                    {
                        PRepository<KRT_FIRMA>.EntityUpdateEt("DT_WORKFLOW", nesnem);
                        Temizle();
                        nesnem = null;
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Güncellendi", false);
                    }
                    else
                        throw new CustomException(((SiteMaster)Master).UyariMesaji);
                }
                catch (CustomException ex)
                {
                    ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
                }
                catch
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Güncelleme Hata Verdi.", true);
                }
            }
        }

        public override void Kayit_Getir(int ID)
        {
            try
            {
                KRT_FIRMA nesnem = PRepository<KRT_FIRMA>.EntityGetir("DT_WORKFLOW", ID);
                if (nesnem != null)
                {
                    txt_FIRMA_ADI.Text = nesnem.FIRMA_ADI;
                    chkAktif.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.AKTIF));
                    AdminKaydet1.RecordId = nesnem.ID;
                    AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
                    //((Button)AdminKaydet1.FindControl("btnSil")).Style.Add("visibility", chkAktif.Checked ? "visible" : "hidden");
                    ((Button)AdminKaydet1.FindControl("btnSil")).Style.Add("display", "none");
                }
                nesnem = null;
            }
            catch 
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Hata Meydana Geldi.", true);
            }
        }
        public override void Sil(int ID)
        {
            try
            {
                if (FirmaHelper.KayitSilinebilirmi(ID, ref ((SiteMaster)Master).UyariMesaji))
                {
                    PRepository<KRT_FIRMA>.EntitySil("DT_WORKFLOW", ID);
                    Temizle();
                    ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Silindi", false);
                }
                else
                    throw new CustomException(((SiteMaster)Master).UyariMesaji);
            }
            catch (CustomException ex)
            {
                ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
            }
            catch
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Silme İşlemi Hata Verdi.", true);
            }
        }
        public override void Temizle()
        {
            txt_FIRMA_ADI.Text = "";
            chkAktif.Checked = true;
            AdminKaydet1.RecordId = 0;
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
            Grid_Doldur();
        }
    }
}