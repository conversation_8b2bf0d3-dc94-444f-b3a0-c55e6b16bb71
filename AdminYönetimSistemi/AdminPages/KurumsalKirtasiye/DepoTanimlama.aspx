﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="DepoTanimlama.aspx.cs" Inherits="AracTakipSistemi.AdminPages.KurumsalKirtasiye.DepoTanimlama" %>

<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>
<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="margin: auto;">
        <asp:Panel ID="Pnl1" GroupingText=" " runat="server" Width="100%" Style="border-style: none;">
            <table style="width: 100%;">
                <tr>
                    <td>Depo Adı</td>
                    <td>
                        <asp:TextBox ID="txt_DEPO_ADI" MaxLength="100"  autocomplete="off" CssClass="textbox" runat="server"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfv_DEPO_ADI" runat="server" ErrorMessage="Depo Adı Alanı Boş Bırakılamaz" ForeColor="#C60C30" ValidationGroup="vg0" ControlToValidate="txt_DEPO_ADI" Display="Dynamic"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td>Aktif</td>
                    <td><asp:CheckBox ID="chkAktif" runat="server" /></td>
                </tr>
                <tr>
                    <td colspan="2">
                        <a class="linkStyle1" href="StokGrupTanimlama.aspx">Stok Grup Tanımlama</a>
                        <a class="linkStyle1" href="FirmaTanimlama.aspx">Firma Tanımlama</a>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <uc1:AdminKaydet runat="server" ID="AdminKaydet1" silmeUyarisi="Pasif etmek istediğinize emin misiniz ?" validasyonGrubu="vg0" UyariGoster="true" guncellemeUyarisi="Güncellemek istediğinize emin misiniz ?" />
                    </td>
                </tr>
            </table>
        </asp:Panel>
        <div style="margin-top: 5px;">
            <dx:ASPxGridView ID="gridViewDepo" runat="server" SettingsBehavior-AllowGroup="true" KeyFieldName="ID" Width="100%" OnAfterPerformCallback="ASPxGridView_AfterPerformCallback">
                <Columns>
                    <dx:GridViewDataColumn Caption="Depo Adı" FieldName="DEPO_ADI" VisibleIndex="2" />
                    <dx:GridViewDataColumn Caption="Aktif" FieldName="AKTIF_DURUM" VisibleIndex="3" />
                    <dx:GridViewDataTextColumn FieldName="Düzenle" VisibleIndex="4">
                        <DataItemTemplate>
                            <asp:LinkButton ID="lnkduzenle" runat="server" Text="Düzenle" OnClick="lnkduzenle_Click"></asp:LinkButton>
                        </DataItemTemplate>
                    </dx:GridViewDataTextColumn>
                </Columns>
                <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" />
                <SettingsPager PageSize="50"></SettingsPager>
                <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                <Styles>
                    <Header BackColor="#C60C30" ForeColor="White"></Header>
                    <Cell>
                        <Border BorderColor="#42145F" />
                    </Cell>
                </Styles>
            </dx:ASPxGridView>
        </div>
    </div>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>
