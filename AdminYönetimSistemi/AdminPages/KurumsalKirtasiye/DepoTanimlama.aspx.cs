﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using Entities;
using FormHelper;
using DevExpress.Web;
using FormHelper.YetkiHelper;
using FormHelper.KurumsalKirtasiye;


namespace AracTakipSistemi.AdminPages.KurumsalKirtasiye
{
    public partial class DepoTanimlama : AdminAbstract
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            ((Button)AdminKaydet1.FindControl("btnSil")).Text = "Pasif Et";
            if (!IsPostBack)
            {
                ((SiteMaster)Master).SayfaBaslikAt("Kırtasiye Depo Tanımlama");
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
            }
        }
        protected void ASPxGridView_AfterPerformCallback(object sender, ASPxGridViewAfterPerformCallbackEventArgs e)
        {
            Grid_Doldur();
        }

        public override void Grid_Doldur()
        {
            try
            {
                gridViewDepo.DataSource=DepoHelper.GridListele();
                gridViewDepo.DataBind();
            }
            catch
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Tablo Sorgu Hata Verdi.", true);
            }
        }
        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(gridViewDepo.GetRowValues(index, "ID"));
            Kayit_Getir(deger);
        }
        public override void Kaydet()
        {
            Page.Validate();
            if (Page.IsValid)
            {
                try
                {
                    KRT_DEPO nesnem = new KRT_DEPO();
                    Esitle(nesnem, true);
                    if (DepoHelper.KayitEklenebilirmi(nesnem, ref ((SiteMaster)Master).UyariMesaji))
                    {
                        string YeniID = PRepository<KRT_DEPO>.EntityKaydet("DT_WORKFLOW", nesnem);
                        Temizle();
                        nesnem = null;
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Edildi", false);
                    }
                    else
                        throw new CustomException(((SiteMaster)Master).UyariMesaji);
                }
                catch (CustomException ex)
                {
                    ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
                }
                catch
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Kayıt Hata Verdi.", true);
                }
            }
        }
        private void Esitle(KRT_DEPO nesnem, bool IsInsert)
        {
            nesnem.DEPO_ADI = txt_DEPO_ADI.Text.Trim();
            nesnem.AKTIF = ConvertionHelper.ConvertValue<int>(chkAktif.Checked).ToString();
            if (IsInsert)
            {
                nesnem.CREATED_BY = this.LoginId;
                nesnem.CREATED = DateTime.Now;
            }
            else
            {
                nesnem.UPDATED_BY = this.LoginId;
                nesnem.UPDATED = DateTime.Now;
            }
        }
        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                try
                {
                    KRT_DEPO nesnem = PRepository<KRT_DEPO>.EntityGetir("DT_WORKFLOW", ID);
                    Esitle(nesnem, false);
                    if (DepoHelper.KayitGuncellenebilirmi(nesnem, ref ((SiteMaster)Master).UyariMesaji))
                    {
                        PRepository<KRT_DEPO>.EntityUpdateEt("DT_WORKFLOW", nesnem);
                        Temizle();
                        nesnem = null;
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Güncellendi", false);
                    }
                    else
                        throw new CustomException(((SiteMaster)Master).UyariMesaji);
                }
                catch (CustomException ex)
                {
                    ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
                }
                catch
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Güncelleme Hata Verdi.", true);
                }
            }
        }
        public override void Kayit_Getir(int ID)
        {
            try
            {
                KRT_DEPO nesnem = PRepository<KRT_DEPO>.EntityGetir("DT_WORKFLOW", ID);
                if (nesnem != null)
                {
                    txt_DEPO_ADI.Text = nesnem.DEPO_ADI;
                    chkAktif.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.AKTIF));
                    AdminKaydet1.RecordId = nesnem.ID;
                    AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
                    //((Button)AdminKaydet1.FindControl("btnSil")).Style.Add("visibility", chkAktif.Checked?"visible":"hidden");
                    ((Button)AdminKaydet1.FindControl("btnSil")).Style.Add("display", "none");
                }
                nesnem = null;
            }
            catch 
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Hata Meydana Geldi.", true);
            }
        }
        public override void Sil(int ID)
        {
            try
            {
                if (DepoHelper.KayitSilinebilirmi(ID, ref ((SiteMaster)Master).UyariMesaji))
                {
                    PRepository<KRT_DEPO>.EntitySil("DT_WORKFLOW", ID);
                    Temizle();
                    ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Silindi", false);
                }
                else
                    throw new CustomException(((SiteMaster)Master).UyariMesaji);
            }
            catch (CustomException ex)
            {
                ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
            }
            catch
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Silme İşlemi Hata Verdi.", true);
            }
        }
        public override void Temizle()
        {
            txt_DEPO_ADI.Text = "";
            AdminKaydet1.RecordId = 0;
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
            chkAktif.Checked = true;
            Grid_Doldur();
        }

    }
}