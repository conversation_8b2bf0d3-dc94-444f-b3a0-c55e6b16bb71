﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using Entities;
using FormHelper;
using DevExpress.Web;
using FormHelper.YetkiHelper;
using FormHelper.KurumsalKirtasiye;

namespace AracTakipSistemi.AdminPages.KurumsalKirtasiye
{
    public partial class StokGrupTanimlama : AdminAbstract
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            ((Button)AdminKaydet1.FindControl("btnSil")).Text = "Pasif Et";
            if (!IsPostBack)
            {
                ((SiteMaster)Master).SayfaBaslikAt("Kırtasiye Stok Grup Tanımlama");
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
            }
        }
        protected void ASPxGridView_AfterPerformCallback(object sender, ASPxGridViewAfterPerformCallbackEventArgs e)
        {
            Grid_Doldur();
        }
        public override void Grid_Doldur()
        {
            try
            {
                gridViewStokGrup.DataSource = StokGrupHelper.GridListele();
                gridViewStokGrup.DataBind();
            }
            catch
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Tablo Sorgu Hata Verdi.", true);
            }
        }
        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(gridViewStokGrup.GetRowValues(index, "ID"));
            Kayit_Getir(deger);
        }
        public override void Kaydet()
        {
            Page.Validate();
            if (Page.IsValid)
            {
                try
                {
                    KRT_STOK_GRUP nesnem = new KRT_STOK_GRUP();
                    Esitle(nesnem, true);
                    if (StokGrupHelper.KayitEklenebilirmi(nesnem, ref ((SiteMaster)Master).UyariMesaji))
                    {
                        string YeniID = PRepository<KRT_STOK_GRUP>.EntityKaydet("DT_WORKFLOW", nesnem);
                        Temizle();
                        nesnem = null;
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Edildi", false);
                    }
                    else
                        throw new CustomException(((SiteMaster)Master).UyariMesaji);
                }
                catch (CustomException ex)
                {
                    ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
                }
                catch
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Kayıt Hata Verdi.", true);
                }
            }
        }
        private void Esitle(KRT_STOK_GRUP nesnem, bool IsInsert)
        {
            nesnem.STOK_GRUP_ADI = txt_STOK_GRUP_ADI.Text.Trim();
            nesnem.AKTIF = ConvertionHelper.ConvertValue<int>(chkAktif.Checked).ToString();
            nesnem.TALEP_FORMUNDA_GOZUKUR = ConvertionHelper.ConvertValue<int>(chk_TALEP_FORMUNDA_GOZUKUR.Checked).ToString();
            if (nesnem.TALEP_FORMUNDA_GOZUKUR == "1")
                nesnem.OZEL_GRUP = ConvertionHelper.ConvertValue<int>(chk_Ozel_Grup.Checked).ToString();
            else
                nesnem.OZEL_GRUP = "0";
            if (IsInsert)
            {
                nesnem.CREATED_BY = this.LoginId;
                nesnem.CREATED = DateTime.Now;
            }
            else
            {
                nesnem.UPDATED_BY = this.LoginId;
                nesnem.UPDATED = DateTime.Now;
            }
        }
        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                try
                {
                    KRT_STOK_GRUP nesnem = PRepository<KRT_STOK_GRUP>.EntityGetir("DT_WORKFLOW", ID);
                    Esitle(nesnem, false);
                    if (StokGrupHelper.KayitGuncellenebilirmi(nesnem, ref ((SiteMaster)Master).UyariMesaji))
                    {
                        PRepository<KRT_STOK_GRUP>.EntityUpdateEt("DT_WORKFLOW", nesnem);
                        Temizle();
                        nesnem = null;
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Güncellendi", false);
                    }
                    else
                        throw new CustomException(((SiteMaster)Master).UyariMesaji);
                }
                catch (CustomException ex)
                {
                    ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
                }
                catch (Exception ex)
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Güncelleme Hata Verdi.", true);
                }
            }
        }

        public override void Kayit_Getir(int ID)
        {
            try
            {
                KRT_STOK_GRUP nesnem = PRepository<KRT_STOK_GRUP>.EntityGetir("DT_WORKFLOW", ID);
                if (nesnem != null)
                {
                    txt_STOK_GRUP_ADI.Text = nesnem.STOK_GRUP_ADI;
                    chkAktif.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.AKTIF));
                    chk_TALEP_FORMUNDA_GOZUKUR.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.TALEP_FORMUNDA_GOZUKUR));
                    if (chk_TALEP_FORMUNDA_GOZUKUR.Checked)
                    {
                        chk_Ozel_Grup.Enabled = true;
                        chk_Ozel_Grup.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.OZEL_GRUP));
                    }
                    else
                    {
                        chk_Ozel_Grup.Enabled = false;
                        chk_Ozel_Grup.Checked = false;
                    }
                    AdminKaydet1.RecordId = nesnem.ID;
                    AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
                    //((Button)AdminKaydet1.FindControl("btnSil")).Style.Add("visibility", chkAktif.Checked ? "visible" : "hidden");
                    ((Button)AdminKaydet1.FindControl("btnSil")).Style.Add("display", "none");
                }
                nesnem = null;
            }
            catch
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Hata Meydana Geldi.", true);
            }
        }
        public override void Sil(int ID)
        {
            try
            {
                if (DepoHelper.KayitSilinebilirmi(ID, ref ((SiteMaster)Master).UyariMesaji))
                {
                    PRepository<KRT_STOK_GRUP>.EntitySil("DT_WORKFLOW", ID);
                    Temizle();
                    ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Silindi", false);
                }
                else
                    throw new CustomException(((SiteMaster)Master).UyariMesaji);
            }
            catch (CustomException ex)
            {
                ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
            }
            catch
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Silme İşlemi Hata Verdi.", true);
            }
        }
        public override void Temizle()
        {
            txt_STOK_GRUP_ADI.Text = "";
            chk_TALEP_FORMUNDA_GOZUKUR.Checked = false;
            chk_Ozel_Grup.Checked = false;
            chk_Ozel_Grup.Enabled = false;
            chkAktif.Checked = true;
            AdminKaydet1.RecordId = 0;
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
            Grid_Doldur();
        }

        protected void chk_TALEP_FORMUNDA_GOZUKUR_CheckedChanged(object sender, EventArgs e)
        {
            if (chk_TALEP_FORMUNDA_GOZUKUR.Checked)
            {

                chk_Ozel_Grup.Enabled = true;
            }
            else
            {
                chk_Ozel_Grup.Enabled =chk_Ozel_Grup.Checked= false;
            }
        }
    }

}