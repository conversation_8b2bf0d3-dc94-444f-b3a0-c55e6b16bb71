﻿using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using DevExpress.Web;
using Entities;
using FormHelper.KurumsalKirtasiye;
using FormHelper.YetkiHelper;
using System;
using System.Collections.Generic;
using Oracle.DataAccess.Client;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.KurumsalKirtasiye
{
    public partial class StokTanimlama : AdminAbstract
    {
        int sene;
        int stokGrupID;
        protected void Page_Load(object sender, EventArgs e)
        {
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            ((Button)AdminKaydet1.FindControl("btnSil")).Text = "Pasif Et";
            if (!IsPostBack)
            {
                ((SiteMaster)Master).SayfaBaslikAt("Kırtasiye Stok Tanımlama");
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
            }
        }

        protected void drp_Sene_SelectedIndexChanged(object sender, EventArgs e)
        {
            Grid_Doldur();
        }
        protected void drpStokGrubu_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (((Button)AdminKaydet1.FindControl("btnKaydet")).Enabled)
                Grid_Doldur();
        }
        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(gridViewStok.GetRowValues(index, "ID"));
            Kayit_Getir(deger);
        }
        protected void ASPxGridView_AfterPerformCallback(object sender, ASPxGridViewAfterPerformCallbackEventArgs e)
        {
            Grid_Doldur();
        }
        public override void Kaydet()
        {
            Page.Validate();
            if (Page.IsValid)
            {
                try
                {
                    KRT_STOK_DETAY nesnem = new KRT_STOK_DETAY();
                    Esitle(nesnem, true);
                    if (StokDetayHelper.KayitEklenebilirmi(nesnem, ref ((SiteMaster)Master).UyariMesaji))
                    {
                        string YeniID = PRepository<KRT_STOK_DETAY>.EntityKaydet("DT_WORKFLOW", nesnem);
                        Temizle();
                        nesnem = null;
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Edildi", false);
                    }
                    else
                        throw new CustomException(((SiteMaster)Master).UyariMesaji);
                }
                catch (CustomException ex)
                {
                    ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
                }
                catch
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Kayıt Hata Verdi.", true);
                }
            }
        }
        private void Esitle(KRT_STOK_DETAY nesnem, bool IsInsert)
        {
            nesnem.SENE = ConvertionHelper.ConvertValue<decimal>(drp_Sene.SelectedItem.Value);
            nesnem.STOK_GRUP_ID = ConvertionHelper.ConvertValue<decimal>(drpStokGrubu.SelectedItem.Value);
            nesnem.STOK_KODU = txt_STOK_KODU.Text.Trim();
            nesnem.STOK_ADI = txt_STOK_ADI.Text.Trim();
            nesnem.BIRIM_FIYAT = GenericIslemler.DecimalKayitIcinAyarla(txt_BIRIM_FIYATI.Text.Trim());
            nesnem.PARA_BIRIMI = drpParaBirimi.SelectedItem.Value;
            nesnem.AKTIF = ConvertionHelper.ConvertValue<int>(chkAktif.Checked).ToString();
            if (IsInsert)
            {
                nesnem.CREATED_BY = this.LoginId;
                nesnem.CREATED = DateTime.Now;
            }
            else
            {
                nesnem.UPDATED_BY = this.LoginId;
                nesnem.UPDATED = DateTime.Now;
            }
        }

        public override void Temizle()
        {
            StokDetayHelper.StokSeneDoldur(drp_Sene);
            drp_Sene.SelectedIndex = 0;
            StokGrupHelper.StokGrubuDoldur(drpStokGrubu);
            GenericIslemler.KutuEsitle(drpStokGrubu, "0");
            txt_BIRIM_FIYATI.Text = "0,00";
            StokDetayHelper.ParaBirimiDoldur(drpParaBirimi);
            drpParaBirimi.SelectedIndex = 0;
            txt_STOK_ADI.Text = txt_STOK_KODU.Text = "";
            chkAktif.Checked = true;
            AdminKaydet1.RecordId = 0;
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
            Grid_Doldur();
        }

        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                try
                {
                    KRT_STOK_DETAY nesnem = PRepository<KRT_STOK_DETAY>.EntityGetir("DT_WORKFLOW", ID);
                    string eskiStokKodu = nesnem.STOK_KODU;
                    string eskiStokAdi = nesnem.STOK_ADI;
                    Esitle(nesnem, false);
                    if (StokDetayHelper.KayitGuncellenebilirmi(nesnem, eskiStokKodu, eskiStokAdi, ref ((SiteMaster)Master).UyariMesaji))
                    {
                        PRepository<KRT_STOK_DETAY>.EntityUpdateEt("DT_WORKFLOW", nesnem);
                        Temizle();
                        nesnem = null;
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Güncellendi", false);
                    }
                    else
                        throw new CustomException(((SiteMaster)Master).UyariMesaji);
                }
                catch (CustomException ex)
                {
                    ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
                }
                catch
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Güncelleme Hata Verdi.", true);
                }
            }
        }

        public override void Kayit_Getir(int ID)
        {
            try
            {
                KRT_STOK_DETAY nesnem = PRepository<KRT_STOK_DETAY>.EntityGetir("DT_WORKFLOW", ID);
                if (nesnem != null)
                {
                    chkAktif.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.AKTIF));
                    GenericIslemler.KutuEsitle(drp_Sene, nesnem.SENE.ToString());
                    GenericIslemler.KutuEsitle(drpStokGrubu, nesnem.STOK_GRUP_ID.ToString());
                    txt_STOK_ADI.Text = nesnem.STOK_ADI;
                    txt_STOK_KODU.Text = nesnem.STOK_KODU;
                    txt_BIRIM_FIYATI.Text = nesnem.BIRIM_FIYAT.ToString();
                    GenericIslemler.KutuEsitle(drpParaBirimi, nesnem.PARA_BIRIMI);
                    AdminKaydet1.RecordId = nesnem.ID;
                    AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
                    //((Button)AdminKaydet1.FindControl("btnSil")).Style.Add("visibility", chkAktif.Checked ? "visible" : "hidden");
                    ((Button)AdminKaydet1.FindControl("btnSil")).Style.Add("display", "none");
                }
                nesnem = null;
            }
            catch
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Hata Meydana Geldi.", true);
            }
        }

        public override void Grid_Doldur()
        {
            try
            {
                sene = ConvertionHelper.ConvertValue<int>(drp_Sene.SelectedItem.Value);
                stokGrupID = ConvertionHelper.ConvertValue<int>(drpStokGrubu.SelectedItem.Value);
                gridViewStok.DataSource = StokDetayHelper.GridListele(sene, stokGrupID);
                gridViewStok.DataBind();

                for (int index = 0; index < gridViewStok.VisibleRowCount; index++)
                {
                    sene = ConvertionHelper.ConvertValue<int>(gridViewStok.GetRowValues(index, "SENE").ToString());
                    if (sene < new StokDetayHelper().SonMalzemeSenesi)
                    {
                        LinkButton lb = gridViewStok.FindRowCellTemplateControl(index, gridViewStok.Columns["Düzenle"] as GridViewDataColumn, "lnkDuzenle") as LinkButton;
                        if(lb!=null)
                        lb.Visible = false;
                    }
                }
            }
            catch
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Tablo Sorgu Hata Verdi.", true);
            }
        }

        public override void Sil(int ID)
        {
            try
            {
                if (StokDetayHelper.KayitSilinebilirmi(ID, ref ((SiteMaster)Master).UyariMesaji))
                {
                    PRepository<KRT_STOK_DETAY>.EntitySil("DT_WORKFLOW", ID);
                    Temizle();
                    ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Silindi", false);
                }
                else
                    throw new CustomException(((SiteMaster)Master).UyariMesaji);
            }
            catch (CustomException ex)
            {
                ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
            }
            catch
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Silme İşlemi Hata Verdi.", true);
            }
        }


    }
}