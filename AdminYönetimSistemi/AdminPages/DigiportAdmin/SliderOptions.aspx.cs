﻿using CoreHelpers;
using Entities.DigiportAdmin;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.DigiportAdmin
{
    public partial class SliderOptions : DigiportSecurePage
    {
        int MID = 0;
        private string Username
        {
            get
            {
                return HttpContext.Current.Request.LogonUserIdentity.Name.Replace("DIGITURK\\", "");
            }
        }
        private long LoginId
        {
            get
            {
                return FormHelper.CoreHelper.GetKullaniciLoginId(Username);
            }
        }
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!String.IsNullOrEmpty(Request.QueryString["MID"]) && CoreHelpers.GenericIslemler.IsNumeric(Request.QueryString["MID"]))
            {
                MID = Convert.ToInt32(Request.QueryString["MID"]);
            }
            else
            {
                Session["ErrorPageMessage"] = Resources.DigiportAdminResource.TipBelirsiz;
                Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
                return;
            }
            if (!(
                ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerID"].Split('-').Contains(MID.ToString())
                ||
                ConfigurationManager.AppSettings["DigiportAdminAnasayfaSolSagKucukBannerID"].Split('-').Contains(MID.ToString())
                ||
                ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"].Split('-').Contains(MID.ToString())
                ||
                ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"].Split('-').Contains(MID.ToString())
                ||
                ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"].Split('-').Contains(MID.ToString())
                ))
            {
                Session["ErrorPageMessage"] = Resources.DigiportAdminResource.TipYanlis;
                Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
                return;
            }
            this.Title = Resources.DigiportAdminResource.SliderSecenekleri;
            if (!IsPostBack && !IsCallback)
            {
                LoadDropdowns();
                LoadData();
            }
        }

        private void LoadDropdowns()
        {
            foreach (var action in Enum.GetValues(FormHelper.CoreHelper.isEnglish() ? typeof(DigiportMenuDisplayHelpers.Base.EffectType) : typeof(DigiportMenuDisplayHelpers.Base.EffectType)))
            {
                int value = (int)action;
                string name = action.ToString().Replace("_", " ");
                drpSliderEfektTipi.Items.Add(new ListItem(name, value.ToString()));
            }

            if (
                ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"].Split('-').Contains(MID.ToString())
                ||
                ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"].Split('-').Contains(MID.ToString())
                ||
                ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"].Split('-').Contains(MID.ToString())
                )
            {
                foreach (var action in Enum.GetValues(FormHelper.CoreHelper.isEnglish() ? typeof(DigiportMenuDisplayHelpers.Base.TransitionEasing2) : typeof(DigiportMenuDisplayHelpers.Base.TransitionEasing2)))
                {
                    int value = (int)action;
                    string name = action.ToString().Replace("_", " ");
                    drpSliderTransitionEasing.Items.Add(new ListItem(name, value.ToString()));
                }
            }
            else
            {
                foreach (var action in Enum.GetValues(FormHelper.CoreHelper.isEnglish() ? typeof(DigiportMenuDisplayHelpers.Base.TransitionEasing) : typeof(DigiportMenuDisplayHelpers.Base.TransitionEasing)))
                {
                    int value = (int)action;
                    string name = action.ToString().Replace("_", " ");
                    drpSliderTransitionEasing.Items.Add(new ListItem(name, value.ToString()));
                }
            }

            foreach (var action in Enum.GetValues(FormHelper.CoreHelper.isEnglish() ? typeof(DigiportMenuDisplayHelpers.Base.TitleMode) : typeof(DigiportMenuDisplayHelpers.Base.TitleMode_Tr)))
            {
                int value = (int)action;
                string name = action.ToString().Replace("_", " ");
                drpSliderTitleMode.Items.Add(new ListItem(name, value.ToString()));
            }

            SortDropDown(drpSliderEfektTipi);
            SortDropDown(drpSliderTransitionEasing);
            SortDropDown(drpSliderTitleMode);

            drpSliderEfektTipi.Items.Insert(0, new ListItem(FormHelper.CoreHelper.isEnglish() ? "Please Select" : "Seçiniz", "-1") { Selected = true });
            drpSliderTransitionEasing.Items.Insert(0, new ListItem(FormHelper.CoreHelper.isEnglish() ? "Please Select" : "Seçiniz", "-1") { Selected = true });
            drpSliderTitleMode.Items.Insert(0, new ListItem(FormHelper.CoreHelper.isEnglish() ? "Please Select" : "Seçiniz", "-1") { Selected = true });
        }

        private void SortDropDown(DropDownList drp)
        {
            List<ListItem> items = drp.Items.Cast<ListItem>().ToList();
            items = items.OrderBy(item => item.Text).ToList();
            drp.Items.Clear();
            drp.Items.AddRange(items.ToArray());
        }

        private void LoadData()
        {
            try
            {
                bool isDebugMode = System.Configuration.ConfigurationManager.AppSettings["debugMode"] == "true";
                string domain = isDebugMode ? ConfigurationManager.AppSettings["DigiflowTestDomain"] : ConfigurationManager.AppSettings["DigiflowLiveDomain"];
                DIGIPORT_ADMIN_SLIDER_OPTIONS entity = PRepository<DIGIPORT_ADMIN_SLIDER_OPTIONS>.EntityGetir("DT_WORKFLOW", "select * from DT_WORKFLOW.DIGIPORT_ADMIN_SLIDER_OPTIONS where MENU_NAME_ID=:MENU_NAME_ID", new Oracle.DataAccess.Client.OracleParameter[] {
                  new Oracle.DataAccess.Client.OracleParameter("MENU_NAME_ID",MID)
                });
                if (entity.ID > 0)
                {
                    txtSliderWidth.Text = entity.SLIDE_WIDTH.ToString();
                    txtSliderHeight.Text = entity.SLIDE_HEIGHT.ToString();
                    drpSliderEfektTipi.SelectedIndex = drpSliderEfektTipi.Items.IndexOf(drpSliderEfektTipi.Items.FindByValue(entity.EFFECT_TYPE.ToString()));
                    drpSliderTransitionEasing.SelectedIndex = drpSliderTransitionEasing.Items.IndexOf(drpSliderTransitionEasing.Items.FindByValue(entity.TRANSITION_EASING.ToString()));
                    drpSliderTitleMode.SelectedIndex = drpSliderTitleMode.Items.IndexOf(drpSliderTitleMode.Items.FindByValue(entity.TITLE_MODE.ToString()));
                    chkShowProgressBar.Checked = entity.SHOW_PROGRESS_BAR == "1";
                    chkAutoSlideActive.Checked = entity.AUTO_SLIDE_ACTIVE == "1";
                    chkShowThumbnails.Checked = entity.SHOW_THUMBNAILS == "1";
                    txtSliderAutoSlideInterval.Text = entity.AUTO_SLIDE_INTERVAL.ToString();
                    txtSliderTransitionDuration.Text = entity.TRANSITION_DURATION.ToString();
                    if (
                        ConfigurationManager.AppSettings["DigiportAdminAnasayfaSolSagKucukBannerID"].Split('-').Contains(MID.ToString())
                        )
                    {
                        chkShowThumbnails.Enabled = false;
                        SetVisibilityForThumbnail(false);
                    }
                    else if (
                        ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerID"].Split('-').Contains(MID.ToString())
                        ||
                        ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"].Split('-').Contains(MID.ToString())
                        ||
                        ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"].Split('-').Contains(MID.ToString())
                        ||
                        ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"].Split('-').Contains(MID.ToString())
                        )
                    {
                        chkShowThumbnails.Enabled = true;
                        chkShowThumbnails.Checked = entity.SHOW_THUMBNAILS == "1";
                        SetVisibilityForThumbnail(chkShowThumbnails.Checked);
                        txtThumbnailWidth.Text = entity.THUMBNAIL_WIDTH.HasValue ? entity.THUMBNAIL_WIDTH.Value.ToString() : "";
                        txtThumbnailHeight.Text = entity.THUMBNAIL_HEIGHT.HasValue ? entity.THUMBNAIL_HEIGHT.Value.ToString() : "";
                    }
                }
                else
                {
                    if (
                        ConfigurationManager.AppSettings["DigiportAdminAnasayfaSolSagKucukBannerID"].Split('-').Contains(MID.ToString())
                        )
                    {
                        txtSliderWidth.Text = ConfigurationManager.AppSettings["DigiportAdminAnasayfaSolSagkucukBannerIzinliBoyut"].Split('x')[0];
                        txtSliderHeight.Text = ConfigurationManager.AppSettings["DigiportAdminAnasayfaSolSagkucukBannerIzinliBoyut"].Split('x')[1];
                        txtThumbnailWidth.Text = "";
                        txtThumbnailHeight.Text = "";
                        chkShowThumbnails.Checked = false;
                        chkShowThumbnails.Enabled = false;
                        SetVisibilityForThumbnail(false);
                    }
                    else if (
                        ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerID"].Split('-').Contains(MID.ToString())
                        ||
                        ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"].Split('-').Contains(MID.ToString())
                        ||
                        ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"].Split('-').Contains(MID.ToString())
                        ||
                        ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"].Split('-').Contains(MID.ToString())
                        )
                    {
                        if (ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerID"].Split('-').Contains(MID.ToString()))
                        {
                            txtSliderWidth.Text = ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerIzinliBoyut"].Split('x')[0];
                            txtSliderHeight.Text = ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerIzinliBoyut"].Split('x')[1];
                            txtThumbnailWidth.Text = ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerThumbnailIzinliBoyut"].Split('x')[0];
                            txtThumbnailHeight.Text = ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerThumbnailIzinliBoyut"].Split('x')[1];
                        }
                        else if (ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"].Split('-').Contains(MID.ToString()))
                        {
                            txtSliderWidth.Text = ConfigurationManager.AppSettings["DigiportAdminAjansSlideIzinliBoyut"].Split('x')[0];
                            txtSliderHeight.Text = ConfigurationManager.AppSettings["DigiportAdminAjansSlideIzinliBoyut"].Split('x')[1];
                            txtThumbnailWidth.Text = ConfigurationManager.AppSettings["DigiportAdminAjansSlideThumbnailIzinliBoyut"].Split('x')[0];
                            txtThumbnailHeight.Text = ConfigurationManager.AppSettings["DigiportAdminAjansSlideThumbnailIzinliBoyut"].Split('x')[1];
                        }
                        else if (ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"].Split('-').Contains(MID.ToString()))
                        {
                            txtSliderWidth.Text = ConfigurationManager.AppSettings["DigiportAdminHrMediaSlidesIzinliBoyut"].Split('x')[0];
                            txtSliderHeight.Text = ConfigurationManager.AppSettings["DigiportAdminHrMediaSlidesIzinliBoyut"].Split('x')[1];
                            txtThumbnailWidth.Text = ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideThumbnailIzinliBoyut"].Split('x')[0];
                            txtThumbnailHeight.Text = ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideThumbnailIzinliBoyut"].Split('x')[1];
                        }
                        else if (ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"].Split('-').Contains(MID.ToString()))
                        {
                            txtSliderWidth.Text = ConfigurationManager.AppSettings["DigiportAdminEducationSlideIzinliBoyut"].Split('x')[0];
                            txtSliderHeight.Text = ConfigurationManager.AppSettings["DigiportAdminEducationSlideIzinliBoyut"].Split('x')[1];
                            txtThumbnailWidth.Text = ConfigurationManager.AppSettings["DigiportAdminEducationSlideThumbnailIzinliBoyut"].Split('x')[0];
                            txtThumbnailHeight.Text = ConfigurationManager.AppSettings["DigiportAdminEducationSlideThumbnailIzinliBoyut"].Split('x')[1];
                        }
                        chkShowThumbnails.Checked = true;
                        chkShowThumbnails.Enabled = true;
                        SetVisibilityForThumbnail(true);
                    }
                    txtSliderAutoSlideInterval.Text = ConfigurationManager.AppSettings["DigiportAdminSliderDefaultAutoSlideInterval"];
                    txtSliderTransitionDuration.Text = ConfigurationManager.AppSettings["DigiportAdminSliderDefaultTransitionDuration"];
                    drpSliderEfektTipi.SelectedIndex = 0;
                    drpSliderTransitionEasing.SelectedIndex = 0;
                    drpSliderTitleMode.SelectedIndex = 0;
                    chkShowProgressBar.Checked = true;
                    chkAutoSlideActive.Checked = true;
                }

                IFrameDisplay.Style["border"] = "none";
                IFrameDisplay.Style["border-radius"] = "10px";
                if (ConfigurationManager.AppSettings["DigiportAdminAnasayfaSolSagKucukBannerID"].Split('-').Contains(MID.ToString()))
                {
                    IFrameDisplay.Style["width"] = (ConvertionHelper.ConvertValue<int>(txtSliderWidth.Text) + 5).ToString() + "px";
                    IFrameDisplay.Style["height"] = (ConvertionHelper.ConvertValue<int>(txtSliderHeight.Text) + 40).ToString() + "px";
                    IFrameDisplay.Src = domain + "Digiport/AnaSayfaSolSagSlide.aspx?slideType=" + MID.ToString();
                }
                else if (ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerID"].Split('-').Contains(MID.ToString()))
                {
                    IFrameDisplay.Style["width"] = (ConvertionHelper.ConvertValue<int>(txtSliderWidth.Text) + 5).ToString() + "px";
                    IFrameDisplay.Style["height"] = (ConvertionHelper.ConvertValue<int>(txtSliderHeight.Text) + (chkShowThumbnails.Enabled && chkShowThumbnails.Checked ? 100 : 40)).ToString() + "px";
                    IFrameDisplay.Src = domain + "Digiport/AnaSayfaUstSlide.aspx?slideType=" + MID.ToString();
                }
                else if (ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"].Split('-').Contains(MID.ToString()))
                {
                    IFrameDisplay.Style["width"] = (ConvertionHelper.ConvertValue<int>(txtSliderWidth.Text) + 310).ToString() + "px";
                    IFrameDisplay.Style["height"] = (ConvertionHelper.ConvertValue<int>(txtSliderHeight.Text) + 55).ToString() + "px";
                    IFrameDisplay.Src = domain + "Digiport/HrMediaSlide.aspx?slideType=" + MID.ToString();
                }
                else if (ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"].Split('-').Contains(MID.ToString()))
                {
                    IFrameDisplay.Style["width"] = (ConvertionHelper.ConvertValue<int>(txtSliderWidth.Text) + 310).ToString() + "px";
                    IFrameDisplay.Style["height"] = (ConvertionHelper.ConvertValue<int>(txtSliderHeight.Text) + 55).ToString() + "px";
                    IFrameDisplay.Src = domain + "Digiport/AjansSlide.aspx?slideType=" + MID.ToString();
                }
                else if (ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"].Split('-').Contains(MID.ToString()))
                {
                    IFrameDisplay.Style["width"] = (ConvertionHelper.ConvertValue<int>(txtSliderWidth.Text) + 310).ToString() + "px";
                    IFrameDisplay.Style["height"] = (ConvertionHelper.ConvertValue<int>(txtSliderHeight.Text) + 55).ToString() + "px";
                    IFrameDisplay.Src = domain + "Digiport/EducationSlide.aspx?slideType=" + MID.ToString();
                }
            }
            catch (Exception ex)
            {
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }

        protected void btnKaydet_Click(object sender, EventArgs e)
        {
            try
            {
                decimal sliderGenislik = 0, sliderYukseklik = 0, sliderAutoSlideInterval = 0, sliderTransitionDuration = 0, thumbnailWidth = 0, thumbnailHeight = 0;
                if (txtSliderWidth.Text == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.SliderGenislikSecin);
                if (txtSliderHeight.Text == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.SliderYukseklikSecin);
                if (!Decimal.TryParse(txtSliderWidth.Text, out sliderGenislik))
                    throw new Exception(Resources.DigiportAdminResource.SliderGenislik + " => " + Resources.DigiportAdminResource.SayiAraligi2);
                if (!Decimal.TryParse(txtSliderHeight.Text, out sliderYukseklik))
                    throw new Exception(Resources.DigiportAdminResource.SliderYukseklik + " => " + Resources.DigiportAdminResource.SayiAraligi2);
                if (txtSliderAutoSlideInterval.Text == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.SliderAutoSlideIntervalSecin);
                if (!Decimal.TryParse(txtSliderAutoSlideInterval.Text, out sliderAutoSlideInterval))
                    throw new Exception(Resources.DigiportAdminResource.SliderAutoSlideInterval + " => " + Resources.DigiportAdminResource.SayiAraligi1);

                if (txtSliderTransitionDuration.Text == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.SliderTransitionDurationSecin);
                if (!Decimal.TryParse(txtSliderTransitionDuration.Text, out sliderTransitionDuration))
                    throw new Exception(Resources.DigiportAdminResource.SliderTransitionDuration + " => " + Resources.DigiportAdminResource.SayiAraligi1);

                if (chkShowThumbnails.Enabled && chkShowThumbnails.Checked)
                {
                    if (txtThumbnailWidth.Text == string.Empty)
                        throw new Exception(Resources.DigiportAdminResource.SliderThumbnailGenislikSecin);
                    if (!Decimal.TryParse(txtThumbnailWidth.Text, out thumbnailWidth))
                        throw new Exception(Resources.DigiportAdminResource.ThumbnailWidth + " => " + Resources.DigiportAdminResource.SayiAraligi2);

                    if (txtThumbnailHeight.Text == string.Empty)
                        throw new Exception(Resources.DigiportAdminResource.SliderThumbnailYukseklikSecin);
                    if (!Decimal.TryParse(txtThumbnailHeight.Text, out thumbnailHeight))
                        throw new Exception(Resources.DigiportAdminResource.ThumbnailHeight + " => " + Resources.DigiportAdminResource.SayiAraligi2);
                }
                else
                {
                    Decimal.TryParse(txtThumbnailWidth.Text, out thumbnailWidth);
                    Decimal.TryParse(txtThumbnailHeight.Text, out thumbnailHeight);
                }
                if (drpSliderEfektTipi.SelectedValue == "-1")
                    throw new Exception(Resources.DigiportAdminResource.SliderEfektTipiSecin);
                if (drpSliderTransitionEasing.SelectedValue == "-1")
                    throw new Exception(Resources.DigiportAdminResource.SliderTransitionEasingSecin);
                if (drpSliderTitleMode.SelectedValue == "-1")
                    throw new Exception(Resources.DigiportAdminResource.SliderTitleModeSecin);

                DIGIPORT_ADMIN_SLIDER_OPTIONS entity = PRepository<DIGIPORT_ADMIN_SLIDER_OPTIONS>.EntityGetir("DT_WORKFLOW", "select * from DT_WORKFLOW.DIGIPORT_ADMIN_SLIDER_OPTIONS where MENU_NAME_ID=:MENU_NAME_ID", new Oracle.DataAccess.Client.OracleParameter[] {
                  new Oracle.DataAccess.Client.OracleParameter("MENU_NAME_ID",MID)
                });
                if (entity.ID == 0)
                {
                    entity = new DIGIPORT_ADMIN_SLIDER_OPTIONS()
                    {
                        MENU_NAME_ID = MID,
                        AUTO_SLIDE_INTERVAL = sliderAutoSlideInterval,
                        EFFECT_TYPE = ConvertionHelper.ConvertValue<decimal>(drpSliderEfektTipi.SelectedValue),
                        TITLE_MODE = ConvertionHelper.ConvertValue<decimal>(drpSliderTitleMode.SelectedValue),
                        TRANSITION_EASING = ConvertionHelper.ConvertValue<decimal>(drpSliderTransitionEasing.SelectedValue),
                        SHOW_PROGRESS_BAR = chkShowProgressBar.Checked ? "1" : "0",
                        AUTO_SLIDE_ACTIVE = chkAutoSlideActive.Checked ? "1":"0",
                        SHOW_THUMBNAILS = chkShowThumbnails.Enabled && chkShowThumbnails.Checked ? "1" : "0",
                        TRANSITION_DURATION = sliderTransitionDuration,
                        SLIDE_WIDTH = sliderGenislik,
                        SLIDE_HEIGHT = sliderYukseklik,
                        THUMBNAIL_WIDTH = thumbnailWidth,
                        THUMBNAIL_HEIGHT = thumbnailHeight,
                        CREATED = DateTime.Now,
                        CREATED_BY = LoginId,
                        LAST_UPDATED = null,
                        LAST_UPDATED_BY = null
                    };
                    PRepository<DIGIPORT_ADMIN_SLIDER_OPTIONS>.EntityKaydet("DT_WORKFLOW", entity);
                }
                else
                {
                    entity.AUTO_SLIDE_INTERVAL = sliderAutoSlideInterval;
                    entity.EFFECT_TYPE = ConvertionHelper.ConvertValue<decimal>(drpSliderEfektTipi.SelectedValue);
                    entity.TITLE_MODE = ConvertionHelper.ConvertValue<decimal>(drpSliderTitleMode.SelectedValue);
                    entity.TRANSITION_EASING = ConvertionHelper.ConvertValue<decimal>(drpSliderTransitionEasing.SelectedValue);
                    entity.SHOW_PROGRESS_BAR = chkShowProgressBar.Checked ? "1" : "0";
                    entity.AUTO_SLIDE_ACTIVE = chkAutoSlideActive.Checked ? "1" : "0";
                    entity.SHOW_THUMBNAILS = chkShowThumbnails.Enabled && chkShowThumbnails.Checked ? "1" : "0";
                    entity.TRANSITION_DURATION = sliderTransitionDuration;
                    entity.SLIDE_WIDTH = sliderGenislik;
                    entity.SLIDE_HEIGHT = sliderYukseklik;
                    entity.THUMBNAIL_WIDTH = thumbnailWidth;
                    entity.THUMBNAIL_HEIGHT = thumbnailHeight;
                    entity.LAST_UPDATED = DateTime.Now;
                    entity.LAST_UPDATED_BY = LoginId;
                    PRepository<DIGIPORT_ADMIN_SLIDER_OPTIONS>.EntityUpdateEt("DT_WORKFLOW", entity);
                }
                LoadData();
                this.Master.PopupGoster(Resources.DigiportAdminResource.Basari, Resources.DigiportAdminResource.KayitBasarili, false);
            }
            catch (Exception ex)
            {
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }

        protected void chkShowThumbnails_CheckedChanged(object sender, EventArgs e)
        {
            SetVisibilityForThumbnail(chkShowThumbnails.Checked);
        }
        private void SetVisibilityForThumbnail(bool visible)
        {
            divThumbnailWidthHeight.Visible = visible;
        }
    }
}