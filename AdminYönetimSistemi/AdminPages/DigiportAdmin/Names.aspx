<%@ Page Title="Digiport Menu - Name Management" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true"
    CodeBehind="Names.aspx.cs" Inherits="AracTakipSistemi.AdminPages.DigiportMenu.Names" %>

<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ MasterType VirtualPath="~/Site.Master" %>
<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:UpdatePanel ID="upnlNames" runat="server" UpdateMode="Conditional">
        <ContentTemplate>
            <style>
                /* ===== CSS Variables ===== */
                :root {
                    --primary: #5c2d91;
                    --primary-light: #8b5a9f;
                    --primary-dark: #462173;
                    --primary-50: #f8f6fc;
                    --primary-100: #e9dff4;
                    --primary-200: #d4c2e8;
                    --primary-600: #5c2d91;
                    --primary-900: #2d1545;
                    --success: #28a745;
                    --success-50: #f0f9f3;
                    --success-100: #d1f2d1;
                    --success-600: #28a745;
                    --danger: #dc3545;
                    --danger-50: #fdf2f2;
                    --danger-100: #fecaca;
                    --danger-200: #fca5a5;
                    --danger-600: #dc2626;
                    --warning: #ffc107;
                    --info: #17a2b8;
                    --gray-50: #f9fafb;
                    --gray-100: #f3f4f6;
                    --gray-200: #e5e7eb;
                    --gray-300: #d1d5db;
                    --gray-400: #9ca3af;
                    --gray-500: #6b7280;
                    --gray-600: #4b5563;
                    --gray-700: #374151;
                    --gray-800: #1f2937;
                    --gray-900: #111827;
                    --radius-sm: 0.25rem;
                    --radius-md: 0.375rem;
                    --radius-lg: 0.5rem;
                    --radius-xl: 0.75rem;
                    --radius-full: 9999px;
                    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                    --transition-fast: 0.15s ease-in-out;
                    --transition-normal: 0.3s ease-in-out;
                    --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                }

                /* ===== Base Layout & Components ===== */
                .app-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 1.5rem;
                    font-family: var(--font-sans);
                }

                @media (min-width: 768px) {
                    .app-container {
                        padding: 2rem;
                    }
                }

                /* Card Components */
                .card {
                    background-color: white;
                    border-radius: var(--radius-lg);
                    box-shadow: var(--shadow-md);
                    overflow: hidden;
                    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
                    margin-bottom: 1.5rem;
                    border: 1px solid var(--gray-100);
                    position: relative;
                }

                    .card:hover {
                        box-shadow: var(--shadow-lg);
                    }

                .card-header {
                    padding: 1.25rem 1.5rem;
                    border-bottom: 1px solid var(--gray-100);
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    background-color: white;
                    position: relative;
                }

                    .card-header::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        height: 100%;
                        width: 4px;
                        background-color: var(--primary);
                        border-radius: 4px 0 0 4px;
                    }

                .card-title {
                    margin: 0;
                    font-size: 1.1rem;
                    font-weight: 600;
                    color: var(--gray-800);
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                    .card-title .icon {
                        color: var(--primary);
                        font-size: 1.2em;
                        opacity: 0.9;
                    }

                .card-body {
                    padding: 1.5rem;
                }

                .card-footer {
                    padding: 1.25rem 1.5rem;
                    border-top: 1px solid var(--gray-100);
                    background-color: var(--gray-50);
                    display: flex;
                    align-items: center;
                    width: 100%;
                    justify-content: space-between;
                }

                /* Typography */
                .page-title-style {
                    margin: 0 0 0.5rem 0;
                    font-size: 1.75rem;
                    font-weight: 700;
                    color: var(--gray-900);
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                    .page-title-style .icon {
                        color: var(--primary);
                    }

                .page-description {
                    margin: 0 0 2rem 0;
                    color: var(--gray-600);
                    max-width: 750px;
                    line-height: 1.6;
                }

                /* Form Controls */
                .form-group {
                    margin-bottom: 1.25rem;
                }

                .form-label {
                    display: block;
                    margin-bottom: 0.5rem;
                    font-weight: 500;
                    color: var(--gray-700);
                    font-size: 0.9rem;
                }

                .form-hint {
                    margin-top: 0.375rem;
                    font-size: 0.8125rem;
                    color: var(--gray-600);
                }

                .form-control {
                    display: block;
                    width: 100%;
                    padding: 0.625rem 0.875rem;
                    font-size: 0.9rem;
                    font-weight: 400;
                    line-height: 1.5;
                    color: var(--gray-800);
                    background-color: white;
                    background-clip: padding-box;
                    border: 1px solid var(--gray-300);
                    border-radius: var(--radius-md);
                    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
                }

                    .form-control:focus {
                        border-color: var(--primary-300);
                        box-shadow: 0 0 0 3px var(--primary-100);
                        outline: 0;
                    }

                    .form-control::placeholder {
                        color: var(--gray-500);
                        opacity: 1;
                    }

                .form-select {
                    display: block !important;
                    width: 100% !important;
                    padding: 0.625rem 2.25rem 0.625rem 0.875rem !important;
                    font-size: 0.9rem !important;
                    font-weight: 400 !important;
                    line-height: 1.5 !important;
                    color: var(--gray-800) !important;
                    background-color: white !important;
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%235c2d91' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e") !important;
                    background-repeat: no-repeat !important;
                    background-position: right 0.875rem center !important;
                    background-size: 16px 12px !important;
                    border: 1px solid var(--gray-300) !important;
                    border-radius: var(--radius-md) !important;
                    appearance: none !important;
                    transition: border-color var(--transition-fast), box-shadow var(--transition-fast) !important;
                }

                    .form-select:focus {
                        border-color: var(--primary-300);
                        box-shadow: 0 0 0 3px var(--primary-100);
                        outline: 0;
                    }

                /* Button Styles */
                .btn {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 500;
                    line-height: 1.5;
                    color: var(--gray-700);
                    text-align: center;
                    vertical-align: middle;
                    cursor: pointer;
                    user-select: none;
                    background-color: transparent;
                    border: 1px solid transparent;
                    padding: 0.625rem 1.25rem;
                    font-size: 0.9rem;
                    border-radius: var(--radius-md);
                    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                    gap: 0.5rem;
                }

                    .btn:focus {
                        outline: 0;
                        box-shadow: 0 0 0 3px var(--gray-200);
                    }

                .btn-primary {
                    color: white !important;
                    background-color: var(--primary) !important;
                    border-color: var(--primary) !important;
                }

                    .btn-primary:hover {
                        background-color: var(--primary-800) !important;
                        border-color: var(--primary-800) !important;
                    }

                    .btn-primary:focus {
                        box-shadow: 0 0 0 3px var(--primary-200) !important;
                    }

                .btn-success {
                    color: white !important;
                    background-color: var(--success) !important;
                    border-color: var(--success) !important;
                }

                    .btn-success:hover {
                        background-color: var(--success-600) !important;
                        border-color: var(--success-600) !important;
                    }

                    .btn-success:focus {
                        box-shadow: 0 0 0 3px var(--success-100) !important;
                    }

                .btn-danger {
                    color: white !important;
                    background-color: var(--danger) !important;
                    border-color: var(--danger) !important;
                }

                    .btn-danger:hover {
                        background-color: var(--danger-600) !important;
                        border-color: var(--danger-600) !important;
                    }

                    .btn-danger:focus {
                        box-shadow: 0 0 0 3px var(--danger-100) !important;
                    }

                .btn-secondary {
                    color: var(--gray-800) !important;
                    background-color: white !important;
                    border-color: var(--gray-300) !important;
                }

                    .btn-secondary:hover {
                        background-color: var(--gray-50) !important;
                        border-color: var(--gray-400) !important;
                    }

                /* Toggle Switch */
                .toggle-container {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .toggle-switch {
                    position: relative;
                    display: inline-block;
                    width: 52px;
                    height: 28px;
                }

                    .toggle-switch input {
                        opacity: 0;
                        width: 0;
                        height: 0;
                    }

                .toggle-slider {
                    position: absolute;
                    cursor: pointer;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-color: var(--gray-300);
                    transition: var(--transition-normal);
                    border-radius: 34px;
                }

                    .toggle-slider:before {
                        position: absolute;
                        content: "";
                        height: 20px;
                        width: 20px;
                        left: 4px;
                        bottom: 4px;
                        background-color: white;
                        transition: var(--transition-normal);
                        border-radius: 50%;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                    }

                input:checked + .toggle-slider {
                    background-color: var(--success);
                }

                    input:checked + .toggle-slider:before {
                        transform: translateX(24px);
                    }

                .toggle-label {
                    font-weight: 500;
                    font-size: 0.9rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .toggle-status {
                    padding: 0.25rem 0.75rem;
                    border-radius: var(--radius-full);
                    font-size: 0.8125rem;
                    font-weight: 600;
                    background-color: var(--success-50);
                    color: var(--success-600);
                    transition: var(--transition-fast);
                }

                    .toggle-status.inactive {
                        background-color: var(--danger-50);
                        color: var(--danger-600);
                    }

                /* Data Grid / Table */
                .data-table-container {
                    border-radius: var(--radius-lg);
                    overflow: hidden;
                    border: 1px solid var(--gray-200);
                    background-color: white;
                    margin-bottom: 1.5rem;
                }

                .data-table {
                    width: 100%;
                    border-collapse: separate;
                    border-spacing: 0;
                }

                    .data-table th {
                        background-color: var(--gray-50);
                        color: var(--gray-700);
                        font-size: 0.8125rem;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 0.02em;
                        padding: 1rem;
                        text-align: left;
                        border-bottom: 2px solid var(--gray-200);
                        position: relative;
                    }

                        .data-table th:not(:last-child)::after {
                            content: '';
                            position: absolute;
                            top: 30%;
                            right: 0;
                            height: 40%;
                            width: 1px;
                            background-color: var(--gray-300);
                        }

                    .data-table td {
                        padding: 1rem;
                        border-bottom: 1px solid var(--gray-200);
                        color: var(--gray-800);
                        vertical-align: middle;
                        font-size: 0.9rem;
                        transition: background-color var(--transition-fast);
                    }

                    .data-table tr {
                        transition: background-color var(--transition-fast);
                    }

                    .data-table tbody tr:hover {
                        background-color: var(--primary-50);
                    }

                    .data-table tr.selected {
                        background-color: var(--primary-100);
                    }

                /* Status Badge */
                .badge {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0.35em 0.75em;
                    font-size: 0.75rem;
                    font-weight: 600;
                    border-radius: var(--radius-full);
                    white-space: nowrap;
                    text-transform: uppercase;
                    letter-spacing: 0.02em;
                }

                .badge-success {
                    background-color: var(--success-50);
                    color: var(--success-600);
                    border: 1px solid var(--success-100);
                }

                    .badge-success::before {
                        content: '';
                        display: inline-block;
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                        background-color: var(--success);
                        margin-right: 0.5rem;
                    }

                .badge-danger {
                    background-color: var(--danger-50);
                    color: var(--danger-600);
                    border: 1px solid var(--danger-100);
                }

                    .badge-danger::before {
                        content: '';
                        display: inline-block;
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                        background-color: var(--danger);
                        margin-right: 0.5rem;
                    }

                /* Action Buttons */
                .action-group {
                    display: flex;
                    gap: 0.5rem;
                }

                .btn-action {
                    width: 2rem;
                    height: 2rem;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: var(--radius-md);
                    background-color: white;
                    color: var(--gray-700);
                    border: 1px solid var(--gray-300);
                    font-size: 0.875rem;
                    cursor: pointer;
                    transition: all var(--transition-fast);
                }

                .btn-action-primary {
                    color: var(--primary);
                    border-color: var(--primary-200);
                }

                    .btn-action-primary:hover {
                        background-color: var(--primary);
                        color: white;
                        border-color: var(--primary);
                    }

                .btn-action-danger {
                    color: var(--danger);
                    border-color: var(--danger-200);
                }

                    .btn-action-danger:hover {
                        background-color: var(--danger);
                        color: white;
                        border-color: var(--danger);
                    }

                /* Pagination */
                .pagination {
                    display: flex !important;
                    justify-content: center !important;
                    gap: 0.25rem !important;
                    margin-top: 1.5rem !important;
                }

                .page-item {
                    list-style: none;
                }

                .page-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0.375rem 0.75rem;
                    min-width: 2.25rem;
                    min-height: 2.25rem;
                    line-height: 1.25;
                    color: var(--gray-800);
                    background-color: white;
                    border: 1px solid var(--gray-300);
                    border-radius: var(--radius-md);
                    cursor: pointer;
                    transition: all var(--transition-fast);
                    font-size: 0.875rem;
                    text-decoration: none;
                }

                    .page-link:hover {
                        background-color: var(--gray-100);
                        border-color: var(--gray-400);
                        z-index: 2;
                        color: var(--primary);
                    }

                .page-link-active {
                    color: white;
                    background-color: var(--primary);
                    border-color: var(--primary);
                    z-index: 3;
                }

                    .page-link-active:hover {
                        color: white;
                        background-color: var(--primary-800);
                        border-color: var(--primary-800);
                    }

                /* Empty State */
                .empty-state {
                    padding: 3rem 2rem;
                    text-align: center;
                    background-color: white;
                    border: 1px dashed var(--gray-300);
                    border-radius: var(--radius-lg);
                    margin: 1rem 0;
                }

                .empty-state-icon {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 80px;
                    height: 80px;
                    border-radius: 50%;
                    background-color: var(--primary-50);
                    color: var(--primary);
                    font-size: 2rem;
                    margin-bottom: 1.5rem;
                }

                .empty-state-title {
                    font-size: 1.25rem;
                    font-weight: 600;
                    color: var(--gray-800);
                    margin-bottom: 0.75rem;
                }

                .empty-state-text {
                    color: var(--gray-600);
                    max-width: 350px;
                    margin: 0 auto 1.5rem;
                }

                /* Info Alert */
                .info-alert {
                    display: flex;
                    align-items: flex-start;
                    gap: 1rem;
                    padding: 1rem;
                    background-color: var(--primary-50);
                    border: 1px solid var(--primary-200);
                    border-radius: var(--radius-md);
                    margin-bottom: 1.5rem;
                }

                .info-alert-icon {
                    color: var(--primary);
                    font-size: 1.25rem;
                    flex-shrink: 0;
                    margin-top: 0.125rem;
                }

                .info-alert-content {
                    flex: 1;
                }

                .info-alert-title {
                    font-weight: 600;
                    color: var(--primary-900);
                    font-size: 0.9375rem;
                    margin-bottom: 0.25rem;
                }

                .info-alert-text {
                    color: var(--primary-800);
                    font-size: 0.875rem;
                    margin-bottom: 0;
                }

                /* Grid Layout */
                .grid {
                    display: grid;
                    grid-template-columns: repeat(12, 1fr);
                    gap: 1.5rem;
                }

                .col-span-12 {
                    grid-column: span 12 / span 12;
                }

                .col-span-6 {
                    grid-column: span 6 / span 6;
                }

                .col-span-4 {
                    grid-column: span 4 / span 4;
                }

                .col-span-3 {
                    grid-column: span 3 / span 3;
                }

                @media (max-width: 1024px) {
                    .lg\:col-span-6 {
                        grid-column: span 6 / span 6;
                    }

                    .lg\:col-span-12 {
                        grid-column: span 12 / span 12;
                    }
                }

                @media (max-width: 768px) {
                    .md\:col-span-12 {
                        grid-column: span 12 / span 12;
                    }
                }
                /* Search Box */
                .search-container {
                    position: relative !important;
                    width: 100% !important;
                }

                .search-input {
                    padding-left: 2.5rem !important;
                    border-radius: var(--radius-md) !important;
                    background-color: white !important;
                    border: 1px solid var(--gray-300) !important;
                    height: 38px !important;
                    font-size: 0.875rem !important;
                    width: 100% !important;
                    transition: all var(--transition-fast) !important;
                }

                    .search-input:focus {
                        border-color: var(--primary-300) !important;
                        box-shadow: 0 0 0 3px var(--primary-100) !important;
                    }

                .search-icon {
                    position: absolute !important;
                    left: 0.875rem !important;
                    top: 50% !important;
                    transform: translateY(-50%) !important;
                    color: var(--gray-500) !important;
                    pointer-events: none !important;
                    font-size: 0.875rem !important;
                }
                /* Utils */
                .flex {
                    display: flex;
                }

                .items-center {
                    align-items: center;
                }

                .justify-between {
                    justify-content: space-between;
                }

                .gap-2 {
                    gap: 0.5rem;
                }

                .gap-4 {
                    gap: 1rem;
                }

                .mb-0 {
                    margin-bottom: 0;
                }

                .mb-2 {
                    margin-bottom: 0.5rem;
                }

                .mb-4 {
                    margin-bottom: 1rem;
                }

                .mb-6 {
                    margin-bottom: 1.5rem;
                }

                .mt-0 {
                    margin-top: 0;
                }

                .mt-2 {
                    margin-top: 0.5rem;
                }

                .mt-4 {
                    margin-top: 1rem;
                }

                .ml-auto {
                    margin-left: auto;
                }

                .text-sm {
                    font-size: 0.875rem;
                }

                .text-xs {
                    font-size: 0.75rem;
                }

                .text-primary {
                    color: var(--primary);
                }

                .text-success {
                    color: var(--success);
                }

                .text-danger {
                    color: var(--danger);
                    display: block !important;
                    width: 100% !important;
                    clear: both !important;
                    margin-top: 0.5rem !important;
                    font-size: 0.875rem !important;
                }

                .text-gray {
                    color: var(--gray-600);
                }

                .font-medium {
                    font-weight: 500;
                }

                .font-semibold {
                    font-weight: 600;
                }

                .font-bold {
                    font-weight: 700;
                }

                .uppercase {
                    text-transform: uppercase;
                }

                .bg-white {
                    background-color: white;
                }

                .bg-gray-50 {
                    background-color: var(--gray-50);
                }

                .bg-primary-50 {
                    background-color: var(--primary-50);
                }

                .border {
                    border: 1px solid var(--gray-200);
                }

                .rounded {
                    border-radius: var(--radius-md);
                }

                .shadow-sm {
                    box-shadow: var(--shadow-sm);
                }

                .relative {
                    position: relative;
                }

                .d-none {
                    display: none;
                }

                /* Form Validation */
                .form-group {
                    position: relative;
                    margin-bottom: 1.5rem;
                }

                    .form-group .text-danger {
                        position: relative;
                        display: block !important;
                        width: 100% !important;
                        margin-top: 0.375rem !important;
                        margin-left: 0 !important;
                        clear: both;
                        font-size: 0.8125rem !important;
                        line-height: 1.25;
                    }

                /* Compact Filter Panel Styles */
                .p-3 {
                    padding: 0.75rem !important;
                }

                .flex {
                    display: flex !important;
                }

                .flex-wrap {
                    flex-wrap: wrap !important;
                }

                .items-end {
                    align-items: flex-end !important;
                }

                .gap-2 {
                    gap: 0.5rem !important;
                }

                .mb-1 {
                    margin-bottom: 0.25rem !important;
                }

                .btn-sm {
                    padding: 0.375rem 0.75rem !important;
                    font-size: 0.8125rem !important;
                }
                /* ===== Modern ASP.NET GridView Styles ===== */
                .data-table-container {
                    border-radius: var(--radius-lg);
                    overflow: hidden;
                    box-shadow: var(--shadow-sm);
                    background: white;
                    height: auto;
                    min-height: 500px;
                    width: 100%;
                    position: relative;
                    display: block;
                    padding: 0;
                    margin: 0;
                    overflow-x: auto; /* Allow horizontal scroll if needed */
                    overflow-y: visible; /* Prevent vertical scroll cutting off content */
                }
                /* Ensure grid card takes full width and maximizes available space */
                .card:last-of-type {
                    width: 100%;
                    max-width: none;
                    margin-bottom: 0;
                }

                    .card:last-of-type .card-body {
                        padding: 0;
                    }

                    /* Enhanced container for better table layout */
                    .card:last-of-type .data-table-container {
                        border-radius: 0;
                        box-shadow: none;
                    }
                /* Grid Table */
                .modern-grid {
                    width: 100% !important;
                    max-width: 100% !important;
                    border-collapse: separate !important;
                    border-spacing: 0 !important;
                    background: white !important;
                    table-layout: fixed !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }
                    /* Responsive Column width distribution */
                    .modern-grid .grid-cell-id,
                    .modern-grid th:nth-child(1),
                    .modern-grid td:nth-child(1) {
                        width: 60px !important;
                        min-width: 60px !important;
                        max-width: 60px !important;
                    }

                    .modern-grid .grid-cell-type,
                    .modern-grid th:nth-child(2),
                    .modern-grid td:nth-child(2) {
                        width: 110px !important;
                        min-width: 110px !important;
                        max-width: 110px !important;
                    }

                    .modern-grid .grid-cell-name,
                    .modern-grid th:nth-child(3),
                    .modern-grid td:nth-child(3) {
                        width: 180px !important;
                        min-width: 180px !important;
                        max-width: 180px !important;
                    }

                    .modern-grid .grid-cell-description,
                    .modern-grid th:nth-child(4),
                    .modern-grid td:nth-child(4) {
                        width: auto !important;
                        min-width: 200px !important;
                    }

                    .modern-grid .grid-cell-status,
                    .modern-grid th:nth-child(5),
                    .modern-grid td:nth-child(5) {
                        width: 120px !important;
                        min-width: 120px !important;
                        max-width: 120px !important;
                    }

                    .modern-grid .grid-cell-created,
                    .modern-grid th:nth-child(6),
                    .modern-grid td:nth-child(6) {
                        width: 200px !important;
                        min-width: 200px !important;
                        max-width: 200px !important;
                    }

                    .modern-grid .grid-cell-actions,
                    .modern-grid th:nth-child(7),
                    .modern-grid td:nth-child(7) {
                        width: 110px !important;
                        min-width: 110px !important;
                        max-width: 110px !important;
                    }
                    /* Grid Headers */
                    .modern-grid .grid-header {
                        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%) !important;
                        border: none !important;
                        position: relative;
                    }

                    .modern-grid .grid-header-cell,
                    .modern-grid th {
                        padding: 1rem 0.875rem !important;
                        font-weight: 600 !important;
                        font-size: 0.875rem !important;
                        color: white !important;
                        text-align: left !important;
                        border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
                        border-bottom: none !important;
                        white-space: nowrap !important;
                        text-transform: uppercase !important;
                        letter-spacing: 0.025em !important;
                        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%) !important;
                    }

                        /* Fix sorting links in headers to be white without underlines */
                        .modern-grid th a,
                        .modern-grid .grid-header-cell a {
                            color: white !important;
                            text-decoration: none !important;
                            display: block !important;
                            width: 100% !important;
                            height: 100% !important;
                            padding: 0 !important;
                        }

                            .modern-grid th a:hover,
                            .modern-grid .grid-header-cell a:hover {
                                color: white !important;
                                text-decoration: none !important;
                            }

                            .modern-grid th a:visited,
                            .modern-grid .grid-header-cell a:visited {
                                color: white !important;
                                text-decoration: none !important;
                            }

                        .modern-grid .grid-header-cell:last-child,
                        .modern-grid th:last-child {
                            border-right: none !important;
                        }

                    .modern-grid .grid-header-actions {
                        text-align: center !important;
                    }

                    /* Grid Rows */
                    .modern-grid .grid-row {
                        background: white !important;
                        transition: all var(--transition-fast) !important;
                        border-bottom: 1px solid var(--gray-100) !important;
                    }

                        .modern-grid .grid-row:hover {
                            background: var(--gray-50) !important;
                            transform: translateY(-1px) !important;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
                        }

                    .modern-grid .grid-row-alt {
                        background: var(--gray-50) !important;
                    }

                        .modern-grid .grid-row-alt:hover {
                            background: var(--primary-50) !important;
                        }

                    .modern-grid .grid-row-selected {
                        background: var(--primary-50) !important;
                        border-left: 4px solid var(--primary) !important;
                    }

                    .modern-grid .grid-row-focused {
                        background: var(--primary-100) !important;
                        border-left: 4px solid var(--primary-600) !important;
                        box-shadow: 0 0 0 2px var(--primary-200) !important;
                    }

                    /* Grid Cells */
                    .modern-grid .grid-cell {
                        padding: 0.875rem !important;
                        border-right: 1px solid var(--gray-100) !important;
                        border-bottom: none !important;
                        font-size: 0.875rem !important;
                        line-height: 1.4 !important;
                        color: var(--gray-800) !important;
                        vertical-align: middle !important;
                    }

                        .modern-grid .grid-cell:last-child {
                            border-right: none !important;
                        }

                    /* Specific Cell Types */
                    .modern-grid .grid-cell-id {
                        font-weight: 600 !important;
                        color: var(--primary-600) !important;
                        text-align: center !important;
                    }

                    .modern-grid .grid-cell-type {
                        font-weight: 500 !important;
                        color: var(--gray-700) !important;
                    }

                    .modern-grid .grid-cell-name {
                        font-weight: 600 !important;
                        color: var(--gray-900) !important;
                    }

                    .modern-grid .grid-cell-description {
                        color: var(--gray-600) !important;
                        max-width: 300px !important;
                        overflow: hidden !important;
                        text-overflow: ellipsis !important;
                        white-space: nowrap !important;
                    }

                    .modern-grid .grid-cell-name-en {
                        color: var(--gray-700) !important;
                        font-style: italic !important;
                    }

                    .modern-grid .grid-cell-status {
                        text-align: center !important;
                    }

                    .modern-grid .grid-cell-created {
                        text-align: center !important;
                    }

                    .modern-grid .grid-cell-actions {
                        text-align: center !important;
                        padding: 0.5rem !important;
                    }

                /* Status Badges */
                .status-badge {
                    display: inline-flex !important;
                    align-items: center !important;
                    gap: 0.375rem !important;
                    padding: 0.25rem 0.625rem !important;
                    border-radius: var(--radius-full) !important;
                    font-size: 0.75rem !important;
                    font-weight: 600 !important;
                    text-transform: uppercase !important;
                    letter-spacing: 0.025em !important;
                    white-space: nowrap !important;
                    margin: 1px;
                }

                .status-active {
                    background: var(--success-100) !important;
                    color: var(--success-600) !important;
                    border: 1px solid var(--success-200) !important;
                }

                .status-inactive {
                    background: var(--danger-100) !important;
                    color: var(--danger-600) !important;
                    border: 1px solid var(--danger-200) !important;
                }

                /* Created Info */
                .created-info {
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    gap: 0.375rem !important;
                    color: var(--gray-600) !important;
                    font-size: 0.8125rem !important;
                }

                    .created-info i {
                        color: var(--primary-600) !important;
                        font-size: 0.875rem !important;
                    }

                .time-info {
                    display: block !important;
                    font-size: 0.75rem !important;
                    color: var(--gray-500) !important;
                    margin-top: 0.125rem !important;
                }
                /* Action Buttons */
                .action-buttons {
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    gap: 0.25rem !important;
                    width: 100% !important;
                    padding: 0 !important;
                    margin: 0 !important;
                }

                .action-btn {
                    display: inline-flex !important;
                    align-items: center !important;
                    gap: 0.25rem !important;
                    padding: 0.25rem 0.5rem !important;
                    border-radius: var(--radius-md) !important;
                    font-size: 0.75rem !important;
                    font-weight: 500 !important;
                    text-decoration: none !important;
                    transition: all var(--transition-fast) !important;
                    border: 1px solid transparent !important;
                    cursor: pointer !important;
                    white-space: nowrap !important;
                    flex: 1 !important;
                    min-width: 0 !important;
                    max-width: 80px !important;
                }

                .action-btn-edit {
                    background: var(--primary-50) !important;
                    color: var(--primary-600) !important;
                    border-color: var(--primary-200) !important;
                }

                    .action-btn-edit:hover {
                        background: var(--primary-100) !important;
                        color: var(--primary-700) !important;
                        border-color: var(--primary-300) !important;
                        transform: translateY(-1px) !important;
                        box-shadow: 0 2px 6px rgba(92, 45, 145, 0.15) !important;
                    }

                .action-btn-delete {
                    background: var(--danger-50) !important;
                    color: var(--danger-600) !important;
                    border-color: var(--danger-200) !important;
                }

                    .action-btn-delete:hover {
                        background: var(--danger-100) !important;
                        color: var(--danger-700) !important;
                        border-color: var(--danger-300) !important;
                        transform: translateY(-1px) !important;
                        box-shadow: 0 2px 6px rgba(220, 53, 69, 0.15) !important;
                    }

                .action-btn i {
                    font-size: 0.875rem !important;
                }

                .action-text {
                    display: none !important;
                }

                @media (min-width: 1200px) {
                    .action-text {
                        display: inline !important;
                    }

                    .action-btn {
                        max-width: none !important;
                        padding: 0.375rem 0.75rem !important;
                        gap: 0.375rem !important;
                    }

                    .action-buttons {
                        gap: 0.375rem !important;
                    }
                }

                /* Filter Row */
                .modern-grid .grid-filter-row {
                    background: var(--gray-50) !important;
                    border-bottom: 1px solid var(--gray-200) !important;
                }

                    .modern-grid .grid-filter-row input {
                        padding: 0.5rem 0.75rem !important;
                        border: 1px solid var(--gray-300) !important;
                        border-radius: var(--radius-sm) !important;
                        font-size: 0.8125rem !important;
                        background: white !important;
                        transition: all var(--transition-fast) !important;
                    }

                        .modern-grid .grid-filter-row input:focus {
                            border-color: var(--primary-300) !important;
                            box-shadow: 0 0 0 2px var(--primary-100) !important;
                            outline: none !important;
                        }

                /* Filter Bar */
                .modern-grid .grid-filter-bar {
                    background: var(--primary-50) !important;
                    border: 1px solid var(--primary-200) !important;
                    border-radius: var(--radius-md) !important;
                    margin: 0.75rem !important;
                    padding: 0.75rem 1rem !important;
                    color: var(--primary-700) !important;
                    font-size: 0.875rem !important;
                }

                /* Pager */
                .modern-grid .grid-pager {
                    background: var(--gray-50) !important;
                    border-top: 1px solid var(--gray-200) !important;
                    padding: 1rem !important;
                }

                .modern-grid .grid-pager-top {
                    background: var(--gray-50) !important;
                    border-bottom: 1px solid var(--gray-200) !important;
                    padding: 0.75rem 1rem !important;
                }

                .modern-grid .grid-pager-bottom {
                    background: var(--gray-50) !important;
                    border-top: 1px solid var(--gray-200) !important;
                    padding: 0.75rem 1rem !important;
                }

                /* Pager Links */
                .modern-grid .grid-pager a {
                    color: var(--primary-600) !important;
                    text-decoration: none !important;
                    padding: 0.375rem 0.75rem !important;
                    border-radius: var(--radius-sm) !important;
                    transition: all var(--transition-fast) !important;
                    margin: 0 0.125rem !important;
                    border: 1px solid transparent !important;
                }

                    .modern-grid .grid-pager a:hover {
                        background: var(--primary-100) !important;
                        border-color: var(--primary-200) !important;
                    }

                .modern-grid .grid-pager .dxpCurrentPageNumber {
                    background: var(--primary-600) !important;
                    color: white !important;
                    padding: 0.375rem 0.75rem !important;
                    border-radius: var(--radius-sm) !important;
                    margin: 0 0.125rem !important;
                    font-weight: 600 !important;
                }

                /* Group Panel */
                .modern-grid .dxgvGroupPanel {
                    background: var(--gray-50) !important;
                    border: 2px dashed var(--gray-300) !important;
                    border-radius: var(--radius-md) !important;
                    padding: 1.5rem !important;
                    margin: 1rem !important;
                    text-align: center !important;
                    color: var(--gray-600) !important;
                    font-size: 0.875rem !important;
                    font-style: italic !important;
                }

                /* Empty Data Row */
                .modern-grid .dxgvEmptyDataRow {
                    background: var(--gray-50) !important;
                    text-align: center !important;
                    padding: 3rem 1rem !important;
                    color: var(--gray-600) !important;
                    font-size: 0.9375rem !important;
                    font-style: italic !important;
                }

                /* Loading Panel */
                .modern-grid .dxlpLoadingPanel {
                    background: rgba(255, 255, 255, 0.95) !important;
                    border-radius: var(--radius-lg) !important;
                    box-shadow: var(--shadow-lg) !important;
                }
                /* Responsive Design */
                @media (max-width: 768px) {
                    .modern-grid .grid-header-cell,
                    .modern-grid .grid-cell {
                        padding: 0.5rem !important;
                        font-size: 0.8125rem !important;
                    }

                    .action-btn {
                        padding: 0.25rem 0.5rem !important;
                        font-size: 0.75rem !important;
                    }

                    .status-badge {
                        font-size: 0.6875rem !important;
                        padding: 0.1875rem 0.5rem !important;
                    }

                    .data-table-container {
                        height: calc(100vh - 300px);
                        min-height: 400px;
                    }

                    /* Hide non-essential columns on mobile */
                    .modern-grid .grid-cell-description,
                    .modern-grid .grid-cell-name-en,
                    .modern-grid .grid-cell-created {
                        display: none !important;
                    }

                    .modern-grid .grid-header-cell:nth-child(4),
                    .modern-grid .grid-header-cell:nth-child(5),
                    .modern-grid .grid-header-cell:nth-child(8) {
                        display: none !important;
                    }
                }

                @media (min-width: 769px) and (max-width: 1024px) {
                    .data-table-container {
                        height: calc(100vh - 350px);
                    }
                }

                @media (min-width: 1025px) {
                    .data-table-container {
                        height: auto;
                    }
                }
                /* Scrollbar Styling */
                .data-table-container::-webkit-scrollbar {
                    width: 8px;
                    height: 8px;
                }

                .data-table-container::-webkit-scrollbar-track {
                    background: var(--gray-100);
                    border-radius: var(--radius-full);
                }

                .data-table-container::-webkit-scrollbar-thumb {
                    background: var(--gray-400);
                    border-radius: var(--radius-full);
                }

                    .data-table-container::-webkit-scrollbar-thumb:hover {
                        background: var(--gray-500);
                    }

                /* DevExpress Grid Overrides for Data Persistence */
                .modern-grid .dxgvLoadingPanel_DevEx {
                    background: rgba(255, 255, 255, 0.9) !important;
                }

                .modern-grid .dxgvLoadingDiv_DevEx {
                    color: var(--primary) !important;
                    font-weight: 600 !important;
                }

                /* Ensure grid maintains structure during callbacks */
                .modern-grid .dxgvControl_DevEx {
                    min-height: 200px !important;
                }

                /* Fix for sorting arrows visibility */
                .modern-grid .dxgvSortUp_DevEx,
                .modern-grid .dxgvSortDown_DevEx {
                    color: white !important;
                    opacity: 0.9 !important;
                }
                /* Header cell hover effect */
                .modern-grid th:hover,
                .modern-grid .grid-header-cell:hover {
                    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%) !important;
                }

                /* Remove extra spacing and ensure proper table layout */
                .modern-grid,
                .modern-grid table {
                    width: 100% !important;
                    max-width: 100% !important;
                    table-layout: fixed !important;
                    border-collapse: collapse !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }

                    /* Ensure proper row and cell layout */
                    .modern-grid tr {
                        width: 100% !important;
                        display: table-row !important;
                    }

                    .modern-grid th,
                    .modern-grid td {
                        display: table-cell !important;
                        vertical-align: middle !important;
                        box-sizing: border-box !important;
                        overflow: hidden !important;
                        text-overflow: ellipsis !important;
                    }

                    .modern-grid .dxgvTable_DevEx tr,
                    .modern-grid tr {
                        width: 100% !important;
                        display: table-row !important;
                    }

                    .modern-grid .dxgvTable_DevEx td,
                    .modern-grid td,
                    .modern-grid th {
                        position: relative !important;
                        display: table-cell !important;
                        vertical-align: middle !important;
                        height: auto !important;
                        box-sizing: border-box !important;
                    }

                    /* Ensure cells don't exceed their allocated width */
                    .modern-grid th,
                    .modern-grid td {
                        width: 100% !important;
                        min-width: 0 !important;
                        max-width: none !important;
                        display: table-cell !important;
                        box-sizing: border-box !important;
                    }
                /* ===== Filter Panel Styles ===== */
                .filter-panel {
                    background: var(--gray-50);
                    border: 1px solid var(--gray-200);
                    border-radius: var(--radius-lg);
                    padding: 1.5rem;
                    margin-bottom: 1.5rem;
                    box-shadow: var(--shadow-sm);
                }

                .filter-row {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 1rem;
                    align-items: end;
                }

                .filter-group {
                    display: flex;
                    flex-direction: column;
                    min-width: 160px;
                    flex: 1;
                }

                .filter-label {
                    font-size: 0.875rem;
                    font-weight: 500;
                    color: var(--gray-700);
                    margin-bottom: 0.5rem;
                    display: block;
                }

                .filter-select,
                .filter-input {
                    padding: 0.5rem 0.75rem;
                    border: 1px solid var(--gray-300);
                    border-radius: var(--radius-md);
                    font-size: 0.875rem;
                    background: white;
                    transition: var(--transition-fast);
                    width: 90%;
                }

                    .filter-select:focus,
                    .filter-input:focus {
                        outline: none;
                        border-color: var(--primary-600);
                        box-shadow: 0 0 0 3px rgba(92, 45, 145, 0.1);
                    }

                    .filter-input::placeholder {
                        color: var(--gray-400);
                    }

                .btn-clear {
                    background: var(--gray-100);
                    color: var(--gray-700);
                    border: 1px solid var(--gray-300);
                    padding: 0.5rem 1rem;
                    border-radius: var(--radius-md);
                    font-size: 0.875rem;
                    font-weight: 500;
                    cursor: pointer;
                    transition: var(--transition-fast);
                    height: fit-content;
                    align-self: flex-end;
                }

                    .btn-clear:hover {
                        background: var(--gray-200);
                        border-color: var(--gray-400);
                    }

                    .btn-clear:active {
                        background: var(--gray-300);
                    }

                @media (max-width: 768px) {
                    .filter-row {
                        flex-direction: column;
                        align-items: stretch;
                    }

                    .filter-group {
                        min-width: 100%;
                    }

                    .btn-clear {
                        align-self: stretch;
                        margin-top: 0.5rem;
                    }
                }
            </style>

            <div class="app-container">
                <h1 class="page-title-style">
                    <i class="fas fa-list-alt icon"></i>
                    <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_PageTitle %>" />
                </h1>
                <p class="page-description">
                    <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_PageDescription %>" />
                </p>

                <!-- Form Card -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-edit icon"></i>
                            <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_FormTitle %>" />
                        </h2>
                    </div>
                    <div class="card-body">
                        <div class="grid">
                            <div class="col-span-12 lg:col-span-6">
                                <div class="form-group">
                                    <label for="<%= drpType.ClientID %>" class="form-label">
                                        <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_Type %>" />
                                    </label>
                                    <asp:DropDownList ID="drpType" runat="server" CssClass="form-select"
                                        DataTextField="TYPE_NAME" DataValueField="ID">
                                    </asp:DropDownList>
                                    <asp:RequiredFieldValidator ID="rfvType" runat="server"
                                        ControlToValidate="drpType" InitialValue="0"
                                        ErrorMessage="<%$ Resources:DigiportAdminResource, Names_TypeRequired %>"
                                        Display="Dynamic" CssClass="text-danger mt-2 text-sm"
                                        ValidationGroup="NamesValidation"
                                        EnableClientScript="false" />
                                </div>
                            </div>
                            <div class="col-span-12 lg:col-span-6">
                                <div class="form-group">
                                    <label for="<%= txtName.ClientID %>" class="form-label">
                                        <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_Name %>" />
                                    </label>
                                    <asp:TextBox ID="txtName" runat="server" CssClass="form-control"
                                        placeholder="<%$ Resources:DigiportAdminResource, Names_EnterName %>">
                                    </asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvName" runat="server"
                                        ControlToValidate="txtName"
                                        ErrorMessage="<%$ Resources:DigiportAdminResource, Names_NameRequired %>"
                                        Display="Dynamic" CssClass="text-danger mt-2 text-sm"
                                        ValidationGroup="NamesValidation"
                                        EnableClientScript="false" />
                                </div>
                            </div>
                            <div class="col-span-12 lg:col-span-6">
                                <div class="form-group">
                                    <label for="<%= txtNameEn.ClientID %>" class="form-label">
                                        <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_NameEn %>" />
                                    </label>
                                    <asp:TextBox ID="txtNameEn" runat="server" CssClass="form-control"
                                        placeholder="<%$ Resources:DigiportAdminResource, Names_EnterNameEn %>">
                                    </asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvNameEn" runat="server"
                                        ControlToValidate="txtNameEn"
                                        ErrorMessage="<%$ Resources:DigiportAdminResource, Names_NameEnRequired %>"
                                        Display="Dynamic" CssClass="text-danger mt-2 text-sm"
                                        ValidationGroup="NamesValidation"
                                        EnableClientScript="false" />
                                </div>
                            </div>
                        </div>
                        <div class="grid">
                            <div class="col-span-12 lg:col-span-6">
                                <div class="form-group">
                                    <label for="<%= txtDescription.ClientID %>" class="form-label">
                                        <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_Description %>" />
                                    </label>
                                    <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control"
                                        TextMode="MultiLine" Rows="3"
                                        placeholder="<%$ Resources:DigiportAdminResource, Names_EnterDescription %>">
                                    </asp:TextBox>
                                </div>
                            </div>
                            <div class="col-span-12 lg:col-span-6">
                                <div class="form-group">
                                    <label for="<%= txtDescriptionEn.ClientID %>" class="form-label">
                                        <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_DescriptionEn %>" />
                                    </label>
                                    <asp:TextBox ID="txtDescriptionEn" runat="server" CssClass="form-control"
                                        TextMode="MultiLine" Rows="3"
                                        placeholder="<%$ Resources:DigiportAdminResource, Names_EnterDescriptionEn %>">
                                    </asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvDescriptionEn" runat="server"
                                        ControlToValidate="txtDescriptionEn"
                                        ErrorMessage="<%$ Resources:DigiportAdminResource, Names_DescriptionEnRequired %>"
                                        Display="Dynamic" CssClass="text-danger mt-2 text-sm"
                                        ValidationGroup="NamesValidation"
                                        EnableClientScript="false" />
                                </div>
                            </div>
                        </div>

                        <div class="grid">
                            <div class="col-span-12 lg:col-span-6">
                                <div class="form-group">
                                    <label for="<%= txtPagePath.ClientID %>" class="form-label">
                                        <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_PagePath %>" />
                                    </label>
                                    <asp:TextBox ID="txtPagePath" runat="server" CssClass="form-control"
                                        placeholder="<%$ Resources:DigiportAdminResource, Names_PagePathExample %>">
                                    </asp:TextBox>
                                    <p class="form-hint">
                                        <i class="fas fa-info-circle"></i>
                                        <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_PagePathHint %>" />
                                    </p>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server"
                                        ControlToValidate="txtPagePath"
                                        ErrorMessage="<%$ Resources:DigiportAdminResource, Names_PagePathRequired %>"
                                        Display="Dynamic" CssClass="text-danger mt-2 text-sm"
                                        ValidationGroup="NamesValidation"
                                        EnableClientScript="false" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-0">
                            <label class="form-label">
                                <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_Status %>" />:
                            </label>
                            <div class="toggle-container">
                                <label class="toggle-switch">
                                    <asp:CheckBox ID="chkAktif" runat="server" Checked="true" />
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">
                                    <span id="statusText" class="toggle-status">
                                        <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_StatusActive %>" />
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div>
                            <p class="text-gray text-sm mb-0">
                                <i class="fas fa-info-circle"></i>
                                <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_FormHint %>" />
                            </p>
                        </div>
                        <uc1:AdminKaydet runat="server" ID="AdminKaydet1" validasyonGrubu="NamesValidation" />
                    </div>
                </div>

                <!-- Grid Card -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-table icon"></i>
                            <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_List %>" />
                        </h2>
                        <div class="badge bg-primary-50 text-primary rounded">
                            <asp:Literal ID="ltlRecordCount" runat="server" Text="<%$ Resources:DigiportAdminResource, Names_RecordCount %>" />
                        </div>
                    </div>
                    <!-- Filter Panel -->
                    <div class="filter-panel">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label class="filter-label">
                                    <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_TypeColumn %>" />:
                                </label>
                                <asp:DropDownList ID="ddlFilterType" runat="server" CssClass="filter-select" AutoPostBack="true" OnSelectedIndexChanged="Filter_Changed">
                                    <asp:ListItem Value="" Text="<%$ Resources:DigiportAdminResource, Names_FilterAll %>" />
                                </asp:DropDownList>
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">
                                    <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_NameColumn %>" />:
                                </label>
                                <asp:TextBox ID="txtFilterName" runat="server" CssClass="filter-input" placeholder="<%$ Resources:DigiportAdminResource, Names_SearchPlaceholder %>" AutoPostBack="true" OnTextChanged="Filter_Changed" />
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">
                                    <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, Names_StatusColumn %>" />:
                                </label>
                                <asp:DropDownList ID="ddlFilterStatus" runat="server" CssClass="filter-select" AutoPostBack="true" OnSelectedIndexChanged="Filter_Changed">
                                    <asp:ListItem Value="" Text="<%$ Resources:DigiportAdminResource, Names_FilterAll %>" />
                                    <asp:ListItem Value="1" Text="<%$ Resources:DigiportAdminResource, Names_StatusActive %>" />
                                    <asp:ListItem Value="0" Text="<%$ Resources:DigiportAdminResource, Names_StatusInactive %>" />
                                </asp:DropDownList>
                            </div>
                            <div class="filter-group">
                                <asp:Button ID="btnClearFilter" runat="server" CssClass="btn btn-secondary btn-sm" Text="<%$ Resources:DigiportAdminResource, Names_Clear %>" OnClick="ClearFilter_Click" />
                            </div>
                        </div>
                    </div>

                    <!-- Data Table Container -->
                    <div class="data-table-container">
                        <asp:GridView ID="grdNames" runat="server"
                            CssClass="modern-grid"
                            AutoGenerateColumns="false"
                            AllowSorting="true"
                            OnSorting="grdNames_Sorting"
                            OnRowCommand="grdNames_RowCommand"
                            OnRowDataBound="grdNames_RowDataBound"
                            EmptyDataText="<%$ Resources:DigiportAdminResource, Names_NoRecordsFound %>">
                            <Columns>
                                <asp:BoundField DataField="ID" HeaderText="<%$ Resources:DigiportAdminResource, Names_ID %>"
                                    SortExpression="ID" HeaderStyle-CssClass="grid-header-cell" ItemStyle-CssClass="grid-cell grid-cell-id">
                                    <HeaderStyle Width="60px" />
                                </asp:BoundField>

                                <asp:BoundField DataField="TYPE_NAME" HeaderText="<%$ Resources:DigiportAdminResource, Names_TypeColumn %>"
                                    SortExpression="TYPE_NAME" HeaderStyle-CssClass="grid-header-cell" ItemStyle-CssClass="grid-cell grid-cell-type">
                                    <HeaderStyle Width="110px" />
                                </asp:BoundField>

                                <asp:BoundField DataField="NAME" HeaderText="<%$ Resources:DigiportAdminResource, Names_NameColumn %>"
                                    SortExpression="NAME" HeaderStyle-CssClass="grid-header-cell" ItemStyle-CssClass="grid-cell grid-cell-name">
                                    <HeaderStyle Width="150px" />
                                </asp:BoundField>

                                <asp:BoundField DataField="DESCRIPTION" HeaderText="<%$ Resources:DigiportAdminResource, Names_DescriptionColumn %>"
                                    SortExpression="DESCRIPTION" HeaderStyle-CssClass="grid-header-cell" ItemStyle-CssClass="grid-cell grid-cell-description" />

                                <asp:TemplateField HeaderText="<%$ Resources:DigiportAdminResource, Names_StatusColumn %>"
                                    SortExpression="AKTIF_DURUM" HeaderStyle-CssClass="grid-header-cell" ItemStyle-CssClass="grid-cell grid-cell-status">
                                    <HeaderStyle Width="90px" />
                                    <ItemTemplate>
                                    <span class='<%# ((string)Eval("AKTIF_DURUM")).Contains("AKTİF") ? "status-badge status-active" : "status-badge status-inactive" %>'>
                                        <%# GetStatusIcon(Eval("AKTIF_DURUM") as string) %>
                                        <%# GetStatusText(Eval("AKTIF_DURUM") as string) %>
                                    </span>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="<%$ Resources:DigiportAdminResource, Names_CreatedColumn %>"
                                    SortExpression="CREATED" HeaderStyle-CssClass="grid-header-cell" ItemStyle-CssClass="grid-cell grid-cell-created">
                                    <HeaderStyle Width="120px" />
                                    <ItemTemplate>
                                        <div class="created-info">
                                            <i class="far fa-calendar-alt"></i>
                                            <span><%# Eval("CREATED", "{0:dd.MM.yyyy}") %></span>
                                            <small class="time-info"><%# Eval("CREATED", "{0:HH:mm}") %></small>
                                        </div>
                                    </ItemTemplate>
                                </asp:TemplateField>

                                <asp:TemplateField HeaderText="<%$ Resources:DigiportAdminResource, Names_Actions %>"
                                    HeaderStyle-CssClass="grid-header-cell grid-header-actions" ItemStyle-CssClass="grid-cell grid-cell-actions">
                                    <HeaderStyle Width="140px" />
                                    <ItemTemplate>
                                        <div class="action-buttons">
                                            <asp:LinkButton ID="lnkEdit" runat="server"
                                                CommandName="EditRecord"
                                                CommandArgument='<%# Eval("ID") %>'
                                                CssClass="action-btn action-btn-edit"
                                                ToolTip="<%$ Resources:DigiportAdminResource, Names_Edit %>"
                                                CausesValidation="false">
                                                <i class="fas fa-edit"></i>
                                                <span class="action-text"><%# GetGlobalResourceObject("DigiportAdminResource", "Edit") %></span>
                                            </asp:LinkButton>
                                        </div>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>

                            <HeaderStyle CssClass="grid-header" />
                            <RowStyle CssClass="grid-row" />
                            <AlternatingRowStyle CssClass="grid-row-alt" />
                            <SelectedRowStyle CssClass="grid-row-selected" />
                            <EmptyDataRowStyle CssClass="grid-empty-row" />
                        </asp:GridView>
                    </div>
                </div>
            </div>
            <%-- Closing app-container div --%>
        </ContentTemplate>
    </asp:UpdatePanel>
    <%-- Hidden literals for JavaScript resource access --%>
    <asp:Literal ID="ltlActiveText" runat="server" Text="<%$ Resources:DigiportAdminResource, Names_StatusActive %>" Visible="false"></asp:Literal>
    <asp:Literal ID="ltlInactiveText" runat="server" Text="<%$ Resources:DigiportAdminResource, Names_StatusInactive %>" Visible="false"></asp:Literal>
    <asp:Literal ID="ltlDeleteConfirmTR" runat="server" Text="<%$ Resources:DigiportAdminResource, DeleteConfirmation %>" Visible="false"></asp:Literal>
    <asp:Literal ID="ltlDeleteConfirmEN" runat="server" Text="<%$ Resources:DigiportAdminResource, DeleteConfirmation %>" Visible="false"></asp:Literal>
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
            initializeInterface();
        });        // Initialize all UI components
        function initializeInterface() {
            setupToggleSwitch();
            setupDropdownEnhancements();
            enhanceGridInteractions();
        }

        // DevExpress Grid Callback Handlers
        function onGridBeginCallback(sender, e) {
            // Add visual feedback during sorting/filtering
            const gridElement = document.getElementById('<%= grdNames.ClientID %>');
            if (gridElement) {
                gridElement.style.opacity = '0.8';
                gridElement.style.pointerEvents = 'none';
            }
        }

        function onGridEndCallback(sender, e) {
            // Restore grid state and re-initialize interactions
            const gridElement = document.getElementById('<%= grdNames.ClientID %>');
            if (gridElement) {
                gridElement.style.opacity = '1';
                gridElement.style.pointerEvents = 'auto';
            }

            // Re-initialize button interactions after grid callback
            setTimeout(function () {
                enhanceGridButtons();
            }, 100);
        }

        // Setup status toggle with visual feedback
        function setupToggleSwitch() {
            const statusToggle = document.getElementById('<%= chkAktif.ClientID %>');
            const statusText = document.getElementById('statusText');
            const activeText = document.getElementById('<%= ltlActiveText.ClientID %>') ? document.getElementById('<%= ltlActiveText.ClientID %>').innerHTML : 'Active';
            const inactiveText = document.getElementById('<%= ltlInactiveText.ClientID %>') ? document.getElementById('<%= ltlInactiveText.ClientID %>').innerHTML : 'Inactive';

            if (statusToggle && statusText) {
                // Set initial state
                updateStatusDisplay();

                // Add change handler
                statusToggle.addEventListener('change', function () {
                    updateStatusDisplay();
                });

                // Update visual display
                function updateStatusDisplay() {
                    if (statusToggle.checked) {
                        statusText.innerText = activeText;
                        statusText.classList.remove('inactive');
                    } else {
                        statusText.innerText = inactiveText;
                        statusText.classList.add('inactive');
                    }
                }
            }
        }

        // Enhance dropdowns with search
        function setupDropdownEnhancements() {
            const dropdowns = document.querySelectorAll('.form-select');

            dropdowns.forEach(dropdown => {
                // Add visual enhancements
                dropdown.addEventListener('focus', function () {
                    this.parentElement?.classList.add('focused');
                });

                dropdown.addEventListener('blur', function () {
                    this.parentElement?.classList.remove('focused');
                });
            });
        }        // Enhance grid interactions
        function enhanceGridInteractions() {
            // Add smooth scrolling to grid after form submission
            const grid = document.getElementById('<%= grdNames.ClientID %>');
            if (grid) {
                // Handle grid callbacks to maintain data
                if (typeof gridNames !== 'undefined') {
                    // Override BeginCallback to ensure data preservation
                    gridNames.BeginCallback.AddHandler(function (s, e) {
                        // Add loading indicator
                        const gridElement = document.getElementById('<%= grdNames.ClientID %>');
                        if (gridElement) {
                            gridElement.style.opacity = '0.7';
                            gridElement.style.pointerEvents = 'none';
                        }
                    });

                    // Override EndCallback to restore grid state
                    gridNames.EndCallback.AddHandler(function (s, e) {
                        // Remove loading indicator
                        const gridElement = document.getElementById('<%= grdNames.ClientID %>');
                        if (gridElement) {
                            gridElement.style.opacity = '1';
                            gridElement.style.pointerEvents = 'auto';
                        }

                        // Re-initialize interactions after callback
                        setTimeout(function () {
                            enhanceGridButtons();
                        }, 100);
                    });
                }

                // Initialize button interactions
                enhanceGridButtons();
            }
        }

        // Separate function for button interactions to allow re-initialization
        function enhanceGridButtons() {
            const grid = document.getElementById('<%= grdNames.ClientID %>');
            if (!grid) return;

            // Add loading states and smooth animations for edit buttons
            const editButtons = grid.querySelectorAll('.action-btn-edit');
            editButtons.forEach(button => {
                // Remove existing listeners to prevent duplicates
                button.removeEventListener('click', handleEditClick);
                button.addEventListener('click', handleEditClick);
            });

            // Add confirmation for delete buttons
            const deleteButtons = grid.querySelectorAll('.action-btn-delete');
            deleteButtons.forEach(button => {
                // Remove existing listeners to prevent duplicates
                button.removeEventListener('click', handleDeleteClick);
                button.addEventListener('click', handleDeleteClick);
            });
        }

        function handleEditClick() {
            // Add loading state for edit
            const icon = this.querySelector('i');
            const originalClass = icon.className;
            icon.className = 'fas fa-spinner fa-spin';

            // Restore icon after a delay (will be replaced by server response)
            setTimeout(() => {
                icon.className = originalClass;
            }, 2000);        }

        // Confirm delete function for OnClientClick
        function confirmDelete() {
            // Get confirmation messages in both languages
            const confirmTR = document.getElementById('<%= ltlDeleteConfirmTR.ClientID %>') ?
                document.getElementById('<%= ltlDeleteConfirmTR.ClientID %>').innerHTML :
                <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, DeleteConfirmation%>" />;
            const confirmEN = document.getElementById('<%= ltlDeleteConfirmEN.ClientID %>') ?
                document.getElementById('<%= ltlDeleteConfirmEN.ClientID %>').innerHTML :
                <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, DeleteConfirmation%>" />;

            // Show bilingual confirmation
            const confirmMessage = confirmTR + '\n\n' + confirmEN;
            return confirm(confirmMessage);
        }

        function handleDeleteClick(e) {
            e.preventDefault(); // Prevent immediate postback            // Get confirmation messages in both languages
            const confirmTR = document.getElementById('<%= ltlDeleteConfirmTR.ClientID %>') ?
                document.getElementById('<%= ltlDeleteConfirmTR.ClientID %>').innerHTML :
                <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, DeleteConfirmation%>" />;
            const confirmEN = document.getElementById('<%= ltlDeleteConfirmEN.ClientID %>') ?
                document.getElementById('<%= ltlDeleteConfirmEN.ClientID %>').innerHTML :
                <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, DeleteConfirmation%>" />;

            // Show bilingual confirmation
            const confirmMessage = confirmTR + '\n\n' + confirmEN;

            if (confirm(confirmMessage)) {
                // User confirmed, proceed with delete
                const icon = this.querySelector('i');
                const originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin';

                // Trigger the server-side click event
                __doPostBack(this.name, '');
            }

            return false;
        }

        // Add new record
        function addNewRecord() {
            // Reset form
            const typeDropdown = document.getElementById('<%= drpType.ClientID %>');
            const nameField = document.getElementById('<%= txtName.ClientID %>');
            const nameEnField = document.getElementById('<%= txtNameEn.ClientID %>');
            const descField = document.getElementById('<%= txtDescription.ClientID %>');
            const descEnField = document.getElementById('<%= txtDescriptionEn.ClientID %>');
            const pathField = document.getElementById('<%= txtPagePath.ClientID %>');
            const statusToggle = document.getElementById('<%= chkAktif.ClientID %>');

            if (typeDropdown) typeDropdown.selectedIndex = 0;
            if (nameField) nameField.value = '';
            if (nameEnField) nameEnField.value = '';
            if (descField) descField.value = '';
            if (descEnField) descEnField.value = '';
            if (pathField) pathField.value = '';
            if (statusToggle) statusToggle.checked = true;

            // Update status display
            setupToggleSwitch();

            // Smooth scroll to form
            const formCard = document.querySelector('.card');
            if (formCard) {
                formCard.scrollIntoView({ behavior: 'smooth', block: 'start' });

                // Add focus animation
                formCard.style.animation = 'pulse 0.6s ease-in-out';
                setTimeout(() => {
                    formCard.style.animation = '';
                }, 600);
            }

            // Focus first field
            if (typeDropdown) {
                setTimeout(() => typeDropdown.focus(), 300);
            }
        }

        // Scroll to grid after successful operation
        function scrollToGrid() {
            const gridCard = document.querySelector('.card:last-of-type');
            if (gridCard) {
                setTimeout(() => {
                    gridCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }, 100);
            }
        }

        // Enhanced form validation feedback
        function enhanceFormValidation() {
            const validators = document.querySelectorAll('.text-danger');
            validators.forEach(validator => {
                if (validator.style.display !== 'none' && validator.innerHTML.trim() !== '') {
                    validator.style.animation = 'shake 0.5s ease-in-out';

                    // Scroll to first error
                    const firstError = document.querySelector('.text-danger[style*="display: block"], .text-danger:not([style*="display: none"])');
                    if (firstError && firstError === validator) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }
            });
        }

        // Re-initialize after UpdatePanel partial postback
        if (typeof (Sys) !== 'undefined' && Sys.WebForms) {
            Sys.WebForms.PageRequestManager.getInstance().add_endRequest(function () {
                initializeInterface(); // Re-initialize all components
                enhanceFormValidation(); // Check for validation errors
            });
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.02); }
                100% { transform: scale(1); }
            }

            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }

            .focused {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(92, 45, 145, 0.15);
            }
        `;
        document.head.appendChild(style);

    </script>
</asp:Content>
