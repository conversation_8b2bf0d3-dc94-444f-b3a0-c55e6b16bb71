using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using CoreHelpers;
using Entities;
using FormHelper.YetkiHelper;
using AdminPages;
using FormHelper.DigiportAdmin;
using System.Data;
using Oracle.DataAccess.Client;
using Resources;

namespace AracTakipSistemi.AdminPages.DigiportMenu
{
    public partial class UserAssignment : AdminAbstract
    {
        #region Properties & Fields        // Helper property to safely access the Master Page
        protected SiteMaster CurrentMaster => this.Master as SiteMaster;

        // Resource-based messages for UI
        private string MSG_SUCCESS => DigiportAdminResource.MSG_SUCCESS;
        private string MSG_ERROR => DigiportAdminResource.MSG_ERROR;
        private string MSG_SELECT_PAGE => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_SelectPage")?.ToString() ?? "Lütfen önce bir sayfa seçiniz.";
        private string MSG_SELECT_USER => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_SelectUser")?.ToString() ?? "Lütfen bir kullanıcı seçiniz.";
        private string MSG_SELECT_GROUP => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_SelectGroup")?.ToString() ?? "Lütfen bir AD grup seçiniz.";
        private string MSG_USER_DUPLICATE => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_UserDuplicate")?.ToString() ?? "Bu kullanıcı bu sayfa için zaten yetkilendirilmiş!";
        private string MSG_GROUP_DUPLICATE => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_GroupDuplicate")?.ToString() ?? "Bu AD grup bu sayfa için zaten yetkilendirilmiş!";
        private string MSG_SAVE_SUCCESS => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_SaveSuccess")?.ToString() ?? "Yetkilendirme başarıyla kaydedildi.";
        private string MSG_UPDATE_SUCCESS => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_UpdateSuccess")?.ToString() ?? "Yetkilendirme başarıyla güncellendi.";
        private string MSG_DELETE_SUCCESS => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_DeleteSuccess")?.ToString() ?? "Yetkilendirme başarıyla silindi.";
        private string MSG_RECORD_NOT_FOUND => DigiportAdminResource.MSG_RECORD_NOT_FOUND;

        // Error messages for logging and display
        private string ERR_PERMISSION => DigiportAdminResource.ERR_PERMISSION;
        private string ERR_CONFIG => DigiportAdminResource.ERR_CONFIG;
        private string ERR_LOAD_PAGES => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_LoadPagesError")?.ToString() ?? "Sayfa listesi yüklenirken hata oluştu.";
        private string ERR_LOAD_USERS => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_LoadUsersError")?.ToString() ?? "Kullanıcı listesi yüklenirken hata oluştu.";
        private string ERR_LOAD_GROUPS => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_LoadGroupsError")?.ToString() ?? "AD Grup listesi yüklenirken hata oluştu.";
        private string ERR_LOAD_GRID => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_LoadGridError")?.ToString() ?? "Yetki listesi yüklenirken hata oluştu.";
        private string ERR_DATA_MAPPING => DigiportAdminResource.ERR_DATA_MAPPING;
        private string ERR_SAVE => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_SaveError")?.ToString() ?? "Yetki kaydedilirken hata oluştu.";
        private string ERR_UPDATE => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_UpdateError")?.ToString() ?? "Yetki güncellenirken hata oluştu.";
        private string ERR_DELETE => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_DeleteError")?.ToString() ?? "Yetki silinirken hata oluştu.";
        private string ERR_GET_RECORD => GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_GetRecordError")?.ToString() ?? "Yetki bilgileri getirilirken hata oluştu.";

        // Constants for dropdown default values
        private const string VAL_DEFAULT_USER = "0";
        private const string VAL_DEFAULT_GROUP = "";
        private const string VAL_DEFAULT_ACTIVE = "Y";
        private const string VAL_DEFAULT_INACTIVE = "N";

        // Manually added button controls that were not auto-generated
        protected System.Web.UI.WebControls.Button btnCreateNew;
        protected System.Web.UI.WebControls.Button btnUpdate;

        #endregion

        #region Page Events
        private void RegisterGridItemsForEventValidation()
        {
            try
            {
                int nameId = ConvertionHelper.ConvertValue<int>(drpName.SelectedValue);
                if (nameId <= 0) return;

                var assignments = UserAssignmentHelper.GetAssignments(nameId, "");
                if (assignments == null || assignments.Rows.Count == 0) return;

                foreach (DataRow row in assignments.Rows)
                {
                    string id = row["ID"].ToString();

                    // Register all commands that might be used
                    ClientScript.RegisterForEventValidation(grdUserAssignments.UniqueID, "DeleteItem$" + id);

                    // If you have sorting enabled
                    ClientScript.RegisterForEventValidation(grdUserAssignments.UniqueID, "Sort$DISPLAY_NAME");
                    ClientScript.RegisterForEventValidation(grdUserAssignments.UniqueID, "Sort$AKTIF");
                    ClientScript.RegisterForEventValidation(grdUserAssignments.UniqueID, "Sort$CREATED");

                    // If you have paging enabled
                    for (int i = 0; i < grdUserAssignments.PageCount; i++)
                    {
                        ClientScript.RegisterForEventValidation(grdUserAssignments.UniqueID, "Page$" + i);
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("RegisterGridItemsForEventValidation", ex);
            }
        }
        private void InitializeLocalizedText()
        {
            try
            {
                // Set localized text for JavaScript consumption
                ltlActiveText.Text = Resources.DigiportAdminResource.Active ?? "Aktif";
                ltlInactiveText.Text = Resources.DigiportAdminResource.Inactive ?? "Pasif";
            }
            catch (Exception ex)
            {
                LogError("InitializeLocalizedText", ex);
                // Fallback to hardcoded Turkish text
                ltlActiveText.Text = "Aktif";
                ltlInactiveText.Text = "Pasif";
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                if (!IsPostBack)
                {
                    // Authorization Check
                    if (!HasPermission())
                    {
                        Response.Redirect("~/AdminPages/Exception/Hata.aspx?Msg=Yetkisiz Erişim", false);
                        Context.ApplicationInstance.CompleteRequest();
                        return;
                    }                    // Set page title
                    ((SiteMaster)this.Master).SayfaBaslikAt("Digiport Sayfa Yetkilendirme");                    // Initialize localized text for JavaScript
                    InitializeLocalizedText();

                    // Initial Data Loading
                    LoadNamesList();
                    LoadUsersDropdown();

                    // Initial UI State
                    pnlAssignmentSection.Visible = false;
                    pnlAssignmentList.Visible = false;
                    ResetFormState();

                    // Register for event validation for GridView
                    RegisterGridItemsForEventValidation();
                }
                else
                {
                    // Initialize localized text for JavaScript on postbacks too
                    InitializeLocalizedText();

                    // On postback, check if we have a stored ID in ViewState
                    object storedId = ViewState["SelectedAssignmentId"];
                    if (storedId != null && int.TryParse(storedId.ToString(), out int viewStateId) && viewStateId > 0)
                    {
                        // If the hidden field value is different from ViewState, use the ViewState value
                        int hiddenFieldId = ConvertionHelper.ConvertValue<int>(hdnSelectedAssignmentId.Value);
                        if (hiddenFieldId != viewStateId)
                        {
                            System.Diagnostics.Debug.WriteLine($"Restoring ID from ViewState: {viewStateId} (hidden field had: {hiddenFieldId})");
                            hdnSelectedAssignmentId.Value = viewStateId.ToString();
                            hdnEditMode.Value = "1";
                        }
                    }

                    // Check the edit mode and set button visibility accordingly
                    int assignmentId = ConvertionHelper.ConvertValue<int>(hdnSelectedAssignmentId.Value);
                    bool isEditMode = assignmentId > 0 || hdnEditMode.Value == "1";

                    btnCreateNew.Visible = !isEditMode;
                    btnUpdate.Visible = isEditMode;

                    System.Diagnostics.Debug.WriteLine($"PostBack - Edit Mode: {isEditMode}, ID: {assignmentId}, Create visible: {btnCreateNew.Visible}, Update visible: {btnUpdate.Visible}");

                    // If we're in edit mode but the form is not showing the correct data, reload it
                    if (isEditMode && assignmentId > 0 &&
                        (selectedEntityDiv.Visible == false || string.IsNullOrEmpty(lblSelectedEntity.Text)))
                    {
                        System.Diagnostics.Debug.WriteLine($"Edit mode active but form not showing data, reloading record ID: {assignmentId}");
                        Kayit_Getir(assignmentId);
                    }
                }

                // Sync checkbox with dropdown for toggle switch
                SyncStatusToggleWithDropdown();
            }
            catch (Exception ex)
            {
                LogError("Page_Load", ex);
                ShowErrorMessage("Sayfa yüklenirken beklenmeyen bir hata oluştu.");
            }
        }

        #endregion

        #region Authorization & Security

        private bool HasPermission()
        {
            try
            {
                string techCorpGroup = ConfigurationManager.AppSettings["AdGroup_TechCorp"];
                string pdostbGroup = ConfigurationManager.AppSettings["AdGroup_PDOSTB"];

                if (string.IsNullOrEmpty(techCorpGroup) || string.IsNullOrEmpty(pdostbGroup))
                {
                    CurrentMaster?.PopupGoster("Konfigürasyon Hatası", ERR_CONFIG, true);
                    return false;
                }

                var userGroups = new YetkiHelper().gruplar()
                                  ?.Select(g => g.ToUpperInvariant()).ToList() ?? new List<string>();

                return userGroups.Contains(techCorpGroup.ToUpperInvariant()) ||
                       userGroups.Contains(pdostbGroup.ToUpperInvariant());
            }
            catch (Exception ex)
            {
                LogError("HasPermission", ex);
                CurrentMaster?.PopupGoster(MSG_ERROR, ERR_PERMISSION, true);
                return false;
            }
        }

        #endregion

        #region Data Loading Methods

        private void LoadNamesList()
        {
            try
            {
                var namesList = UserAssignmentHelper.GetNamesList();
                drpName.DataSource = namesList;
                drpName.DataTextField = "NAME";
                drpName.DataValueField = "ID";
                drpName.DataBind();
                drpName.Items.Insert(0, new ListItem(DigiportAdminResource.UserAssignment_SelectPage, "0"));
            }
            catch (Exception ex)
            {
                LogError("LoadNamesList", ex);
                ShowErrorMessage(ERR_LOAD_PAGES);
            }
        }

        private void LoadUsersDropdown()
        {
            try
            {
                var usersList = UserAssignmentHelper.GetUsersList();
                drpUsers.DataSource = usersList;
                drpUsers.DataTextField = "NAME_SURNAME";
                drpUsers.DataValueField = "F_LOGIN_ID";
                drpUsers.DataBind();
                drpUsers.Items.Insert(0, new ListItem(DigiportAdminResource.UserAssignment_SelectUser, VAL_DEFAULT_USER));
            }
            catch (Exception ex)
            {
                LogError("LoadUsersDropdown", ex);
                ShowErrorMessage(ERR_LOAD_USERS);
            }
        }

        #endregion

        #region UI Event Handlers
        protected void drpName_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                int nameId = ConvertionHelper.ConvertValue<int>(drpName.SelectedValue);
                bool isPageSelected = nameId > 0;

                // Check if we're in edit mode
                int assignmentId = ConvertionHelper.ConvertValue<int>(hdnSelectedAssignmentId.Value);
                bool isEditMode = assignmentId > 0 || hdnEditMode.Value == "1";

                // Update UI visibility based on selection
                pnlAssignmentSection.Visible = isPageSelected;
                pnlAssignmentList.Visible = isPageSelected;

                if (isPageSelected)
                {
                    lblSelectedPageName.Text = drpName.SelectedItem.Text;

                    // Only reset form state if not in edit mode
                    if (!isEditMode)
                    {
                        ResetFormState();
                    }

                    Grid_Doldur();

                    // Register GridView items for event validation after loading the grid
                    RegisterGridItemsForEventValidation();
                }
                else
                {
                    lblSelectedPageName.Text = string.Empty;
                    grdUserAssignments.DataSource = null;
                    grdUserAssignments.DataBind();
                }
            }
            catch (Exception ex)
            {
                LogError("drpName_SelectedIndexChanged", ex);
                ShowErrorMessage("Sayfa seçilirken bir hata oluştu.");
            }
        }
        protected void rbAssignmentType_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                bool isUserSelected = rbUser.Checked;
                divUserSelection.Visible = isUserSelected;
                divGroupSelection.Visible = !isUserSelected;

                rfvUser.Enabled = isUserSelected;
                rfvGroup.Enabled = !isUserSelected;

                // Reset the *other* dropdown
                if (isUserSelected)
                {
                    if (drpGroups.Items.FindByValue(VAL_DEFAULT_GROUP) != null)
                        drpGroups.SelectedValue = VAL_DEFAULT_GROUP;
                }
                else
                {
                    if (drpUsers.Items.FindByValue(VAL_DEFAULT_USER) != null)
                        drpUsers.SelectedValue = VAL_DEFAULT_USER;
                }

                // Update client-side UI state
                ScriptManager.RegisterStartupScript(this, GetType(), "UpdateSelectionUI",
                    "if(typeof(setupAssignmentTypeToggle) === 'function') { setupAssignmentTypeToggle(); }", true);
            }
            catch (Exception ex)
            {
                LogError("rbAssignmentType_CheckedChanged", ex);
                ShowErrorMessage("Yetki tipi değiştirilirken bir hata oluştu.");
            }
        }

        private void LoadGroupsDropdown(string domain)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Getting AD groups list for domain: {domain}");
                var groupsList = UserAssignmentHelper.GetAdGroupsList(domain);
                System.Diagnostics.Debug.WriteLine($"AD groups returned: {groupsList.Rows.Count}");

                // Store the currently selected group if any
                string currentlySelectedGroup = drpGroups.SelectedValue;

                // Get the group from ViewState if we're in edit mode
                string groupToSelect = ViewState["SelectedADGroup"] as string;
                if (string.IsNullOrEmpty(groupToSelect))
                {
                    groupToSelect = currentlySelectedGroup;
                }

                System.Diagnostics.Debug.WriteLine($"Group to select after binding: {groupToSelect}");

                drpGroups.DataSource = groupsList;
                drpGroups.DataTextField = "AD_GROUP";
                drpGroups.DataValueField = "AD_GROUP";
                drpGroups.DataBind();

                // Always make sure the default item is "--Grup Seçiniz--" with empty value                if (drpGroups.Items.FindByValue("") == null)
                {
                    drpGroups.Items.Insert(0, new ListItem(DigiportAdminResource.UserAssignment_SelectGroup, ""));
                }

                // Try to select the previously selected group or the one from ViewState
                if (!string.IsNullOrEmpty(groupToSelect) && groupToSelect != "")
                {
                    if (drpGroups.Items.FindByValue(groupToSelect) != null)
                    {
                        drpGroups.SelectedValue = groupToSelect;
                        System.Diagnostics.Debug.WriteLine($"Successfully selected group: {groupToSelect}");
                    }
                    else
                    {
                        // If the group is not in the dropdown, add it manually
                        System.Diagnostics.Debug.WriteLine($"Group {groupToSelect} not found in dropdown, adding it manually");
                        ListItem newItem = new ListItem(groupToSelect, groupToSelect);
                        drpGroups.Items.Add(newItem);
                        drpGroups.SelectedValue = groupToSelect;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Groups loaded: {drpGroups.Items.Count} items, Selected: {drpGroups.SelectedValue}");
            }
            catch (Exception ex)
            {
                LogError("LoadGroupsDropdown", ex);
                ShowErrorMessage(DigiportAdminResource.UserAssignment_ErrorLoadingGroups + ": " + ex.Message);                // Still make sure there's a default item in the dropdown even in case of error
                drpGroups.Items.Clear();
                drpGroups.Items.Insert(0, new ListItem(DigiportAdminResource.UserAssignment_SelectGroup, ""));

                // Try to restore the selected group from ViewState
                string groupToSelect = ViewState["SelectedADGroup"] as string;
                if (!string.IsNullOrEmpty(groupToSelect))
                {
                    ListItem newItem = new ListItem(groupToSelect, groupToSelect);
                    drpGroups.Items.Add(newItem);
                    drpGroups.SelectedValue = groupToSelect;
                    System.Diagnostics.Debug.WriteLine($"Added group from ViewState after error: {groupToSelect}");
                }
            }
        }

        protected void btnSaveAssignment_Click(object sender, EventArgs e)
        {
            try
            {
                int nameId = ConvertionHelper.ConvertValue<int>(drpName.SelectedValue);
                if (nameId == 0)
                {
                    ShowErrorMessage(MSG_SELECT_PAGE);
                    return;
                }

                int assignmentId = ConvertionHelper.ConvertValue<int>(hdnSelectedAssignmentId.Value);
                bool isEditMode = assignmentId > 0;

                // Log the current state
                System.Diagnostics.Debug.WriteLine($"Save button clicked: EditMode={isEditMode}, AssignmentID={assignmentId}");

                // Validate selection
                if (rbUser.Checked && drpUsers.SelectedValue == VAL_DEFAULT_USER)
                {
                    ShowErrorMessage(MSG_SELECT_USER);
                    return;
                }

                if (rbAdGroup.Checked && string.IsNullOrEmpty(drpGroups.SelectedValue))
                {
                    ShowErrorMessage(MSG_SELECT_GROUP);
                    return;
                }

                // Sync status toggle checkbox with dropdown
                SyncDropdownWithStatusToggle();

                // Perform save or update
                if (isEditMode)
                {
                    System.Diagnostics.Debug.WriteLine($"Updating existing record with ID: {assignmentId}");
                    Guncelle(assignmentId);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Creating new record");
                    Kaydet();
                }
            }
            catch (Exception ex)
            {
                LogError("btnSaveAssignment_Click", ex);
                ShowErrorMessage("İşlem sırasında bir hata oluştu.");
            }
        }

        protected void btnCancelEdit_Click(object sender, EventArgs e)
        {
            try
            {
                Temizle();
            }
            catch (Exception ex)
            {
                LogError("btnCancelEdit_Click", ex);
                ShowErrorMessage("İşlem iptal edilirken bir hata oluştu.");
            }
        }

        protected void grdUserAssignments_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "EditItem")
                {
                    int recID = Convert.ToInt32(e.CommandArgument);
                    System.Diagnostics.Debug.WriteLine($"Edit command received for ID: {recID}");

                    // Clear any previous ViewState data
                    ViewState["SelectedADGroup"] = null;

                    // Store ID in both hidden field and ViewState for redundancy
                    hdnSelectedAssignmentId.Value = recID.ToString();
                    hdnEditMode.Value = "1";
                    ViewState["SelectedAssignmentId"] = recID;

                    // Now load the record with the correct ID
                    Kayit_Getir(recID);

                    // Make the assignment form panel visible
                    pnlAssignmentSection.Visible = true;

                    // Show update button, hide create button
                    btnCreateNew.Visible = false;
                    btnUpdate.Visible = true;

                    // Update the UI selection without using client-side JS
                    foreach (GridViewRow row in grdUserAssignments.Rows)
                    {
                        if (row.RowType == DataControlRowType.DataRow)
                        {
                            // Get the ID from the row's data attribute
                            string rowId = row.Attributes["data-id"];
                            if (rowId == recID.ToString())
                            {
                                row.CssClass = row.CssClass + " selected";
                            }
                            else if (row.CssClass != null && row.CssClass.Contains("selected"))
                            {
                                row.CssClass = row.CssClass.Replace(" selected", "");
                            }
                        }
                    }

                    // Ensure the assignment section is visible after update
                    ScriptManager.RegisterStartupScript(this, GetType(), "ScrollToAssignment",
                        "setTimeout(function() { document.getElementById('divAssignmentSection').scrollIntoView({behavior: 'smooth'}); }, 100);",
                        true);
                }
                else if (e.CommandName == "DeleteItem")
                {
                    int recID = Convert.ToInt32(e.CommandArgument);
                    System.Diagnostics.Debug.WriteLine($"Delete command received for ID: {recID}");
                    Sil(recID);
                }
                else if (e.CommandName == "Refresh")
                {
                    // Just refresh the grid
                    Grid_Doldur();
                }
            }
            catch (Exception ex)
            {
                LogError($"grdUserAssignments_RowCommand", ex);
                ShowErrorMessage("İşlem sırasında bir hata oluştu: " + ex.Message);
            }
        }
        protected void grdUserAssignments_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            try
            {
                if (e.Row.RowType == DataControlRowType.DataRow)
                {
                    // Get the data item
                    DataRowView drv = e.Row.DataItem as DataRowView;
                    if (drv != null)
                    {
                        // Add row attributes for client-side interactions
                        string id = drv["ID"].ToString();
                        e.Row.Attributes["data-id"] = id;
                        e.Row.Attributes["id"] = "gridRow_" + id;

                        // Add additional data attributes as needed
                        var loginId = drv["F_LOGIN_ID"];
                        var adGroup = drv["AD_GROUP"];

                        if (loginId != DBNull.Value && Convert.ToDecimal(loginId) > 0)
                        {
                            e.Row.Attributes["data-type"] = "user";
                        }
                        else if (adGroup != DBNull.Value && !string.IsNullOrEmpty(adGroup.ToString()))
                        {
                            e.Row.Attributes["data-type"] = "group";
                        }

                        // Highlight the selected row if it matches the current edit ID
                        int selectedId = ConvertionHelper.ConvertValue<int>(hdnSelectedAssignmentId.Value);
                        if (selectedId > 0 && id == selectedId.ToString())
                        {
                            e.Row.CssClass += " selected";
                            System.Diagnostics.Debug.WriteLine($"Row {id} selected because it matches selectedId {selectedId}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("grdUserAssignments_RowDataBound", ex);
                // Don't show an error message as this would interrupt the rendering flow
            }
        }
        protected void btnAddNew_Click(object sender, EventArgs e)
        {
            // Reset form to add mode
            hdnSelectedAssignmentId.Value = "0";
            hdnEditMode.Value = "0";
            ViewState["SelectedAssignmentId"] = null;
            ViewState["SelectedADGroup"] = null;

            // Show assignment section
            pnlAssignmentSection.Visible = true;

            // Toggle button visibility
            btnCreateNew.Visible = true;
            btnUpdate.Visible = false;

            // Reset form fields
            ResetFormState();
        }
        protected void grdUserAssignments_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            try
            {
                grdUserAssignments.PageIndex = e.NewPageIndex;
                Grid_Doldur();
            }
            catch (Exception ex)
            {
                LogError("grdUserAssignments_PageIndexChanging", ex);
                ShowErrorMessage("Sayfa değiştirme işlemi sırasında bir hata oluştu.");
            }
        }

        protected void grdUserAssignments_Sorting(object sender, GridViewSortEventArgs e)
        {
            try
            {
                // Get the current data source
                int nameId = ConvertionHelper.ConvertValue<int>(drpName.SelectedValue);
                string searchTerm = txtSearchAssignments.Text.Trim();
                string statusFilter = drpStatusFilter.SelectedValue;

                var dataSource = UserAssignmentHelper.GetAssignments(nameId, searchTerm);

                // Apply status filter if selected
                if (!string.IsNullOrEmpty(statusFilter))
                {
                    DataView dv = dataSource.DefaultView;
                    dv.RowFilter = $"AKTIF = '{statusFilter}'";
                    dataSource = dv.ToTable();
                }

                // Apply sorting
                DataView sortedView = dataSource.DefaultView;

                // Get the current sort direction from ViewState or default to ascending
                string sortDirection = ViewState["SortDirection"] as string;
                if (sortDirection == "ASC" && ViewState["SortExpression"] as string == e.SortExpression)
                {
                    sortDirection = "DESC";
                }
                else
                {
                    sortDirection = "ASC";
                }

                // Store the new sort expression and direction
                ViewState["SortExpression"] = e.SortExpression;
                ViewState["SortDirection"] = sortDirection;

                // Apply the sort
                sortedView.Sort = $"{e.SortExpression} {sortDirection}";

                // Bind the sorted data
                grdUserAssignments.DataSource = sortedView.ToTable();
                grdUserAssignments.DataBind();
            }
            catch (Exception ex)
            {
                LogError("grdUserAssignments_Sorting", ex);
                ShowErrorMessage("Sıralama işlemi sırasında bir hata oluştu.");
            }
        }

        // Filter handlers for the grid - removed DevExpress specific code

        protected void txtSearchAssignments_TextChanged(object sender, EventArgs e)
        {
            try
            {
                grdUserAssignments.PageIndex = 0;
                Grid_Doldur();

                // Update client-side counter
                ScriptManager.RegisterStartupScript(this, GetType(), "UpdateCounterUI",
                    "if(typeof(countRecords) === 'function') { countRecords(); }", true);
            }
            catch (Exception ex)
            {
                LogError("txtSearchAssignments_TextChanged", ex);
                ShowErrorMessage("Arama işlemi sırasında bir hata oluştu.");
            }
        }

        protected void drpStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                grdUserAssignments.PageIndex = 0;
                Grid_Doldur();

                // Update client-side counter
                ScriptManager.RegisterStartupScript(this, GetType(), "UpdateCounterUI",
                    "if(typeof(countRecords) === 'function') { countRecords(); }", true);
            }
            catch (Exception ex)
            {
                LogError("drpStatusFilter_SelectedIndexChanged", ex);
                ShowErrorMessage("Durum filtresi uygulanırken bir hata oluştu.");
            }
        }

        // For custom pagination dropdown if implemented
        protected void ddlPage_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                DropDownList ddlPages = sender as DropDownList;
                if (ddlPages != null && int.TryParse(ddlPages.SelectedValue, out int pageIndex))
                {
                    grdUserAssignments.PageIndex = pageIndex;
                    Grid_Doldur();
                }
            }
            catch (Exception ex)
            {
                LogError("ddlPage_SelectedIndexChanged", ex);
                ShowErrorMessage("Sayfa değiştirme işlemi sırasında bir hata oluştu.");
            }
        }

        protected void btnCreateNew_Click(object sender, EventArgs e)
        {
            try
            {
                int nameId = ConvertionHelper.ConvertValue<int>(drpName.SelectedValue);
                if (nameId == 0)
                {
                    ShowErrorMessage(MSG_SELECT_PAGE);
                    return;
                }

                // Make sure we're in Create mode
                hdnSelectedAssignmentId.Value = "0";
                hdnEditMode.Value = "0";

                System.Diagnostics.Debug.WriteLine("Oluştur button clicked - creating new record");

                // Validate selection
                if (rbUser.Checked && drpUsers.SelectedValue == VAL_DEFAULT_USER)
                {
                    ShowErrorMessage(MSG_SELECT_USER);
                    return;
                }

                if (rbAdGroup.Checked && string.IsNullOrEmpty(drpGroups.SelectedValue))
                {
                    ShowErrorMessage(MSG_SELECT_GROUP);
                    return;
                }                // Sync status toggle checkbox with dropdown
                SyncDropdownWithStatusToggle();

                // Create a new record
                Kaydet();

                // Update the UpdatePanel to refresh the UI
                upnlUserAssignment.Update();
            }
            catch (Exception ex)
            {
                LogError("btnCreateNew_Click", ex);
                ShowErrorMessage("Oluşturma işlemi sırasında bir hata oluştu.");
            }
        }

        protected void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                int nameId = ConvertionHelper.ConvertValue<int>(drpName.SelectedValue);
                if (nameId == 0)
                {
                    ShowErrorMessage(MSG_SELECT_PAGE);
                    return;
                }

                int assignmentId = ConvertionHelper.ConvertValue<int>(hdnSelectedAssignmentId.Value);
                if (assignmentId <= 0)
                {
                    ShowErrorMessage("Güncellenecek kayıt bulunamadı. Lütfen önce bir kayıt seçiniz.");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"Güncelle button clicked - updating record with ID: {assignmentId}");

                // Validate selection
                if (rbUser.Checked && drpUsers.SelectedValue == VAL_DEFAULT_USER)
                {
                    ShowErrorMessage(MSG_SELECT_USER);
                    return;
                }

                if (rbAdGroup.Checked && string.IsNullOrEmpty(drpGroups.SelectedValue))
                {
                    ShowErrorMessage(MSG_SELECT_GROUP);
                    return;
                }                // Sync status toggle checkbox with dropdown
                SyncDropdownWithStatusToggle();

                // Update the existing record
                Guncelle(assignmentId);

                // Update the UpdatePanel to refresh the UI
                upnlUserAssignment.Update();
            }
            catch (Exception ex)
            {
                LogError("btnUpdate_Click", ex);
                ShowErrorMessage("Güncelleme işlemi sırasında bir hata oluştu.");
            }
        }

        // Override the page's Kayit_Getir to support the modal approach
        public override void Kayit_Getir(int ID)
        {
            try
            {
                // Double check that the ID is correct
                System.Diagnostics.Debug.WriteLine($"Kayit_Getir called with ID: {ID}, hdnSelectedAssignmentId.Value: {hdnSelectedAssignmentId.Value}");

                // Use the ID parameter directly, not the hidden field value which might be reset
                var entity = PRepository<DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT>.EntityGetir("DT_WORKFLOW", ID);
                if (entity != null)
                {
                    // Make sure the hidden field still has the correct ID
                    hdnSelectedAssignmentId.Value = entity.ID.ToString();
                    hdnEditMode.Value = "1";
                    ViewState["SelectedAssignmentId"] = entity.ID;

                    // Toggle button visibility for edit mode
                    btnCreateNew.Visible = false;
                    btnUpdate.Visible = true;

                    System.Diagnostics.Debug.WriteLine($"Record loaded for editing: ID={entity.ID}, Showing Update button, hiding Create button");

                    // Set page (NAME) selection
                    if (drpName.Items.FindByValue(entity.NAME_ID.ToString()) != null)
                    {
                        drpName.SelectedValue = entity.NAME_ID.ToString();
                        lblSelectedPageName.Text = drpName.SelectedItem.Text;
                    }

                    // Show the edit UI
                    pnlAssignmentSection.Visible = true;
                    pnlAssignmentList.Visible = true;

                    // Determine whether this is a user or group assignment
                    if (entity.F_LOGIN_ID > 0) // User Assignment
                    {
                        rbUser.Checked = true;
                        rbAdGroup.Checked = false;
                        divUserSelection.Visible = true;
                        divGroupSelection.Visible = false;
                        rfvUser.Enabled = true;
                        rfvGroup.Enabled = false;

                        if (drpUsers.Items.FindByValue(entity.F_LOGIN_ID.ToString()) != null)
                        {
                            drpUsers.SelectedValue = entity.F_LOGIN_ID.ToString();
                        }
                    }
                    else if (!string.IsNullOrEmpty(entity.AD_GROUP)) // Group Assignment
                    {
                        rbUser.Checked = false;
                        rbAdGroup.Checked = true;
                        divUserSelection.Visible = false;
                        divGroupSelection.Visible = true;
                        rfvUser.Enabled = false;
                        rfvGroup.Enabled = true;

                        // Determine domain from group format or stored domain
                        string domain = DetermineGroupDomain(entity.AD_GROUP);
                        System.Diagnostics.Debug.WriteLine($"Determined domain: {domain} for group: {entity.AD_GROUP}");

                        // Select domain type
                        drpDomain.SelectedValue = domain;

                        // Store the AD group to select after groups are loaded
                        ViewState["SelectedADGroup"] = entity.AD_GROUP;

                        // Load groups for selected domain
                        LoadGroupsDropdown(domain);

                        // Try to select the group
                        if (drpGroups.Items.FindByValue(entity.AD_GROUP) != null)
                        {
                            drpGroups.SelectedValue = entity.AD_GROUP;
                            System.Diagnostics.Debug.WriteLine($"Group {entity.AD_GROUP} selected successfully");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"Group {entity.AD_GROUP} not found in dropdown, adding manually");

                            // Add the group manually if it doesn't exist in the dropdown
                            ListItem newItem = new ListItem(entity.AD_GROUP, entity.AD_GROUP);
                            drpGroups.Items.Add(newItem);
                            drpGroups.SelectedValue = entity.AD_GROUP;
                        }
                    }

                    // Set status
                    bool isActive = entity.AKTIF == "1";
                    drpStatus.SelectedValue = isActive ? "1" : "0";
                    chkStatus.Checked = isActive;

                    // Display the selected entity details
                    selectedEntityDiv.Visible = true;
                    lblSelectedEntity.Text = entity.DISPLAY_NAME;

                    // Sync UI state
                    ScriptManager.RegisterStartupScript(this, GetType(), "SyncUIAfterLoad",
                        "if(typeof(setupAssignmentTypeToggle) === 'function') { setupAssignmentTypeToggle(); }" +
                        "if(typeof(setupStatusToggle) === 'function') { setupStatusToggle(); }",
                        true);

                    // Ensure grid loads with correct data
                    Grid_Doldur();
                }
                else
                {
                    ShowErrorMessage("Kayıt bulunamadı.");
                }
            }
            catch (Exception ex)
            {
                LogError("Kayit_Getir", ex);
                ShowErrorMessage(ERR_GET_RECORD);
                Temizle();
            }
        }
        #endregion

        #region CRUD Operations

        public override void Grid_Doldur()
        {
            int nameId = ConvertionHelper.ConvertValue<int>(drpName.SelectedValue);
            if (nameId <= 0)
            {
                grdUserAssignments.DataSource = null;
                grdUserAssignments.DataBind();
                pnlAssignmentList.Visible = false;
                return;
            }

            pnlAssignmentList.Visible = true;

            try
            {
                string searchTerm = "";
                if (txtSearchAssignments != null && !string.IsNullOrEmpty(txtSearchAssignments.Text))
                {
                    searchTerm = txtSearchAssignments.Text.Trim();
                }

                string statusFilter = "";
                if (drpStatusFilter != null && !string.IsNullOrEmpty(drpStatusFilter.SelectedValue))
                {
                    statusFilter = drpStatusFilter.SelectedValue;
                }

                var dataSource = UserAssignmentHelper.GetAssignments(nameId, searchTerm);

                // Apply status filter if selected
                if (!string.IsNullOrEmpty(statusFilter))
                {
                    // Convert DataTable to DataView to apply filter
                    DataView dv = dataSource.DefaultView;
                    dv.RowFilter = $"AKTIF = '{statusFilter}'";
                    dataSource = dv.ToTable();
                }

                // Apply sorting if available
                string sortExpression = ViewState["SortExpression"] as string;
                string sortDirection = ViewState["SortDirection"] as string;

                if (!string.IsNullOrEmpty(sortExpression) && !string.IsNullOrEmpty(sortDirection))
                {
                    DataView sortedView = dataSource.DefaultView;
                    sortedView.Sort = $"{sortExpression} {sortDirection}";
                    dataSource = sortedView.ToTable();
                }

                grdUserAssignments.DataSource = dataSource;
                grdUserAssignments.DataBind();

                // Register grid items for event validation after binding
                RegisterGridItemsForEventValidation();

                // Add client-side script to setup row handlers and count records
                ScriptManager.RegisterStartupScript(this, GetType(), "SetupGridAfterBind",
                    "if(typeof(setupGridRowHandlers) === 'function') { setupGridRowHandlers(); }" +
                    "if(typeof(countRecords) === 'function') { countRecords(); }",
                    true);
            }
            catch (Exception ex)
            {
                LogError("Grid_Doldur", ex);
                ShowErrorMessage(ERR_LOAD_GRID);

                grdUserAssignments.DataSource = null;
                grdUserAssignments.DataBind();
            }
        }

        private bool Esitle(DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT entity)
        {
            try
            {
                int nameId = ConvertionHelper.ConvertValue<int>(drpName.SelectedValue);
                if (nameId == 0) return false;

                entity.NAME_ID = nameId;

                // Get status from checkbox directly with debug output
                System.Diagnostics.Debug.WriteLine($"Setting AKTIF value: checkbox checked={chkStatus.Checked}");
                entity.AKTIF = chkStatus.Checked ? "1" : "0";

                if (rbUser.Checked)
                {
                    decimal loginId = ConvertionHelper.ConvertValue<decimal>(drpUsers.SelectedValue);
                    if (loginId == 0) return false;

                    entity.F_LOGIN_ID = loginId;
                    entity.DISPLAY_NAME = UserAssignmentHelper.GetDisplayNameByLogin(loginId);
                    entity.AD_GROUP = null;
                }
                else if (rbAdGroup.Checked)
                {
                    string groupName = drpGroups.SelectedValue;
                    if (string.IsNullOrEmpty(groupName)) return false;

                    entity.F_LOGIN_ID = 0; // Using 0 as convention for group assignments
                    entity.AD_GROUP = groupName;
                    entity.DISPLAY_NAME = groupName;
                }
                else
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                LogError("Esitle", ex);
                ShowErrorMessage(ERR_DATA_MAPPING);
                return false;
            }
        }

        public override void Kaydet()
        {
            try
            {
                var entity = new DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT();
                if (!Esitle(entity)) return;

                // Check for duplicate entries
                if (rbUser.Checked && UserAssignmentHelper.MukerrerKayitVarMi(
                    ((int)entity.NAME_ID), entity.F_LOGIN_ID.ToString(), 0))
                {
                    ShowErrorMessage(MSG_USER_DUPLICATE);
                    return;
                }

                if (rbAdGroup.Checked && UserAssignmentHelper.MukerrerAdGroupKayitVarMi(
                    ((int)entity.NAME_ID), entity.AD_GROUP, 0))
                {
                    ShowErrorMessage(MSG_GROUP_DUPLICATE);
                    return;
                }

                // Set audit fields
                entity.CREATED = DateTime.Now;
                entity.CREATED_BY = this.LoginId;

                // Save entity
                PRepository<DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT>.EntityKaydet("DT_WORKFLOW", entity);

                // Show success message
                ShowSuccessMessage(MSG_SAVE_SUCCESS);

                // Reset form and refresh grid
                Temizle();
                Grid_Doldur();

                // Add client-side script to update UI
                ScriptManager.RegisterStartupScript(this, GetType(), "RefreshGridAfterSave",
                    "if(typeof(refreshGrid) === 'function') { refreshGrid(); }",
                    true);
            }
            catch (Exception ex)
            {
                LogError("Kaydet", ex);
                ShowErrorMessage(ERR_SAVE);
            }
        }

        public override void Guncelle(int ID)
        {
            try
            {
                var entity = PRepository<DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT>.EntityGetir("DT_WORKFLOW", ID);
                if (entity == null)
                {
                    ShowErrorMessage(MSG_RECORD_NOT_FOUND);
                    return;
                }

                // Store original values for duplication check
                decimal? originalLoginId = entity.F_LOGIN_ID;
                string originalAdGroup = entity.AD_GROUP;

                // Map form values to entity
                if (!Esitle(entity)) return;

                // Check for duplicate entries when changed
                if (rbUser.Checked && entity.F_LOGIN_ID != originalLoginId &&
                    UserAssignmentHelper.MukerrerKayitVarMi(((int)entity.NAME_ID), entity.F_LOGIN_ID.ToString(), ID))
                {
                    ShowErrorMessage(MSG_USER_DUPLICATE);
                    return;
                }

                if (rbAdGroup.Checked && entity.AD_GROUP != originalAdGroup &&
                    UserAssignmentHelper.MukerrerAdGroupKayitVarMi(((int)entity.NAME_ID), entity.AD_GROUP, ID))
                {
                    ShowErrorMessage(MSG_GROUP_DUPLICATE);
                    return;
                }

                entity.LAST_UPDATED = DateTime.Now;
                entity.LAST_UPDATED_BY = this.LoginId;

                // Update entity
                PRepository<DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT>.EntityUpdateEt("DT_WORKFLOW", entity);

                // Show success message and refresh
                ShowSuccessMessage(MSG_UPDATE_SUCCESS);
                Temizle();
                Grid_Doldur();

                // Add client-side script to update UI
                ScriptManager.RegisterStartupScript(this, GetType(), "RefreshGridAfterUpdate",
                    "if(typeof(refreshGrid) === 'function') { refreshGrid(); }",
                    true);
            }
            catch (Exception ex)
            {
                LogError("Guncelle", ex);
                ShowErrorMessage(ERR_UPDATE);
            }
        }

        //public override void Kayit_Getir(int ID)
        //{
        //    try
        //    {
        //        var entity = PRepository<DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT>.EntityGetir("DT_WORKFLOW", ID);
        //        if (entity != null)
        //        {
        //            // Set hidden fields
        //            hdnSelectedAssignmentId.Value = entity.ID.ToString();
        //            hdnEditMode.Value = "1";

        //            // Toggle button visibility for edit mode
        //            btnCreateNew.Visible = false;
        //            btnUpdate.Visible = true;

        //            System.Diagnostics.Debug.WriteLine($"Record loaded for editing: ID={entity.ID}, Showing Update button, hiding Create button");

        //            // Set page (NAME) selection
        //            if (drpName.Items.FindByValue(entity.NAME_ID.ToString()) != null)
        //            {
        //                drpName.SelectedValue = entity.NAME_ID.ToString();
        //                lblSelectedPageName.Text = drpName.SelectedItem.Text;
        //            }

        //            // Show the edit UI
        //            pnlAssignmentSection.Visible = true;
        //            pnlAssignmentList.Visible = true;

        //            // Determine whether this is a user or group assignment
        //            if (entity.F_LOGIN_ID > 0) // User Assignment
        //            {
        //                rbUser.Checked = true;
        //                rbAdGroup.Checked = false;
        //                divUserSelection.Visible = true;
        //                divGroupSelection.Visible = false;
        //                rfvUser.Enabled = true;
        //                rfvGroup.Enabled = false;

        //                if (drpUsers.Items.FindByValue(entity.F_LOGIN_ID.ToString()) != null)
        //                {
        //                    drpUsers.SelectedValue = entity.F_LOGIN_ID.ToString();
        //                }
        //            }
        //            else if (!string.IsNullOrEmpty(entity.AD_GROUP)) // Group Assignment
        //            {
        //                rbUser.Checked = false;
        //                rbAdGroup.Checked = true;
        //                divUserSelection.Visible = false;
        //                divGroupSelection.Visible = true;
        //                rfvUser.Enabled = false;
        //                rfvGroup.Enabled = true;

        //                // Determine domain from group format or stored domain
        //                string domain = DetermineGroupDomain(entity.AD_GROUP);
        //                System.Diagnostics.Debug.WriteLine($"Determined domain: {domain} for group: {entity.AD_GROUP}");

        //                // Select domain type
        //                drpDomain.SelectedValue = domain;

        //                // Load groups for selected domain
        //                LoadGroupsDropdown(domain);

        //                // Select the group
        //                if (drpGroups.Items.FindByValue(entity.AD_GROUP) != null)
        //                {
        //                    drpGroups.SelectedValue = entity.AD_GROUP;
        //                }
        //                else
        //                {
        //                    System.Diagnostics.Debug.WriteLine($"Group {entity.AD_GROUP} not found in dropdown");
        //                }
        //            }

        //            // Set status
        //            bool isActive = entity.AKTIF == "1";
        //            drpStatus.SelectedValue = isActive ? "1" : "0";
        //            chkStatus.Checked = isActive;

        //            // Display the selected entity details
        //            selectedEntityDiv.Visible = true;
        //            lblSelectedEntity.Text = entity.DISPLAY_NAME;

        //            // Sync UI state
        //            ScriptManager.RegisterStartupScript(this, GetType(), "SyncUIAfterLoad",
        //                "if(typeof(setupAssignmentTypeToggle) === 'function') { setupAssignmentTypeToggle(); }" +
        //                "if(typeof(setupStatusToggle) === 'function') { setupStatusToggle(); }",
        //                true);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        LogError("Kayit_Getir", ex);
        //        ShowErrorMessage(ERR_GET_RECORD);
        //        Temizle();
        //    }
        //}

        protected void drpDomain_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                string selectedDomain = drpDomain.SelectedValue;
                System.Diagnostics.Debug.WriteLine($"Domain selection changed to: {selectedDomain}");

                // Check if we're in edit mode
                int assignmentId = ConvertionHelper.ConvertValue<int>(hdnSelectedAssignmentId.Value);
                bool isEditMode = assignmentId > 0 || hdnEditMode.Value == "1";
                System.Diagnostics.Debug.WriteLine($"Edit mode: {isEditMode}, Assignment ID: {assignmentId}");

                // Only load groups if a domain is selected
                if (!string.IsNullOrEmpty(selectedDomain))
                {
                    // If we're in edit mode, make sure we preserve the selected group
                    if (isEditMode && ViewState["SelectedADGroup"] == null)
                    {
                        // Store the current group selection if not already stored
                        string currentGroup = drpGroups.SelectedValue;
                        if (!string.IsNullOrEmpty(currentGroup) && currentGroup != "")
                        {
                            ViewState["SelectedADGroup"] = currentGroup;
                            System.Diagnostics.Debug.WriteLine($"Stored current group in ViewState: {currentGroup}");
                        }
                    }

                    LoadGroupsDropdown(selectedDomain);
                }
                else
                {
                    // Clear groups dropdown if no domain selected
                    drpGroups.Items.Clear();
                    drpGroups.Items.Insert(0, new ListItem(DigiportAdminResource.UserAssignment_SelectGroup, ""));

                    // If we're in edit mode, try to restore the selected group
                    string groupToSelect = ViewState["SelectedADGroup"] as string;
                    if (!string.IsNullOrEmpty(groupToSelect) && isEditMode)
                    {
                        ListItem newItem = new ListItem(groupToSelect, groupToSelect);
                        drpGroups.Items.Add(newItem);
                        drpGroups.SelectedValue = groupToSelect;
                        System.Diagnostics.Debug.WriteLine($"Restored group from ViewState: {groupToSelect}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("drpDomain_SelectedIndexChanged", ex);
                ShowErrorMessage("Domain seçimi sırasında bir hata oluştu: " + ex.Message);
            }
        }

        // Helper method to determine domain from group name or format
        private string DetermineGroupDomain(string groupName)
        {
            // Logic to determine domain based on group name or format
            if (string.IsNullOrEmpty(groupName))
                return string.Empty;

            // Check for digiturk cc domain indicators in group name
            if (groupName.ToLower().Contains("cc") ||
                groupName.ToLower().EndsWith(".cc") ||
                groupName.ToLower().Contains("call center"))
            {
                System.Diagnostics.Debug.WriteLine($"Group '{groupName}' identified as Digiturk CC domain");
                return "digiturk cc";
            }

            // Default to digiturk domain
            System.Diagnostics.Debug.WriteLine($"Group '{groupName}' defaulted to Digiturk domain");
            return "digiturk";
        }

        public override void Sil(int ID)
        {
            try
            {
                // Create a direct SQL delete command for the specific ID without any additional parameters
                string sql = "DELETE FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT WHERE ID = :ID";

                // Only bind the ID parameter
                OracleParameter[] dbParams = new OracleParameter[]
                {
                    new OracleParameter("ID", OracleDbType.Int32) { Value = ID }
                };

                // Execute the delete command directly using DAL
                System.Diagnostics.Debug.WriteLine($"Executing delete SQL for ID: {ID}");
                int result = Convert.ToInt32(DataAccessLayer.DAL.ExecuteNonQuery_Oracle("DT_WORKFLOW", sql, dbParams));

                if (result > 0)
                {
                    // Success - record was deleted
                    System.Diagnostics.Debug.WriteLine($"Delete successful, {result} rows affected");
                    ShowSuccessMessage(MSG_DELETE_SUCCESS);

                    // Refresh grid after deletion
                    Grid_Doldur();

                    // Re-register grid items for event validation after deletion
                    RegisterGridItemsForEventValidation();

                    // Clear form if deleted item was being edited
                    if (hdnSelectedAssignmentId.Value == ID.ToString())
                        Temizle();

                    // Add client-side script to update UI
                    ScriptManager.RegisterStartupScript(this, GetType(), "RefreshGridAfterDelete",
                        "if(typeof(refreshGrid) === 'function') { refreshGrid(); }",
                        true);
                }
                else
                {
                    // No records deleted - possibly already gone?
                    System.Diagnostics.Debug.WriteLine("Delete command executed but no rows affected");
                    ShowErrorMessage("Kayıt bulunamadı veya silinemedi.");
                }
            }
            catch (Exception ex)
            {
                LogError("Sil", ex);
                ShowErrorMessage(ERR_DELETE + ": " + ex.Message);
            }
        }

        public override void Temizle()
        {
            ResetFormState();
        }

        #endregion

        #region Helper Methods

        private void ResetFormState()
        {
            // Reset hidden fields
            hdnSelectedAssignmentId.Value = "0";
            hdnEditMode.Value = "0";

            // Reset radio buttons
            rbUser.Checked = true;
            rbAdGroup.Checked = false;
            divUserSelection.Visible = true;
            divGroupSelection.Visible = false;
            rfvUser.Enabled = true;
            rfvGroup.Enabled = false;

            // Reset dropdowns to default values
            drpUsers.ClearSelection();
            if (drpUsers.Items.FindByValue(VAL_DEFAULT_USER) != null)
                drpUsers.SelectedValue = VAL_DEFAULT_USER;

            drpGroups.ClearSelection();
            if (drpGroups.Items.FindByValue(VAL_DEFAULT_GROUP) != null)
                drpGroups.SelectedValue = VAL_DEFAULT_GROUP;

            drpStatus.ClearSelection();
            if (drpStatus.Items.FindByValue("1") != null)
                drpStatus.SelectedValue = "1";

            // Reset status toggle
            chkStatus.Checked = true;

            // Hide selected entity info
            selectedEntityDiv.Visible = false;
            lblSelectedEntity.Text = "";

            // Set button visibility for create mode
            btnCreateNew.Visible = true;
            btnUpdate.Visible = false;

            // Update client-side UI state
            ScriptManager.RegisterStartupScript(this, GetType(), "ResetFormUI",
                "if(typeof(setupAssignmentTypeToggle) === 'function') { setupAssignmentTypeToggle(); }" +
                "if(typeof(setupStatusToggle) === 'function') { setupStatusToggle(); }",
                true);
        }

        private void ShowSuccessMessage(string message)
        {
            CurrentMaster?.PopupGoster(MSG_SUCCESS, message, false);
        }

        private void ShowErrorMessage(string message)
        {
            CurrentMaster?.PopupGoster(MSG_ERROR, message, true);
        }

        private void LogError(string methodName, Exception ex)
        {
            // Replace with proper logging mechanism as needed
            System.Diagnostics.Debug.WriteLine($"ERROR in {methodName}: {ex.Message}");
            System.Diagnostics.Debug.WriteLine(ex.StackTrace);
        }

        // Helper method to sync checkbox with dropdown for toggle switch
        private void SyncStatusToggleWithDropdown()
        {
            try
            {
                // Ensure checkbox state matches dropdown
                if (drpStatus.SelectedValue == "1")
                {
                    chkStatus.Checked = true;
                }
                else
                {
                    chkStatus.Checked = false;
                }

                // Debug values to help troubleshoot
                System.Diagnostics.Debug.WriteLine($"Status sync from dropdown: Value={drpStatus.SelectedValue}, Setting Checkbox={chkStatus.Checked}");
            }
            catch (Exception ex)
            {
                LogError("SyncStatusToggleWithDropdown", ex);
                // Don't show message for this internal operation
            }
        }

        // Helper method to sync dropdown with checkbox for toggle switch
        private void SyncDropdownWithStatusToggle()
        {
            try
            {
                // Ensure dropdown state matches checkbox
                drpStatus.SelectedValue = chkStatus.Checked ? "1" : "0";

                // Debug values to help troubleshoot
                System.Diagnostics.Debug.WriteLine($"Status toggle UI: Checkbox={chkStatus.Checked}, Dropdown Value={drpStatus.SelectedValue}");
            }
            catch (Exception ex)
            {
                LogError("SyncDropdownWithStatusToggle", ex);
                // Don't show message for this internal operation
            }
        }

        #endregion
    }
}