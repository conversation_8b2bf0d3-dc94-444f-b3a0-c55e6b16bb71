﻿using CoreHelpers;
using DevExpress.Web;
using Entities.DigiportAdmin;
using FormHelper.DigiportAdmin;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.DigiportAdmin.Pages
{
    public partial class MainTopBanner : DigiportSecurePage
    {
        private readonly string componentName = "AnaSayfaUstSlide";
        int MID = 0;
        private int SlideId
        {
            get
            {
                string slideId = hdnSlideId.Value;
                if (slideId != string.Empty)
                    return Convert.ToInt32(slideId);
                else
                    return 0;
            }
        }
        DigiportHtmlEditorMediaHelper helper;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!String.IsNullOrEmpty(Request.QueryString["MID"]) && CoreHelpers.GenericIslemler.IsNumeric(Request.QueryString["MID"]))
            {
                if (!ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerID"].Split('-').Contains(MenuId.Value.ToString()))
                {
                    Session["ErrorPageMessage"] = Resources.DigiportAdminResource.TipYanlis;
                    Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
                    return;
                }
                MID = MenuId.Value;
            }
            else
            {
                Session["ErrorPageMessage"] = Resources.DigiportAdminResource.TipBelirsiz;
                Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
                return;
            }
            DigiportAdminHtmlContent.ComponentName = componentName;
            helper = new DigiportHtmlEditorMediaHelper(DigiportAdminHtmlContent.ComponentName);
            this.Title = FormHelper.DigiportAdmin.NameHelper.GetComponentNameOrDescription(MID, false, FormHelper.CoreHelper.isEnglish());
            if (!IsPostBack && !IsCallback)
            {
                lblSlideImageSize.Text = string.Format("[{0}px - {1}px]", ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerIzinliBoyut"].Split('x')[0], ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerIzinliBoyut"].Split('x')[1]);
                lblThumbnailSize.Text = string.Format("[{0}px - {1}px]", ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerThumbnailIzinliBoyut"].Split('x')[0], ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerThumbnailIzinliBoyut"].Split('x')[1]);
                linkSliderSecenekleri.NavigateUrl = "~/AdminPages/DigiportAdmin/SliderOptions.aspx?MID=" + MID;
                pnlAll.GroupingText = this.Title;
                string allowedExtensionsSlide = System.Configuration.ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerIzinliTipler"];
                string allowedExtensionsThumbnail = System.Configuration.ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerThumbnailIzinliTipler"];
                fuSlide.Attributes["accept"] = allowedExtensionsSlide;
                fuThumbnail.Attributes["accept"] = allowedExtensionsThumbnail;

                foreach (var action in Enum.GetValues(FormHelper.CoreHelper.isEnglish() ? typeof(DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions_En) : typeof(DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions)))
                {
                    int value = (int)action;
                    string name = action.ToString().Replace("_", " ");
                    if (
                        value == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç
                        ||
                        value == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç
                      )
                        continue;
                    drpClickAction.Items.Add(new ListItem(name, value.ToString()));
                }
                drpClickAction.Items.Insert(0, new ListItem(FormHelper.CoreHelper.isEnglish() ? "Please Select" : "Seçiniz", "-1") { Selected = true });
                rdBtnListActive.Items.Add(new ListItem(Resources.DigiportAdminResource.Tumu, "2") { Selected = false });
                rdBtnListActive.Items.Add(new ListItem(Resources.DigiportAdminResource.Aktif, "1") { Selected = true });
                rdBtnListActive.Items.Add(new ListItem(Resources.DigiportAdminResource.Pasif, "0") { Selected = false });
                helper.ClearMediaRemoveSession();
                helper.ClearMediaUploadSession();
                PanelleriDuzenle(true, false, false);
            }
        }

        protected void ASPxGridView_AfterPerformCallback(object sender, ASPxGridViewAfterPerformCallbackEventArgs e)
        {
            GridYukle();
        }

        protected void btnKaydet_Click(object sender, EventArgs e)
        {
            string savePath = string.Empty;
            try
            {
                string allowedExtensionsSlide = System.Configuration.ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerIzinliTipler"];
                string allowedExtensionsThumbnail = System.Configuration.ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerThumbnailIzinliTipler"];
                if (fuSlide.HasFile && !allowedExtensionsSlide.Split(',').Contains(Path.GetExtension(fuSlide.FileName).ToLower()))
                    throw new Exception(Resources.DigiportAdminResource.SlideDosyaFormatiYanlis + allowedExtensionsSlide);
                if (fuThumbnail.HasFile && !allowedExtensionsThumbnail.Split(',').Contains(Path.GetExtension(fuThumbnail.FileName).ToLower()))
                    throw new Exception(Resources.DigiportAdminResource.ThumbnailDosyaFormatiYanlis + allowedExtensionsThumbnail);
                if (SlideId == 0 && !fuSlide.HasFile)
                    throw new Exception(Resources.DigiportAdminResource.YeniSlideDosyaSec);
                if (SlideId == 0 && !fuThumbnail.HasFile)
                    throw new Exception(Resources.DigiportAdminResource.YeniThumbnailDosyaSec);

                if (fuSlide.HasFile)
                {
                    int izinliGenislik = int.Parse(ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerIzinliBoyut"].Split('x')[0]);
                    int izinliYukseklik = int.Parse(ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerIzinliBoyut"].Split('x')[1]);

                    using (System.Drawing.Image img = System.Drawing.Image.FromStream(fuSlide.PostedFile.InputStream))
                    {
                        int yuklenenGenislik = img.Width;
                        int yuklenenYukseklik = img.Height;
                        if (yuklenenGenislik != izinliGenislik || yuklenenYukseklik != izinliYukseklik)
                            throw new Exception(string.Format(Resources.DigiportAdminResource.SlideBoyutlari, yuklenenGenislik, yuklenenYukseklik, izinliGenislik, izinliYukseklik));
                    }
                }
                if (fuThumbnail.HasFile)
                {
                    int izinliGenislik = int.Parse(ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerThumbnailIzinliBoyut"].Split('x')[0]);
                    int izinliYukseklik = int.Parse(ConfigurationManager.AppSettings["DigiportAdminAnasayfaUstBannerThumbnailIzinliBoyut"].Split('x')[1]);

                    using (System.Drawing.Image img = System.Drawing.Image.FromStream(fuThumbnail.PostedFile.InputStream))
                    {
                        int yuklenenGenislik = img.Width;
                        int yuklenenYukseklik = img.Height;
                        if (yuklenenGenislik != izinliGenislik || yuklenenYukseklik != izinliYukseklik)
                            throw new Exception(string.Format(Resources.DigiportAdminResource.ThumbnailBoyutlari, yuklenenGenislik, yuklenenYukseklik, izinliGenislik, izinliYukseklik));
                    }
                }
                if (txtSlideName.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.SlideAdiGirin);
                if (txtBaslangicTarihi.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.BaslangicTarihiSec);
                if (txtBitisTarihi.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihiSec);
                DateTime dateStart = DateTime.Today, dateEnd = DateTime.Today;
                if (!DateTime.TryParse(txtBaslangicTarihi.Text.Trim(), out dateStart))
                    throw new Exception(Resources.DigiportAdminResource.BaslangicTarihFormati);
                if (!DateTime.TryParse(txtBitisTarihi.Text.Trim(), out dateEnd))
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihFormati);
                int siraNo = 1;
                if (!Int32.TryParse(txtSiraNo.Text.Trim(), out siraNo))
                    throw new Exception(Resources.DigiportAdminResource.GecersizSiraNo);
                if (drpClickAction.SelectedValue == "-1")
                    throw new Exception(Resources.DigiportAdminResource.TiklanmaIslemiSecin);
                if (
                    (drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç).ToString())
                    && txtTargetLink.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.HedefLinkGirin);
                int popupWidth = 0, popupHeight = 0;
                if (
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç).ToString()
                    )
                {
                    if (!Int32.TryParse(txtPopupWidth.Text.Trim(), out popupWidth))
                        throw new Exception(Resources.DigiportAdminResource.GecersizPopupGenislik);
                    if (!Int32.TryParse(txtPopupHeight.Text.Trim(), out popupHeight))
                        throw new Exception(Resources.DigiportAdminResource.GecersizPopupYukseklik);
                }


                DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE entity;
                if (SlideId == 0)
                    entity = new DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE();
                else
                    entity = PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityGetir("DT_WORKFLOW", SlideId);

                bool originalStateIsActive = entity.GetSet_ID != 0 && entity.ACTIVE == "1";
                string directoryPathDigiport = "/Content/DigiportAdmin/Digiport/" + componentName + "/" + DateTime.Today.Year.ToString() + "/" + DateTime.Today.Month.ToString() + "/";
                if (!Directory.Exists(Server.MapPath(directoryPathDigiport)))
                    Directory.CreateDirectory(Server.MapPath(directoryPathDigiport));

                if (fuSlide.HasFile)
                {
                    if (entity.GetSet_ID > 0 && entity.SLIDE_IMAGE_PATH != string.Empty)
                    {
                        string oldFilePath = Server.MapPath(entity.SLIDE_IMAGE_PATH);
                        if (File.Exists(oldFilePath))
                            File.Delete(oldFilePath);
                    }
                    string imageName = Guid.NewGuid().ToString() + Path.GetExtension(fuSlide.FileName);
                    string filePath = directoryPathDigiport + imageName;
                    savePath = Server.MapPath(filePath);
                    fuSlide.SaveAs(savePath);
                    entity.SLIDE_IMAGE_PATH = filePath;
                }
                if (fuThumbnail.HasFile)
                {
                    if (entity.GetSet_ID > 0 && entity.THUMBNAIL_IMAGE_PATH != string.Empty)
                    {
                        string oldFilePath = Server.MapPath(entity.THUMBNAIL_IMAGE_PATH);
                        if (File.Exists(oldFilePath))
                            File.Delete(oldFilePath);
                    }
                    string imageName = Guid.NewGuid().ToString() + Path.GetExtension(fuThumbnail.FileName);
                    string filePath = directoryPathDigiport + imageName;
                    savePath = Server.MapPath(filePath);
                    fuThumbnail.SaveAs(savePath);
                    entity.THUMBNAIL_IMAGE_PATH = filePath;
                }
                entity.MENU_NAME_ID = SlideId == 0 ? MID : entity.MENU_NAME_ID;
                entity.SLIDE_NAME = txtSlideName.Text.Trim();
                entity.SLIDE_CLICK_ACTION = ConvertionHelper.ConvertValue<decimal>(drpClickAction.SelectedValue);
                entity.SLIDE_TARGET_LINK = entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç) ? txtTargetLink.Text.Trim() : null;
                entity.SLIDE_TARGET_CONTENT = entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? ((TextBox)DigiportAdminHtmlContent.FindControl("txtHtmlContent")).Text.Trim().Replace("SummerNoteTempUploads", "SummerNoteUploads") : null;
                entity.SLIDE_TARGET_HEADLINE = entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? txtPopupSayfaBaslik.Text.Trim() : null;
                entity.SLIDE_POPUP_WIDTH = entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? popupWidth : (decimal?)null;
                entity.SLIDE_POPUP_HEIGHT = entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? popupHeight : (decimal?)null;
                entity.ACTIVE = hdnActive.Value.ToLower() == "true" ? "1" : "0";
                entity.VALID_DATE_START = dateStart;
                entity.VALID_DATE_END = dateEnd;
                if (entity.GetSet_ID == 0)
                {
                    entity.ORDER_NO = siraNo;
                    entity.CREATED_BY = LoginId;
                    entity.CREATED = DateTime.Now;
                    entity.DELETED = "0";
                    PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityKaydet("DT_WORKFLOW", entity);
                }
                else
                {
                    entity.LAST_UPDATED_BY = LoginId;
                    entity.LAST_UPDATED = DateTime.Now;
                    PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityUpdateEt("DT_WORKFLOW", entity);
                    if ((siraNo != entity.ORDER_NO || !originalStateIsActive) && entity.ACTIVE == "1")
                        SiraNoTekrarDuzenle(entity.ID, entity.ORDER_NO, !originalStateIsActive);
                }
                helper.MoveUploadedContentToRealFolder("/Content/SummerNoteUploads/Digiport/" + componentName + "/" + DateTime.Today.Year.ToString() + "/" + DateTime.Today.Month.ToString());
                helper.DeleteRealFolderContents();
                PanelleriDuzenle(true, false, false);
                this.Master.PopupGoster(Resources.DigiportAdminResource.Basari, Resources.DigiportAdminResource.KayitBasarili, false);
            }
            catch (Exception ex)
            {
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
                if (savePath != string.Empty && File.Exists(savePath))
                    File.Delete(savePath);
            }
        }

        private void SiraNoTekrarDuzenle(int changedSlideId, decimal newOrderNo, bool changedAsActive)
        {
            try
            {
                List<ItemOrderInfo> slides = AnasayfaUstSlideHelper.GetSlidesTable(MID, 1).Rows.Cast<DataRow>().Select(row => new ItemOrderInfo()
                {
                    ItemId = ConvertionHelper.ConvertValue<int>(row["ID"].ToString()),
                    ItemOrder_Old = ConvertionHelper.ConvertValue<int>(row["ORDER_NO"].ToString()),
                    ItemOrder_New = 0,
                    Changed = false
                }).OrderBy(s => s.ItemOrder_Old).ToList();

                SortHelper.SiraNoTekrarAyarla(slides, changedSlideId, newOrderNo, changedAsActive);
                if (slides.Exists(x => x.Changed))
                {
                    foreach (ItemOrderInfo itemChanged in slides.Where(x => x.Changed).ToList())
                    {
                        DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE slide = PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityGetir("DT_WORKFLOW", Convert.ToInt32(itemChanged.ItemId));
                        slide.ORDER_NO = itemChanged.ItemOrder_New;
                        slide.LAST_UPDATED = DateTime.Now;
                        slide.LAST_UPDATED_BY = LoginId;
                        PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityUpdateEt("DT_WORKFLOW", slide);
                    }
                }
            }
            catch
            {
            }
        }

        protected void drpClickAction_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (drpClickAction.SelectedItem.Value == "-1" || drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Olay_Yok).ToString())
                KaydetGorunumAyarla(false, false, false);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(true, false, false);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(true, true, false);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(false, false, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(false, true, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(false, false, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(false, true, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(false, false, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(false, true, true);
        }
        private void KaydetPanelGetir()
        {

            if (SlideId == 0)
            {
                drpClickAction.SelectedIndex = 0;

                txtSlideName.Text = txtBaslangicTarihi.Text = txtBitisTarihi.Text = txtTargetLink.Text = ((TextBox)DigiportAdminHtmlContent.FindControl("txtHtmlContent")).Text = txtPopupSayfaBaslik.Text = ((TextBox)DigiportAdminHtmlContent.FindControl("summernote")).Text = string.Empty;
                txtSiraNo.Text = AnasayfaUstSlideHelper.GetNextSlideNo(MID);
                txtPopupWidth.Text = ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[0];
                txtPopupHeight.Text = ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[1];
                chkActive.Checked = true;
                hdnActive.Value = "true";
                imgSlide.ImageUrl = string.Empty;
                imgThumbnail.ImageUrl = string.Empty;
                KaydetGorunumAyarla(false, false, false);
                txtSiraNo.Enabled = false;
            }
            else
            {
                DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE slide = PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityGetir("DT_WORKFLOW", SlideId);
                imgSlide.ImageUrl = slide.SLIDE_IMAGE_PATH;
                imgThumbnail.ImageUrl = slide.THUMBNAIL_IMAGE_PATH;
                txtSlideName.Text = slide.SLIDE_NAME;
                txtPopupSayfaBaslik.Text = slide.SLIDE_TARGET_HEADLINE;
                txtBaslangicTarihi.Text = slide.VALID_DATE_START.ToString("dd.MM.yyyy");
                txtBitisTarihi.Text = slide.VALID_DATE_END.ToString("dd.MM.yyyy");
                txtSiraNo.Text = slide.ORDER_NO.ToString();
                txtSiraNo.Enabled = slide.ACTIVE == "1";
                chkActive.Checked = slide.ACTIVE == "1";
                hdnActive.Value = slide.ACTIVE == "1" ? "true" : "false";
                drpClickAction.Items.Cast<ListItem>().ToList().ForEach(x => x.Selected = false);
                drpClickAction.Items.FindByValue(slide.SLIDE_CLICK_ACTION.ToString()).Selected = true;

                txtTargetLink.Text = slide.SLIDE_TARGET_LINK;
                txtPopupWidth.Text = slide.SLIDE_POPUP_WIDTH != null ? slide.SLIDE_POPUP_WIDTH.ToString() : ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[0];
                txtPopupHeight.Text = slide.SLIDE_POPUP_HEIGHT != null ? slide.SLIDE_POPUP_HEIGHT.ToString() : ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[1];
                ((TextBox)DigiportAdminHtmlContent.FindControl("txtHtmlContent")).Text = ((TextBox)DigiportAdminHtmlContent.FindControl("summernote")).Text = slide.SLIDE_TARGET_CONTENT;
                KaydetGorunumAyarla(
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç)
                    ,
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ,
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    );
            }
        }
        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(gridSlides.GetRowValues(index, "ID"));
            hdnSlideId.Value = deger.ToString();
            PanelleriDuzenle(false, false, true);
        }
        private void KaydetGorunumAyarla(bool linkBilgileriGoster, bool popupBilgileriGoster, bool popupContentGoster)
        {
            divHedefLink.Visible = linkBilgileriGoster;
            divPopupSize.Visible = popupBilgileriGoster;
            divPopupHtmlContent.Style["display"] = popupContentGoster ? "block" : "none";
        }

        protected void btnYeniKayit_Click(object sender, EventArgs e)
        {
            PanelleriDuzenle(false, true, false);
        }

        private void PanelleriDuzenle(bool listelemeModu, bool yeniKayitModu, bool guncellemeModu)
        {
            if (yeniKayitModu)
                hdnSlideId.Value = "0";
            btnYeniKayit.Visible = listelemeModu;
            linkSliderSecenekleri.Visible = listelemeModu;
            btnSil.Visible = guncellemeModu;
            btnKayitIptal.Visible = yeniKayitModu || guncellemeModu;
            pnlKaydet.Visible = yeniKayitModu || guncellemeModu;
            pnlGrid.Visible = listelemeModu;
            if (yeniKayitModu || guncellemeModu)
                KaydetPanelGetir();
            if (listelemeModu)
                GridYukle();
        }

        private void GridYukle()
        {
            DataTable dt = AnasayfaUstSlideHelper.GetSlidesTable(MID, ConvertionHelper.ConvertValue<int>(rdBtnListActive.SelectedValue));
            gridSlides.DataSource = dt;
            gridSlides.DataBind();
            if (rdBtnListActive.SelectedValue == "1")
            {
                gridSlides.SettingsPager.Mode = GridViewPagerMode.ShowAllRecords;
                gridSlides.SettingsPager.PageSize = 0;
            }
            else
            {
                gridSlides.SettingsPager.Mode = GridViewPagerMode.ShowPager;
                gridSlides.SettingsPager.PageSize = 10;
            }
        }

        protected void btnKayitIptal_Click(object sender, EventArgs e)
        {
            PanelleriDuzenle(true, false, false);
        }

        public bool ClickLinkVisible(string clickActionId)
        {
            return clickActionId != ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Olay_Yok).ToString();
        }

        protected void rdBtnListActive_SelectedIndexChanged(object sender, EventArgs e)
        {
            GridYukle();
        }

        protected void chkActiveGrid_CheckedChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE entity = null;
            try
            {
                int index = (((CheckBox)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _slideId = ConvertionHelper.ConvertValue<int>(gridSlides.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityGetir("DT_WORKFLOW", _slideId);
                entity.ACTIVE = ((CheckBox)sender).Checked ? "1" : "0";
                entity.LAST_UPDATED_BY = LoginId;
                entity.LAST_UPDATED = DateTime.Now;
                PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityUpdateEt("DT_WORKFLOW", entity);
                if (entity.ACTIVE == "1")
                    SiraNoTekrarDuzenle(entity.ID, entity.ORDER_NO, entity.ACTIVE == "1");
                GridYukle();
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((CheckBox)sender).Checked = entity.ACTIVE == "1";
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }

        protected void dateEditStartGrid_ValueChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE entity = null;
            try
            {
                int index = (((ASPxDateEdit)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _slideId = ConvertionHelper.ConvertValue<int>(gridSlides.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityGetir("DT_WORKFLOW", _slideId);
                DateTime dateNew = ((ASPxDateEdit)sender).Date;
                if (dateNew >= entity.VALID_DATE_END)
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihiBaslagictanIleriOlsun);
                entity.VALID_DATE_START = dateNew;
                entity.LAST_UPDATED_BY = LoginId;
                entity.LAST_UPDATED = DateTime.Now;
                PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityUpdateEt("DT_WORKFLOW", entity);
                GridYukle();
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((ASPxDateEdit)sender).Date = entity.VALID_DATE_START;
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }

        protected void dateEditEndGrid_ValueChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE entity = null;
            try
            {
                int index = (((ASPxDateEdit)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _slideId = ConvertionHelper.ConvertValue<int>(gridSlides.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityGetir("DT_WORKFLOW", _slideId);
                DateTime dateNew = ((ASPxDateEdit)sender).Date;
                if (dateNew <= entity.VALID_DATE_START)
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihiBaslagictanIleriOlsun);
                entity.VALID_DATE_END = dateNew;
                entity.LAST_UPDATED_BY = LoginId;
                entity.LAST_UPDATED = DateTime.Now;
                PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityUpdateEt("DT_WORKFLOW", entity);
                GridYukle();
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((ASPxDateEdit)sender).Date = entity.VALID_DATE_END;
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }

        protected void txtSiraNoGrid_TextChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE entity = null;
            try
            {
                int index = (((TextBox)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _slideId = ConvertionHelper.ConvertValue<int>(gridSlides.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityGetir("DT_WORKFLOW", _slideId);
                decimal newOrderNo = ConvertionHelper.ConvertValue<decimal>(((TextBox)sender).Text);
                if (entity.ACTIVE == "1")
                {
                    SiraNoTekrarDuzenle(entity.ID, newOrderNo, false);
                    GridYukle();
                }
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((TextBox)sender).Text = entity.ORDER_NO.ToString();
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
        protected void btnSil_Click(object sender, EventArgs e)
        {
            try
            {
                if (SlideId > 0)
                {
                    DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE entity = PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityGetir("DT_WORKFLOW", SlideId);
                    if (entity != null)
                    {
                        List<string> pathsToDelete = new List<string>();
                        if (!string.IsNullOrEmpty(entity.SLIDE_TARGET_CONTENT))
                        {
                            pathsToDelete.AddRange(helper.ExtractSummerNoteUploadPaths(entity.SLIDE_TARGET_CONTENT));
                        }
                        pathsToDelete.Add(Server.MapPath(entity.SLIDE_IMAGE_PATH));
                        if (!string.IsNullOrEmpty(entity.THUMBNAIL_IMAGE_PATH))
                            pathsToDelete.Add(Server.MapPath(entity.THUMBNAIL_IMAGE_PATH));

                        foreach (string path in pathsToDelete)
                        {
                            if (File.Exists(path))
                                File.Delete(path);
                        }
                        entity.DELETED = "1";
                        entity.LAST_UPDATED_BY = LoginId;
                        entity.LAST_UPDATED = DateTime.Now;
                        PRepository<DIGIPORT_ADMIN_ANASAYFA_UST_SLIDE>.EntityUpdateEt("DT_WORKFLOW", entity);
                        PanelleriDuzenle(true, false, false);
                        this.Master.PopupGoster(Resources.DigiportAdminResource.Basari, Resources.DigiportAdminResource.SilmeBasarili, false);
                    }
                }
            }
            catch (Exception ex)
            {
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
    }
}