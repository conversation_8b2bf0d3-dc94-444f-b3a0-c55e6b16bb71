﻿<%@ Page Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="MainpageLeftMenu.aspx.cs" ValidateRequest="false" Inherits="AracTakipSistemi.AdminPages.DigiportAdmin.Pages.MainpageLeftMenu" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>
<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ Register Src="~/UserControl/DigiportAdminHtmlContent.ascx" TagPrefix="uc1" TagName="DigiportAdminHtmlContent" %>
<%@ Register Src="~/UserControl/DigiportAdminHtmlContentNoMedia.ascx" TagPrefix="uc1" TagName="DigiportAdminHtmlContentNoMedia" %>

<%@ MasterType VirtualPath="~/Site.Master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="/css/DigiportAdmin.css?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>" rel="stylesheet" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <script type="text/javascript">
        var debounceTimers = {};

        function debouncePostback(event, ctrl, delay) {
            if (!NumericControl(event, ctrl, 5, 1, 99999))
                return false;
            var id = ctrl.id;
            if (debounceTimers[id]) {
                clearTimeout(debounceTimers[id]);
            }
            debounceTimers[id] = setTimeout(function () {
                __doPostBack(id, '');
            }, delay);
        }
    </script>
    <asp:Panel ID="pnlAll" Style="width: 100%" GroupingText="" runat="server">
        <div>
            <a href="#" id="anchorClickEvent" target="_blank" style="display: none;"></a>
            <asp:Button runat="server" ID="btnYeniKayit" Text="<%$ Resources:DigiportAdminResource, YeniIcerik %>" OnClick="btnYeniKayit_Click" />
            <asp:Button runat="server" ID="btnKayitIptal" Text="<%$ Resources:DigiportAdminResource, Geri %>" OnClick="btnKayitIptal_Click" />
            <asp:Button runat="server" ID="btnSil" Text="<%$ Resources:DigiportAdminResource, Sil %>" OnClick="btnSil_Click" />
            <asp:ConfirmButtonExtender runat="server" ID="confirmDelete" ConfirmText="<%$ Resources:DigiportAdminResource, ConfirmeDelete %>" TargetControlID="btnSil" />
        </div>
        <asp:Panel ID="pnlKaydet" GroupingText="<%$ Resources:DigiportAdminResource, ContentInformation %>" Visible="false" runat="server">
            <asp:HiddenField runat="server" ID="hdnContentId" />
            <div class="container-fluid">
                <div class="grid-container">
                    <div class="grid-item">
                        <div class="inner-div">
                            <div class="divCaption">
                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, Icerik %>" runat="server" />
                            </div>
                            <div>
                                <uc1:DigiportAdminHtmlContentNoMedia runat="server" ID="DigiportAdminHtmlContentNoMedia" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid-container">
                    <div class="grid-item">
                        <div class="inner-div">
                            <div class="divCaption">
                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, Aktif %>" runat="server" />
                            </div>
                            <div>
                                <label class="switch">
                                    <asp:CheckBox ID="chkActive" runat="server" />
                                    <span class="switch-slider round"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="grid-item">
                        <div class="inner-div">
                            <div class="divCaption">
                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, SiraNo %>" runat="server" />
                            </div>
                            <div>
                                <asp:TextBox runat="server" ID="txtSiraNo" MaxLength="3" autocomplete="off" Text="1" oninput="return NumericControl(event, this,5,1,99999);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                                <div>
                                    <asp:RequiredFieldValidator ID="rfvSiraNo" runat="server" ControlToValidate="txtSiraNo" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SiraNoSecin %>"></asp:RequiredFieldValidator>
                                    <asp:RangeValidator ID="rvSiraNo" runat="server" ControlToValidate="txtSiraNo" ErrorMessage="<%$ Resources:DigiportAdminResource, SayiAraligi2 %>" MaximumValue="99999" MinimumValue="1" Type="Integer" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid-container">
                    <div class="grid-item">
                        <div class="inner-div">
                            <div class="divCaption">
                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, TiklanmaIslemi %>" runat="server" />
                            </div>
                            <div>
                                <asp:DropDownList ID="drpClickAction" AutoPostBack="true" OnSelectedIndexChanged="drpClickAction_SelectedIndexChanged" runat="server">
                                </asp:DropDownList>
                                <div>
                                    <asp:RequiredFieldValidator ID="rfv5" runat="server" ControlToValidate="drpClickAction" InitialValue="-1" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, TiklanmaIslemiSecin %>"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grid-item">
                        <div class="inner-div" id="divHedefLink" runat="server">
                            <div class="divCaption">
                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, TiklanmaHedefLink %>" runat="server" />
                            </div>
                            <div>
                                <asp:TextBox runat="server" ID="txtTargetLink" autocomplete="off" MaxLength="500" placeholder="<%$ Resources:DigiportAdminResource, HedefLink %>"></asp:TextBox>
                                <div>
                                    <asp:RequiredFieldValidator ID="rfvHedefLink" runat="server" ControlToValidate="txtTargetLink" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, HedefLinkGirin %>"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                        </div>
                        <div class="inner-div" id="divPopupSize" runat="server">
                            <div class="divCaption">
                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, AcilanPencereBoyutlari %>" runat="server" />
                            </div>
                            <div>
                                <asp:TextBox runat="server" ID="txtPopupWidth" autocomplete="off" MaxLength="4" Text="" oninput="return NumericControl(event, this,5,1,99999);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                                <span style="font-weight: bold;">x</span>
                                <asp:TextBox runat="server" ID="txtPopupHeight" autocomplete="off" MaxLength="4" Text="" oninput="return NumericControl(event, this,5,1,99999);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid-container" id="divPopupHtmlContent" runat="server">
                    <div class="grid-item">
                        <div class="inner-div">
                            <div class="divCaption">
                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, PopupSayfaBaslik %>" runat="server" />
                            </div>
                            <div>
                                <asp:TextBox runat="server" ID="txtPopupSayfaBaslik" autocomplete="off" MaxLength="300" CssClass="txtMultiline" TextMode="MultiLine" placeholder="<%$ Resources:DigiportAdminResource, PopupSayfaBaslik %>"></asp:TextBox>
                            </div>
                        </div>
                        <div class="inner-div">
                            <div class="divCaption">
                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, PopupSayfaIcerik %>" runat="server" />
                            </div>
                            <div>
                                <uc1:DigiportAdminHtmlContent runat="server" ID="DigiportAdminHtmlContent" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <asp:Button runat="server" ID="btnKaydet" Text="<%$ Resources:DigiportAdminResource, Kaydet %>" ValidationGroup="grp1" OnClick="btnKaydet_Click" />
            <div>
                <asp:ValidationSummary runat="server" ID="validationSummary" ValidationGroup="grp1" ForeColor="#d62108" />
            </div>
        </asp:Panel>
        <asp:Panel ID="pnlGrid" GroupingText="<%$ Resources:DigiportAdminResource, IcerikListe %>" runat="server">
            <div>
                <asp:RadioButtonList runat="server" ID="rdBtnListActive" RepeatDirection="Horizontal" Font-Bold="true" Font-Size="14px" OnSelectedIndexChanged="rdBtnListActive_SelectedIndexChanged" AutoPostBack="true">
                </asp:RadioButtonList>
            </div>
            <dx:ASPxGridView ID="gridContent" KeyFieldName="ID" Width="100%" runat="server" OnAfterPerformCallback="ASPxGridView_AfterPerformCallback">
                <Columns>
                    <dx:GridViewDataTextColumn Caption="<%$ Resources:DigiportAdminResource, Icerik %>" FieldName="CONTENT" VisibleIndex="1">
                        <DataItemTemplate>
                            <div id="divCONTENT" title='<%# Eval("CONTENT") %>' runat="server">
                                <%# Eval("CONTENT") %>
                            </div>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Left">
                        </CellStyle>
                    </dx:GridViewDataTextColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Aktif %>" FieldName="" VisibleIndex="2" Width="100px" Settings-AllowHeaderFilter="False" Settings-AllowAutoFilter="False">
                        <DataItemTemplate>
                            <asp:HiddenField runat="server" ID="hdnContentIdGrid" Value='<%# Eval("ID") %>' />
                            <label class="switch">
                                <asp:CheckBox ID="chkActiveGrid" AutoPostBack="true" OnCheckedChanged="chkActiveGrid_CheckedChanged" Checked='<%# Eval("ACTIVE").ToString()=="1" %>' runat="server" />
                                <span class="switch-slider round" onclick="return showloader();"></span>
                            </label>
                            <div>
                                <asp:Label runat="server" Text="<%$ Resources:DigiportAdminResource, ClickEvent %>" contentid='<%# Eval("ID") %>' clickEventType='<%# Eval("CLICK_ACTION") %>' CssClass="clickEventLink" ID="lblClickEventGrid" Visible='<%# ClickLinkVisible(Eval("CLICK_ACTION").ToString()) %>'></asp:Label>
                                <asp:Label ID="txtDigiflowUrl" Text='<%# Eval("DIGIFLOW_URL").ToString() %>' contentid='<%# Eval("ID") %>' CssClass="txtDigiflowUrl" runat="server" Style="display: none;"></asp:Label>
                                <asp:Label ID="txtDigiportUrl" Text='<%# Eval("DIGIPORT_URL").ToString() %>' contentid='<%# Eval("ID") %>' CssClass="txtDigiportUrl" runat="server" Style="display: none;"></asp:Label>
                                <asp:Label ID="txtAjansUrl" Text='<%# Eval("AJANS_URL").ToString() %>' contentid='<%# Eval("ID") %>' CssClass="txtAjansUrl" runat="server" Style="display: none;"></asp:Label>
                                <asp:Label ID="txtTargetLinkGrid" Text='<%# Eval("TARGET_LINK").ToString() %>' contentid='<%# Eval("ID") %>' CssClass="txtTargetLinkGrid" runat="server" Style="display: none;"></asp:Label>
                                <asp:Label ID="txtPopupWidthGrid" Text='<%# Eval("POPUP_WIDTH").ToString() %>' contentid='<%# Eval("ID") %>' CssClass="txtPopupWidthGrid" runat="server" Style="display: none;"></asp:Label>
                                <asp:Label ID="txtPopupHeightGrid" Text='<%# Eval("POPUP_HEIGHT").ToString() %>' contentid='<%# Eval("ID") %>' CssClass="txtPopupHeightGrid" runat="server" Style="display: none;"></asp:Label>
                            </div>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, SiraNo %>" FieldName="ORDER_NO" VisibleIndex="3" Width="50px">
                        <DataItemTemplate>
                            <asp:TextBox runat="server" ID="txtSiraNoGrid" onchange="return showloader();" Enabled='<%# Eval("ACTIVE").ToString()=="1" %>' AutoPostBack="false" OnTextChanged="txtSiraNoGrid_TextChanged" MaxLength="3" autocomplete="off" Text='<%# Eval("ORDER_NO").ToString() %>' oninput="return debouncePostback(event,this, 1000);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Duzenle %>" FieldName="" VisibleIndex="4" Width="40px" Settings-AllowHeaderFilter="False">
                        <DataItemTemplate>
                            <div>
                                <asp:LinkButton ID="lnkGuncelle" Text="" OnClick="lnkduzenle_Click" CausesValidation="false" runat="server">
                                    <i class="fas fa-edit icon" style="font-size:25px;"></i>
                                </asp:LinkButton>
                            </div>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                </Columns>
                <SettingsPager PageSize="20"></SettingsPager>
                <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" ShowGroupPanel="false" />
                <SettingsText EmptyDataRow="<%$ Resources:DigiportAdminResource, GridKayitBulunmadi %>" FilterBarClear="<%$ Resources:DigiportAdminResource, TemizleFiltre %>" />
                <SettingsBehavior AllowSelectSingleRowOnly="true" ConfirmDelete="true" />
                <Styles>
                    <Header BackColor="#C60C30" ForeColor="White"></Header>
                </Styles>
            </dx:ASPxGridView>
        </asp:Panel>
    </asp:Panel>
    <script src="/Scripts/DigiportAdmin/MainpageLeftMenu.js?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>"></script>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>
