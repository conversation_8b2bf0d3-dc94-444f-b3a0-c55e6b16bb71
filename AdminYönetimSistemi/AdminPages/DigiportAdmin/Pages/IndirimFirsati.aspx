﻿<%@ Page Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="IndirimFirsati.aspx.cs" ValidateRequest="false" Inherits="AracTakipSistemi.AdminPages.DigiportAdmin.Pages.IndirimFirsati" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>
<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ Register Src="~/UserControl/DigiportAdminHtmlContent.ascx" TagPrefix="uc1" TagName="DigiportAdminHtmlContent" %>
<%@ Register Src="~/UserControl/DigiportAdminHrAppHtmlContent.ascx" TagPrefix="uc1" TagName="DigiportAdminHrAppHtmlContent" %>

<%@ MasterType VirtualPath="~/Site.Master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="/css/DigiportAdmin.css?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>" rel="stylesheet" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <script type="text/javascript">
        var debounceTimers = {};

        function debouncePostback(event, ctrl, delay) {
            if (!NumericControl(event, ctrl, 5, 1, 99999))
                return false;
            var id = ctrl.id;
            if (debounceTimers[id]) {
                clearTimeout(debounceTimers[id]);
            }
            debounceTimers[id] = setTimeout(function () {
                __doPostBack(id, '');
            }, delay);
        }
    </script>
    <asp:Panel ID="pnlAll" Style="width: 100%" GroupingText="" runat="server">
        <div>
            <a href="#" id="anchorClickEvent" target="_blank" style="display: none;"></a>
            <asp:Button runat="server" ID="btnYeniKayit" Text="<%$ Resources:DigiportAdminResource, YeniIndirimFirsati %>" OnClick="btnYeniKayit_Click" />
            <asp:Button runat="server" ID="btnKayitIptal" Text="<%$ Resources:DigiportAdminResource, Geri %>" OnClick="btnKayitIptal_Click" />
            <asp:Button runat="server" ID="btnSil" Text="<%$ Resources:DigiportAdminResource, Sil %>" OnClick="btnSil_Click" />
            <asp:ConfirmButtonExtender runat="server" ID="confirmDelete" ConfirmText="<%$ Resources:DigiportAdminResource, ConfirmeDelete %>" TargetControlID="btnSil" />
            <asp:HyperLink runat="server" ID="linkKategori" CssClass="linkStyleAsButton" Target="_self" Text="<%$ Resources:DigiportAdminResource, KategoriIslemleri %>"></asp:HyperLink>
        </div>
        <asp:Panel ID="pnlKaydet" GroupingText="<%$ Resources:DigiportAdminResource, DiscountOpportunityInformation %>" Visible="false" runat="server">
            <asp:HiddenField runat="server" ID="hdnIndirimFirsatiId" />
            <div class="container-fluid">
                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                    <ContentTemplate>
                        <div class="grid-container">
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, Baslik %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtBaslik" MaxLength="200" autocomplete="off" placeholder="<%$ Resources:DigiportAdminResource, Baslik %>"></asp:TextBox>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfvBaslik" runat="server" ControlToValidate="txtBaslik" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, BaslikGirin %>"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, Kategori %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:DropDownList ID="drpKategori" runat="server">
                                        </asp:DropDownList>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfvKategori" runat="server" ControlToValidate="drpKategori" InitialValue="-1" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, KategoriSecin %>"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, BaslangicTarihi %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox ID="txtBaslangicTarihi" onkeypress="return DateControl(event);" autocomplete="off" runat="server" MaxLength="10" placeholder="<%$ Resources:DigiportAdminResource, Baslangic %>" Style="width: 100px" />
                                        <asp:CalendarExtender ID="CalendarExtenderBaslangicTarihi" runat="server" CssClass="ajax__calendar"
                                            Format="dd.MM.yyyy" TargetControlID="txtBaslangicTarihi"></asp:CalendarExtender>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfvBaslangicTarihi" runat="server" ControlToValidate="txtBaslangicTarihi" Display="Dynamic" ForeColor="#d62108" ValidationGroup="grp1" ErrorMessage="<%$ Resources:DigiportAdminResource, BaslangicTarihiSec %>"></asp:RequiredFieldValidator>
                                            <asp:RangeValidator ID="rvBaslangicTarihi" runat="server" ControlToValidate="txtBaslangicTarihi" ErrorMessage="<%$ Resources:DigiportAdminResource, BaslangicTarihFormati %>" MaximumValue="01.01.2199" MinimumValue="01.01.1900" Type="Date" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                                            <asp:CompareValidator ID="cvTarihler" runat="server" ControlToCompare="txtBaslangicTarihi" ValidationGroup="grp1" ControlToValidate="txtBitisTarihi" Operator="GreaterThan" Type="Date" ErrorMessage="<%$ Resources:DigiportAdminResource, BitisTarihiBaslagictanIleriOlsun %>" ForeColor="#d62108" Display="Dynamic" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, BitisTarihi %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox ID="txtBitisTarihi" onkeypress="return DateControl(event);" autocomplete="off" runat="server" MaxLength="10" placeholder="<%$ Resources:DigiportAdminResource, Baslangic %>" Style="width: 100px" />
                                        <asp:CalendarExtender ID="CalendarExtenderBitisTarihi" runat="server" CssClass="ajax__calendar"
                                            Format="dd.MM.yyyy" TargetControlID="txtBitisTarihi"></asp:CalendarExtender>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfvBitisTarihi" runat="server" ControlToValidate="txtBitisTarihi" Display="Dynamic" ForeColor="#d62108" ValidationGroup="grp1" ErrorMessage="<%$ Resources:DigiportAdminResource, BitisTarihiSec %>"></asp:RequiredFieldValidator>
                                            <asp:RangeValidator ID="rvBitisTarihi" runat="server" ControlToValidate="txtBitisTarihi" ErrorMessage="<%$ Resources:DigiportAdminResource, BaslangicTarihFormati %>" MaximumValue="01.01.2199" MinimumValue="01.01.1900" Type="Date" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="grid-container">
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, Kurum %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtKurum" MaxLength="200" autocomplete="off" placeholder="<%$ Resources:DigiportAdminResource, Kurum %>"></asp:TextBox>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfvKurum" runat="server" ControlToValidate="txtKurum" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, KurumSeciniz %>"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, Lokasyon %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtLokasyon" MaxLength="250" autocomplete="off" placeholder="<%$ Resources:DigiportAdminResource, Lokasyon %>"></asp:TextBox>
                                        <div>
                                            <%--<asp:RequiredFieldValidator ID="rfvLokasyon" runat="server" ControlToValidate="txtLokasyon" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, LokasyonGirin %>"></asp:RequiredFieldValidator>--%>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, IndirimOrani %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtIndirimOrani" MaxLength="2" autocomplete="off" Text="" oninput="return NumericControl(event, this,3,0,100);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                                        <span>%</span>
                                        <div>
                                            <%--<asp:RequiredFieldValidator ID="rfvIndirimOrani" runat="server" ControlToValidate="txtIndirimOrani" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, IndirimOraniSecin %>"></asp:RequiredFieldValidator>--%>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, SiraNo %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtSiraNo" MaxLength="3" autocomplete="off" Text="1" oninput="return NumericControl(event, this,5,1,99999);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfvSiraNo" runat="server" ControlToValidate="txtSiraNo" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SiraNoSecin %>"></asp:RequiredFieldValidator>
                                            <asp:RangeValidator ID="rvSiraNo" runat="server" ControlToValidate="txtSiraNo" ErrorMessage="<%$ Resources:DigiportAdminResource, SayiAraligi2 %>" MaximumValue="99999" MinimumValue="1" Type="Integer" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="grid-container">
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, AvantajKosullari %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtAvantajKosullari" MaxLength="500" TextMode="MultiLine" CssClass="txtMultiline" autocomplete="off" placeholder="<%$ Resources:DigiportAdminResource, AvantajKosullari %>"></asp:TextBox>
                                        <div>
                                            <%--<asp:RequiredFieldValidator ID="rfvAvantajKosullari" runat="server" ControlToValidate="txtAvantajKosullari" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, AvantajKosullariSecin %>"></asp:RequiredFieldValidator>--%>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, Adres %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtAdres" MaxLength="500" TextMode="MultiLine" CssClass="txtMultiline" autocomplete="off" placeholder="<%$ Resources:DigiportAdminResource, Adres %>"></asp:TextBox>
                                        <div>
                                            <%--<asp:RequiredFieldValidator ID="rfvAdres" runat="server" ControlToValidate="txtAdres" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, AdresGirin %>"></asp:RequiredFieldValidator>--%>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, Telefon %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtTelefon" MaxLength="150" autocomplete="off" placeholder="<%$ Resources:DigiportAdminResource, Telefon %>"></asp:TextBox>
                                        <div>
                                            <%--<asp:RequiredFieldValidator ID="rfvTelefon" runat="server" ControlToValidate="txtLokasyon" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, TelefonGirin %>"></asp:RequiredFieldValidator>--%>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, Aktif %>" runat="server" />
                                    </div>
                                    <div>
                                        <label class="switch">
                                            <asp:CheckBox ID="chkActive" runat="server" />
                                            <span class="switch-slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="grid-container">
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, TiklanmaIslemi %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:DropDownList ID="drpClickAction" AutoPostBack="true" OnSelectedIndexChanged="drpClickAction_SelectedIndexChanged" runat="server">
                                        </asp:DropDownList>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfv5" runat="server" ControlToValidate="drpClickAction" InitialValue="-1" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, TiklanmaIslemiSecin %>"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div" id="divHedefLink" runat="server">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, TiklanmaHedefLink %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtTargetLink" autocomplete="off" MaxLength="500" placeholder="<%$ Resources:DigiportAdminResource, HedefLink %>"></asp:TextBox>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfvHedefLink" runat="server" ControlToValidate="txtTargetLink" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, HedefLinkGirin %>"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </div>
                                <div class="inner-div" id="divPopupSize" runat="server">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, AcilanPencereBoyutlari %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtPopupWidth" autocomplete="off" MaxLength="4" Text="" oninput="return NumericControl(event, this,5,1,99999);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                                        <span style="font-weight: bold;">x</span>
                                        <asp:TextBox runat="server" ID="txtPopupHeight" autocomplete="off" MaxLength="4" Text="" oninput="return NumericControl(event, this,5,1,99999);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="grid-container" id="divPopupHtmlContent" runat="server">
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, PopupSayfaBaslik %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtPopupSayfaBaslik" autocomplete="off" MaxLength="300" CssClass="txtMultiline" TextMode="MultiLine" placeholder="<%$ Resources:DigiportAdminResource, PopupSayfaBaslik %>"></asp:TextBox>
                                    </div>
                                </div>
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, PopupSayfaIcerik %>" runat="server" />
                                    </div>
                                    <div>
                                        <uc1:DigiportAdminHtmlContent runat="server" ID="DigiportAdminHtmlContent" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="container-fluid">
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, HrAppEnabled %>" runat="server" />
                                    </div>
                                    <div>
                                        <label class="switch">
                                            <asp:CheckBox ID="chkHrAppEnabled" AutoPostBack="true" OnCheckedChanged="chkHrAppEnabled_CheckedChanged" runat="server" />
                                            <span class="switch-slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ContentTemplate>
                    <Triggers>
                        <asp:PostBackTrigger ControlID="btnKaydet" />
                    </Triggers>
                </asp:UpdatePanel>
            </div>
            <asp:UpdatePanel ID="UpdatePanel3" UpdateMode="Conditional" runat="server">
                <ContentTemplate>
                    <div class="container-fluid">
                        <div id="divWrapperHrApp1" runat="server" visible="false">
                            <div class="grid-container">
                                <div class="grid-item">
                                    <div class="inner-div">
                                        <div class="divCaption">
                                            <span>
                                                <asp:Literal Text="Logo" runat="server" />
                                            </span>
                                            <asp:Label ID="lblLogoSize" CssClass="lblimagesize" runat="server"></asp:Label>
                                        </div>
                                        <div>
                                            <asp:Image runat="server" ID="imgLogo" CssClass="imgChosen" ClientIDMode="Static" />
                                            <div>
                                                <asp:FileUpload runat="server" ID="fuLogo" AllowMultiple="false" Style="display: none;" />
                                                <asp:Button runat="server" ID="btnSelectLogo" OnClientClick="return false;" Text="<%$ Resources:DigiportAdminResource, YuklenilecekLogoSecin %>" />
                                                <asp:Button runat="server" ID="btnClearFileSelectionLogo" OnClientClick="return false;" Text="<%$ Resources:DigiportAdminResource, SecileniTemizle %>" />
                                                <label id="lblSeciliLogo" class="lblChosen"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ContentTemplate>
            </asp:UpdatePanel>


            <div class="container-fluid">

                <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                    <ContentTemplate>
                        <asp:Panel ID="PanelIKAPP" GroupingText="<%$ Resources:DigiportAdminResource, IKAppInformation %>" runat="server" Visible="false">
                            <div class="grid-container">
                                <div class="grid-item">
                                    <div class="inner-div">
                                        <div class="divCaption">
                                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, TiklanmaIslemi %>" runat="server" />
                                        </div>
                                        <div>
                                            <asp:DropDownList ID="drpClickActionHrApp" AutoPostBack="true" OnSelectedIndexChanged="drpClickActionHrApp_SelectedIndexChanged" runat="server">
                                            </asp:DropDownList>
                                        </div>
                                        <div>
                                            <asp:CustomValidator ID="ClickActionHrApp_CustomValidate" Display="Dynamic" runat="server" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, HrAppTiklanmaIslemiSecin %>"
                                                ClientValidationFunction="ValidatedrpClickActionHrApp" ValidationGroup="grp1"></asp:CustomValidator>
                                        </div>
                                    </div>
                                </div>
                                <div class="grid-item">
                                    <div class="inner-div" id="divHedefLinkHrApp" runat="server">
                                        <div class="divCaption">
                                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, TiklanmaHedefLink %>" runat="server" />
                                        </div>
                                        <div>
                                            <asp:TextBox runat="server" ID="txtTargetLinkHrApp" autocomplete="off" MaxLength="500" placeholder="<%$ Resources:DigiportAdminResource, HedefLink %>"></asp:TextBox>
                                        </div>
                                        <div>
                                            <asp:CustomValidator ID="TargetLinkHrApp_CustomValidate" Display="Dynamic" runat="server" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, HrAppHedefLinkGirin %>"
                                                ClientValidationFunction="ValidatedrpTargetLinkHrApp" ValidationGroup="grp1"></asp:CustomValidator>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-container" id="divPopupHtmlContentHrApp" runat="server">
                                <div class="grid-item">
                                    <div class="inner-div">
                                        <div class="divCaption">
                                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, PopupSayfaBaslik %>" runat="server" />
                                        </div>
                                        <div>
                                            <asp:TextBox runat="server" ID="txtPopupSayfaBaslikHrApp" autocomplete="off" MaxLength="300" CssClass="txtMultiline" TextMode="MultiLine" placeholder="<%$ Resources:DigiportAdminResource, PopupSayfaBaslik %>"></asp:TextBox>
                                        </div>
                                    </div>
                                    <div class="inner-div">
                                        <div class="divCaption">
                                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, PopupSayfaIcerik %>" runat="server" />
                                        </div>
                                        <div>
                                            <uc1:DigiportAdminHrAppHtmlContent runat="server" ID="DigiportAdminHrAppHtmlContent" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </asp:Panel>
                        <asp:Button runat="server" ID="btnKaydet" Text="<%$ Resources:DigiportAdminResource, Kaydet %>" ValidationGroup="grp1" OnClick="btnKaydet_Click" />
                        <div>
                            <asp:ValidationSummary runat="server" ID="validationSummary" ValidationGroup="grp1" ForeColor="#d62108" />
                        </div>
                    </ContentTemplate>
                    <Triggers>
                        <asp:PostBackTrigger ControlID="btnKaydet" />
                    </Triggers>
                </asp:UpdatePanel>
            </div>

        </asp:Panel>
        <asp:Panel ID="pnlGrid" GroupingText="<%$ Resources:DigiportAdminResource, IndirimFirsatiListe %>" runat="server">
            <div>
                <asp:RadioButtonList runat="server" ID="rdBtnListActive" RepeatDirection="Horizontal" Font-Bold="true" Font-Size="14px" OnSelectedIndexChanged="rdBtnListActive_SelectedIndexChanged" AutoPostBack="true">
                </asp:RadioButtonList>
            </div>
            <dx:ASPxGridView ID="gridIndirimFirsati" KeyFieldName="ID" Width="100%" runat="server" OnAfterPerformCallback="ASPxGridView_AfterPerformCallback">
                <Columns>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Baslik %>" FieldName="BASLIK" VisibleIndex="1">
                        <CellStyle HorizontalAlign="Left">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Kategori %>" Width="110px" FieldName="KATEGORI" VisibleIndex="2">
                        <DataItemTemplate>
                            <asp:Label runat="server" Text='<%# Eval("KATEGORI") %>' ID="lblKATEGORI"></asp:Label>
                            <div>
                                <asp:Label runat="server" Text="<%$ Resources:DigiportAdminResource, ClickEvent %>" indirimfirsatiid='<%# Eval("ID") %>' clickEventType='<%# Eval("CLICK_ACTION") %>' CssClass="clickEventLink" ID="lblClickEventGrid" Visible='<%# ClickLinkVisible(Eval("CLICK_ACTION").ToString()) %>'></asp:Label>
                                <div>
                                    <asp:Label ID="txtDigiflowUrl" Text='<%# Eval("DIGIFLOW_URL").ToString() %>' indirimfirsatiid='<%# Eval("ID") %>' CssClass="txtDigiflowUrl" runat="server" Style="display: none;"></asp:Label>
                                    <asp:Label ID="txtDigiportUrl" Text='<%# Eval("DIGIPORT_URL").ToString() %>' indirimfirsatiid='<%# Eval("ID") %>' CssClass="txtDigiportUrl" runat="server" Style="display: none;"></asp:Label>
                                    <asp:Label ID="txtAjansUrl" Text='<%# Eval("AJANS_URL").ToString() %>' indirimfirsatiid='<%# Eval("ID") %>' CssClass="txtAjansUrl" runat="server" Style="display: none;"></asp:Label>
                                    <asp:Label ID="txtTargetLinkGrid" Text='<%# Eval("TARGET_LINK").ToString() %>' indirimfirsatiid='<%# Eval("ID") %>' CssClass="txtTargetLinkGrid" runat="server" Style="display: none;"></asp:Label>
                                    <asp:Label ID="txtPopupWidthGrid" Text='<%# Eval("POPUP_WIDTH").ToString() %>' indirimfirsatiid='<%# Eval("ID") %>' CssClass="txtPopupWidthGrid" runat="server" Style="display: none;"></asp:Label>
                                    <asp:Label ID="txtPopupHeightGrid" Text='<%# Eval("POPUP_HEIGHT").ToString() %>' indirimfirsatiid='<%# Eval("ID") %>' CssClass="txtPopupHeightGrid" runat="server" Style="display: none;"></asp:Label>
                                </div>
                            </div>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Left">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Baslangic %>" FieldName="BASLANGIC_TARIHI" VisibleIndex="3" Width="110px">
                        <DataItemTemplate>
                            <dx:ASPxDateEdit ID="dateEditStartGrid" AutoPostBack="true" OnValueChanged="dateEditStartGrid_ValueChanged" runat="server" Value='<%# Eval("BASLANGIC_TARIHI") %>' DisplayFormatString="dd.MM.yyyy">
                                <ClientSideEvents ValueChanged="function(s,e){ showloader(); }" />
                            </dx:ASPxDateEdit>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Bitis %>" FieldName="BITIS_TARIHI" VisibleIndex="4" Width="110px">
                        <DataItemTemplate>
                            <dx:ASPxDateEdit ID="dateEditEndGrid" AutoPostBack="true" OnValueChanged="dateEditEndGrid_ValueChanged" runat="server" Value='<%# Eval("BITIS_TARIHI") %>' DisplayFormatString="dd.MM.yyyy">
                                <ClientSideEvents ValueChanged="function(s,e){ showloader(); }" />
                            </dx:ASPxDateEdit>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Aktif %>" FieldName="" VisibleIndex="5" Width="70px" Settings-AllowHeaderFilter="False" Settings-AllowAutoFilter="False">
                        <DataItemTemplate>
                            <asp:HiddenField runat="server" ID="hdnIndirimFirsatiIdGrid" Value='<%# Eval("ID") %>' />
                            <label class="switch">
                                <asp:CheckBox ID="chkActiveGrid" AutoPostBack="true" OnCheckedChanged="chkActiveGrid_CheckedChanged" Checked='<%# Eval("ACTIVE").ToString()=="1" %>' runat="server" />
                                <span class="switch-slider round" onclick="return showloader();"></span>
                            </label>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, SiraNo %>" FieldName="ORDER_NO" VisibleIndex="6" Width="50px">
                        <DataItemTemplate>
                            <asp:TextBox runat="server" ID="txtSiraNoGrid" onchange="return showloader();" Enabled='<%# Eval("ACTIVE").ToString()=="1" %>' AutoPostBack="false" OnTextChanged="txtSiraNoGrid_TextChanged" MaxLength="3" autocomplete="off" Text='<%# Eval("ORDER_NO").ToString() %>' oninput="return debouncePostback(event,this, 1000);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Duzenle %>" FieldName="" VisibleIndex="7" Width="40px" Settings-AllowHeaderFilter="False">
                        <DataItemTemplate>
                            <div>
                                <asp:LinkButton ID="lnkGuncelle" Text="" OnClick="lnkduzenle_Click" CausesValidation="false" runat="server">
                                    <i class="fas fa-edit icon" style="font-size:25px;"></i>
                                </asp:LinkButton>
                            </div>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                </Columns>
                <SettingsPager PageSize="20"></SettingsPager>
                <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" ShowGroupPanel="false" />
                <SettingsText EmptyDataRow="<%$ Resources:DigiportAdminResource, GridKayitBulunmadi %>" FilterBarClear="<%$ Resources:DigiportAdminResource, TemizleFiltre %>" />
                <SettingsBehavior AllowSelectSingleRowOnly="true" ConfirmDelete="true" />
                <Styles>
                    <Header BackColor="#C60C30" ForeColor="White"></Header>
                </Styles>
            </dx:ASPxGridView>
        </asp:Panel>
    </asp:Panel>

    <script src="/Scripts/DigiportAdmin/IndirimFirsati.js?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>"></script>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>
