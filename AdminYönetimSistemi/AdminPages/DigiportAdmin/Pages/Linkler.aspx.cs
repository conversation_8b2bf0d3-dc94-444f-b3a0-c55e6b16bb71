﻿using CoreHelpers;
using DevExpress.Web;
using Entities.DigiportAdmin;
using FormHelper.DigiportAdmin;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.DigiportAdmin.Pages
{
    public partial class Linkler : DigiportSecurePage
    {
        private readonly string componentName = "Linkler";
        int MID = 0;
        private int LinkId
        {
            get
            {
                string linkId = hdnLinkId.Value;
                if (linkId != string.Empty)
                    return Convert.ToInt32(linkId);
                else
                    return 0;
            }
        }
        DigiportHtmlEditorMediaHelper helper;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!String.IsNullOrEmpty(Request.QueryString["MID"]) && CoreHelpers.GenericIslemler.IsNumeric(Request.QueryString["MID"]))
            {
                if (!ConfigurationManager.AppSettings["DigiportAdminLinklerID"].Split('-').Contains(MenuId.Value.ToString()))
                {
                    Session["ErrorPageMessage"] = Resources.DigiportAdminResource.TipYanlis;
                    Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
                    return;
                }
                MID = MenuId.Value;
            }
            else
            {
                Session["ErrorPageMessage"] = Resources.DigiportAdminResource.TipBelirsiz;
                Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
                return;
            }
            DigiportAdminHtmlContent.ComponentName = componentName;
            helper = new DigiportHtmlEditorMediaHelper(DigiportAdminHtmlContent.ComponentName);
            this.Title = FormHelper.DigiportAdmin.NameHelper.GetComponentNameOrDescription(MID, false, FormHelper.CoreHelper.isEnglish());
            if (!IsPostBack && !IsCallback)
            {
                pnlAll.GroupingText = this.Title;
                foreach (var action in Enum.GetValues(FormHelper.CoreHelper.isEnglish() ? typeof(DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions_En) : typeof(DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions)))
                {
                    int value = (int)action;
                    string name = action.ToString().Replace("_", " ");
                    if (
                        value == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç
                        ||
                        value == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç
                      )
                        continue;
                    drpClickAction.Items.Add(new ListItem(name, value.ToString()));
                }
                drpClickAction.Items.Insert(0, new ListItem(FormHelper.CoreHelper.isEnglish() ? "Please Select" : "Seçiniz", "-1") { Selected = true });
                rdBtnListActive.Items.Add(new ListItem(Resources.DigiportAdminResource.Tumu, "2") { Selected = false });
                rdBtnListActive.Items.Add(new ListItem(Resources.DigiportAdminResource.Aktif, "1") { Selected = true });
                rdBtnListActive.Items.Add(new ListItem(Resources.DigiportAdminResource.Pasif, "0") { Selected = false });
                helper.ClearMediaRemoveSession();
                helper.ClearMediaUploadSession();
                PanelleriDuzenle(true, false, false);
            }
        }
        protected void ASPxGridView_AfterPerformCallback(object sender, ASPxGridViewAfterPerformCallbackEventArgs e)
        {
            GridYukle();
        }
        protected void rdBtnListActive_SelectedIndexChanged(object sender, EventArgs e)
        {
            GridYukle();
        }
        protected void btnYeniKayit_Click(object sender, EventArgs e)
        {
            PanelleriDuzenle(false, true, false);
        }
        protected void btnKayitIptal_Click(object sender, EventArgs e)
        {
            PanelleriDuzenle(true, false, false);
        }
        protected void btnKaydet_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtLinkAdi.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.LinkAdiGirin);
                int siraNo = 1;
                if (!Int32.TryParse(txtSiraNo.Text.Trim(), out siraNo))
                    throw new Exception(Resources.DigiportAdminResource.GecersizSiraNo);
                if (drpClickAction.SelectedValue == "-1")
                    throw new Exception(Resources.DigiportAdminResource.TiklanmaIslemiSecin);
                if ((drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç).ToString() 
                    || 
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç).ToString()) 
                    && txtTargetLink.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.HedefLinkGirin);
                int popupWidth = 0, popupHeight = 0;
                if (drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç).ToString() 
                    || 
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç).ToString() 
                    || 
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç).ToString()
                    )
                {
                    if (!Int32.TryParse(txtPopupWidth.Text.Trim(), out popupWidth))
                        throw new Exception(Resources.DigiportAdminResource.GecersizPopupGenislik);
                    if (!Int32.TryParse(txtPopupHeight.Text.Trim(), out popupHeight))
                        throw new Exception(Resources.DigiportAdminResource.GecersizPopupYukseklik);
                }
                DIGIPORT_ADMIN_LINKLER entity;
                if (LinkId == 0)
                    entity = new DIGIPORT_ADMIN_LINKLER();
                else
                    entity = PRepository<DIGIPORT_ADMIN_LINKLER>.EntityGetir("DT_WORKFLOW", LinkId);
                bool originalStateIsActive = entity.GetSet_ID != 0 && entity.ACTIVE == "1";
                entity.MENU_NAME_ID = LinkId == 0 ? MID : entity.MENU_NAME_ID;
                entity.LINK_ADI = txtLinkAdi.Text.Trim();
                entity.NOTLAR = txtNotlar.Text.Trim() != string.Empty ? txtNotlar.Text.Trim() : null;

                entity.CLICK_ACTION = ConvertionHelper.ConvertValue<decimal>(drpClickAction.SelectedValue);
                entity.TARGET_LINK = entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç) ? txtTargetLink.Text.Trim() : null;
                entity.TARGET_CONTENT = entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? ((TextBox)DigiportAdminHtmlContent.FindControl("txtHtmlContent")).Text.Trim().Replace("SummerNoteTempUploads", "SummerNoteUploads") : null;
                entity.TARGET_HEADLINE = entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? txtPopupSayfaBaslik.Text.Trim() : null;
                entity.POPUP_WIDTH = entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? popupWidth : (decimal?)null;
                entity.POPUP_HEIGHT = entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? popupHeight : (decimal?)null;
                entity.ACTIVE = chkActive.Checked ? "1" : "0";

                if (entity.GetSet_ID == 0)
                {
                    entity.ORDER_NO = siraNo;
                    entity.CREATED_BY = LoginId;
                    entity.CREATED = DateTime.Now;
                    entity.DELETED = "0";
                    PRepository<DIGIPORT_ADMIN_LINKLER>.EntityKaydet("DT_WORKFLOW", entity);
                }
                else
                {
                    entity.LAST_UPDATED_BY = LoginId;
                    entity.LAST_UPDATED = DateTime.Now;
                    PRepository<DIGIPORT_ADMIN_LINKLER>.EntityUpdateEt("DT_WORKFLOW", entity);
                    if ((siraNo != entity.ORDER_NO || !originalStateIsActive) && entity.ACTIVE == "1")
                        SiraNoTekrarDuzenle(entity.ID, entity.ORDER_NO, !originalStateIsActive);
                }
                helper.MoveUploadedContentToRealFolder("/Content/SummerNoteUploads/Digiport/" + componentName + "/" + DateTime.Today.Year.ToString() + "/" + DateTime.Today.Month.ToString());
                helper.DeleteRealFolderContents();
                PanelleriDuzenle(true, false, false);
                this.Master.PopupGoster(Resources.DigiportAdminResource.Basari, Resources.DigiportAdminResource.KayitBasarili, false);
            }
            catch (Exception ex)
            {
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
        protected void btnSil_Click(object sender, EventArgs e)
        {
            try
            {
                if (LinkId > 0)
                {
                    DIGIPORT_ADMIN_LINKLER entity = PRepository<DIGIPORT_ADMIN_LINKLER>.EntityGetir("DT_WORKFLOW", LinkId);
                    if (entity != null)
                    {
                        List<string> pathsToDelete = new List<string>();
                        if (!string.IsNullOrEmpty(entity.TARGET_CONTENT))
                        {
                            pathsToDelete.AddRange(helper.ExtractSummerNoteUploadPaths(entity.TARGET_CONTENT));
                        }

                        foreach (string path in pathsToDelete)
                        {
                            if (File.Exists(path))
                                File.Delete(path);
                        }
                        entity.DELETED = "1";
                        entity.LAST_UPDATED_BY = LoginId;
                        entity.LAST_UPDATED = DateTime.Now;
                        PRepository<DIGIPORT_ADMIN_LINKLER>.EntityUpdateEt("DT_WORKFLOW", entity);
                        PanelleriDuzenle(true, false, false);
                        this.Master.PopupGoster(Resources.DigiportAdminResource.Basari, Resources.DigiportAdminResource.SilmeBasarili, false);
                    }
                }
            }
            catch (Exception ex)
            {
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
        private void PanelleriDuzenle(bool listelemeModu, bool yeniKayitModu, bool guncellemeModu)
        {
            if (yeniKayitModu)
                hdnLinkId.Value = "0";
            btnYeniKayit.Visible = listelemeModu;
            btnSil.Visible = guncellemeModu;
            btnKayitIptal.Visible = yeniKayitModu || guncellemeModu;
            pnlKaydet.Visible = yeniKayitModu || guncellemeModu;
            pnlGrid.Visible = listelemeModu;
            if (yeniKayitModu || guncellemeModu)
                KaydetPanelGetir();
            if (listelemeModu)
                GridYukle();
        }

        private void KaydetPanelGetir()
        {
            if (LinkId == 0)
            {
                txtLinkAdi.Text = txtNotlar.Text = txtTargetLink.Text = ((TextBox)DigiportAdminHtmlContent.FindControl("txtHtmlContent")).Text = txtPopupSayfaBaslik.Text = ((TextBox)DigiportAdminHtmlContent.FindControl("summernote")).Text = string.Empty;
                chkActive.Checked = true;
                drpClickAction.SelectedIndex = 0;
                txtSiraNo.Text = LinklerHelper.GetNextOrderNo();
                txtPopupWidth.Text = ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[0];
                txtPopupHeight.Text = ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[1];
                KaydetGorunumAyarla(false, false, false);
                txtSiraNo.Enabled = false;
            }
            else
            {
                DIGIPORT_ADMIN_LINKLER entity = PRepository<DIGIPORT_ADMIN_LINKLER>.EntityGetir("DT_WORKFLOW", LinkId);
                txtLinkAdi.Text = entity.LINK_ADI;
                txtNotlar.Text = entity.NOTLAR;
                txtPopupSayfaBaslik.Text = entity.TARGET_HEADLINE;
                txtSiraNo.Text = entity.ORDER_NO.ToString();
                txtSiraNo.Enabled = entity.ACTIVE == "1";
                txtTargetLink.Text = entity.TARGET_LINK;
                txtPopupWidth.Text = entity.POPUP_WIDTH != null ? entity.POPUP_WIDTH.ToString() : ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[0];
                txtPopupHeight.Text = entity.POPUP_HEIGHT != null ? entity.POPUP_HEIGHT.ToString() : ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[1];
                ((TextBox)DigiportAdminHtmlContent.FindControl("txtHtmlContent")).Text = ((TextBox)DigiportAdminHtmlContent.FindControl("summernote")).Text = entity.TARGET_CONTENT;
                chkActive.Checked = entity.ACTIVE == "1";
                drpClickAction.Items.Cast<ListItem>().ToList().ForEach(x => x.Selected = false);
                drpClickAction.Items.FindByValue(entity.CLICK_ACTION.ToString()).Selected = true;
                KaydetGorunumAyarla(
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç)
                    ,
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ,
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    );
            }
        }
        public bool ClickLinkVisible(string clickActionId)
        {
            return clickActionId != ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Olay_Yok).ToString();
        }
        private void GridYukle()
        {
            DataTable dt = LinklerHelper.LinklerTabloGetir(ConvertionHelper.ConvertValue<int>(rdBtnListActive.SelectedValue));
            gridLinkler.DataSource = dt;
            gridLinkler.DataBind();
        }

        protected void drpClickAction_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (drpClickAction.SelectedItem.Value == "-1" || drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Olay_Yok).ToString())
                KaydetGorunumAyarla(false, false, false);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(true, false, false);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(true, true, false);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(false, false, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(false, true, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(false, false, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(false, true, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(false, false, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(false, true, true);
        }
        private void KaydetGorunumAyarla(bool linkBilgileriGoster, bool popupBilgileriGoster, bool popupContentGoster)
        {
            divHedefLink.Visible = linkBilgileriGoster;
            divPopupSize.Visible = popupBilgileriGoster;
            divPopupHtmlContent.Style["display"] = popupContentGoster ? "block" : "none";
        }
        protected void chkActiveGrid_CheckedChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_LINKLER entity = null;
            try
            {
                int index = (((CheckBox)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _linkId = ConvertionHelper.ConvertValue<int>(gridLinkler.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_LINKLER>.EntityGetir("DT_WORKFLOW", _linkId);
                entity.ACTIVE = ((CheckBox)sender).Checked ? "1" : "0";
                entity.LAST_UPDATED_BY = LoginId;
                entity.LAST_UPDATED = DateTime.Now;
                PRepository<DIGIPORT_ADMIN_LINKLER>.EntityUpdateEt("DT_WORKFLOW", entity);
                if (entity.ACTIVE == "1")
                    SiraNoTekrarDuzenle(entity.ID, entity.ORDER_NO, entity.ACTIVE == "1");
                GridYukle();
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((CheckBox)sender).Checked = entity.ACTIVE == "1";
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(gridLinkler.GetRowValues(index, "ID"));
            hdnLinkId.Value = deger.ToString();
            PanelleriDuzenle(false, false, true);
        }

        protected void txtSiraNoGrid_TextChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_LINKLER entity = null;
            try
            {
                int index = (((TextBox)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _linkId = ConvertionHelper.ConvertValue<int>(gridLinkler.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_LINKLER>.EntityGetir("DT_WORKFLOW", _linkId);
                decimal newOrderNo = ConvertionHelper.ConvertValue<decimal>(((TextBox)sender).Text);
                if (entity.ACTIVE == "1")
                {
                    SiraNoTekrarDuzenle(entity.ID, newOrderNo, false);
                    GridYukle();
                }
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((TextBox)sender).Text = entity.ORDER_NO.ToString();
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
        private void SiraNoTekrarDuzenle(int changedItemId, decimal newOrderNo, bool changedAsActive)
        {
            try
            {
                List<ItemOrderInfo> linkler = LinklerHelper.LinklerTabloGetir(1).Rows.Cast<DataRow>().Select(row => new ItemOrderInfo()
                {
                    ItemId = ConvertionHelper.ConvertValue<int>(row["ID"].ToString()),
                    ItemOrder_Old = ConvertionHelper.ConvertValue<int>(row["ORDER_NO"].ToString()),
                    ItemOrder_New = 0,
                    Changed = false
                }).OrderBy(s => s.ItemOrder_Old).ToList();

                SortHelper.SiraNoTekrarAyarla(linkler, changedItemId, newOrderNo, changedAsActive);
                if (linkler.Exists(x => x.Changed))
                {
                    foreach (ItemOrderInfo itemChanged in linkler.Where(x => x.Changed).ToList())
                    {
                        DIGIPORT_ADMIN_LINKLER entity = PRepository<DIGIPORT_ADMIN_LINKLER>.EntityGetir("DT_WORKFLOW", Convert.ToInt32(itemChanged.ItemId));
                        entity.ORDER_NO = itemChanged.ItemOrder_New;
                        entity.LAST_UPDATED = DateTime.Now;
                        entity.LAST_UPDATED_BY = LoginId;
                        PRepository<DIGIPORT_ADMIN_LINKLER>.EntityUpdateEt("DT_WORKFLOW", entity);
                    }
                }
            }
            catch
            {
            }
        }
    }
}