﻿using CoreHelpers;
using DevExpress.Web;
using Entities.DigiportAdmin;
using FormHelper.DigiportAdmin;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.DigiportAdmin.Pages
{
    public partial class IndirimFirsati : DigiportSecurePage
    {
        private readonly string componentName = "IndirimFirsati";
        int MID = 0;
        private int IndirimFirsatiId
        {
            get
            {
                string firsatId = hdnIndirimFirsatiId.Value;
                if (firsatId != string.Empty)
                    return Convert.ToInt32(firsatId);
                else
                    return 0;
            }
        }
        DigiportHtmlEditorMediaHelper helper;
        DigiportMediaHrAppHelper helperHrApp;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!String.IsNullOrEmpty(Request.QueryString["MID"]) && CoreHelpers.GenericIslemler.IsNumeric(Request.QueryString["MID"]))
            {
                if (!ConfigurationManager.AppSettings["DigiportAdminIndirimFirsatiID"].Split('-').Contains(MenuId.Value.ToString()))
                {
                    Session["ErrorPageMessage"] = Resources.DigiportAdminResource.TipYanlis;
                    Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
                    return;
                }
                MID = MenuId.Value;
            }
            else
            {
                Session["ErrorPageMessage"] = Resources.DigiportAdminResource.TipBelirsiz;
                Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
                return;
            }
            DigiportAdminHtmlContent.ComponentName = componentName;
            DigiportAdminHrAppHtmlContent.ComponentName = componentName;
            helper = new DigiportHtmlEditorMediaHelper(DigiportAdminHtmlContent.ComponentName);
            helperHrApp = new DigiportMediaHrAppHelper(DigiportAdminHrAppHtmlContent.ComponentName);
            this.Title = FormHelper.DigiportAdmin.NameHelper.GetComponentNameOrDescription(MID, false, FormHelper.CoreHelper.isEnglish());
            if (!IsPostBack && !IsCallback)
            {
                linkKategori.NavigateUrl = "~/AdminPages/DigiportAdmin/IndirimFirsatiKategori.aspx?MID=" + MID;
                pnlAll.GroupingText = this.Title;
                drpKategori.DataSource = IndirimFirsatiHelper.IndirimFirsatiKategoriTabloGetir(FormHelper.CoreHelper.isEnglish(), 1);
                drpKategori.DataValueField = "ID";
                drpKategori.DataTextField = FormHelper.CoreHelper.isEnglish() ? "KATEGORI_ADI_EN" : "KATEGORI_ADI";
                drpKategori.DataBind();
                drpKategori.Items.Insert(0, new ListItem(FormHelper.CoreHelper.isEnglish() ? "Please Select" : "Seçiniz", "-1") { Selected = true });
                foreach (var action in Enum.GetValues(FormHelper.CoreHelper.isEnglish() ? typeof(DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions_En) : typeof(DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions)))
                {
                    int value = (int)action;
                    string name = action.ToString().Replace("_", " ");
                    if (
                        value == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç
                        ||
                        value == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç
                      )
                        continue;
                    drpClickAction.Items.Add(new ListItem(name, value.ToString()));
                }
                drpClickAction.Items.Insert(0, new ListItem(FormHelper.CoreHelper.isEnglish() ? "Please Select" : "Seçiniz", "-1") { Selected = true });
                rdBtnListActive.Items.Add(new ListItem(Resources.DigiportAdminResource.Tumu, "2") { Selected = false });
                rdBtnListActive.Items.Add(new ListItem(Resources.DigiportAdminResource.Aktif, "1") { Selected = true });
                rdBtnListActive.Items.Add(new ListItem(Resources.DigiportAdminResource.Pasif, "0") { Selected = false });
                lblLogoSize.Text = string.Format("[{0}px - {1}px]", ConfigurationManager.AppSettings["DigiportAdminIndirimFirsatiLogoIzinliBoyutHrApp"].Split('x')[0], ConfigurationManager.AppSettings["DigiportAdminIndirimFirsatiLogoIzinliBoyutHrApp"].Split('x')[1]);
                string allowedExtensionsLogoHrApp = System.Configuration.ConfigurationManager.AppSettings["DigiportAdminIndirimFirsatiLogoIzinliTiplerHrApp"];
                fuLogo.Attributes["accept"] = allowedExtensionsLogoHrApp;
                foreach (var action in Enum.GetValues(FormHelper.CoreHelper.isEnglish() ? typeof(DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions_En) : typeof(DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions)))
                {
                    int value = (int)action;
                    string name = action.ToString().Replace("_", " ");
                    drpClickActionHrApp.Items.Add(new ListItem(name, value.ToString()));
                }
                drpClickActionHrApp.Items.Insert(0, new ListItem(FormHelper.CoreHelper.isEnglish() ? "Please Select" : "Seçiniz", "-1") { Selected = true });
                helper.ClearMediaRemoveSession();
                helper.ClearMediaUploadSession();
                helperHrApp.ClearMediaRemoveSession();
                helperHrApp.ClearMediaUploadSession();
                PanelleriDuzenle(true, false, false);
            }
        }

        protected void ASPxGridView_AfterPerformCallback(object sender, ASPxGridViewAfterPerformCallbackEventArgs e)
        {
            GridYukle();
        }
        protected void rdBtnListActive_SelectedIndexChanged(object sender, EventArgs e)
        {
            GridYukle();
        }
        private void PanelleriDuzenle(bool listelemeModu, bool yeniKayitModu, bool guncellemeModu)
        {
            if (yeniKayitModu)
                hdnIndirimFirsatiId.Value = "0";
            btnYeniKayit.Visible = listelemeModu;
            linkKategori.Visible = listelemeModu;
            btnSil.Visible = guncellemeModu;
            btnKayitIptal.Visible = yeniKayitModu || guncellemeModu;
            pnlKaydet.Visible = yeniKayitModu || guncellemeModu;
            pnlGrid.Visible = listelemeModu;
            if (yeniKayitModu || guncellemeModu)
                KaydetPanelGetir();
            if (listelemeModu)
                GridYukle();
        }

        private void GridYukle()
        {
            DataTable dt = IndirimFirsatiHelper.IndirimFirsatiTabloGetir(FormHelper.CoreHelper.isEnglish(), ConvertionHelper.ConvertValue<int>(rdBtnListActive.SelectedValue));
            gridIndirimFirsati.DataSource = dt;
            gridIndirimFirsati.DataBind();
        }
        private void KaydetPanelGetir()
        {
            if (IndirimFirsatiId == 0)
            {
                drpKategori.SelectedIndex = 0;
                txtBaslik.Text = txtBaslangicTarihi.Text = txtBitisTarihi.Text = txtKurum.Text = txtAvantajKosullari.Text = txtIndirimOrani.Text = txtTargetLink.Text = txtTargetLinkHrApp.Text = txtLokasyon.Text = txtAdres.Text = txtPopupSayfaBaslik.Text = txtTelefon.Text = ((TextBox)DigiportAdminHtmlContent.FindControl("txtHtmlContent")).Text = ((TextBox)DigiportAdminHtmlContent.FindControl("summernote")).Text = ((TextBox)DigiportAdminHrAppHtmlContent.FindControl("txtHtmlContentHrApp")).Text = ((TextBox)DigiportAdminHrAppHtmlContent.FindControl("summernoteHrApp")).Text = string.Empty;

                chkActive.Checked = true;
                drpClickAction.SelectedIndex = 0;
                drpClickActionHrApp.SelectedIndex = 0;
                txtSiraNo.Text = IndirimFirsatiHelper.GetNextOrderNo();
                txtPopupWidth.Text = ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[0];
                txtPopupHeight.Text = ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[1];
                KaydetGorunumAyarla(false, false, false);
                txtSiraNo.Enabled = false;
                imgLogo.ImageUrl = string.Empty;
                chkHrAppEnabled.Checked = true;
                KaydetGorunumAyarlaHrApp(false, false);
            }
            else
            {
                DIGIPORT_ADMIN_INDIRIM_FIRST entity = PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityGetir("DT_WORKFLOW", IndirimFirsatiId);
                txtBaslik.Text = entity.BASLIK;
                txtPopupSayfaBaslik.Text = entity.HTML_BASLIK;
                txtBaslangicTarihi.Text = entity.BASLANGIC_TARIHI.ToString("dd.MM.yyyy");
                txtBitisTarihi.Text = entity.BITIS_TARIHI.ToString("dd.MM.yyyy");
                txtKurum.Text = entity.KURUM_ADI;
                txtAvantajKosullari.Text = entity.AVANTAJ_KOSULLARI;
                txtIndirimOrani.Text = entity.INDIRIM_ORANI.HasValue && entity.INDIRIM_ORANI.Value != 0 ? entity.INDIRIM_ORANI.Value.ToString() : "";
                txtTargetLink.Text = entity.TARGET_LINK;
                txtPopupWidth.Text = entity.POPUP_WIDTH != null ? entity.POPUP_WIDTH.ToString() : ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[0];
                txtPopupHeight.Text = entity.POPUP_HEIGHT != null ? entity.POPUP_HEIGHT.ToString() : ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[1];
                txtLokasyon.Text = entity.LOKASYON;
                txtAdres.Text = entity.ADRES;
                txtSiraNo.Text = entity.ORDER_NO.ToString();
                txtSiraNo.Enabled = entity.ACTIVE == "1";
                txtTelefon.Text = entity.TELEFON;
                ((TextBox)DigiportAdminHtmlContent.FindControl("txtHtmlContent")).Text = ((TextBox)DigiportAdminHtmlContent.FindControl("summernote")).Text = entity.HTML_ICERIK;
                chkActive.Checked = entity.ACTIVE == "1";
                drpKategori.Items.Cast<ListItem>().ToList().ForEach(x => x.Selected = false);
                drpKategori.Items.FindByValue(entity.KATEGORI.ToString()).Selected = true;
                drpClickAction.Items.Cast<ListItem>().ToList().ForEach(x => x.Selected = false);
                drpClickAction.Items.FindByValue(entity.CLICK_ACTION.ToString()).Selected = true;
                chkHrAppEnabled.Checked = entity.HRAPP_ENABLED == "1";
                imgLogo.ImageUrl = entity.HRAPP_LOGO_IMAGE_PATH;
                drpClickActionHrApp.Items.Cast<ListItem>().ToList().ForEach(x => x.Selected = false);
                if (entity.HRAPP_CLICK_ACTION != null)
                    drpClickActionHrApp.Items.FindByValue(entity.HRAPP_CLICK_ACTION.ToString()).Selected = true;
                else
                    drpClickActionHrApp.SelectedIndex = 0;
                txtTargetLinkHrApp.Text = entity.HRAPP_TARGET_LINK;
                txtPopupSayfaBaslikHrApp.Text = entity.HRAPP_TARGET_HEADLINE;
                ((TextBox)DigiportAdminHrAppHtmlContent.FindControl("txtHtmlContentHrApp")).Text = ((TextBox)DigiportAdminHrAppHtmlContent.FindControl("summernoteHrApp")).Text = entity.HRAPP_TARGET_CONTENT;

                KaydetGorunumAyarla(
                     entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                     ||
                     entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç)
                     ,
                     entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                     ||
                     entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                     ||
                     entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                     ||
                     entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                     ,
                     entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç)
                     ||
                     entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                     ||
                     entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç)
                     ||
                     entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                     ||
                     entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç)
                     ||
                     entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                     );

                KaydetGorunumAyarlaHrApp(
                    entity.HRAPP_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_İçi_Yönlendirme)
                    ||
                    entity.HRAPP_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_Dışı_Yönlendirme)
                    ,
                    entity.HRAPP_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Popup_Aç)
                    );
            }
        }

        protected void btnKaydet_Click(object sender, EventArgs e)
        {
            string savePath = string.Empty;
            try
            {
                string allowedExtensionsLogoHrApp = System.Configuration.ConfigurationManager.AppSettings["DigiportAdminIndirimFirsatiLogoIzinliTiplerHrApp"];
                if (txtBaslik.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.BaslikGirin);
                if (txtBaslangicTarihi.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.BaslangicTarihiSec);
                if (txtBitisTarihi.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihiSec);
                DateTime dateStart = DateTime.Today, dateEnd = DateTime.Today;
                if (!DateTime.TryParse(txtBaslangicTarihi.Text.Trim(), out dateStart))
                    throw new Exception(Resources.DigiportAdminResource.BaslangicTarihFormati);
                if (!DateTime.TryParse(txtBitisTarihi.Text.Trim(), out dateEnd))
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihFormati);
                if (drpKategori.SelectedIndex == 0)
                    throw new Exception(Resources.DigiportAdminResource.KategoriSecin);
                if (txtKurum.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.KurumSeciniz);
                int siraNo = 1;
                if (!Int32.TryParse(txtSiraNo.Text.Trim(), out siraNo))
                    throw new Exception(Resources.DigiportAdminResource.GecersizSiraNo);
                if (drpClickAction.SelectedValue == "-1")
                    throw new Exception(Resources.DigiportAdminResource.TiklanmaIslemiSecin);
                if ((drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç).ToString())
                    && txtTargetLink.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.HedefLinkGirin);
                int popupWidth = 0, popupHeight = 0;
                if (drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç).ToString()
                    )
                {
                    if (!Int32.TryParse(txtPopupWidth.Text.Trim(), out popupWidth))
                        throw new Exception(Resources.DigiportAdminResource.GecersizPopupGenislik);
                    if (!Int32.TryParse(txtPopupHeight.Text.Trim(), out popupHeight))
                        throw new Exception(Resources.DigiportAdminResource.GecersizPopupYukseklik);
                }
                if (fuLogo.HasFile && chkHrAppEnabled.Checked && !allowedExtensionsLogoHrApp.Split(',').Contains(Path.GetExtension(fuLogo.FileName).ToLower()))
                    throw new Exception(Resources.DigiportAdminResource.LogoHrAppDosyaFormatiYanlis + allowedExtensionsLogoHrApp);

                if (fuLogo.HasFile && chkHrAppEnabled.Checked)
                {
                    int izinliGenislik = int.Parse(ConfigurationManager.AppSettings["DigiportAdminIndirimFirsatiLogoIzinliBoyutHrApp"].Split('x')[0]);
                    int izinliYukseklik = int.Parse(ConfigurationManager.AppSettings["DigiportAdminIndirimFirsatiLogoIzinliBoyutHrApp"].Split('x')[1]);

                    using (System.Drawing.Image img = System.Drawing.Image.FromStream(fuLogo.PostedFile.InputStream))
                    {
                        int yuklenenGenislik = img.Width;
                        int yuklenenYukseklik = img.Height;
                        if (yuklenenGenislik != izinliGenislik || yuklenenYukseklik != izinliYukseklik)
                            throw new Exception(string.Format(Resources.DigiportAdminResource.HrAppLogoBoyutlari, yuklenenGenislik, yuklenenYukseklik, izinliGenislik, izinliYukseklik));
                    }
                }

                decimal indirimOrani = 0;
                if (txtIndirimOrani.Text != string.Empty && !Decimal.TryParse(txtIndirimOrani.Text.Trim(), out indirimOrani))
                    throw new Exception(Resources.DigiportAdminResource.SayiAraligi3);


                DIGIPORT_ADMIN_INDIRIM_FIRST entity;
                if (IndirimFirsatiId == 0)
                    entity = new DIGIPORT_ADMIN_INDIRIM_FIRST();
                else
                    entity = PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityGetir("DT_WORKFLOW", IndirimFirsatiId);
                bool originalStateIsActive = entity.GetSet_ID != 0 && entity.ACTIVE == "1";
                string directoryPathHrApp = "/Content/DigiportAdmin/HrApp/" + componentName + "/" + DateTime.Today.Year.ToString() + "/" + DateTime.Today.Month.ToString() + "/";
                if (!Directory.Exists(Server.MapPath(directoryPathHrApp)))
                    Directory.CreateDirectory(Server.MapPath(directoryPathHrApp));

                if (fuLogo.HasFile && chkHrAppEnabled.Checked)
                {
                    if (entity.GetSet_ID > 0 && entity.HRAPP_LOGO_IMAGE_PATH != string.Empty)
                    {
                        string oldFilePath = Server.MapPath(entity.HRAPP_LOGO_IMAGE_PATH);
                        if (File.Exists(oldFilePath))
                            File.Delete(oldFilePath);
                    }
                    string imageName = Guid.NewGuid().ToString() + Path.GetExtension(fuLogo.FileName);
                    string filePath = directoryPathHrApp + imageName;
                    savePath = Server.MapPath(filePath);
                    fuLogo.SaveAs(savePath);
                    entity.HRAPP_LOGO_IMAGE_PATH = filePath;
                }

                entity.MENU_NAME_ID = IndirimFirsatiId == 0 ? MID : entity.MENU_NAME_ID;
                entity.BASLIK = txtBaslik.Text.Trim();
                entity.KATEGORI = ConvertionHelper.ConvertValue<decimal>(drpKategori.SelectedValue);
                entity.KURUM_ADI = txtKurum.Text.Trim();
                entity.AVANTAJ_KOSULLARI = txtAvantajKosullari.Text.Trim() != string.Empty ? txtAvantajKosullari.Text.Trim() : null;
                entity.INDIRIM_ORANI = indirimOrani != 0 ? indirimOrani : (decimal?)null;
                entity.BASLANGIC_TARIHI = dateStart;
                entity.BITIS_TARIHI = dateEnd;
                entity.LOKASYON = txtLokasyon.Text.Trim() != string.Empty ? txtLokasyon.Text.Trim() : null;
                entity.ADRES = txtAdres.Text.Trim() != string.Empty ? txtAdres.Text.Trim() : null;
                entity.TELEFON = txtTelefon.Text.Trim() != string.Empty ? txtTelefon.Text.Trim() : null;

                entity.CLICK_ACTION = ConvertionHelper.ConvertValue<decimal>(drpClickAction.SelectedValue);
                entity.TARGET_LINK = entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç) ? txtTargetLink.Text.Trim() : null;
                entity.HTML_ICERIK = entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? ((TextBox)DigiportAdminHtmlContent.FindControl("txtHtmlContent")).Text.Trim().Replace("SummerNoteTempUploads", "SummerNoteUploads") : null;
                entity.HTML_BASLIK = entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? txtPopupSayfaBaslik.Text.Trim() : null;
                entity.POPUP_WIDTH = entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? popupWidth : (decimal?)null;
                entity.POPUP_HEIGHT = entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? popupHeight : (decimal?)null;
                entity.ACTIVE = chkActive.Checked ? "1" : "0";
                entity.HRAPP_ENABLED = chkHrAppEnabled.Checked ? "1" : "0";
                if (chkHrAppEnabled.Checked)
                {
                    entity.HRAPP_CLICK_ACTION = ConvertionHelper.ConvertValue<decimal>(drpClickActionHrApp.SelectedValue);
                    entity.HRAPP_TARGET_CONTENT = entity.HRAPP_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Popup_Aç)
                    ? ((TextBox)DigiportAdminHrAppHtmlContent.FindControl("txtHtmlContentHrApp")).Text.Trim().Replace("SummerNoteTempUploads", "SummerNoteUploads") : null;
                    entity.HRAPP_TARGET_HEADLINE = entity.HRAPP_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Popup_Aç)
                    ? txtPopupSayfaBaslikHrApp.Text.Trim() : null;
                    entity.HRAPP_TARGET_LINK = entity.HRAPP_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_Dışı_Yönlendirme) || entity.HRAPP_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_İçi_Yönlendirme) ? txtTargetLinkHrApp.Text.Trim() : null;
                    if (entity.HRAPP_CLICK_ACTION == -1)
                        throw new Exception(Resources.DigiportAdminResource.HrAppTiklanmaIslemiSecin);
                    if ((entity.HRAPP_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_İçi_Yönlendirme) || entity.HRAPP_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_Dışı_Yönlendirme)) && String.IsNullOrEmpty(entity.HRAPP_TARGET_LINK))
                        throw new Exception(Resources.DigiportAdminResource.HrAppTiklanmaIslemiSecin);
                }

                if (entity.GetSet_ID == 0)
                {
                    entity.ORDER_NO = siraNo;
                    entity.CREATED_BY = LoginId;
                    entity.CREATED = DateTime.Now;
                    entity.DELETED = "0";
                    PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityKaydet("DT_WORKFLOW", entity);
                }
                else
                {
                    entity.LAST_UPDATED_BY = LoginId;
                    entity.LAST_UPDATED = DateTime.Now;
                    PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityUpdateEt("DT_WORKFLOW", entity);
                    if ((siraNo != entity.ORDER_NO || !originalStateIsActive) && entity.ACTIVE == "1")
                        SiraNoTekrarDuzenle(entity.ID, entity.ORDER_NO, !originalStateIsActive);
                }
                helper.MoveUploadedContentToRealFolder("/Content/SummerNoteUploads/Digiport/" + componentName + "/" + DateTime.Today.Year.ToString() + "/" + DateTime.Today.Month.ToString());
                helper.DeleteRealFolderContents();
                if (chkHrAppEnabled.Checked)
                {
                    helperHrApp.MoveUploadedContentToRealFolder("/Content/SummerNoteUploads/HrApp/" + componentName + "/" + DateTime.Today.Year.ToString() + "/" + DateTime.Today.Month.ToString());
                    helperHrApp.DeleteRealFolderContents();
                }
                PanelleriDuzenle(true, false, false);
                this.Master.PopupGoster(Resources.DigiportAdminResource.Basari, Resources.DigiportAdminResource.KayitBasarili, false);
            }
            catch (Exception ex)
            {
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }

        public bool ClickLinkVisible(string clickActionId)
        {
            return clickActionId != ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Olay_Yok).ToString();
        }

        protected void drpClickAction_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (drpClickAction.SelectedItem.Value == "-1" || drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Olay_Yok).ToString())
                KaydetGorunumAyarla(false, false, false);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(true, false, false);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(true, true, false);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(false, false, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(false, true, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(false, false, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(false, true, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(false, false, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(false, true, true);
        }
        private void KaydetGorunumAyarla(bool linkBilgileriGoster, bool popupBilgileriGoster, bool popupContentGoster)
        {
            divHedefLink.Visible = linkBilgileriGoster;
            divPopupSize.Visible = popupBilgileriGoster;
            divPopupHtmlContent.Style["display"] = popupContentGoster ? "block" : "none";
        }

        protected void btnYeniKayit_Click(object sender, EventArgs e)
        {
            PanelleriDuzenle(false, true, false);
        }
        protected void btnKayitIptal_Click(object sender, EventArgs e)
        {
            PanelleriDuzenle(true, false, false);
        }
        protected void btnSil_Click(object sender, EventArgs e)
        {
            try
            {
                if (IndirimFirsatiId > 0)
                {
                    DIGIPORT_ADMIN_INDIRIM_FIRST entity = PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityGetir("DT_WORKFLOW", IndirimFirsatiId);
                    if (entity != null)
                    {
                        List<string> pathsToDelete = new List<string>();
                        if (!string.IsNullOrEmpty(entity.HRAPP_LOGO_IMAGE_PATH))
                            pathsToDelete.Add(Server.MapPath(entity.HRAPP_LOGO_IMAGE_PATH));
                        if (!string.IsNullOrEmpty(entity.HTML_ICERIK))
                            pathsToDelete.AddRange(helper.ExtractSummerNoteUploadPaths(entity.HTML_ICERIK));
                        if (!string.IsNullOrEmpty(entity.HRAPP_TARGET_CONTENT))
                            pathsToDelete.AddRange(helperHrApp.ExtractSummerNoteUploadPaths(entity.HRAPP_TARGET_CONTENT));

                        foreach (string path in pathsToDelete)
                        {
                            if (File.Exists(path))
                                File.Delete(path);
                        }
                        entity.DELETED = "1";
                        entity.LAST_UPDATED_BY = LoginId;
                        entity.LAST_UPDATED = DateTime.Now;
                        PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityUpdateEt("DT_WORKFLOW", entity);
                        PanelleriDuzenle(true, false, false);
                        this.Master.PopupGoster(Resources.DigiportAdminResource.Basari, Resources.DigiportAdminResource.SilmeBasarili, false);
                    }
                }
            }
            catch (Exception ex)
            {
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }

        protected void txtSiraNoGrid_TextChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_INDIRIM_FIRST entity = null;
            try
            {
                int index = (((TextBox)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _IndirimFirsatiId = ConvertionHelper.ConvertValue<int>(gridIndirimFirsati.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityGetir("DT_WORKFLOW", _IndirimFirsatiId);
                decimal newOrderNo = ConvertionHelper.ConvertValue<decimal>(((TextBox)sender).Text);
                if (entity.ACTIVE == "1")
                {
                    SiraNoTekrarDuzenle(entity.ID, newOrderNo, false);
                    GridYukle();
                }
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((TextBox)sender).Text = entity.ORDER_NO.ToString();
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
        private void SiraNoTekrarDuzenle(int changedItemId, decimal newOrderNo, bool changedAsActive)
        {
            try
            {
                List<ItemOrderInfo> indirimFirsatlari = IndirimFirsatiHelper.IndirimFirsatiTabloGetir(false, 1).Rows.Cast<DataRow>().Select(row => new ItemOrderInfo()
                {
                    ItemId = ConvertionHelper.ConvertValue<int>(row["ID"].ToString()),
                    ItemOrder_Old = ConvertionHelper.ConvertValue<int>(row["ORDER_NO"].ToString()),
                    ItemOrder_New = 0,
                    Changed = false
                }).OrderBy(s => s.ItemOrder_Old).ToList();

                SortHelper.SiraNoTekrarAyarla(indirimFirsatlari, changedItemId, newOrderNo, changedAsActive);
                if (indirimFirsatlari.Exists(x => x.Changed))
                {
                    foreach (ItemOrderInfo itemChanged in indirimFirsatlari.Where(x => x.Changed).ToList())
                    {
                        DIGIPORT_ADMIN_INDIRIM_FIRST firsat = PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityGetir("DT_WORKFLOW", Convert.ToInt32(itemChanged.ItemId));
                        firsat.ORDER_NO = itemChanged.ItemOrder_New;
                        firsat.LAST_UPDATED = DateTime.Now;
                        firsat.LAST_UPDATED_BY = LoginId;
                        PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityUpdateEt("DT_WORKFLOW", firsat);
                    }
                }
            }
            catch
            {
            }
        }
        protected void dateEditStartGrid_ValueChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_INDIRIM_FIRST entity = null;
            try
            {
                int index = (((ASPxDateEdit)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _indirimFirsatiId = ConvertionHelper.ConvertValue<int>(gridIndirimFirsati.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityGetir("DT_WORKFLOW", _indirimFirsatiId);
                DateTime dateNew = ((ASPxDateEdit)sender).Date;
                if (dateNew >= entity.BITIS_TARIHI)
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihiBaslagictanIleriOlsun);
                entity.BASLANGIC_TARIHI = dateNew;
                entity.LAST_UPDATED_BY = LoginId;
                entity.LAST_UPDATED = DateTime.Now;
                PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityUpdateEt("DT_WORKFLOW", entity);
                GridYukle();
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((ASPxDateEdit)sender).Date = entity.BASLANGIC_TARIHI;
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
        protected void dateEditEndGrid_ValueChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_INDIRIM_FIRST entity = null;
            try
            {
                int index = (((ASPxDateEdit)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _indirimFirsatiId = ConvertionHelper.ConvertValue<int>(gridIndirimFirsati.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityGetir("DT_WORKFLOW", _indirimFirsatiId);
                DateTime dateNew = ((ASPxDateEdit)sender).Date;
                if (dateNew <= entity.BASLANGIC_TARIHI)
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihiBaslagictanIleriOlsun);
                entity.BITIS_TARIHI = dateNew;
                entity.LAST_UPDATED_BY = LoginId;
                entity.LAST_UPDATED = DateTime.Now;
                PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityUpdateEt("DT_WORKFLOW", entity);
                GridYukle();
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((ASPxDateEdit)sender).Date = entity.BITIS_TARIHI;
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
        protected void chkActiveGrid_CheckedChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_INDIRIM_FIRST entity = null;
            try
            {
                int index = (((CheckBox)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _indirimFirsatiId = ConvertionHelper.ConvertValue<int>(gridIndirimFirsati.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityGetir("DT_WORKFLOW", _indirimFirsatiId);
                entity.ACTIVE = ((CheckBox)sender).Checked ? "1" : "0";
                entity.LAST_UPDATED_BY = LoginId;
                entity.LAST_UPDATED = DateTime.Now;
                PRepository<DIGIPORT_ADMIN_INDIRIM_FIRST>.EntityUpdateEt("DT_WORKFLOW", entity);
                if (entity.ACTIVE == "1")
                    SiraNoTekrarDuzenle(entity.ID, entity.ORDER_NO, entity.ACTIVE == "1");
                GridYukle();
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((CheckBox)sender).Checked = entity.ACTIVE == "1";
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(gridIndirimFirsati.GetRowValues(index, "ID"));
            hdnIndirimFirsatiId.Value = deger.ToString();
            PanelleriDuzenle(false, false, true);
        }
        protected void drpClickActionHrApp_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (drpClickActionHrApp.SelectedItem.Value == "-1" || drpClickActionHrApp.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Olay_Yok).ToString())
                KaydetGorunumAyarlaHrApp(false, false);
            else if (drpClickActionHrApp.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_İçi_Yönlendirme).ToString() || drpClickActionHrApp.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_Dışı_Yönlendirme).ToString())
                KaydetGorunumAyarlaHrApp(true, false);
            else if (drpClickActionHrApp.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Popup_Aç).ToString())
                KaydetGorunumAyarlaHrApp(false, true);
        }
        private void KaydetGorunumAyarlaHrApp(bool linkBilgileriGoster, bool popupContentGoster)
        {
            divHedefLinkHrApp.Visible = linkBilgileriGoster;
            divPopupHtmlContentHrApp.Style["display"] = popupContentGoster ? "block" : "none";
            HrAppPanelGorunumAyarla();
        }

        protected void chkHrAppEnabled_CheckedChanged(object sender, EventArgs e)
        {
            HrAppPanelGorunumAyarla();
            UpdatePanel3.Update();
        }

        private void HrAppPanelGorunumAyarla()
        {
            divWrapperHrApp1.Visible = PanelIKAPP.Visible = chkHrAppEnabled.Checked;
        }
    }
}