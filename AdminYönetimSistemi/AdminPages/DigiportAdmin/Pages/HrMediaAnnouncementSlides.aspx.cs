﻿using CoreHelpers;
using DevExpress.Web;
using Entities.DigiportAdmin;
using FormHelper.DigiportAdmin;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.DigiportAdmin.Pages
{
    public partial class HrMediaAnnouncementSlides : DigiportSecurePage
    {
        private string componentName = "";
        int MID = 0;
        private int SlideId
        {
            get
            {
                string slideId = hdnSlideId.Value;
                if (slideId != string.Empty)
                    return Convert.ToInt32(slideId);
                else
                    return 0;
            }
        }
        DigiportHtmlEditorMediaHelper helper;
        DigiportMediaHrAppHelper helperHrApp;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!String.IsNullOrEmpty(Request.QueryString["MID"]) && CoreHelpers.GenericIslemler.IsNumeric(Request.QueryString["MID"]))
            {
                if (
                    ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"] != MenuId.Value.ToString()
                    &&
                    ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"] != MenuId.Value.ToString()
                    &&
                    ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"] != MenuId.Value.ToString()
                    )
                {
                    Session["ErrorPageMessage"] = Resources.DigiportAdminResource.TipYanlis;
                    Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
                    return;
                }
                MID = MenuId.Value;
            }
            else
            {
                Session["ErrorPageMessage"] = Resources.DigiportAdminResource.TipBelirsiz;
                Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
                return;
            }
            componentName = GetComponentName(MID);
            DigiportAdminHtmlContent.ComponentName = componentName;
            DigiportAdminHrAppHtmlContent.ComponentName = componentName;
            helper = new DigiportHtmlEditorMediaHelper(DigiportAdminHtmlContent.ComponentName);
            helperHrApp = new DigiportMediaHrAppHelper(DigiportAdminHrAppHtmlContent.ComponentName);
            this.Title = FormHelper.DigiportAdmin.NameHelper.GetComponentNameOrDescription(MID, false, FormHelper.CoreHelper.isEnglish());
            if (!IsPostBack && !IsCallback)
            {
                linkKategori.NavigateUrl = "~/AdminPages/DigiportAdmin/HrMediaKategori.aspx?MID=" + MID;
                rvBaslangicTarihi.MinimumValue = DateTime.Today.ToString("dd.MM.yyyy");
                rvBaslangicTarihi.MaximumValue = DateTime.MaxValue.ToString("dd.MM.yyyy");
                rvBitisTarihi.MinimumValue = DateTime.Today.AddDays(1).ToString("dd.MM.yyyy");
                rvBitisTarihi.MaximumValue = DateTime.MaxValue.ToString("dd.MM.yyyy");
                if (MenuId.Value.ToString() == ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"])
                {
                    lblSlideImageSize.Text = string.Format("[{0}px - {1}px]", ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideIzinliBoyut"].Split('x')[0], ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideIzinliBoyut"].Split('x')[1]);
                    lblThumbnailSize.Text = string.Format("[{0}px - {1}px]", ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideThumbnailIzinliBoyut"].Split('x')[0], ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideThumbnailIzinliBoyut"].Split('x')[1]);
                    lblAppSlideImageSize.Text = string.Format("[{0}px - {1}px]", ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideIzinliBoyutHrApp"].Split('x')[0], ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideIzinliBoyutHrApp"].Split('x')[1]);
                }
                else if (MenuId.Value.ToString() == ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"])
                {
                    lblSlideImageSize.Text = string.Format("[{0}px - {1}px]", ConfigurationManager.AppSettings["DigiportAdminAjansSlideIzinliBoyut"].Split('x')[0], ConfigurationManager.AppSettings["DigiportAdminAjansSlideIzinliBoyut"].Split('x')[1]);
                    lblThumbnailSize.Text = string.Format("[{0}px - {1}px]", ConfigurationManager.AppSettings["DigiportAdminAjansSlideThumbnailIzinliBoyut"].Split('x')[0], ConfigurationManager.AppSettings["DigiportAdminAjansSlideThumbnailIzinliBoyut"].Split('x')[1]);
                    lblAppSlideImageSize.Text = string.Format("[{0}px - {1}px]", ConfigurationManager.AppSettings["DigiportAdminAjansSlideIzinliBoyutHrApp"].Split('x')[0], ConfigurationManager.AppSettings["DigiportAdminAjansSlideIzinliBoyutHrApp"].Split('x')[1]);
                }
                else if (MenuId.Value.ToString() == ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"])
                {
                    lblSlideImageSize.Text = string.Format("[{0}px - {1}px]", ConfigurationManager.AppSettings["DigiportAdminEducationSlideIzinliBoyut"].Split('x')[0], ConfigurationManager.AppSettings["DigiportAdminEducationSlideIzinliBoyut"].Split('x')[1]);
                    lblThumbnailSize.Text = string.Format("[{0}px - {1}px]", ConfigurationManager.AppSettings["DigiportAdminEducationSlideThumbnailIzinliBoyut"].Split('x')[0], ConfigurationManager.AppSettings["DigiportAdminEducationSlideThumbnailIzinliBoyut"].Split('x')[1]);
                    lblAppSlideImageSize.Text = string.Format("[{0}px - {1}px]", ConfigurationManager.AppSettings["DigiportAdminEducationSlideIzinliBoyutHrApp"].Split('x')[0], ConfigurationManager.AppSettings["DigiportAdminEducationSlideIzinliBoyutHrApp"].Split('x')[1]);
                }
                linkSliderSecenekleri.NavigateUrl = "~/AdminPages/DigiportAdmin/SliderOptions.aspx?MID=" + MID;
                pnlAll.GroupingText = this.Title;
                string allowedExtensionsSlide = System.Configuration.ConfigurationManager.AppSettings[GetSlideIzinliTipler(MID)];
                string allowedExtensionsThumbnail = System.Configuration.ConfigurationManager.AppSettings[GetThumbnailIzinliTipler(MID)];
                string allowedExtensionsSlideHrApp = System.Configuration.ConfigurationManager.AppSettings[GetSlideIzinliTiplerHrApp(MID)];
                fuSlide.Attributes["accept"] = allowedExtensionsSlide;
                fuThumbnail.Attributes["accept"] = allowedExtensionsThumbnail;
                fuAppSlide.Attributes["accept"] = allowedExtensionsSlideHrApp;

                foreach (var action in Enum.GetValues(FormHelper.CoreHelper.isEnglish() ? typeof(DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions_En) : typeof(DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions)))
                {
                    int value = (int)action;
                    string name = action.ToString().Replace("_", " ");
                    if (
                            (
                                MenuId.Value.ToString() == ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"]
                                ||
                                MenuId.Value.ToString() == ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"]
                            )
                            &&
                            (
                            value == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç
                            ||
                            value == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç
                            )
                      )
                        continue;

                    if (
                            (
                                MenuId.Value.ToString() == ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"]
                            )
                            &&
                            (
                            value == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç
                            ||
                            value == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç
                            )
                      )
                        continue;

                    drpClickAction.Items.Add(new ListItem(name, value.ToString()));
                }
                drpClickAction.Items.Insert(0, new ListItem(FormHelper.CoreHelper.isEnglish() ? "Please Select" : "Seçiniz", "-1") { Selected = true });
                foreach (var action in Enum.GetValues(FormHelper.CoreHelper.isEnglish() ? typeof(DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions_En) : typeof(DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions)))
                {
                    int value = (int)action;
                    string name = action.ToString().Replace("_", " ");
                    drpClickActionHrApp.Items.Add(new ListItem(name, value.ToString()));
                }
                drpClickActionHrApp.Items.Insert(0, new ListItem(FormHelper.CoreHelper.isEnglish() ? "Please Select" : "Seçiniz", "-1") { Selected = true });
                rdBtnListActive.Items.Add(new ListItem(Resources.DigiportAdminResource.Tumu, "2") { Selected = false });
                rdBtnListActive.Items.Add(new ListItem(Resources.DigiportAdminResource.Aktif, "1") { Selected = true });
                rdBtnListActive.Items.Add(new ListItem(Resources.DigiportAdminResource.Pasif, "0") { Selected = false });
                drpCategory.DataSource = SliderKategoriHelper.KategoriTabloGetir(SliderKategoriHelper.KategoriTipGetir(MID), 1, FormHelper.CoreHelper.isEnglish());
                drpCategory.DataValueField = "ID";
                drpCategory.DataTextField = FormHelper.CoreHelper.isEnglish() ? "KATEGORI_ADI_EN" : "KATEGORI_ADI";
                drpCategory.DataBind();
                drpCategory.Items.Insert(0, new ListItem(FormHelper.CoreHelper.isEnglish() ? "Please Select" : "Seçiniz", "-1") { Selected = true });
                helper.ClearMediaRemoveSession();
                helper.ClearMediaUploadSession();
                helperHrApp.ClearMediaRemoveSession();
                helperHrApp.ClearMediaUploadSession();
                PanelleriDuzenle(true, false, false);
            }
        }



        private string GetSlideIzinliTiplerHrApp(int mID)
        {
            if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"])
                return "DigiportAdminHrMediaSlideIzinliTiplerHrApp";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"])
                return "DigiportAdminAjansSlideIzinliTiplerHrApp";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"])
                return "DigiportAdminEducationSlideIzinliTiplerHrApp";
            return "";
        }

        private string GetThumbnailIzinliTipler(int mID)
        {
            if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"])
                return "DigiportAdminHrMediaSlideThumbnailIzinliTipler";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"])
                return "DigiportAdminAjansSlideThumbnailIzinliTipler";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"])
                return "DigiportAdminEducationSlideThumbnailIzinliTipler";
            return "";
        }

        private string GetSlideIzinliTipler(int mID)
        {
            if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"])
                return "DigiportAdminHrMediaSlideIzinliTipler";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"])
                return "DigiportAdminAjansSlideIzinliTipler";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"])
                return "DigiportAdminEducationSlideIzinliTipler";
            return "";
        }

        private string GetComponentName(int mID)
        {
            if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"])
                return "HrMediaSlide";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"])
                return "AjansSlide";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"])
                return "EducationSlide";
            return "";
        }
        private string GetSlideIzinliBoyut(int mID)
        {
            if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"])
                return "DigiportAdminHrMediaSlideIzinliBoyut";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"])
                return "DigiportAdminAjansSlideIzinliBoyut";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"])
                return "DigiportAdminEducationSlideIzinliBoyut";
            return "";
        }
        private string GetThumbnailIzinliBoyut(int mID)
        {
            if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"])
                return "DigiportAdminHrMediaSlideThumbnailIzinliBoyut";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"])
                return "DigiportAdminAjansSlideThumbnailIzinliBoyut";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"])
                return "DigiportAdminEducationSlideThumbnailIzinliBoyut";
            return "";
        }
        private string GetSlideIzinliBoyutHrApp(int mID)
        {
            if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"])
                return "DigiportAdminHrMediaSlideIzinliBoyutHrApp";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"])
                return "DigiportAdminAjansSlideIzinliBoyutHrApp";
            else if (mID.ToString() == ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"])
                return "DigiportAdminEducationSlideIzinliBoyutHrApp";
            return "";
        }

        protected void ASPxGridView_AfterPerformCallback(object sender, ASPxGridViewAfterPerformCallbackEventArgs e)
        {
            GridYukle();
        }

        protected void btnKaydet_Click(object sender, EventArgs e)
        {
            string savePath = string.Empty;
            try
            {
                string allowedExtensionsSlide = System.Configuration.ConfigurationManager.AppSettings[GetSlideIzinliTipler(MID)];
                string allowedExtensionsThumbnail = System.Configuration.ConfigurationManager.AppSettings[GetThumbnailIzinliTipler(MID)];
                string allowedExtensionsSlideHrApp = System.Configuration.ConfigurationManager.AppSettings[GetSlideIzinliTiplerHrApp(MID)];
                if (fuSlide.HasFile && !allowedExtensionsSlide.Split(',').Contains(Path.GetExtension(fuSlide.FileName).ToLower()))
                    throw new Exception(Resources.DigiportAdminResource.SlideDosyaFormatiYanlis + allowedExtensionsSlide);
                if (fuThumbnail.HasFile && !allowedExtensionsThumbnail.Split(',').Contains(Path.GetExtension(fuThumbnail.FileName).ToLower()))
                    throw new Exception(Resources.DigiportAdminResource.ThumbnailDosyaFormatiYanlis + allowedExtensionsThumbnail);
                if (fuAppSlide.HasFile && chkHrAppEnabled.Checked && !allowedExtensionsSlideHrApp.Split(',').Contains(Path.GetExtension(fuAppSlide.FileName).ToLower()))
                    throw new Exception(Resources.DigiportAdminResource.SlideHrAppDosyaFormatiYanlis + allowedExtensionsSlideHrApp);
                if (SlideId == 0 && !fuSlide.HasFile)
                    throw new Exception(Resources.DigiportAdminResource.YeniDuyuruResmiSec);
                if (SlideId == 0 && chkHrAppEnabled.Checked && !fuAppSlide.HasFile)
                    throw new Exception(Resources.DigiportAdminResource.YuklenilecekDuyuruAppResmiSecin);

                if (fuSlide.HasFile)
                {
                    int izinliGenislik = int.Parse(ConfigurationManager.AppSettings[GetSlideIzinliBoyut(MID)].Split('x')[0]);
                    int izinliYukseklik = int.Parse(ConfigurationManager.AppSettings[GetSlideIzinliBoyut(MID)].Split('x')[1]);

                    using (System.Drawing.Image img = System.Drawing.Image.FromStream(fuSlide.PostedFile.InputStream))
                    {
                        int yuklenenGenislik = img.Width;
                        int yuklenenYukseklik = img.Height;
                        if (yuklenenGenislik != izinliGenislik || yuklenenYukseklik != izinliYukseklik)
                            throw new Exception(string.Format(Resources.DigiportAdminResource.SlideBoyutlari, yuklenenGenislik, yuklenenYukseklik, izinliGenislik, izinliYukseklik));
                    }
                }
                if (fuThumbnail.HasFile)
                {
                    int izinliGenislik = int.Parse(ConfigurationManager.AppSettings[GetThumbnailIzinliBoyut(MID)].Split('x')[0]);
                    int izinliYukseklik = int.Parse(ConfigurationManager.AppSettings[GetThumbnailIzinliBoyut(MID)].Split('x')[1]);

                    using (System.Drawing.Image img = System.Drawing.Image.FromStream(fuThumbnail.PostedFile.InputStream))
                    {
                        int yuklenenGenislik = img.Width;
                        int yuklenenYukseklik = img.Height;
                        if (yuklenenGenislik != izinliGenislik || yuklenenYukseklik != izinliYukseklik)
                            throw new Exception(string.Format(Resources.DigiportAdminResource.ThumbnailBoyutlari, yuklenenGenislik, yuklenenYukseklik, izinliGenislik, izinliYukseklik));
                    }
                }
                if (fuAppSlide.HasFile && chkHrAppEnabled.Checked)
                {
                    int izinliGenislik = int.Parse(ConfigurationManager.AppSettings[GetSlideIzinliBoyutHrApp(MID)].Split('x')[0]);
                    int izinliYukseklik = int.Parse(ConfigurationManager.AppSettings[GetSlideIzinliBoyutHrApp(MID)].Split('x')[1]);

                    using (System.Drawing.Image img = System.Drawing.Image.FromStream(fuAppSlide.PostedFile.InputStream))
                    {
                        int yuklenenGenislik = img.Width;
                        int yuklenenYukseklik = img.Height;
                        if (yuklenenGenislik != izinliGenislik || yuklenenYukseklik != izinliYukseklik)
                            throw new Exception(string.Format(Resources.DigiportAdminResource.HrAppSlideBoyutlari, yuklenenGenislik, yuklenenYukseklik, izinliGenislik, izinliYukseklik));
                    }
                }
                if (txtSlideName.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.DuyuruAdiGirin);
                if (txtBaslangicTarihi.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.BaslangicTarihiSec);
                if (txtBitisTarihi.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihiSec);
                DateTime dateStart = DateTime.Today, dateEnd = DateTime.Today;
                if (!DateTime.TryParse(txtBaslangicTarihi.Text.Trim(), out dateStart))
                    throw new Exception(Resources.DigiportAdminResource.BaslangicTarihFormati);
                if (!DateTime.TryParse(txtBitisTarihi.Text.Trim(), out dateEnd))
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihFormati);
                if (dateEnd <= dateStart)
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihiBaslagictanIleriOlsun);
                if (SlideId == 0 && dateStart < DateTime.Today)
                    throw new Exception(Resources.DigiportAdminResource.IleriTarihBaslangic);
                if (SlideId == 0 && dateEnd < DateTime.Today.AddDays(1))
                    throw new Exception(Resources.DigiportAdminResource.IleriTarihBitis);
                int siraNo = 1;
                if (!Int32.TryParse(txtSiraNo.Text.Trim(), out siraNo))
                    throw new Exception(Resources.DigiportAdminResource.GecersizSiraNo);
                if (drpClickAction.SelectedValue == "-1")
                    throw new Exception(Resources.DigiportAdminResource.TiklanmaIslemiSecin);
                if ((drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç).ToString()) && txtTargetLink.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.HedefLinkGirin);
                int popupWidth = 0, popupHeight = 0;
                if (drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç).ToString()
                    ||
                    drpClickAction.SelectedValue == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç).ToString())
                {
                    if (!Int32.TryParse(txtPopupWidth.Text.Trim(), out popupWidth))
                        throw new Exception(Resources.DigiportAdminResource.GecersizPopupGenislik);
                    if (!Int32.TryParse(txtPopupHeight.Text.Trim(), out popupHeight))
                        throw new Exception(Resources.DigiportAdminResource.GecersizPopupYukseklik);
                }

                DIGIPORT_ADMIN_HR_MEDIA_SLIDE entity;
                if (SlideId == 0)
                    entity = new DIGIPORT_ADMIN_HR_MEDIA_SLIDE();
                else
                    entity = PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityGetir("DT_WORKFLOW", SlideId);
                bool originalStateIsActive = entity.GetSet_ID != 0 && entity.ACTIVE == "1";
                string directoryPathDigiport = "/Content/DigiportAdmin/Digiport/" + componentName + "/" + DateTime.Today.Year.ToString() + "/" + DateTime.Today.Month.ToString() + "/";
                if (!Directory.Exists(Server.MapPath(directoryPathDigiport)))
                    Directory.CreateDirectory(Server.MapPath(directoryPathDigiport));
                string directoryPathHrApp = "/Content/DigiportAdmin/HrApp/" + componentName + "/" + DateTime.Today.Year.ToString() + "/" + DateTime.Today.Month.ToString() + "/";
                if (!Directory.Exists(Server.MapPath(directoryPathHrApp)))
                    Directory.CreateDirectory(Server.MapPath(directoryPathHrApp));

                if (fuSlide.HasFile)
                {
                    if (entity.GetSet_ID > 0 && entity.SLIDE_IMAGE_PATH != string.Empty)
                    {
                        string oldFilePath = Server.MapPath(entity.SLIDE_IMAGE_PATH);
                        if (File.Exists(oldFilePath))
                            File.Delete(oldFilePath);
                    }
                    string imageName = Guid.NewGuid().ToString() + Path.GetExtension(fuSlide.FileName);
                    string filePath = directoryPathDigiport + imageName;
                    savePath = Server.MapPath(filePath);
                    fuSlide.SaveAs(savePath);
                    entity.SLIDE_IMAGE_PATH = filePath;
                    entity.THUMBNAIL_IMAGE_PATH = null;
                }

                if (fuThumbnail.HasFile)
                {
                    if (entity.GetSet_ID > 0 && entity.THUMBNAIL_IMAGE_PATH != string.Empty)
                    {
                        string oldFilePath = Server.MapPath(entity.THUMBNAIL_IMAGE_PATH);
                        if (File.Exists(oldFilePath))
                            File.Delete(oldFilePath);
                    }
                    string imageName = Guid.NewGuid().ToString() + Path.GetExtension(fuThumbnail.FileName);
                    string filePath = directoryPathDigiport + imageName;
                    savePath = Server.MapPath(filePath);
                    fuThumbnail.SaveAs(savePath);
                    entity.THUMBNAIL_IMAGE_PATH = filePath;
                }
                else if (fuSlide.HasFile)
                {
                    if (entity.GetSet_ID > 0 && entity.THUMBNAIL_IMAGE_PATH != string.Empty)
                    {
                        string oldFilePath = Server.MapPath(entity.THUMBNAIL_IMAGE_PATH);
                        if (File.Exists(oldFilePath))
                            File.Delete(oldFilePath);
                    }
                    string imageName = Guid.NewGuid().ToString() + Path.GetExtension(fuSlide.FileName);
                    string filePath = directoryPathDigiport + imageName;
                    savePath = Server.MapPath(filePath);
                    fuSlide.SaveAs(savePath);
                    entity.THUMBNAIL_IMAGE_PATH = filePath;
                }
                if (fuAppSlide.HasFile && chkHrAppEnabled.Checked)
                {
                    if (entity.GetSet_ID > 0 && entity.HRAPP_SLIDE_IMAGE_PATH != string.Empty)
                    {
                        string oldFilePath = Server.MapPath(entity.HRAPP_SLIDE_IMAGE_PATH);
                        if (File.Exists(oldFilePath))
                            File.Delete(oldFilePath);
                    }
                    string imageName = Guid.NewGuid().ToString() + Path.GetExtension(fuAppSlide.FileName);
                    string filePath = directoryPathHrApp + imageName;
                    savePath = Server.MapPath(filePath);
                    fuAppSlide.SaveAs(savePath);
                    entity.HRAPP_SLIDE_IMAGE_PATH = filePath;
                }
                entity.MENU_NAME_ID = SlideId == 0 ? MID : entity.MENU_NAME_ID;
                entity.SLIDE_NAME = txtSlideName.Text.Trim();
                entity.SLIDE_CLICK_ACTION = ConvertionHelper.ConvertValue<decimal>(drpClickAction.SelectedValue);
                entity.SLIDE_TARGET_LINK = entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç) ? txtTargetLink.Text.Trim() : null;
                entity.SLIDE_TARGET_CONTENT = entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? ((TextBox)DigiportAdminHtmlContent.FindControl("txtHtmlContent")).Text.Trim().Replace("SummerNoteTempUploads", "SummerNoteUploads") : null;
                entity.SLIDE_TARGET_HEADLINE = entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? txtPopupSayfaBaslik.Text.Trim() : null;
                entity.SLIDE_POPUP_WIDTH = entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? popupWidth : (decimal?)null;
                entity.SLIDE_POPUP_HEIGHT = entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    entity.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ? popupHeight : (decimal?)null;
                entity.ACTIVE = hdnActive.Value.ToLower() == "true" ? "1" : "0";
                entity.VALID_DATE_START = dateStart;
                entity.VALID_DATE_END = dateEnd;
                entity.CATEGORY = drpCategory.SelectedIndex != 0 ? ConvertionHelper.ConvertValue<decimal>(drpCategory.SelectedValue) : (decimal?)null;
                entity.HRAPP_ENABLED = chkHrAppEnabled.Checked ? "1" : "0";
                if (chkHrAppEnabled.Checked)
                {
                    entity.HRAPP_SLIDE_CLICK_ACTION = ConvertionHelper.ConvertValue<decimal>(drpClickActionHrApp.SelectedValue);
                    entity.HRAPP_SLIDE_TARGET_CONTENT = entity.HRAPP_SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Popup_Aç)
                    ? ((TextBox)DigiportAdminHrAppHtmlContent.FindControl("txtHtmlContentHrApp")).Text.Trim().Replace("SummerNoteTempUploads", "SummerNoteUploads") : null;
                    entity.HRAPP_SLIDE_TARGET_HEADLINE = entity.HRAPP_SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Popup_Aç)
                    ? txtPopupSayfaBaslikHrApp.Text.Trim() : null;
                    entity.HRAPP_SLIDE_TARGET_LINK = entity.HRAPP_SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_Dışı_Yönlendirme) || entity.HRAPP_SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_İçi_Yönlendirme) ? txtTargetLinkHrApp.Text.Trim() : null;
                    if (entity.HRAPP_SLIDE_CLICK_ACTION == -1)
                        throw new Exception(Resources.DigiportAdminResource.HrAppTiklanmaIslemiSecin);
                    if ((entity.HRAPP_SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_İçi_Yönlendirme) || entity.HRAPP_SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_Dışı_Yönlendirme)) && String.IsNullOrEmpty(entity.HRAPP_SLIDE_TARGET_LINK))
                        throw new Exception(Resources.DigiportAdminResource.HrAppTiklanmaIslemiSecin);
                }
                if (entity.GetSet_ID == 0)
                {
                    entity.ORDER_NO = siraNo;
                    entity.CREATED_BY = LoginId;
                    entity.CREATED = DateTime.Now;
                    entity.DELETED = "0";
                    PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityKaydet("DT_WORKFLOW", entity);
                }
                else
                {
                    entity.LAST_UPDATED_BY = LoginId;
                    entity.LAST_UPDATED = DateTime.Now;
                    PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityUpdateEt("DT_WORKFLOW", entity);
                    if ((siraNo != entity.ORDER_NO || !originalStateIsActive) && entity.ACTIVE == "1")
                        SiraNoTekrarDuzenle(entity.ID, entity.ORDER_NO, !originalStateIsActive);
                }
                helper.MoveUploadedContentToRealFolder("/Content/SummerNoteUploads/Digiport/" + componentName + "/" + DateTime.Today.Year.ToString() + "/" + DateTime.Today.Month.ToString());
                helper.DeleteRealFolderContents();
                if (chkHrAppEnabled.Checked)
                {
                    helperHrApp.MoveUploadedContentToRealFolder("/Content/SummerNoteUploads/HrApp/" + componentName + "/" + DateTime.Today.Year.ToString() + "/" + DateTime.Today.Month.ToString());
                    helperHrApp.DeleteRealFolderContents();
                }
                PanelleriDuzenle(true, false, false);
                this.Master.PopupGoster(Resources.DigiportAdminResource.Basari, Resources.DigiportAdminResource.KayitBasarili, false);
            }
            catch (Exception ex)
            {
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
                if (savePath != string.Empty && File.Exists(savePath))
                    File.Delete(savePath);
            }
        }



        private void SiraNoTekrarDuzenle(int changedSlideId, decimal newOrderNo, bool changedAsActive)
        {
            try
            {
                List<ItemOrderInfo> slides = HrMediaSlideHelper.GetSlidesTable(MID, 1).Rows.Cast<DataRow>().Select(row => new ItemOrderInfo()
                {
                    ItemId = ConvertionHelper.ConvertValue<int>(row["ID"].ToString()),
                    ItemOrder_Old = ConvertionHelper.ConvertValue<int>(row["ORDER_NO"].ToString()),
                    ItemOrder_New = 0,
                    Changed = false
                }).OrderBy(s => s.ItemOrder_Old).ToList();

                SortHelper.SiraNoTekrarAyarla(slides, changedSlideId, newOrderNo, changedAsActive);
                if (slides.Exists(x => x.Changed))
                {
                    foreach (ItemOrderInfo itemChanged in slides.Where(x => x.Changed).ToList())
                    {
                        DIGIPORT_ADMIN_HR_MEDIA_SLIDE slide = PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityGetir("DT_WORKFLOW", Convert.ToInt32(itemChanged.ItemId));
                        slide.ORDER_NO = itemChanged.ItemOrder_New;
                        slide.LAST_UPDATED = DateTime.Now;
                        slide.LAST_UPDATED_BY = LoginId;
                        PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityUpdateEt("DT_WORKFLOW", slide);
                    }
                }
            }
            catch
            {
            }
        }

        protected void drpClickAction_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (drpClickAction.SelectedItem.Value == "-1" || drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Olay_Yok).ToString())
                KaydetGorunumAyarla(false, false, false);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(true, false, false);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(true, true, false);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(false, false, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(false, true, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(false, false, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(false, true, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç).ToString())
                KaydetGorunumAyarla(false, false, true);
            else if (drpClickAction.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç).ToString())
                KaydetGorunumAyarla(false, true, true);
        }
        private void KaydetPanelGetir()
        {
            if (SlideId == 0)
            {
                drpClickAction.SelectedIndex = 0;
                drpClickActionHrApp.SelectedIndex = 0;
                txtSlideName.Text = txtBaslangicTarihi.Text = txtBitisTarihi.Text = txtTargetLink.Text = txtTargetLinkHrApp.Text = ((TextBox)DigiportAdminHtmlContent.FindControl("txtHtmlContent")).Text = ((TextBox)DigiportAdminHtmlContent.FindControl("summernote")).Text = ((TextBox)DigiportAdminHrAppHtmlContent.FindControl("txtHtmlContentHrApp")).Text = ((TextBox)DigiportAdminHrAppHtmlContent.FindControl("summernoteHrApp")).Text = txtPopupSayfaBaslik.Text = txtPopupSayfaBaslikHrApp.Text = string.Empty;
                txtSiraNo.Text = HrMediaSlideHelper.GetNextSlideNo(MID);
                txtPopupWidth.Text = ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[0];
                txtPopupHeight.Text = ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[1];
                chkActive.Checked = true;
                hdnActive.Value = "true";
                imgSlide.ImageUrl = string.Empty;
                imgThumbnail.ImageUrl = string.Empty;
                imgAppSlide.ImageUrl = string.Empty;
                chkHrAppEnabled.Checked = true;
                KaydetGorunumAyarla(false, false, false);
                KaydetGorunumAyarlaHrApp(false, false);
                drpCategory.SelectedIndex = 0;
                txtSiraNo.Enabled = false;
            }
            else
            {
                DIGIPORT_ADMIN_HR_MEDIA_SLIDE slide = PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityGetir("DT_WORKFLOW", SlideId);
                imgSlide.ImageUrl = slide.SLIDE_IMAGE_PATH;
                imgThumbnail.ImageUrl = slide.THUMBNAIL_IMAGE_PATH;
                imgAppSlide.ImageUrl = slide.HRAPP_SLIDE_IMAGE_PATH;
                txtSlideName.Text = slide.SLIDE_NAME;
                txtPopupSayfaBaslik.Text = slide.SLIDE_TARGET_HEADLINE;
                txtBaslangicTarihi.Text = slide.VALID_DATE_START.ToString("dd.MM.yyyy");
                txtBitisTarihi.Text = slide.VALID_DATE_END.ToString("dd.MM.yyyy");
                txtSiraNo.Text = slide.ORDER_NO.ToString();
                txtSiraNo.Enabled = slide.ACTIVE == "1";
                chkActive.Checked = slide.ACTIVE == "1";
                hdnActive.Value = slide.ACTIVE == "1" ? "true" : "false";
                drpClickAction.Items.Cast<ListItem>().ToList().ForEach(x => x.Selected = false);
                drpClickAction.Items.FindByValue(slide.SLIDE_CLICK_ACTION.ToString()).Selected = true;
                drpClickActionHrApp.Items.Cast<ListItem>().ToList().ForEach(x => x.Selected = false);
                if (slide.HRAPP_SLIDE_CLICK_ACTION != null)
                    drpClickActionHrApp.Items.FindByValue(slide.HRAPP_SLIDE_CLICK_ACTION.ToString()).Selected = true;
                else
                    drpClickActionHrApp.SelectedIndex = 0;
                drpCategory.Items.Cast<ListItem>().ToList().ForEach(x => x.Selected = false);
                if (slide.CATEGORY != null)
                    drpCategory.Items.FindByValue(slide.CATEGORY.ToString()).Selected = true;
                else
                    drpCategory.SelectedIndex = 0;


                chkHrAppEnabled.Checked = slide.HRAPP_ENABLED == "1";
                txtTargetLink.Text = slide.SLIDE_TARGET_LINK;
                txtTargetLinkHrApp.Text = slide.HRAPP_SLIDE_TARGET_LINK;
                txtPopupSayfaBaslikHrApp.Text = slide.HRAPP_SLIDE_TARGET_HEADLINE;
                txtPopupWidth.Text = slide.SLIDE_POPUP_WIDTH != null ? slide.SLIDE_POPUP_WIDTH.ToString() : ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[0];
                txtPopupHeight.Text = slide.SLIDE_POPUP_HEIGHT != null ? slide.SLIDE_POPUP_HEIGHT.ToString() : ConfigurationManager.AppSettings["DigiportAdminHtmlContentWindowDefaultSize"].Split('x')[1];
                ((TextBox)DigiportAdminHtmlContent.FindControl("txtHtmlContent")).Text = ((TextBox)DigiportAdminHtmlContent.FindControl("summernote")).Text = slide.SLIDE_TARGET_CONTENT;
                ((TextBox)DigiportAdminHrAppHtmlContent.FindControl("txtHtmlContentHrApp")).Text = ((TextBox)DigiportAdminHrAppHtmlContent.FindControl("summernoteHrApp")).Text = slide.HRAPP_SLIDE_TARGET_CONTENT;
                KaydetGorunumAyarla(
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sekmede_Aç)
                    ,
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    ,
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç)
                    ||
                    slide.SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç)
                    );

                KaydetGorunumAyarlaHrApp(
                    slide.HRAPP_SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_İçi_Yönlendirme)
                    ||
                    slide.HRAPP_SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_Dışı_Yönlendirme)
                    ,
                    slide.HRAPP_SLIDE_CLICK_ACTION == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Popup_Aç)
                    );
            }
        }
        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(gridSlides.GetRowValues(index, "ID"));
            hdnSlideId.Value = deger.ToString();
            PanelleriDuzenle(false, false, true);
        }
        private void KaydetGorunumAyarla(bool linkBilgileriGoster, bool popupBilgileriGoster, bool popupContentGoster)
        {
            divHedefLink.Visible = linkBilgileriGoster;
            divPopupSize.Visible = popupBilgileriGoster;
            divPopupHtmlContent.Style["display"] = popupContentGoster ? "block" : "none";
        }
        private void KaydetGorunumAyarlaHrApp(bool linkBilgileriGoster, bool popupContentGoster)
        {
            divHedefLinkHrApp.Visible = linkBilgileriGoster;
            divPopupHtmlContentHrApp.Style["display"] = popupContentGoster ? "block" : "none";
            HrAppPanelGorunumAyarla();
        }

        protected void btnYeniKayit_Click(object sender, EventArgs e)
        {
            PanelleriDuzenle(false, true, false);
        }

        private void PanelleriDuzenle(bool listelemeModu, bool yeniKayitModu, bool guncellemeModu)
        {
            if (yeniKayitModu)
                hdnSlideId.Value = "0";
            rvBaslangicTarihi.Visible = rvBitisTarihi.Visible = SlideId == 0;
            btnYeniKayit.Visible = listelemeModu;
            linkSliderSecenekleri.Visible = listelemeModu;
            btnSil.Visible = guncellemeModu;
            btnKayitIptal.Visible = yeniKayitModu || guncellemeModu;
            pnlKaydet.Visible = yeniKayitModu || guncellemeModu;
            pnlGrid.Visible = listelemeModu;
            if (yeniKayitModu || guncellemeModu)
                KaydetPanelGetir();
            if (listelemeModu)
                GridYukle();
        }

        private void GridYukle()
        {
            DataTable dt = HrMediaSlideHelper.GetSlidesTable(MID, ConvertionHelper.ConvertValue<int>(rdBtnListActive.SelectedValue));
            gridSlides.DataSource = dt;
            gridSlides.DataBind();
        }

        protected void btnKayitIptal_Click(object sender, EventArgs e)
        {
            PanelleriDuzenle(true, false, false);
        }

        public bool ClickLinkVisible(string clickActionId)
        {
            return clickActionId != ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Olay_Yok).ToString();
        }

        protected void rdBtnListActive_SelectedIndexChanged(object sender, EventArgs e)
        {
            GridYukle();
        }

        protected void chkActiveGrid_CheckedChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_HR_MEDIA_SLIDE entity = null;
            try
            {
                int index = (((CheckBox)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _slideId = ConvertionHelper.ConvertValue<int>(gridSlides.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityGetir("DT_WORKFLOW", _slideId);
                entity.ACTIVE = ((CheckBox)sender).Checked ? "1" : "0";
                entity.LAST_UPDATED_BY = LoginId;
                entity.LAST_UPDATED = DateTime.Now;
                PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityUpdateEt("DT_WORKFLOW", entity);
                if (entity.ACTIVE == "1")
                    SiraNoTekrarDuzenle(entity.ID, entity.ORDER_NO, entity.ACTIVE == "1");
                GridYukle();
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((CheckBox)sender).Checked = entity.ACTIVE == "1";
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }

        protected void dateEditStartGrid_ValueChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_HR_MEDIA_SLIDE entity = null;
            try
            {
                int index = (((ASPxDateEdit)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _slideId = ConvertionHelper.ConvertValue<int>(gridSlides.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityGetir("DT_WORKFLOW", _slideId);
                DateTime dateNew = ((ASPxDateEdit)sender).Date;
                if (dateNew >= entity.VALID_DATE_END)
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihiBaslagictanIleriOlsun);
                entity.VALID_DATE_START = dateNew;
                entity.LAST_UPDATED_BY = LoginId;
                entity.LAST_UPDATED = DateTime.Now;
                PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityUpdateEt("DT_WORKFLOW", entity);
                GridYukle();
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((ASPxDateEdit)sender).Date = entity.VALID_DATE_START;
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }

        protected void dateEditEndGrid_ValueChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_HR_MEDIA_SLIDE entity = null;
            try
            {
                int index = (((ASPxDateEdit)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _slideId = ConvertionHelper.ConvertValue<int>(gridSlides.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityGetir("DT_WORKFLOW", _slideId);
                DateTime dateNew = ((ASPxDateEdit)sender).Date;
                if (dateNew <= entity.VALID_DATE_START)
                    throw new Exception(Resources.DigiportAdminResource.BitisTarihiBaslagictanIleriOlsun);
                entity.VALID_DATE_END = dateNew;
                entity.LAST_UPDATED_BY = LoginId;
                entity.LAST_UPDATED = DateTime.Now;
                PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityUpdateEt("DT_WORKFLOW", entity);
                GridYukle();
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((ASPxDateEdit)sender).Date = entity.VALID_DATE_END;
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }

        protected void txtSiraNoGrid_TextChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_HR_MEDIA_SLIDE entity = null;
            try
            {
                int index = (((TextBox)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _slideId = ConvertionHelper.ConvertValue<int>(gridSlides.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityGetir("DT_WORKFLOW", _slideId);
                decimal newOrderNo = ConvertionHelper.ConvertValue<decimal>(((TextBox)sender).Text);
                if (entity.ACTIVE == "1")
                {
                    SiraNoTekrarDuzenle(entity.ID, newOrderNo, false);
                    GridYukle();
                }
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((TextBox)sender).Text = entity.ORDER_NO.ToString();
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
        protected void btnSil_Click(object sender, EventArgs e)
        {
            try
            {
                if (SlideId > 0)
                {
                    DIGIPORT_ADMIN_HR_MEDIA_SLIDE entity = PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityGetir("DT_WORKFLOW", SlideId);
                    if (entity != null)
                    {
                        List<string> pathsToDelete = new List<string>();
                        if (!string.IsNullOrEmpty(entity.SLIDE_TARGET_CONTENT))
                            pathsToDelete.AddRange(helper.ExtractSummerNoteUploadPaths(entity.SLIDE_TARGET_CONTENT));
                        if (!string.IsNullOrEmpty(entity.SLIDE_IMAGE_PATH))
                            pathsToDelete.Add(Server.MapPath(entity.SLIDE_IMAGE_PATH));
                        if (!string.IsNullOrEmpty(entity.THUMBNAIL_IMAGE_PATH))
                            pathsToDelete.Add(Server.MapPath(entity.THUMBNAIL_IMAGE_PATH));
                        if (!string.IsNullOrEmpty(entity.HRAPP_SLIDE_TARGET_CONTENT))
                            pathsToDelete.AddRange(helperHrApp.ExtractSummerNoteUploadPaths(entity.HRAPP_SLIDE_TARGET_CONTENT));
                        if (!string.IsNullOrEmpty(entity.HRAPP_SLIDE_IMAGE_PATH))
                            pathsToDelete.Add(Server.MapPath(entity.HRAPP_SLIDE_IMAGE_PATH));

                        foreach (string path in pathsToDelete)
                        {
                            if (File.Exists(path))
                                File.Delete(path);
                        }
                        entity.DELETED = "1";
                        entity.LAST_UPDATED_BY = LoginId;
                        entity.LAST_UPDATED = DateTime.Now;
                        PRepository<DIGIPORT_ADMIN_HR_MEDIA_SLIDE>.EntityUpdateEt("DT_WORKFLOW", entity);
                        PanelleriDuzenle(true, false, false);
                        this.Master.PopupGoster(Resources.DigiportAdminResource.Basari, Resources.DigiportAdminResource.SilmeBasarili, false);
                    }
                }
            }
            catch (Exception ex)
            {
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }

        protected void drpClickActionHrApp_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (drpClickActionHrApp.SelectedItem.Value == "-1" || drpClickActionHrApp.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Olay_Yok).ToString())
                KaydetGorunumAyarlaHrApp(false, false);
            else if (drpClickActionHrApp.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_İçi_Yönlendirme).ToString() || drpClickActionHrApp.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Uygulama_Dışı_Yönlendirme).ToString())
                KaydetGorunumAyarlaHrApp(true, false);
            else if (drpClickActionHrApp.SelectedItem.Value == ((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiHrAppComponentClickActions.Popup_Aç).ToString())
                KaydetGorunumAyarlaHrApp(false, true);
        }

        protected void chkHrAppEnabled_CheckedChanged(object sender, EventArgs e)
        {
            HrAppPanelGorunumAyarla();
            UpdatePanel3.Update();
        }

        private void HrAppPanelGorunumAyarla()
        {
            divWrapperHrApp1.Visible = PanelIKAPP.Visible = chkHrAppEnabled.Checked;
        }
    }
}