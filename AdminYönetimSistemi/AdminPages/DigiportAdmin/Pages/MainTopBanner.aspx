﻿<%@ Page Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="MainTopBanner.aspx.cs" ValidateRequest="false" Inherits="AracTakipSistemi.AdminPages.DigiportAdmin.Pages.MainTopBanner" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>
<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ Register Src="~/UserControl/DigiportAdminHtmlContent.ascx" TagPrefix="uc1" TagName="DigiportAdminHtmlContent" %>

<%@ MasterType VirtualPath="~/Site.Master" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="/css/DigiportAdmin.css?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>" rel="stylesheet" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <script type="text/javascript">
        var debounceTimers = {};

        function debouncePostback(event, ctrl, delay) {
            if (!NumericControl(event, ctrl, 5, 1, 99999))
                return false;
            var id = ctrl.id;
            if (debounceTimers[id]) {
                clearTimeout(debounceTimers[id]);
            }
            debounceTimers[id] = setTimeout(function () {
                __doPostBack(id, '');
            }, delay);
        }
    </script>
    <asp:Panel ID="pnlAll" Style="width: 100%" GroupingText="" runat="server">
        <div>
            <a href="#" id="anchorClickEvent" target="_blank" style="display: none;"></a>
            <asp:Button runat="server" ID="btnYeniKayit" Text="<%$ Resources:DigiportAdminResource, YeniSlide %>" OnClick="btnYeniKayit_Click" />
            <asp:Button runat="server" ID="btnKayitIptal" Text="<%$ Resources:DigiportAdminResource, Geri %>" OnClick="btnKayitIptal_Click" />
            <asp:Button runat="server" ID="btnSil" Text="<%$ Resources:DigiportAdminResource, Sil %>" OnClick="btnSil_Click" />
            <asp:ConfirmButtonExtender runat="server" ID="confirmDelete" ConfirmText="<%$ Resources:DigiportAdminResource, ConfirmeDelete %>" TargetControlID="btnSil" />
            <asp:HyperLink runat="server" ID="linkSliderSecenekleri" CssClass="linkStyleAsButton" Target="_blank" Text="<%$ Resources:DigiportAdminResource, SliderSecenekleri %>"></asp:HyperLink>
        </div>
        <asp:Panel ID="pnlKaydet" GroupingText="<%$ Resources:DigiportAdminResource, SlideBilgileri %>" Visible="false" runat="server">
            <asp:HiddenField runat="server" ID="hdnSlideId" />
            <div class="container-fluid">
                <div class="grid-container">
                    <div class="grid-item">
                        <div class="inner-div">
                            <div class="divCaption">
                                <span>
                                    <asp:Literal Text="<%$ Resources:DigiportAdminResource, Slide %>" runat="server" /></span>
                                <asp:Label ID="lblSlideImageSize" CssClass="lblimagesize" runat="server"></asp:Label>
                            </div>
                            <div>
                                <asp:Image runat="server" ID="imgSlide" CssClass="imgChosen" ClientIDMode="Static" />
                                <div>
                                    <asp:FileUpload runat="server" ID="fuSlide" AllowMultiple="false" Style="display: none;" />
                                    <asp:Button runat="server" ID="btnSelectFile" OnClientClick="return false;" Text="<%$ Resources:DigiportAdminResource, YuklenilecekSlideSecin %>" />
                                    <asp:Button runat="server" ID="btnClearFileSelection" OnClientClick="return false;" Text="<%$ Resources:DigiportAdminResource, SecileniTemizle %>" />
                                    <label id="lblSeciliDosyaAdi" class="lblChosen"></label>
                                </div>
                                <div>
                                    <asp:CustomValidator ID="imgSlide_CustomValidate" Display="Dynamic" runat="server" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, YuklenilecekSlideSecin %>"
                                        ClientValidationFunction="ValidateimgSlide" ValidationGroup="grp1"></asp:CustomValidator>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grid-item">
                        <div class="inner-div">
                            <div class="divCaption">
                                <span>
                                    <asp:Literal Text="<%$ Resources:DigiportAdminResource, Thumbnail %>" runat="server" /></span>
                                <asp:Label ID="lblThumbnailSize" CssClass="lblimagesize" runat="server"></asp:Label>
                            </div>
                            <div>
                                <asp:Image runat="server" ID="imgThumbnail" CssClass="imgChosen" ClientIDMode="Static" />
                                <div>
                                    <asp:FileUpload runat="server" ID="fuThumbnail" AllowMultiple="false" Style="display: none;" />
                                    <asp:Button runat="server" ID="btnSelectThumbnail" OnClientClick="return false;" Text="<%$ Resources:DigiportAdminResource, YuklenilecekThumbnailSecin %>" />
                                    <asp:Button runat="server" ID="btnClearFileSelectionThumbnail" OnClientClick="return false;" Text="<%$ Resources:DigiportAdminResource, SecileniTemizle %>" />
                                    <label id="lblSeciliThumbnailAdi" class="lblChosen"></label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                <ContentTemplate>
                    <div class="container-fluid">
                        <div class="grid-container">
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, SlideAdi %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtSlideName" MaxLength="200" autocomplete="off" placeholder="<%$ Resources:DigiportAdminResource, SlideAdi %>"></asp:TextBox>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfv1" runat="server" ControlToValidate="txtSlideName" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SlideAdiGirin %>"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, GecerlilikAraligi %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox ID="txtBaslangicTarihi" onkeypress="return DateControl(event);" autocomplete="off" runat="server" MaxLength="10" placeholder="<%$ Resources:DigiportAdminResource, Baslangic %>" Style="width: 100px" />
                                        <asp:CalendarExtender ID="CalendarExtender1" runat="server" CssClass="ajax__calendar"
                                            Format="dd.MM.yyyy" TargetControlID="txtBaslangicTarihi"></asp:CalendarExtender>
                                        <asp:TextBox ID="txtBitisTarihi" onkeypress="return DateControl(event);" autocomplete="off" runat="server" MaxLength="10" placeholder="<%$ Resources:DigiportAdminResource, Bitis %>" Style="width: 100px; margin-left: 10px;" />
                                        <asp:CalendarExtender ID="CalendarExtender2" runat="server" CssClass="ajax__calendar"
                                            Format="dd.MM.yyyy" TargetControlID="txtBitisTarihi"></asp:CalendarExtender>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfv2" runat="server" ControlToValidate="txtBaslangicTarihi" Display="Dynamic" ForeColor="#d62108" ValidationGroup="grp1" ErrorMessage="<%$ Resources:DigiportAdminResource, BaslangicTarihiSec %>"></asp:RequiredFieldValidator>
                                            <asp:RangeValidator ID="rv1" runat="server" ControlToValidate="txtBaslangicTarihi" ErrorMessage="<%$ Resources:DigiportAdminResource, BaslangicTarihFormati %>" MaximumValue="01.01.2199" MinimumValue="01.01.1900" Type="Date" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                                            <asp:RequiredFieldValidator ID="rfv3" runat="server" ControlToValidate="txtBitisTarihi" ValidationGroup="grp1" ForeColor="#d62108" Display="Dynamic" ErrorMessage="<%$ Resources:DigiportAdminResource, BitisTarihiSec %>"></asp:RequiredFieldValidator>
                                            <asp:RangeValidator ID="rv2" runat="server" ControlToValidate="txtBitisTarihi" ErrorMessage="<%$ Resources:DigiportAdminResource, BitisTarihFormati %>" MaximumValue="01.01.2199" MinimumValue="01.01.1900" Type="Date" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                                            <asp:CompareValidator ID="cvTarihler" runat="server" ControlToCompare="txtBaslangicTarihi" ValidationGroup="grp1" ControlToValidate="txtBitisTarihi" Operator="GreaterThan" Type="Date" ErrorMessage="<%$ Resources:DigiportAdminResource, BitisTarihiBaslagictanIleriOlsun %>" ForeColor="#d62108" Display="Dynamic" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, SiraNo %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtSiraNo" MaxLength="3" autocomplete="off" Text="1" oninput="return NumericControl(event, this,5,1,99999);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfv4" runat="server" ControlToValidate="txtSiraNo" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SiraNoSecin %>"></asp:RequiredFieldValidator>
                                            <asp:RangeValidator ID="rv3" runat="server" ControlToValidate="txtSiraNo" ErrorMessage="<%$ Resources:DigiportAdminResource, SayiAraligi2 %>" MaximumValue="99999" MinimumValue="1" Type="Integer" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, Aktif %>" runat="server" />
                                    </div>
                                    <div>
                                        <label class="switch">
                                            <asp:CheckBox ID="chkActive" runat="server" />
                                            <span class="switch-slider round"></span>
                                            <asp:HiddenField ID="hdnActive" runat="server" />
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="grid-container">
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, TiklanmaIslemi %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:DropDownList ID="drpClickAction" AutoPostBack="true" OnSelectedIndexChanged="drpClickAction_SelectedIndexChanged" runat="server">
                                        </asp:DropDownList>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfv5" runat="server" ControlToValidate="drpClickAction" InitialValue="-1" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, TiklanmaIslemiSecin %>"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="grid-item">
                                <div class="inner-div" id="divHedefLink" runat="server">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, TiklanmaHedefLink %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtTargetLink" autocomplete="off" MaxLength="500" placeholder="<%$ Resources:DigiportAdminResource, HedefLink %>"></asp:TextBox>
                                        <div>
                                            <asp:RequiredFieldValidator ID="rfvHedefLink" runat="server" ControlToValidate="txtTargetLink" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, HedefLinkGirin %>"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </div>
                                <div class="inner-div" id="divPopupSize" runat="server">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, AcilanPencereBoyutlari %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtPopupWidth" autocomplete="off" MaxLength="4" Text="" oninput="return NumericControl(event, this,5,1,99999);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                                        <span style="font-weight: bold;">x</span>
                                        <asp:TextBox runat="server" ID="txtPopupHeight" autocomplete="off" MaxLength="4" Text="" oninput="return NumericControl(event, this,5,1,99999);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="grid-container" id="divPopupHtmlContent" runat="server">
                            <div class="grid-item">
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, PopupSayfaBaslik %>" runat="server" />
                                    </div>
                                    <div>
                                        <asp:TextBox runat="server" ID="txtPopupSayfaBaslik" autocomplete="off" MaxLength="300" CssClass="txtMultiline" TextMode="MultiLine" placeholder="<%$ Resources:DigiportAdminResource, PopupSayfaBaslik %>"></asp:TextBox>
                                    </div>
                                </div>
                                <div class="inner-div">
                                    <div class="divCaption">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, PopupSayfaIcerik %>" runat="server" />
                                    </div>
                                    <div>
                                        <uc1:DigiportAdminHtmlContent runat="server" ID="DigiportAdminHtmlContent" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <asp:Button runat="server" ID="btnKaydet" Text="<%$ Resources:DigiportAdminResource, Kaydet %>" ValidationGroup="grp1" OnClick="btnKaydet_Click" />
                    <div>
                        <asp:ValidationSummary runat="server" ID="validationSummary" ValidationGroup="grp1" ForeColor="#d62108" />
                    </div>
                </ContentTemplate>
                <Triggers>
                    <asp:PostBackTrigger ControlID="btnKaydet" />
                </Triggers>
            </asp:UpdatePanel>
        </asp:Panel>
        <asp:Panel ID="pnlGrid" GroupingText="<%$ Resources:DigiportAdminResource, SlideListe %>" runat="server">
            <div>
                <asp:RadioButtonList runat="server" ID="rdBtnListActive" RepeatDirection="Horizontal" Font-Bold="true" Font-Size="14px" OnSelectedIndexChanged="rdBtnListActive_SelectedIndexChanged" AutoPostBack="true">
                </asp:RadioButtonList>
            </div>
            <dx:ASPxGridView ID="gridSlides" KeyFieldName="ID" Width="100%" runat="server" OnAfterPerformCallback="ASPxGridView_AfterPerformCallback">
                <Columns>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, SlideAdi %>" FieldName="SLIDE_NAME" VisibleIndex="1">
                        <CellStyle HorizontalAlign="Left">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataImageColumn Caption="Slide" FieldName="" VisibleIndex="2" Width="110px">
                        <DataItemTemplate>
                            <asp:HiddenField runat="server" ID="hdnSlideIdGrid" Value='<%# Eval("ID") %>' />
                            <asp:HyperLink runat="server" Target="_blank" NavigateUrl='<%# Eval("SLIDE_IMAGE_PATH") %>'>
                                <asp:Image runat="server" ID="imgSlideGrid" CssClass="grid-slide-img" ImageUrl='<%# Eval("SLIDE_IMAGE_PATH") %>' />
                            </asp:HyperLink>
                            <div>
                                <asp:Label runat="server" Text="<%$ Resources:DigiportAdminResource, ClickEvent %>" slideId='<%# Eval("ID") %>' clickEventType='<%# Eval("SLIDE_CLICK_ACTION") %>' CssClass="clickEventLink" ID="lblClickEventGrid" Visible='<%# ClickLinkVisible(Eval("SLIDE_CLICK_ACTION").ToString()) %>'></asp:Label>
                            </div>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataImageColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Baslangic %>" FieldName="VALID_DATE_START" VisibleIndex="3" Width="110px">
                        <DataItemTemplate>
                            <dx:ASPxDateEdit ID="dateEditStartGrid" AutoPostBack="true" OnValueChanged="dateEditStartGrid_ValueChanged" runat="server" Value='<%# Eval("VALID_DATE_START") %>' DisplayFormatString="dd.MM.yyyy">
                                <ClientSideEvents ValueChanged="function(s,e){ showloader(); }" />
                            </dx:ASPxDateEdit>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Bitis %>" FieldName="VALID_DATE_END" VisibleIndex="4" Width="110px">
                        <DataItemTemplate>
                            <dx:ASPxDateEdit ID="dateEditEndGrid" AutoPostBack="true" OnValueChanged="dateEditEndGrid_ValueChanged" runat="server" Value='<%# Eval("VALID_DATE_END") %>' DisplayFormatString="dd.MM.yyyy">
                                <ClientSideEvents ValueChanged="function(s,e){ showloader(); }" />
                            </dx:ASPxDateEdit>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Aktif %>" FieldName="" VisibleIndex="5" Width="70px" Settings-AllowHeaderFilter="False" Settings-AllowAutoFilter="False">
                        <DataItemTemplate>
                            <label class="switch">
                                <asp:CheckBox ID="chkActiveGrid" AutoPostBack="true" OnCheckedChanged="chkActiveGrid_CheckedChanged" Checked='<%# Eval("ACTIVE").ToString()=="1" %>' runat="server" />
                                <span class="switch-slider round" onclick="return showloader();"></span>
                            </label>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, SiraNo %>" FieldName="ORDER_NO" VisibleIndex="6" Width="50px">
                        <DataItemTemplate>
                            <asp:TextBox runat="server" ID="txtSiraNoGrid" onchange="return showloader();" AutoPostBack="false" Enabled='<%# Eval("ACTIVE").ToString()=="1" %>' OnTextChanged="txtSiraNoGrid_TextChanged" MaxLength="3" autocomplete="off" Text='<%# Eval("ORDER_NO").ToString() %>' oninput="return debouncePostback(event,this, 1000);" TextMode="Number" Style="width: 50px;"></asp:TextBox>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Duzenle %>" FieldName="" VisibleIndex="7" Width="40px" Settings-AllowHeaderFilter="False">
                        <DataItemTemplate>
                            <div>
                                <asp:LinkButton ID="lnkGuncelle" Text="" OnClick="lnkduzenle_Click" CausesValidation="false" runat="server">
                                    <i class="fas fa-edit icon" style="font-size:25px;"></i>
                                </asp:LinkButton>
                            </div>
                            <div>
                                <asp:TextBox ID="txtHtmlContentGrid" TextMode="MultiLine" Text='<%# Eval("SLIDE_TARGET_CONTENT").ToString() %>' slideId='<%# Eval("ID") %>' CssClass="txtHtmlContentGrid" runat="server" Style="display: none;"></asp:TextBox>
                                <asp:TextBox runat="server" ID="txtPopupSayfaBaslikGrid" TextMode="MultiLine" Text='<%# Eval("SLIDE_TARGET_HEADLINE").ToString() %>' slideId='<%# Eval("ID") %>' CssClass="txtPopupSayfaBaslikGrid" Style="display: none;"></asp:TextBox>
                                <asp:Label ID="txtDigiflowUrl" Text='<%# Eval("DIGIFLOW_URL").ToString() %>' slideId='<%# Eval("ID") %>' CssClass="txtDigiflowUrl" runat="server" Style="display: none;"></asp:Label>
                                <asp:Label ID="txtDigiportUrl" Text='<%# Eval("DIGIPORT_URL").ToString() %>' slideId='<%# Eval("ID") %>' CssClass="txtDigiportUrl" runat="server" Style="display: none;"></asp:Label>
                                <asp:Label ID="txtAjansUrl" Text='<%# Eval("AJANS_URL").ToString() %>' slideId='<%# Eval("ID") %>' CssClass="txtAjansUrl" runat="server" Style="display: none;"></asp:Label>
                                <asp:Label ID="txtTargetLinkGrid" Text='<%# Eval("SLIDE_TARGET_LINK").ToString() %>' slideId='<%# Eval("ID") %>' CssClass="txtTargetLinkGrid" runat="server" Style="display: none;"></asp:Label>
                                <asp:Label ID="txtPopupWidthGrid" Text='<%# Eval("SLIDE_POPUP_WIDTH").ToString() %>' slideId='<%# Eval("ID") %>' CssClass="txtPopupWidthGrid" runat="server" Style="display: none;"></asp:Label>
                                <asp:Label ID="txtPopupHeightGrid" Text='<%# Eval("SLIDE_POPUP_HEIGHT").ToString() %>' slideId='<%# Eval("ID") %>' CssClass="txtPopupHeightGrid" runat="server" Style="display: none;"></asp:Label>
                            </div>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                </Columns>
                <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" ShowGroupPanel="false" />
                <SettingsText EmptyDataRow="<%$ Resources:DigiportAdminResource, GridKayitBulunmadi %>" FilterBarClear="<%$ Resources:DigiportAdminResource, TemizleFiltre %>" />
                <SettingsBehavior AllowSelectSingleRowOnly="true" ConfirmDelete="true" />
                <Styles>
                    <Header BackColor="#C60C30" ForeColor="White"></Header>
                </Styles>
            </dx:ASPxGridView>
        </asp:Panel>
    </asp:Panel>
    <script src="/Scripts/DigiportAdmin/MainTopBanner.js?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>"></script>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>
