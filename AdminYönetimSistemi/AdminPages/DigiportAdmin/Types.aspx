<%@ Page Title="<%$ Resources:DigiportAdminResource, TypeManagement %>" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true"
    CodeBehind="Types.aspx.cs" Inherits="AracTakipSistemi.AdminPages.DigiportMenu.Types" %>

<%@ MasterType VirtualPath="~/Site.Master" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <style>
        /* ===== Base Layout & Components ===== */
        .app-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1.5rem;
            font-family: var(--font-sans);
        }

        @media (min-width: 768px) {
            .app-container {
                padding: 2rem;
            }
        }

        /* Card Components */
        .card {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            transition: transform var(--transition-normal), box-shadow var(--transition-normal);
            margin-bottom: 1.5rem;
            border: 1px solid var(--gray-100);
            position: relative;
        }

            .card:hover {
                box-shadow: var(--shadow-lg);
            }

        .card-header {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid var(--gray-100);
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: white;
            position: relative;
        }

            .card-header::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 4px;
                background-color: var(--primary);
                border-radius: 4px 0 0 4px;
            }

        .card-title {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

            .card-title .icon {
                color: var(--primary);
                font-size: 1.2em;
                opacity: 0.9;
            }

        .card-body {
            padding: 1.5rem;
        }

        .card-footer {
            padding: 1.25rem 1.5rem;
            border-top: 1px solid var(--gray-100);
            background-color: var(--gray-50);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        /* Typography */
        .page-title-style {
            margin: 0 0 0.5rem 0;
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--gray-900);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

            .page-title-style .icon {
                color: var(--primary);
            }

        .page-description {
            margin: 0 0 2rem 0;
            color: var(--gray-600);
            max-width: 750px;
            line-height: 1.6;
        }

        /* Form Controls */
        .form-group {
            margin-bottom: 1.25rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--gray-700);
            font-size: 0.9rem;
        }

        .form-hint {
            margin-top: 0.375rem;
            font-size: 0.8125rem;
            color: var(--gray-600);
        }

        .form-control {
            display: block;
            width: 100%;
            padding: 0.625rem 0.875rem;
            font-size: 0.9rem;
            font-weight: 400;
            line-height: 1.5;
            color: var(--gray-800);
            background-color: white;
            background-clip: padding-box;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
        }

            .form-control:focus {
                border-color: var(--primary-300);
                box-shadow: 0 0 0 3px var(--primary-100);
                outline: 0;
            }

            .form-control::placeholder {
                color: var(--gray-500);
                opacity: 1;
            }

        .form-select {
            display: block !important;
            width: 100% !important;
            padding: 0.625rem 2.25rem 0.625rem 0.875rem !important;
            font-size: 0.9rem !important;
            font-weight: 400 !important;
            line-height: 1.5 !important;
            color: var(--gray-800) !important;
            background-color: white !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%235c2d91' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e") !important;
            background-repeat: no-repeat !important;
            background-position: right 0.875rem center !important;
            background-size: 16px 12px !important;
            border: 1px solid var(--gray-300) !important;
            border-radius: var(--radius-md) !important;
            appearance: none !important;
            transition: border-color var(--transition-fast), box-shadow var(--transition-fast) !important;
        }

            .form-select:focus {
                border-color: var(--primary-300);
                box-shadow: 0 0 0 3px var(--primary-100);
                outline: 0;
            }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            line-height: 1.5;
            color: var(--gray-700);
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            user-select: none;
            background-color: transparent;
            border: 1px solid transparent;
            padding: 0.625rem 1.25rem;
            font-size: 0.9rem;
            border-radius: var(--radius-md);
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            gap: 0.5rem;
        }

            .btn:focus {
                outline: 0;
                box-shadow: 0 0 0 3px var(--gray-200);
            }

        .btn-primary {
            color: white !important;
            background-color: var(--primary) !important;
            border-color: var(--primary) !important;
        }

            .btn-primary:hover {
                background-color: var(--primary-800) !important;
                border-color: var(--primary-800) !important;
            }

            .btn-primary:focus {
                box-shadow: 0 0 0 3px var(--primary-200) !important;
            }

        .btn-success {
            color: white !important;
            background-color: var(--success) !important;
            border-color: var(--success) !important;
        }

            .btn-success:hover {
                background-color: var(--success-600) !important;
                border-color: var(--success-600) !important;
            }

            .btn-success:focus {
                box-shadow: 0 0 0 3px var(--success-100) !important;
            }

        .btn-danger {
            color: white !important;
            background-color: var(--danger) !important;
            border-color: var(--danger) !important;
        }

            .btn-danger:hover {
                background-color: var(--danger-600) !important;
                border-color: var(--danger-600) !important;
            }

            .btn-danger:focus {
                box-shadow: 0 0 0 3px var(--danger-100) !important;
            }

        .btn-secondary {
            color: var(--gray-800) !important;
            background-color: white !important;
            border-color: var(--gray-300) !important;
        }

            .btn-secondary:hover {
                background-color: var(--gray-50) !important;
                border-color: var(--gray-400) !important;
            }

        /* Toggle Switch */
        .toggle-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 52px;
            height: 28px;
        }

            .toggle-switch input {
                opacity: 0;
                width: 0;
                height: 0;
            }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--gray-300);
            transition: var(--transition-normal);
            border-radius: 34px;
        }

            .toggle-slider:before {
                position: absolute;
                content: "";
                height: 20px;
                width: 20px;
                left: 4px;
                bottom: 4px;
                background-color: white;
                transition: var(--transition-normal);
                border-radius: 50%;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }

        input:checked + .toggle-slider {
            background-color: var(--success);
        }

            input:checked + .toggle-slider:before {
                transform: translateX(24px);
            }

        .toggle-label {
            font-weight: 500;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .toggle-status {
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: 0.8125rem;
            font-weight: 600;
            background-color: var(--success-50);
            color: var(--success-600);
            transition: var(--transition-fast);
        }

            .toggle-status.inactive {
                background-color: var(--danger-50);
                color: var(--danger-600);
            }

        /* Data Grid / Table */
        .data-table-container {
            border-radius: var(--radius-lg);
            overflow: hidden;
            border: 1px solid var(--gray-200);
            background-color: white;
            margin-bottom: 1.5rem;
        }

        .data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

            .data-table th {
                background-color: var(--gray-50);
                color: var(--gray-700);
                font-size: 0.8125rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.02em;
                padding: 1rem;
                text-align: left;
                border-bottom: 2px solid var(--gray-200);
                position: relative;
            }

                .data-table th:not(:last-child)::after {
                    content: '';
                    position: absolute;
                    top: 30%;
                    right: 0;
                    height: 40%;
                    width: 1px;
                    background-color: var(--gray-300);
                }

            .data-table td {
                padding: 1rem;
                border-bottom: 1px solid var(--gray-200);
                color: var(--gray-800);
                vertical-align: middle;
                font-size: 0.9rem;
                transition: background-color var(--transition-fast);
            }

            .data-table tr {
                transition: background-color var(--transition-fast);
            }

            .data-table tbody tr:hover {
                background-color: var(--primary-50);
            }

            .data-table tr.selected {
                background-color: var(--primary-100);
            }

        /* Status Badge */
        .badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.35em 0.75em;
            font-size: 0.75rem;
            font-weight: 600;
            border-radius: var(--radius-full);
            white-space: nowrap;
            text-transform: uppercase;
            letter-spacing: 0.02em;
        }

        .badge-success {
            background-color: var(--success-50);
            color: var(--success-600);
            border: 1px solid var(--success-100);
        }

            .badge-success::before {
                content: '';
                display: inline-block;
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: var(--success);
                margin-right: 0.5rem;
            }

        .badge-danger {
            background-color: var(--danger-50);
            color: var(--danger-600);
            border: 1px solid var(--danger-100);
        }

            .badge-danger::before {
                content: '';
                display: inline-block;
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: var(--danger);
                margin-right: 0.5rem;
            }

        /* Action Buttons */
        .action-group {
            display: flex;
            gap: 0.5rem;
        }

        .btn-action {
            width: 5rem;
            height: 2rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-md);
            background-color: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
            text-decoration: none;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .btn-action-primary {
            color: var(--primary);
            display: flex;
            flex-direction: row;
            gap: 5px;
            border-color: var(--primary-200);
        }

            .btn-action-primary:hover {
                background-color: var(--primary);
                color: white;
                border-color: var(--primary);
            }

        .btn-action-danger {
            color: var(--danger);
            border-color: var(--danger-200);
        }

            .btn-action-danger:hover {
                background-color: var(--danger);
                color: white;
                border-color: var(--danger);
            }

        /* Pagination */
        .pagination {
            display: flex !important;
            justify-content: center !important;
            gap: 0.25rem !important;
            margin-top: 1.5rem !important;
        }

        .page-item {
            list-style: none;
        }

        .page-link {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.375rem 0.75rem;
            min-width: 2.25rem;
            min-height: 2.25rem;
            line-height: 1.25;
            color: var(--gray-800);
            background-color: white;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-fast);
            font-size: 0.875rem;
            text-decoration: none;
        }

            .page-link:hover {
                background-color: var(--gray-100);
                border-color: var(--gray-400);
                z-index: 2;
                color: var(--primary);
            }

        .page-link-active {
            color: white;
            background-color: var(--primary);
            border-color: var(--primary);
            z-index: 3;
        }

            .page-link-active:hover {
                color: white;
                background-color: var(--primary-800);
                border-color: var(--primary-800);
            }

        /* Empty State */
        .empty-state {
            padding: 3rem 2rem;
            text-align: center;
            background-color: white;
            border: 1px dashed var(--gray-300);
            border-radius: var(--radius-lg);
            margin: 1rem 0;
        }

        .empty-state-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: var(--primary-50);
            color: var(--primary);
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }

        .empty-state-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.75rem;
        }

        .empty-state-text {
            color: var(--gray-600);
            max-width: 350px;
            margin: 0 auto 1.5rem;
        }

        /* Grid Layout */
        .grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 1.5rem;
        }

        .col-span-12 {
            grid-column: span 12 / span 12;
        }

        .col-span-6 {
            grid-column: span 6 / span 6;
        }

        .col-span-4 {
            grid-column: span 4 / span 4;
        }

        .col-span-3 {
            grid-column: span 3 / span 3;
        }

        @media (max-width: 1024px) {
            .lg\:col-span-6 {
                grid-column: span 6 / span 6;
            }

            .lg\:col-span-12 {
                grid-column: span 12 / span 12;
            }
        }

        @media (max-width: 768px) {
            .md\:col-span-12 {
                grid-column: span 12 / span 12;
            }
        }

        /* Utils */
        .flex {
            display: flex;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .gap-4 {
            gap: 1rem;
        }

        .mb-0 {
            margin-bottom: 0;
        }

        .mb-2 {
            margin-bottom: 0.5rem;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .mb-6 {
            margin-bottom: 1.5rem;
        }

        .mt-0 {
            margin-top: 0;
        }

        .mt-2 {
            margin-top: 0.5rem;
        }

        .mt-4 {
            margin-top: 1rem;
        }

        .ml-auto {
            margin-left: auto;
        }

        .text-sm {
            font-size: 0.875rem;
        }

        .text-xs {
            font-size: 0.75rem;
        }

        .text-primary {
            color: var(--primary);
        }

        .text-success {
            color: var(--success);
        }

        .text-danger {
            color: var(--danger);
        }

        .text-gray {
            color: var(--gray-600);
        }

        .font-medium {
            font-weight: 500;
        }

        .font-semibold {
            font-weight: 600;
        }

        .font-bold {
            font-weight: 700;
        }

        .uppercase {
            text-transform: uppercase;
        }

        .bg-white {
            background-color: white;
        }

        .bg-gray-50 {
            background-color: var(--gray-50);
        }

        .bg-primary-50 {
            background-color: var(--primary-50);
        }

        .border {
            border: 1px solid var(--gray-200);
        }

        .rounded {
            border-radius: var(--radius-md);
        }

        .shadow-sm {
            box-shadow: var(--shadow-sm);
        }

        .relative {
            position: relative;
        }

        .d-none {
            display: none;
        }

        /* Empty State */

        /* Additional styles for better spacing and alignment if needed */
        .form-card, .grid-card {
            margin-bottom: 2rem;
        }

        .button-group {
            margin-top: 1.5rem;
            display: flex;
            gap: 0.5rem; /* Space between buttons */
        }

        .grid-header {
            display: flex;
            width: 100%;
            justify-content: space-between;
            align-items: center;
        }
    </style>

    <asp:UpdatePanel ID="upnlTypes" runat="server" UpdateMode="Conditional">
        <ContentTemplate>
            <div class="app-container">
                <h1 class="page-title-style">
                    <span class="icon">&#x1F4C2;</span>
                    <asp:Literal ID="ltlPageTitle" runat="server" Text="<%$ Resources:DigiportAdminResource, TypeManagement %>"></asp:Literal>
                </h1>
                <p class="page-description">
                    <asp:Literal ID="ltlPageDescription" runat="server" Text="<%$ Resources:DigiportAdminResource, TypeManagementDescription %>"></asp:Literal>
                </p>

                <div class="card form-card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <span class="icon">&#x270F;</span>
                            <asp:Literal ID="ltlFormTitle" runat="server" Text="<%$ Resources:DigiportAdminResource, AddNewType %>"></asp:Literal>
                        </h2>
                    </div>
                    <div class="card-body">
                        <asp:HiddenField ID="hdRecordId" runat="server" Value="0" />
                        <div class="form-group">
                            <label for="<%= txtTypeName.ClientID %>" class="form-label">
                                <asp:Literal ID="ltlTypeNameLabel" runat="server" Text="<%$ Resources:DigiportAdminResource, TypeName %>"></asp:Literal>:</label>
                            <asp:TextBox ID="txtTypeName" runat="server" CssClass="form-control" placeholder="<%$ Resources:DigiportAdminResource, TypeNamePlaceholder %>"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvTypeName" runat="server" ControlToValidate="txtTypeName"
                                ErrorMessage="<%$ Resources:DigiportAdminResource, TypeNameRequired %>" Display="Dynamic" CssClass="text-danger form-hint"></asp:RequiredFieldValidator>
                        </div>
                        <div class="form-group">
                            <label for="<%= txtDescription.ClientID %>" class="form-label">
                                <asp:Literal ID="ltlDescriptionLabel" runat="server" Text="<%$ Resources:DigiportAdminResource, Description %>"></asp:Literal>:</label>
                            <asp:TextBox ID="txtDescription" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="3" placeholder="<%$ Resources:DigiportAdminResource, DescriptionPlaceholder %>"></asp:TextBox>
                        </div>
                        <div class="form-group">
                            <div class="toggle-container">
                                <label class="toggle-switch">
                                    <asp:CheckBox ID="chkAktif" runat="server" Checked="true" />
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">
                                    <asp:Literal ID="ltlStatusLabel" runat="server" Text="<%$ Resources:DigiportAdminResource, Status %>"></asp:Literal>:</span>
                                <span class="toggle-status" id="statusText"><%= chkAktif.Checked ? GetGlobalResourceObject("DigiportAdminResource", "Active") : GetGlobalResourceObject("DigiportAdminResource", "Inactive") %></span>
                            </div>
                        </div>                        <div class="button-group">
                            <asp:Button ID="btnKaydet" runat="server" Text="<%$ Resources:DigiportAdminResource, Save %>" CssClass="btn btn-primary" OnClick="btnKaydet_Click" />
                            <asp:Button ID="btnGuncelle" runat="server" Text="<%$ Resources:DigiportAdminResource, Update %>" CssClass="btn btn-success" OnClick="btnGuncelle_Click" Visible="false" />
                            <asp:HiddenField ID="hdnDeleteConfirmation" runat="server"
                                Value="<%$ Resources:DigiportAdminResource, DeleteConfirmation %>" />

                            <asp:Button ID="btnSil" runat="server" Text="<%$ Resources:DigiportAdminResource, Delete %>"
                                CssClass="btn btn-danger" OnClick="btnSil_Click" Visible="true"
                                OnClientClick="return showConfirmation();" />
                            <asp:Button ID="btnCancel" runat="server" Text="<%$ Resources:DigiportAdminResource, Cancel %>" CssClass="btn btn-secondary" OnClick="btnCancel_Click" CausesValidation="false" Visible="false" />
                            <asp:Button ID="btnTemizle" runat="server" Text="<%$ Resources:DigiportAdminResource, Clear %>" CssClass="btn btn-secondary" OnClick="btnTemizle_Click" CausesValidation="false" />
                        </div>
                    </div>
                </div>

                <div class="card grid-card">
                    <div class="card-header">
                        <div class="grid-header">
                            <h2 class="card-title">
                                <span class="icon">&#x1F4CB;</span>
                                <asp:Literal ID="ltlExistingTypesTitle" runat="server" Text="<%$ Resources:DigiportAdminResource, ExistingTypes %>"></asp:Literal>
                            </h2>
                            <asp:Literal ID="ltlRecordCount" runat="server"></asp:Literal>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="data-table-container">
                            <asp:GridView ID="grdTypes" runat="server" AutoGenerateColumns="False" CssClass="data-table"
                                AllowPaging="True" PageSize="10" OnPageIndexChanging="grdTypes_PageIndexChanging"
                                OnRowCommand="grdTypes_RowCommand" DataKeyNames="ID">
                                <Columns>
                                    <asp:BoundField DataField="ID" HeaderText="<%$ Resources:DigiportAdminResource, ID %>" SortExpression="ID" />
                                    <asp:BoundField DataField="TYPE_NAME" HeaderText="<%$ Resources:DigiportAdminResource, TypeName %>" SortExpression="NAME" />
                                    <asp:BoundField DataField="DESCRIPTION" HeaderText="<%$ Resources:DigiportAdminResource, Description %>" SortExpression="DESCRIPTION" />
                                    <asp:TemplateField HeaderText="<%$ Resources:DigiportAdminResource, Status %>">
                                        <ItemTemplate>
                                            <span class='badge <%# Eval("AKTIF").ToString() == "Y" ? "badge-success" : "badge-danger" %>'>
                                                <%# Eval("AKTIF").ToString() == "Y" ? GetGlobalResourceObject("DigiportAdminResource", "Active") : GetGlobalResourceObject("DigiportAdminResource", "Inactive") %>
                                            </span>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="<%$ Resources:DigiportAdminResource, Actions %>">
                                        <ItemTemplate>
                                            <div class="action-group">
                                                <asp:LinkButton ID="lnkEdit" runat="server"
                                                    CssClass="btn-action btn-action-primary"
                                                    CommandName="EditRecord"
                                                    CommandArgument='<%# Eval("ID") %>'
                                                    CausesValidation="false"
                                                    Text='<%# "✎ " + GetGlobalResourceObject("DigiportAdminResource", "Edit") %>'>
                                                </asp:LinkButton>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                                <PagerStyle CssClass="pagination" />
                                <EmptyDataTemplate>
                                    <div class="empty-state">
                                        <asp:Literal runat="server" Text="<%$ Resources:DigiportAdminResource, NoTypesFound%>" />
                                    </div>
                                </EmptyDataTemplate>
                            </asp:GridView>
                        </div>
                    </div>
                </div>
            </div>
        </ContentTemplate>
    </asp:UpdatePanel>

    <!-- Hidden literals for JavaScript resource access -->
    <asp:Literal ID="ltlActiveText" runat="server" Text="<%$ Resources:DigiportAdminResource, Active %>" Visible="false"></asp:Literal>
    <asp:Literal ID="ltlInactiveText" runat="server" Text="<%$ Resources:DigiportAdminResource, Inactive %>" Visible="false"></asp:Literal>


    <script type="text/javascript">
        function showConfirmation() {
            var confirmationMessage = document.getElementById('<%= hdnDeleteConfirmation.ClientID %>').value;
            return confirm(confirmationMessage);
        }
        document.addEventListener('DOMContentLoaded', function () {
            // Get resource texts
            var activeText = '<%= ltlActiveText.Text %>';
            var inactiveText = '<%= ltlInactiveText.Text %>';

            // Manages the toggle switch text
            const chkAktif = document.getElementById('<%= chkAktif.ClientID %>');
            const statusText = document.getElementById('statusText');

            if (chkAktif && statusText) {
                function updateStatusText() {
                    statusText.textContent = chkAktif.checked ? activeText : inactiveText;
                    statusText.className = chkAktif.checked ? "toggle-status" : "toggle-status inactive";
                }
                chkAktif.addEventListener('change', updateStatusText);
                updateStatusText(); // Initial state

                // If using UpdatePanel, re-attach event after partial postback
                if (typeof (Sys) !== 'undefined' && Sys.WebForms) {
                    Sys.WebForms.PageRequestManager.getInstance().add_endRequest(function () {
                        const chkAktifUpdated = document.getElementById('<%= chkAktif.ClientID %>');
                        const statusTextUpdated = document.getElementById('statusText');
                        if (chkAktifUpdated && statusTextUpdated) {
                            function updateStatusTextUpdated() {
                                statusTextUpdated.textContent = chkAktifUpdated.checked ? activeText : inactiveText;
                                statusTextUpdated.className = chkAktifUpdated.checked ? "toggle-status" : "toggle-status inactive";
                            }
                            chkAktifUpdated.addEventListener('change', updateStatusTextUpdated);
                            updateStatusTextUpdated();
                        }
                    });
                }
            }
        });
    </script>
</asp:Content>
