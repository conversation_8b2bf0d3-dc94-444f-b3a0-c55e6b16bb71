﻿// UserDebug.aspx.cs
using System;
using System.Text;
using AracTakipSistemi.Authentication;

namespace AracTakipSistemi
{
    public partial class UserDebug : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            StringBuilder sb = new StringBuilder();

            // Basic debug information
            sb.AppendLine("<h2>User Authentication Debug Information</h2>");
            sb.AppendLine(Global.GetSessionDebugInfo());

            // Force a fresh authentication attempt
            sb.AppendLine("<h3>Force Authentication</h3>");
            bool result = AuthenticationManager.AuthenticateUser(System.Web.HttpContext.Current);
            sb.AppendLine($"Authentication result: {result}<br/>");

            // Show session after authentication attempt
            sb.AppendLine("<h3>Session After Authentication Attempt</h3>");
            sb.AppendLine($"LoginId: {Session["LoginId"] ?? "null"}<br/>");
            sb.AppendLine($"Username: {Session["Username"] ?? "null"}<br/>");
            sb.AppendLine($"UserFullName: {Session["UserFullName"] ?? "null"}<br/>");
            sb.AppendLine($"IsAdmin: {Session["IsAdmin"] ?? "null"}<br/>");

            // Display utility links
            sb.AppendLine("<h3>Utility Links</h3>");
            sb.AppendLine("<a href='UserDebug.aspx' class='btn btn-primary'>Refresh Without LoginId</a>&nbsp;");

            // Show impersonation links for common users
            sb.AppendLine("<h4>Quick Impersonation Links</h4>");
            sb.AppendLine("<a href='UserDebug.aspx?LoginId=1763' class='btn btn-sm btn-info'>KEREM BAYRAKTAR (1763)</a>&nbsp;");
            sb.AppendLine("<a href='UserDebug.aspx?LoginId=2001' class='btn btn-sm btn-info'>ZÜLFİYE TEZEMIR (2001)</a>&nbsp;");
            sb.AppendLine("<a href='UserDebug.aspx?LoginId=1739' class='btn btn-sm btn-info'>İDİL ÇETİN (1739)</a>&nbsp;");
            sb.AppendLine("<a href='UserDebug.aspx?LoginId=1734' class='btn btn-sm btn-info'>İBRAHİM AYDIN (1734)</a>&nbsp;");

            litDebug.Text = sb.ToString();
        }
    }
}