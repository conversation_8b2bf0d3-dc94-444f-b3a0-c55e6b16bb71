﻿using CoreHelpers;
using DevExpress.Web;
using Entities.DigiportAdmin;
using FormHelper.DigiportAdmin;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.DigiportAdmin
{
    public partial class HrMediaKategori : DigiportSecurePage
    {
        int MID = 0;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!String.IsNullOrEmpty(Request.QueryString["MID"]) && CoreHelpers.GenericIslemler.IsNumeric(Request.QueryString["MID"]))
            {
                if (
                    ConfigurationManager.AppSettings["DigiportAdminHrMediaSlideID"] != MenuId.Value.ToString()
                    &&
                    ConfigurationManager.AppSettings["DigiportAdminAjansSlideID"] != MenuId.Value.ToString()
                    &&
                    ConfigurationManager.AppSettings["DigiportAdminEducationSlideID"] != MenuId.Value.ToString()
                    )
                {
                    Session["ErrorPageMessage"] = Resources.DigiportAdminResource.TipYanlis;
                    Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
                    return;
                }
                MID = MenuId.Value;
            }
            else
            {
                Session["ErrorPageMessage"] = Resources.DigiportAdminResource.TipBelirsiz;
                Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
                return;
            }
            if (!IsPostBack && !IsCallback)
            {
                this.Title = Resources.DigiportAdminResource.KategoriIslemleri;
                foreach (var action in Enum.GetValues(FormHelper.CoreHelper.isEnglish() ? typeof(FormHelper.DigiportAdmin.SliderKategoriTip_En) : typeof(FormHelper.DigiportAdmin.SliderKategoriTip)))
                {
                    int value = (int)action;
                    string name = action.ToString().Replace("_", " ");
                    drpTip.Items.Add(new ListItem(name, value.ToString()));
                }
                drpTip.Items.Insert(0, new ListItem(FormHelper.CoreHelper.isEnglish() ? "Please Select" : "Seçiniz", "-1"));
                GridYukle();
                VeriYukleTemizle(0);
            }
        }
        protected void ASPxGridView_AfterPerformCallback(object sender, ASPxGridViewAfterPerformCallbackEventArgs e)
        {
            GridYukle();
        }
        protected void btnKaydet_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtKategoriAdiTr.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.ZorunluAlan);
                if (txtKategoriAdiEn.Text.Trim() == string.Empty)
                    throw new Exception(Resources.DigiportAdminResource.ZorunluAlan);
                if (drpTip.SelectedValue=="-1")
                    throw new Exception(Resources.DigiportAdminResource.TipSecin);

                DIGIPORT_ADMIN_SLIDER_KAT entity;
                if (hdnSliderKategoriId.Value == "0")
                    entity = new DIGIPORT_ADMIN_SLIDER_KAT();
                else
                    entity = PRepository<DIGIPORT_ADMIN_SLIDER_KAT>.EntityGetir("DT_WORKFLOW", ConvertionHelper.ConvertValue<int>(hdnSliderKategoriId.Value));

                entity.KATEGORI_ADI = txtKategoriAdiTr.Text.Trim();
                entity.KATEGORI_ADI_EN = txtKategoriAdiEn.Text.Trim();
                entity.TIP = drpTip.SelectedValue;
                entity.AKTIF = chkAktif.Checked ? "1" : "0";

                if (entity.GetSet_ID == 0)
                {
                    entity.CREATED_BY = LoginId;
                    entity.CREATED = DateTime.Now;
                    PRepository<DIGIPORT_ADMIN_SLIDER_KAT>.EntityKaydet("DT_WORKFLOW", entity);
                }
                else
                {
                    entity.LAST_UPDATED_BY = LoginId;
                    entity.LAST_UPDATED = DateTime.Now;
                    PRepository<DIGIPORT_ADMIN_SLIDER_KAT>.EntityUpdateEt("DT_WORKFLOW", entity);
                }
                GridYukle();
                VeriYukleTemizle(0);
                this.Master.PopupGoster(Resources.DigiportAdminResource.Basari, Resources.DigiportAdminResource.KayitBasarili, false);
            }
            catch (Exception ex)
            {
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
        protected void btnKayitIptal_Click(object sender, EventArgs e)
        {
            VeriYukleTemizle(0);
            GridYukle();
        }
        protected void chkActiveGrid_CheckedChanged(object sender, EventArgs e)
        {
            DIGIPORT_ADMIN_SLIDER_KAT entity = null;
            try
            {
                int index = (((CheckBox)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
                int _sliderKategoriId = ConvertionHelper.ConvertValue<int>(gridViewKategori.GetRowValues(index, "ID"));
                entity = PRepository<DIGIPORT_ADMIN_SLIDER_KAT>.EntityGetir("DT_WORKFLOW", _sliderKategoriId);
                entity.AKTIF = ((CheckBox)sender).Checked ? "1" : "0";
                entity.LAST_UPDATED_BY = LoginId;
                entity.LAST_UPDATED = DateTime.Now;
                PRepository<DIGIPORT_ADMIN_SLIDER_KAT>.EntityUpdateEt("DT_WORKFLOW", entity);
                GridYukle();
                VeriYukleTemizle(0);
            }
            catch (Exception ex)
            {
                if (entity != null)
                    ((CheckBox)sender).Checked = entity.AKTIF == "1";
                this.Master.PopupGoster(Resources.DigiportAdminResource.Hata, Resources.DigiportAdminResource.HataOldu + ex.Message, true);
            }
        }
        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(gridViewKategori.GetRowValues(index, "ID"));
            VeriYukleTemizle(deger);
        }

        private void VeriYukleTemizle(int deger)
        {
            hdnSliderKategoriId.Value = deger.ToString();
            drpTip.Items.Cast<ListItem>().ToList().ForEach(x => x.Selected = false);
            if (deger != 0)
            {
                DIGIPORT_ADMIN_SLIDER_KAT entity = PRepository<DIGIPORT_ADMIN_SLIDER_KAT>.EntityGetir("DT_WORKFLOW", deger);
                txtKategoriAdiTr.Text = entity.KATEGORI_ADI;
                txtKategoriAdiEn.Text = entity.KATEGORI_ADI_EN;
                drpTip.Items.FindByValue(entity.TIP).Selected = true;
                chkAktif.Checked = entity.AKTIF == "1";
                btnKayitIptal.Visible = true;
            }
            else
            {
                txtKategoriAdiEn.Text = txtKategoriAdiTr.Text = string.Empty;
                chkAktif.Checked = true;
                btnKayitIptal.Visible = false;
                drpTip.Items.FindByValue(SliderKategoriHelper.KategoriTipGetir(MID).ToString()).Selected = true;
            }
        }

        private void GridYukle()
        {
            DataTable dt = FormHelper.DigiportAdmin.SliderKategoriHelper.KategoriTabloGetir(SliderKategoriHelper.KategoriTipGetir(MID), 2, FormHelper.CoreHelper.isEnglish());
            gridViewKategori.DataSource = dt;
            gridViewKategori.DataBind();
        }
    }
}