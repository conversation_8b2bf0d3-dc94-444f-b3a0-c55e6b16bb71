﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="SliderOptions.aspx.cs" Inherits="AracTakipSistemi.AdminPages.DigiportAdmin.SliderOptions" %>

<%@ MasterType VirtualPath="~/Site.Master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="/css/DigiportAdmin.css?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>" rel="stylesheet" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:Panel ID="pnlAll" Style="width: 100%" GroupingText="<%$ Resources:DigiportAdminResource, SliderSecenekleri %>" runat="server">
        <div>
            <asp:Button runat="server" ID="btnKaydet" Text="<%$ Resources:DigiportAdminResource, Kaydet %>" ValidationGroup="grp1" OnClick="btnKaydet_Click" />
        </div>
        <div class="container-fluid">
            <div class="grid-container">
                <div class="grid-item">
                    <div class="inner-div">
                        <div class="divCaption">
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, SliderGenislik %>" runat="server" />
                        </div>
                        <div>
                            <asp:TextBox runat="server" ID="txtSliderWidth" Enabled="false" MaxLength="5" autocomplete="off" Text="" oninput="return NumericControl(event, this,5,100,99999);" TextMode="Number" Style="width: 90%;"></asp:TextBox>
                            <div>
                                <asp:RequiredFieldValidator ID="rfv1" runat="server" ControlToValidate="txtSliderWidth" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SliderGenislikSecin %>"></asp:RequiredFieldValidator>
                                <asp:RangeValidator ID="rv1" runat="server" ControlToValidate="txtSliderWidth" ErrorMessage="<%$ Resources:DigiportAdminResource, SayiAraligi2 %>" MaximumValue="99999" MinimumValue="1" Type="Integer" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid-item">
                    <div class="inner-div">
                        <div class="divCaption">
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, SliderYukseklik %>" runat="server" />
                        </div>
                        <div>
                            <asp:TextBox runat="server" ID="txtSliderHeight" Enabled="false" MaxLength="5" autocomplete="off" Text="" oninput="return NumericControl(event, this,5,100,99999);" TextMode="Number" Style="width: 90%;"></asp:TextBox>
                            <div>
                                <asp:RequiredFieldValidator ID="rfv2" runat="server" ControlToValidate="txtSliderHeight" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SliderYukseklikSecin %>"></asp:RequiredFieldValidator>
                                <asp:RangeValidator ID="rv2" runat="server" ControlToValidate="txtSliderHeight" ErrorMessage="<%$ Resources:DigiportAdminResource, SayiAraligi2 %>" MaximumValue="99999" MinimumValue="1" Type="Integer" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid-item">
                    <div class="inner-div">
                        <div class="divCaption">
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, SliderAutoSlideInterval %>" runat="server" />
                        </div>
                        <div>
                            <asp:TextBox runat="server" ID="txtSliderAutoSlideInterval" MaxLength="3" autocomplete="off" Text="" oninput="return NumericControl(event, this,3,1,999);" TextMode="Number" Style="width: 90%;"></asp:TextBox>
                            <div>
                                <asp:RequiredFieldValidator ID="rfv3" runat="server" ControlToValidate="txtSliderAutoSlideInterval" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SliderAutoSlideIntervalSecin %>"></asp:RequiredFieldValidator>
                                <asp:RangeValidator ID="rv3" runat="server" ControlToValidate="txtSliderAutoSlideInterval" ErrorMessage="<%$ Resources:DigiportAdminResource, SayiAraligi1 %>" MaximumValue="999" MinimumValue="1" Type="Integer" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid-item">
                    <div class="inner-div">
                        <div class="divCaption">
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, SliderTransitionDuration %>" runat="server" />
                        </div>
                        <div>
                            <asp:TextBox runat="server" ID="txtSliderTransitionDuration" MaxLength="3" autocomplete="off" Text="" oninput="return NumericControl(event, this,3,1,999);" TextMode="Number" Style="width: 90%;"></asp:TextBox>
                            <div>
                                <asp:RequiredFieldValidator ID="rf4" runat="server" ControlToValidate="txtSliderTransitionDuration" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SliderTransitionDurationSecin %>"></asp:RequiredFieldValidator>
                                <asp:RangeValidator ID="rv4" runat="server" ControlToValidate="txtSliderTransitionDuration" ErrorMessage="<%$ Resources:DigiportAdminResource, SayiAraligi1 %>" MaximumValue="999" MinimumValue="1" Type="Integer" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid-item">
                    <div class="inner-div">
                        <div class="divCaption">
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, SliderEfektTipi %>" runat="server" />
                        </div>
                        <div>
                            <asp:DropDownList ID="drpSliderEfektTipi" runat="server" Style="width: 100%;">
                            </asp:DropDownList>
                            <div>
                                <asp:RequiredFieldValidator ID="rfv5" runat="server" ControlToValidate="drpSliderEfektTipi" InitialValue="-1" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SliderEfektTipiSecin %>"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid-container">

                <div class="grid-item">
                    <div class="inner-div">
                        <div class="divCaption">
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, SliderTransitionEasing %>" runat="server" />
                        </div>
                        <div>
                            <asp:DropDownList ID="drpSliderTransitionEasing" runat="server" Style="width: 100%;">
                            </asp:DropDownList>
                            <div>
                                <asp:RequiredFieldValidator ID="rf6" runat="server" ControlToValidate="drpSliderTransitionEasing" InitialValue="-1" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SliderTransitionEasingSecin %>"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid-item">
                    <div class="inner-div">
                        <div class="divCaption">
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, SliderTitleMode %>" runat="server" />
                        </div>
                        <div>
                            <asp:DropDownList ID="drpSliderTitleMode" runat="server" Style="width: 100%;">
                            </asp:DropDownList>
                            <div>
                                <asp:RequiredFieldValidator ID="rf7" runat="server" ControlToValidate="drpSliderTitleMode" InitialValue="-1" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SliderTitleModeSecin %>"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid-item">
                    <div class="inner-div">
                        <div class="divCaption">
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, ShowProgressBar %>" runat="server" />
                        </div>
                        <div>
                            <label class="switch">
                                <asp:CheckBox ID="chkShowProgressBar" runat="server" />
                                <span class="switch-slider round"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="grid-item">
                    <div class="inner-div">
                        <div class="divCaption">
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, ShowThumbnails %>" runat="server" />
                        </div>
                        <div>
                            <label class="switch">
                                <asp:CheckBox ID="chkShowThumbnails" Enabled="false" AutoPostBack="true" OnCheckedChanged="chkShowThumbnails_CheckedChanged" runat="server" />
                                <span class="switch-slider round"></span>
                            </label>
                        </div>
                    </div>
                </div>

            </div>

            <div class="grid-container" runat="server" visible="false" id="divThumbnailWidthHeight">

                <div class="grid-item">
                    <div class="inner-div">
                        <div class="divCaption">
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, ThumbnailWidth %>" runat="server" />
                        </div>
                        <div>
                            <asp:TextBox runat="server" ID="txtThumbnailWidth" Enabled="false" MaxLength="5" autocomplete="off" Text="" oninput="return NumericControl(event, this,5,100,99999);" TextMode="Number" Style="width: 90%;"></asp:TextBox>
                            <div>
                                <asp:RequiredFieldValidator ID="rfThumbnailWidth" runat="server" ControlToValidate="txtThumbnailWidth" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SliderThumbnailGenislikSecin %>"></asp:RequiredFieldValidator>
                                <asp:RangeValidator ID="rvThumbnailWidth" runat="server" ControlToValidate="txtThumbnailWidth" ErrorMessage="<%$ Resources:DigiportAdminResource, SayiAraligi2 %>" MaximumValue="99999" MinimumValue="1" Type="Integer" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid-item">
                    <div class="inner-div">
                        <div class="divCaption">
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, ThumbnailHeight %>" runat="server" />
                        </div>
                        <div>
                            <asp:TextBox runat="server" ID="txtThumbnailHeight" Enabled="false" MaxLength="5" autocomplete="off" Text="" oninput="return NumericControl(event, this,5,100,99999);" TextMode="Number" Style="width: 90%;"></asp:TextBox>
                            <div>
                                <asp:RequiredFieldValidator ID="rfThumbnailHeight" runat="server" ControlToValidate="txtThumbnailHeight" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, SliderThumbnailYukseklikSecin %>"></asp:RequiredFieldValidator>
                                <asp:RangeValidator ID="rvThumbnailHeight" runat="server" ControlToValidate="txtThumbnailHeight" ErrorMessage="<%$ Resources:DigiportAdminResource, SayiAraligi2 %>" MaximumValue="99999" MinimumValue="1" Type="Integer" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108"></asp:RangeValidator>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid-container">
                <div class="grid-item">
                    <div class="inner-div">
                        <div class="divCaption">
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, AutoSlideActive %>" runat="server" />
                        </div>
                        <div>
                            <label class="switch">
                                <asp:CheckBox ID="chkAutoSlideActive" runat="server" />
                                <span class="switch-slider round"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="margin-top: 10px;">
            <iframe runat="server" seamless scrolling="no" id="IFrameDisplay"></iframe>
        </div>
    </asp:Panel>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>
