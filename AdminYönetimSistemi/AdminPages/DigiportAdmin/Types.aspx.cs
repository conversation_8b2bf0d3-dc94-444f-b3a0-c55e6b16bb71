using AdminPages;
using Entities;
using System;
using System.Collections.Generic;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.DigiportMenu
{
    public partial class Types : AdminAbstract
    {
        #region Properties & Fields

        protected SiteMaster CurrentMaster
        {
            get { return this.Master as SiteMaster; }
        }

        private int RecordId
        {
            get { return Convert.ToInt32(hdRecordId.Value); }
            set { hdRecordId.Value = value.ToString(); }
        }

        private enum EkranModu
        {
            Kaydet,
            Guncelle
        }

        private EkranModu CurrentScreenMode
        {
            get
            {
                return ViewState["EkranModu"] == null ? EkranModu.Kaydet : (EkranModu)ViewState["EkranModu"];
            }
            set
            {
                ViewState["EkranModu"] = value;
            }
        }
        private string MSG_SUCCESS => GetGlobalResourceObject("DigiportAdminResource", "IslemBasarili")?.ToString() ?? "İşlem Başarılı";
        private string MSG_ERROR => GetGlobalResourceObject("DigiportAdminResource", "Hata")?.ToString() ?? "Hata";
        private string MSG_RECORD_NOT_FOUND => GetGlobalResourceObject("DigiportAdminResource", "KayitBulunamadi")?.ToString() ?? "İşlem yapılacak kayıt bulunamadı.";
        private string MSG_DUPLICATE => GetGlobalResourceObject("DigiportAdminResource", "MukerrerKayit")?.ToString() ?? "Aynı isimde tip zaten mevcut.";
        private string MSG_SAVE_SUCCESS => GetGlobalResourceObject("DigiportAdminResource", "TipEklemeBasarili")?.ToString() ?? "Tip başarıyla eklendi.";
        private string MSG_UPDATE_SUCCESS => GetGlobalResourceObject("DigiportAdminResource", "TipGuncellemeBasarili")?.ToString() ?? "Tip başarıyla güncellendi.";
        private string MSG_DELETE_SUCCESS => GetGlobalResourceObject("DigiportAdminResource", "TipSilmeBasarili")?.ToString() ?? "Tip başarıyla silindi.";        private string ERR_PERMISSION => GetGlobalResourceObject("DigiportAdminResource", "ERR_PERMISSION")?.ToString() ?? "Yetki kontrolü sırasında bir hata oluştu.";
        private string ERR_CONFIG => GetGlobalResourceObject("DigiportAdminResource", "ERR_CONFIG")?.ToString() ?? "Gerekli AD Grup ayarları bulunamadı.";
        private string ERR_LOAD_GRID => GetGlobalResourceObject("DigiportAdminResource", "ERR_LOAD_GRID")?.ToString() ?? "Kayıt listesi yüklenirken hata oluştu.";
        private string ERR_DATA_MAPPING => GetGlobalResourceObject("DigiportAdminResource", "ERR_DATA_MAPPING")?.ToString() ?? "Form verileri atanırken hata oluştu.";
        private string ERR_SAVE => GetGlobalResourceObject("DigiportAdminResource", "ERR_SAVE")?.ToString() ?? "Kayıt eklenirken hata oluştu.";
        private string ERR_UPDATE => GetGlobalResourceObject("DigiportAdminResource", "ERR_UPDATE")?.ToString() ?? "Kayıt güncellenirken hata oluştu.";
        private string ERR_DELETE => GetGlobalResourceObject("DigiportAdminResource", "ERR_DELETE")?.ToString() ?? "Kayıt silinirken hata oluştu.";
        private string ERR_GET_RECORD => GetGlobalResourceObject("DigiportAdminResource", "ERR_GET_RECORD")?.ToString() ?? "Kayıt bilgileri getirilirken hata oluştu.";
        private string MSG_UPDATE_NO_RECORD => GetGlobalResourceObject("DigiportAdminResource", "GuncellemeKayitSecilmemis")?.ToString() ?? "Güncelleme için kayıt seçilmemiş.";
        private string MSG_DELETE_NO_RECORD => GetGlobalResourceObject("DigiportAdminResource", "SilmeKayitSecilmemis")?.ToString() ?? "Silme için kayıt seçilmemiş.";

        #endregion

        #region Page Events
        protected void Page_Load(object sender, EventArgs e)
        {
            // Verify user session exists
            if (Session["LoginId"] == null)
            {
                // Try to re-authenticate
                AracTakipSistemi.Authentication.AuthenticationManager.AuthenticateUser(Context);                // If still no session, this is a critical error
                if (Session["LoginId"] == null)
                {
                    this.Master.PopupGoster(MSG_ERROR, GetGlobalResourceObject("DigiportAdminResource", "SessionNotFound")?.ToString() ?? "Kullanıcı oturumu bulunamadı. Lütfen sisteme yeniden giriş yapın.", true);
                    return;
                }
            }

            // Ensure SiteMaster has SetFormPermission or remove/comment out this line if not applicable
            // CurrentMaster.SetFormPermission(this.Page, "Digiport Menu Type Management");
            if (!IsPostBack)
            {
                SetScreenMode(EkranModu.Kaydet);
                Grid_Doldur();
            }
        }

        #endregion

        #region UI Event Handlers

        protected void btnKaydet_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                Kaydet();
            }
        }

        protected void btnGuncelle_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                if (RecordId > 0)
                {
                    Guncelle(RecordId);
                }
                else
                {
                    this.Master.PopupGoster(MSG_ERROR, MSG_UPDATE_NO_RECORD, true);
                }
            }
        }

        protected void btnSil_Click(object sender, EventArgs e)
        {
            if (RecordId > 0)
            {
                Sil(RecordId);
            }
            else
            {
                this.Master.PopupGoster(MSG_ERROR, MSG_DELETE_NO_RECORD, true);
            }
        }
        protected void btnTemizle_Click(object sender, EventArgs e)
        {
            Temizle();
            upnlTypes.Update();
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            // Cancel edit mode - reset to new item mode and clear form
            Temizle();
            upnlTypes.Update();
        }

        protected void grdTypes_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            grdTypes.PageIndex = e.NewPageIndex;
            Grid_Doldur();
            upnlTypes.Update(); // Uncommented - this will refresh the UpdatePanel
        }

        protected void grdTypes_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            if (e.CommandName == "EditRecord")
            {
                int id = Convert.ToInt32(e.CommandArgument);
                Kayit_Getir(id);
                upnlTypes.Update(); // Uncommented - this will refresh the UpdatePanel
            }
        }

        #endregion

        #region CRUD Operations

        public override void Kaydet()
        {
            try
            {
                if (FormHelper.DigiportAdmin.TypeHelper.IsDuplicateName(txtTypeName.Text.Trim()))
                {
                    this.Master.PopupGoster(MSG_ERROR, MSG_DUPLICATE, true);
                    return;
                }

                DIGIPORT_ADMIN_MENU_TYPE newType = new DIGIPORT_ADMIN_MENU_TYPE();
                MapFormToEntity(newType);

                bool success = FormHelper.DigiportAdmin.TypeHelper.AddType(newType, Session["LoginId"]);

                if (success)
                {
                    this.Master.PopupGoster(MSG_SUCCESS, MSG_SAVE_SUCCESS, false);
                    Grid_Doldur();
                    Temizle();
                }
                else
                {
                    this.Master.PopupGoster(MSG_ERROR, ERR_SAVE, true);
                }
            }
            catch
            {
                this.Master.PopupGoster(MSG_ERROR, ERR_SAVE, true);
            }
            upnlTypes.Update(); // Uncommented - this will refresh the UpdatePanel
        }

        public override void Guncelle(int id)
        {
            try
            {
                if (id == 0)
                {
                    this.Master.PopupGoster(MSG_ERROR, MSG_RECORD_NOT_FOUND, true);
                    return;
                }

                if (FormHelper.DigiportAdmin.TypeHelper.IsDuplicateName(txtTypeName.Text.Trim(), id))
                {
                    this.Master.PopupGoster(MSG_ERROR, MSG_DUPLICATE, true);
                    return;
                }

                DIGIPORT_ADMIN_MENU_TYPE typeToUpdate = FormHelper.DigiportAdmin.TypeHelper.GetTypeById(id);
                if (typeToUpdate == null)
                {
                    this.Master.PopupGoster(MSG_ERROR, MSG_RECORD_NOT_FOUND, true);
                    return;
                }

                MapFormToEntity(typeToUpdate);
                bool success = FormHelper.DigiportAdmin.TypeHelper.UpdateType(typeToUpdate, Session["LoginId"]);

                if (success)
                {
                    this.Master.PopupGoster(MSG_SUCCESS, MSG_UPDATE_SUCCESS, false);
                    Grid_Doldur();
                    Temizle();
                }
                else
                {
                    this.Master.PopupGoster(MSG_ERROR, ERR_UPDATE, true);
                }
            }
            catch
            {
                this.Master.PopupGoster(MSG_ERROR, ERR_UPDATE, true);
            }
            upnlTypes.Update(); // Uncommented - this will refresh the UpdatePanel
        }


        public override void Sil(int id)
        {
            try
            {
                if (id == 0)
                {
                    this.Master.PopupGoster(MSG_ERROR, MSG_RECORD_NOT_FOUND, true);
                    return;
                }

                bool success = FormHelper.DigiportAdmin.TypeHelper.DeleteType(id, Session["LoginId"]);

                if (success)
                {
                    this.Master.PopupGoster(MSG_SUCCESS, MSG_DELETE_SUCCESS, false);
                    Grid_Doldur();
                    Temizle();
                }
                else
                {
                    this.Master.PopupGoster(MSG_ERROR, ERR_DELETE, true);
                }
            }
            catch
            {
                this.Master.PopupGoster(MSG_ERROR, ERR_DELETE, true);
            }
            upnlTypes.Update(); // Uncommented - this will refresh the UpdatePanel
        }

        #endregion

        #region Helper Methods

        public override void Grid_Doldur()
        {
            try
            {
                List<DIGIPORT_ADMIN_MENU_TYPE> types = FormHelper.DigiportAdmin.TypeHelper.GetAllTypes();
                grdTypes.DataSource = types;
                grdTypes.DataBind();
                ltlRecordCount.Text = string.Format(GetGlobalResourceObject("DigiportAdminResource", "TotalRecords")?.ToString() ?? "Toplam Kayıt: {0}", types.Count);
            }
            catch (Exception e)
            {
                this.Master.PopupGoster(MSG_ERROR, ERR_LOAD_GRID, true);
                grdTypes.DataSource = new List<DIGIPORT_ADMIN_MENU_TYPE>();
                grdTypes.DataBind();
                ltlRecordCount.Text = GetGlobalResourceObject("DigiportAdminResource", "TotalRecordsZero")?.ToString() ?? "Toplam Kayıt: 0";
            }
        }

        public override void Kayit_Getir(int id)
        {
            try
            {
                DIGIPORT_ADMIN_MENU_TYPE type = FormHelper.DigiportAdmin.TypeHelper.GetTypeById(id);
                if (type != null)
                {
                    MapEntityToForm(type);
                    SetScreenMode(EkranModu.Guncelle);
                }
                else
                {
                    this.Master.PopupGoster(MSG_ERROR, MSG_RECORD_NOT_FOUND, true);
                    Temizle();
                }
            }
            catch
            {
                this.Master.PopupGoster(MSG_ERROR, ERR_GET_RECORD, true);
                Temizle();
            }
        }

        public override void Temizle() // Changed to public
        {
            txtTypeName.Text = string.Empty;
            txtDescription.Text = string.Empty;
            chkAktif.Checked = true;
            RecordId = 0;
            SetScreenMode(EkranModu.Kaydet);
        }

        private void MapFormToEntity(DIGIPORT_ADMIN_MENU_TYPE entity) // Changed from DIGIPORT_TYPES
        {
            try
            {
                entity.TYPE_NAME = txtTypeName.Text.Trim(); // Changed from NAME
                entity.DESCRIPTION = txtDescription.Text.Trim();
                entity.AKTIF = chkAktif.Checked ? "Y" : "N"; // Changed from IS_ACTIVE and mapped to string
            }
            catch
            {
                this.Master.PopupGoster(MSG_ERROR, ERR_DATA_MAPPING, true);
            }
        }

        private void MapEntityToForm(DIGIPORT_ADMIN_MENU_TYPE entity)
        {
            try
            {
                txtTypeName.Text = entity.TYPE_NAME;
                txtDescription.Text = entity.DESCRIPTION;
                chkAktif.Checked = (entity.AKTIF == "Y");
                RecordId = (int)entity.ID;
            }
            catch
            {
                this.Master.PopupGoster(MSG_ERROR, ERR_DATA_MAPPING, true);
            }
        }
        private void SetScreenMode(EkranModu mode)
        {
            CurrentScreenMode = mode;
            var ltlFormTitle = FindControl("ltlFormTitle") as Literal;

            if (mode == EkranModu.Kaydet)
            {
                if (ltlFormTitle != null)
                    ltlFormTitle.Text = GetGlobalResourceObject("DigiportAdminResource", "AddNewType")?.ToString() ?? "Yeni Tip Ekle";
                btnKaydet.Visible = true;
                btnGuncelle.Visible = false;
                btnSil.Visible = false;
                btnCancel.Visible = false;
            }
            else // Guncelle mode
            {
                if (ltlFormTitle != null)
                    ltlFormTitle.Text = GetGlobalResourceObject("DigiportAdminResource", "EditType")?.ToString() ?? "Tip Düzenle";
                btnKaydet.Visible = false;
                btnGuncelle.Visible = true;
                btnSil.Visible = true;
                btnCancel.Visible = true;
            }
        }
        #endregion
    }
}