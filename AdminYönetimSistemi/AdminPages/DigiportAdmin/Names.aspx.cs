using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using Entities;
using FormHelper.DigiportAdmin;
using FormHelper.YetkiHelper;
using Resources;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.DigiportMenu
{
    public partial class Names : AdminAbstract
    {
        #region Properties & Fields

        // Helper property to safely access the Master Page
        protected SiteMaster CurrentMaster => this.Master as SiteMaster;

        // Resource references for UI messages
        private string MSG_SUCCESS => DigiportAdminResource.MSG_SUCCESS;
        private string MSG_ERROR => DigiportAdminResource.MSG_ERROR;
        private string MSG_RECORD_NOT_FOUND => DigiportAdminResource.MSG_RECORD_NOT_FOUND;
        private string MSG_DUPLICATE => DigiportAdminResource.MSG_DUPLICATE;
        private string MSG_SAVE_SUCCESS => DigiportAdminResource.MSG_SAVE_SUCCESS;
        private string MSG_UPDATE_SUCCESS => DigiportAdminResource.MSG_UPDATE_SUCCESS;
        private string MSG_DELETE_SUCCESS => DigiportAdminResource.MSG_DELETE_SUCCESS;

        // Error messages for logging and display
        private string ERR_PERMISSION => DigiportAdminResource.ERR_PERMISSION;
        private string ERR_CONFIG => DigiportAdminResource.ERR_CONFIG;
        private string ERR_LOAD_TYPES => DigiportAdminResource.ERR_LOAD_TYPES;
        private string ERR_LOAD_GRID => DigiportAdminResource.ERR_LOAD_GRID;
        private string ERR_DATA_MAPPING => DigiportAdminResource.ERR_DATA_MAPPING;
        private string ERR_SAVE => DigiportAdminResource.ERR_SAVE;
        private string ERR_UPDATE => DigiportAdminResource.ERR_UPDATE;
        private string ERR_DELETE => DigiportAdminResource.ERR_DELETE;
        private string ERR_GET_RECORD => DigiportAdminResource.ERR_GET_RECORD;

        #endregion

        #region Page Events
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // Enable ViewState for data persistence
                this.EnableViewState = true;
                grdNames.EnableViewState = true;

                AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
                AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
                AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
                AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
                AdminKaydet1.validasyonGrubu = "NamesValidation";

                // Remove update confirmation for direct updates
                AdminKaydet1.guncellemeUyarisi = ""; if (!IsPostBack)
                {
                    // Authorization Check
                    if (!HasPermission())
                    {
                        Response.Redirect("~/AdminPages/Exception/Hata.aspx?Msg=" + DigiportAdminResource.UnauthorizedAccess, false);
                        Context.ApplicationInstance.CompleteRequest();
                        return;
                    }

                      // Set page title
                      ((SiteMaster)this.Master).SayfaBaslikAt(DigiportAdminResource.DigiportMenuNameManagement);

                    // Initialize AdminKaydet control to Kaydet (new item) mode
                    AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
                    // Initial Data Loading
                    LoadTypesList();
                    Grid_Doldur();
                }

                // Register scripts after control initialization
                RegisterClientScripts();
            }
            catch (Exception ex)
            {
                LogError("Page_Load", ex);
                ShowErrorMessage(DigiportAdminResource.ERR_PAGE_LOAD);
            }
        }

        #endregion

        #region Authorization & Security

        private bool HasPermission()
        {
            try
            {
                string techCorpGroup = ConfigurationManager.AppSettings["AdGroup_TechCorp"];
                string pdostbGroup = ConfigurationManager.AppSettings["AdGroup_PDOSTB"]; if (string.IsNullOrEmpty(techCorpGroup) || string.IsNullOrEmpty(pdostbGroup))
                {
                    CurrentMaster?.PopupGoster(GetGlobalResourceObject("DigiportAdminResource", "ConfigurationError")?.ToString() ?? "Konfigürasyon Hatası", ERR_CONFIG, true);
                    return false;
                }

                var userGroups = new YetkiHelper().gruplar()
                                  ?.Select(g => g.ToUpperInvariant()).ToList() ?? new List<string>();

                return userGroups.Contains(techCorpGroup.ToUpperInvariant()) ||
                       userGroups.Contains(pdostbGroup.ToUpperInvariant());
            }
            catch (Exception ex)
            {
                LogError("HasPermission", ex);
                CurrentMaster?.PopupGoster(MSG_ERROR, ERR_PERMISSION, true);
                return false;
            }
        }

        #endregion

        #region Data Loading Methods
        private void LoadTypesList()
        {
            try
            {
                var typesList = NameHelper.GetTypesList();

                // Populate main form dropdown
                drpType.DataTextField = "TYPE_NAME";
                drpType.DataValueField = "ID";
                drpType.DataSource = typesList;
                drpType.DataBind();

                // Populate filter dropdown
                ddlFilterType.DataTextField = "TYPE_NAME";
                ddlFilterType.DataValueField = "ID";
                ddlFilterType.DataSource = typesList;
                ddlFilterType.DataBind();

                // Ensure "All" item is at the top of filter dropdown
                if (ddlFilterType.Items.Count > 0 && ddlFilterType.Items[0].Value != "")
                {
                    ddlFilterType.Items.Insert(0, new ListItem(DigiportAdminResource.Names_FilterAll, ""));
                }
            }
            catch (Exception ex)
            {
                LogError("LoadTypesList", ex);
                ShowErrorMessage(ERR_LOAD_TYPES);
            }
        }

        #endregion

        #region UI Event Handlers

        protected void grdNames_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "EditRecord")
                {
                    int id = Convert.ToInt32(e.CommandArgument);
                    Kayit_Getir(id);
                    upnlNames.Update(); // Refresh the UpdatePanel to show updated form
                }
                else if (e.CommandName == "DeleteRecord")
                {
                    int id = Convert.ToInt32(e.CommandArgument);

                    // Perform the actual delete operation
                    try
                    {
                        bool result = NameHelper.NameSil(id);
                        if (result)
                        {
                            ShowSuccessMessage(DigiportAdminResource.Names_RecordDeletedSuccess);
                            Grid_Doldur(); // Refresh the grid
                        }
                        else
                        {
                            ShowErrorMessage(DigiportAdminResource.Names_RecordDeletedError);
                        }
                    }
                    catch (Exception deleteEx)
                    {
                        LogError("Delete operation", deleteEx);
                        ShowErrorMessage("Delete failed: " + deleteEx.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("grdNames_RowCommand", ex);
                ShowErrorMessage("Action failed: " + ex.Message);
            }
        }

        protected void grdNames_Sorting(object sender, GridViewSortEventArgs e)
        {
            try
            {
                // Store current sort expression and direction in ViewState
                string currentSortExpression = ViewState["SortExpression"] as string ?? "";
                string currentSortDirection = ViewState["SortDirection"] as string ?? "ASC";

                if (currentSortExpression == e.SortExpression)
                {
                    // Toggle sort direction for the same column
                    currentSortDirection = currentSortDirection == "ASC" ? "DESC" : "ASC";
                }
                else
                {
                    // New column, default to ASC
                    currentSortDirection = "ASC";
                }

                ViewState["SortExpression"] = e.SortExpression;
                ViewState["SortDirection"] = currentSortDirection;

                // Apply filtering with current sort
                ApplyFiltersAndSort();
            }
            catch (Exception ex)
            {
                LogError("grdNames_Sorting", ex);
                ShowErrorMessage("Sorting failed: " + ex.Message);
            }
        }

        protected void grdNames_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            try
            {
                if (e.Row.RowType == DataControlRowType.DataRow)
                {
                    // Add any custom styling or logic for data rows here
                    // For example, apply alternating row styles, conditional formatting, etc.
                }
            }
            catch (Exception ex)
            {
                LogError("grdNames_RowDataBound", ex);
            }
        }

        protected void Filter_Changed(object sender, EventArgs e)
        {
            try
            {
                // Reset to first page when filtering changes
                grdNames.PageIndex = 0;

                // Apply filters and reload data
                ApplyFiltersAndSort();

                // Update the UpdatePanel
                upnlNames.Update();
            }
            catch (Exception ex)
            {
                LogError("Filter_Changed", ex);
                ShowErrorMessage("Filter update failed: " + ex.Message);
            }
        }

        protected void ClearFilter_Click(object sender, EventArgs e)
        {
            try
            {
                // Clear all filter controls
                ddlFilterType.SelectedValue = "";
                txtFilterName.Text = "";
                ddlFilterStatus.SelectedValue = "";

                // Reset to first page
                grdNames.PageIndex = 0;

                // Clear sort settings
                ViewState["SortExpression"] = null;
                ViewState["SortDirection"] = null;

                // Reload data without filters
                ApplyFiltersAndSort();

                // Update the UpdatePanel
                upnlNames.Update();
            }
            catch (Exception ex)
            {
                LogError("ClearFilter_Click", ex);
                ShowErrorMessage("Clear filter failed: " + ex.Message);
            }
        }

        // For custom pagination dropdown if implemented
        protected void ddlPage_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                DropDownList ddlPages = sender as DropDownList;
                if (ddlPages != null && int.TryParse(ddlPages.SelectedValue, out int pageIndex))
                {
                    grdNames.PageIndex = pageIndex;
                    ApplyFiltersAndSort();
                }
            }
            catch (Exception ex)
            {
                LogError("ddlPage_SelectedIndexChanged", ex);
                ShowErrorMessage(DigiportAdminResource.ERR_PAGE_CHANGE);
            }
        }

        #endregion

        #region Filter and Data Binding Methods

        private void ApplyFiltersAndSort()
        {
            try
            {
                // Get filter values from controls
                int typeId = 0;
                if (!string.IsNullOrEmpty(ddlFilterType.SelectedValue))
                {
                    int.TryParse(ddlFilterType.SelectedValue, out typeId);
                }

                string name = txtFilterName.Text.Trim();
                string status = ddlFilterStatus.SelectedValue;

                // Load filtered data
                var dt = NameHelper.GridListele(typeId, name, status);

                if (dt != null)
                {
                    // Apply sorting if specified
                    string sortExpression = ViewState["SortExpression"] as string;
                    string sortDirection = ViewState["SortDirection"] as string ?? "ASC";

                    if (!string.IsNullOrEmpty(sortExpression))
                    {
                        var dataView = dt.DefaultView;
                        dataView.Sort = $"{sortExpression} {sortDirection}";
                        dt = dataView.ToTable();
                    }

                    // Bind data to grid
                    grdNames.DataSource = dt;
                    grdNames.DataBind();

                    // Update record count display
                    ltlRecordCount.Text = string.Format(DigiportAdminResource.Names_RecordCount, dt.Rows.Count);
                }
                else
                {
                    // Handle empty data
                    grdNames.DataSource = null;
                    grdNames.DataBind();
                    ltlRecordCount.Text = string.Format(DigiportAdminResource.Names_RecordCount, 0);
                }
            }
            catch (Exception ex)
            {
                LogError("ApplyFiltersAndSort", ex);
                ShowErrorMessage(ERR_LOAD_GRID);

                // Set empty data source on error
                grdNames.DataSource = null;
                grdNames.DataBind();
                ltlRecordCount.Text = string.Format(DigiportAdminResource.Names_RecordCount, 0);
            }
        }

        #endregion

        #region CRUD Operations

        public override void Grid_Doldur()
        {
            try
            {
                // Use the new filtering method instead of loading all data
                ApplyFiltersAndSort();
            }
            catch (Exception ex)
            {
                LogError("Grid_Doldur", ex);
                ShowErrorMessage(ERR_LOAD_GRID);
            }
        }
        private void Esitle(DIGIPORT_ADMIN_MENU_NAME entity)
        {
            try
            {
                entity.TYPE_ID = ConvertionHelper.ConvertValue<int>(drpType.SelectedValue);
                entity.NAME = txtName.Text.Trim();
                entity.DESCRIPTION = txtDescription.Text.Trim();
                entity.NAME_EN = txtNameEn.Text.Trim();
                entity.DESCRIPTION_EN = txtDescriptionEn.Text.Trim();
                entity.PAGE_PATH = txtPagePath.Text.Trim();
                entity.AKTIF = chkAktif.Checked ? "1" : "0";
            }
            catch (Exception ex)
            {
                LogError("Esitle", ex);
                throw new Exception(ERR_DATA_MAPPING, ex);
            }
        }
        public override void Kaydet()
        {
            try
            {
                Page.Validate("NamesValidation");
                if (!Page.IsValid) return;

                int typeId = ConvertionHelper.ConvertValue<int>(drpType.SelectedValue);
                if (typeId == 0)
                {
                    ShowErrorMessage(DigiportAdminResource.MSG_SELECT_TYPE);
                    return;
                }
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    ShowErrorMessage(DigiportAdminResource.MSG_NAME_REQUIRED);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtNameEn.Text))
                {
                    ShowErrorMessage(DigiportAdminResource.Names_NameEnRequired);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtDescriptionEn.Text))
                {
                    ShowErrorMessage(DigiportAdminResource.Names_DescriptionEnRequired);
                    return;
                }
                if (string.IsNullOrWhiteSpace(txtPagePath.Text))
                {
                    ShowErrorMessage(DigiportAdminResource.Names_PagePathRequired);
                    return;
                }

                // Check for duplicate
                if (NameHelper.MukerrerKayitVarMi(typeId, txtName.Text.Trim(), 0))
                {
                    ShowErrorMessage(MSG_DUPLICATE);
                    return;
                }

                var entity = new DIGIPORT_ADMIN_MENU_NAME();
                Esitle(entity);

                // Set audit fields
                entity.CREATED = DateTime.Now;
                entity.CREATED_BY = this.LoginId;

                // Save to database
                string newId = PRepository<DIGIPORT_ADMIN_MENU_NAME>.EntityKaydet("DT_WORKFLOW", entity);
                ShowSuccessMessage(MSG_SAVE_SUCCESS);
                Temizle();
                Grid_Doldur();

                // Force UpdatePanel refresh and client-side grid refresh
                upnlNames.Update();
                ScriptManager.RegisterStartupScript(this, GetType(), "RefreshGrid",
                    "if(typeof(gridNames) !== 'undefined') { gridNames.Refresh(); }", true);
            }
            catch (Exception ex)
            {
                LogError("Kaydet", ex);
                ShowErrorMessage(ERR_SAVE);
            }
        }
        public override void Guncelle(int ID)
        {
            try
            {
                Page.Validate("NamesValidation");
                if (!Page.IsValid) return;

                var entity = GetEntityById(ID);
                if (entity == null)
                {
                    ShowErrorMessage(MSG_RECORD_NOT_FOUND);
                    return;
                }
                int typeId = ConvertionHelper.ConvertValue<int>(drpType.SelectedValue);
                if (typeId == 0)
                {
                    ShowErrorMessage(DigiportAdminResource.MSG_SELECT_TYPE);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtNameEn.Text))
                {
                    ShowErrorMessage(DigiportAdminResource.Names_NameEnRequired);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtDescriptionEn.Text))
                {
                    ShowErrorMessage(DigiportAdminResource.Names_DescriptionEnRequired);
                    return;
                }
                if (string.IsNullOrWhiteSpace(txtPagePath.Text))
                {
                    ShowErrorMessage(DigiportAdminResource.Names_PagePathRequired);
                    return;
                }
                

                // Check for duplicate (excluding current record)
                if (NameHelper.MukerrerKayitVarMi(typeId, txtName.Text.Trim(), ID))
                {
                    ShowErrorMessage(MSG_DUPLICATE);
                    return;
                }

                // Map form values to entity
                Esitle(entity);                // Set audit fields
                entity.LAST_UPDATED = DateTime.Now;
                entity.LAST_UPDATED_BY = this.LoginId;

                // Debug logging before EntityUpdateEt
                System.Diagnostics.Debug.WriteLine($"Before EntityUpdateEt - Entity ID: {entity.ID}, GetSet_ID: {entity.GetSet_ID}");
                System.Diagnostics.Debug.WriteLine($"Before EntityUpdateEt - Entity Type: {entity.GetType().Name}");
                // Update database
                PRepository<DIGIPORT_ADMIN_MENU_NAME>.EntityUpdateEt("DT_WORKFLOW", entity);
                ShowSuccessMessage(MSG_UPDATE_SUCCESS);
                Temizle();
                Grid_Doldur();

                // Force UpdatePanel refresh and client-side grid refresh
                upnlNames.Update();
                ScriptManager.RegisterStartupScript(this, GetType(), "RefreshGrid",
                    "if(typeof(gridNames) !== 'undefined') { gridNames.Refresh(); }", true);
            }
            catch (Exception ex)
            {
                LogError("Guncelle", ex);
                ShowErrorMessage(ERR_UPDATE);
            }
        }
        public override void Kayit_Getir(int ID)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Names.aspx Kayit_Getir - Attempting to get record with ID: {ID}");

                // Use alternative method to get entity by ID since decimal ID might be causing issues
                var entity = GetEntityById(ID);

                if (entity != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Names.aspx Kayit_Getir - Found entity: ID={entity.ID}, Name={entity.NAME}");

                    // Set form fields
                    if (drpType.Items.FindByValue(entity.TYPE_ID.ToString()) != null)
                    {
                        drpType.SelectedValue = entity.TYPE_ID.ToString();
                    }
                    txtName.Text = entity.NAME ?? "";
                    txtDescription.Text = entity.DESCRIPTION ?? "";
                    txtNameEn.Text = entity.NAME_EN ?? "";
                    txtDescriptionEn.Text = entity.DESCRIPTION_EN ?? "";
                    txtPagePath.Text = entity.PAGE_PATH ?? "";

                    // Check if AKTIF is Y/1 or similar active values
                    chkAktif.Checked = (entity.AKTIF == "Y" || entity.AKTIF == "1" || entity.AKTIF?.ToUpper() == "ACTIVE");

                    // Set control state
                    AdminKaydet1.RecordId = (int)entity.ID;
                    AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;

                    System.Diagnostics.Debug.WriteLine($"Names.aspx Kayit_Getir - Successfully loaded entity data");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Names.aspx Kayit_Getir - No entity found for ID: {ID}");
                    ShowErrorMessage(MSG_RECORD_NOT_FOUND);
                    Temizle();
                }
            }
            catch (Exception ex)
            {
                LogError("Kayit_Getir", ex);
                ShowErrorMessage(ERR_GET_RECORD);
                Temizle();
            }
        }
        /// <summary>
        /// Helper method to get entity by ID with proper error handling
        /// </summary>
        private DIGIPORT_ADMIN_MENU_NAME GetEntityById(int id)
        {
            try
            {
                // Convert int ID to decimal for proper entity matching
                decimal decimalId = Convert.ToDecimal(id);

                // Try to get entity using direct SQL query to avoid EntityGetir issues with decimal IDs
                string sql = $"SELECT * FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME WHERE ID = {decimalId}";
                var dt = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);

                if (dt != null && dt.Rows.Count > 0)
                {
                    var row = dt.Rows[0]; var entity = new DIGIPORT_ADMIN_MENU_NAME
                    {
                        ID = Convert.ToDecimal(row["ID"]),
                        TYPE_ID = Convert.ToDecimal(row["TYPE_ID"]),
                        NAME = row["NAME"]?.ToString(),
                        DESCRIPTION = row["DESCRIPTION"]?.ToString(),
                        NAME_EN = row["NAME_EN"]?.ToString(),
                        DESCRIPTION_EN = row["DESCRIPTION_EN"]?.ToString(),
                        PAGE_PATH = row["PAGE_PATH"]?.ToString(),
                        AKTIF = row["AKTIF"]?.ToString(),
                        CREATED = Convert.ToDateTime(row["CREATED"]),
                        CREATED_BY = Convert.ToDecimal(row["CREATED_BY"]),
                        LAST_UPDATED = row["LAST_UPDATED"] != DBNull.Value ? (DateTime?)Convert.ToDateTime(row["LAST_UPDATED"]) : null,
                        LAST_UPDATED_BY = row["LAST_UPDATED_BY"] != DBNull.Value ? (decimal?)Convert.ToDecimal(row["LAST_UPDATED_BY"]) : null
                    };

                    // Critical fix: Set the EntityBase ID property via GetSet_ID to ensure EntityUpdateEt works
                    entity.GetSet_ID = id;
                    System.Diagnostics.Debug.WriteLine($"GetEntityById - Entity retrieved and ID set: {entity.ID}, GetSet_ID: {entity.GetSet_ID}");

                    return entity;
                }

                // If SQL approach fails, try standard method as fallback
                try
                {
                    var entity = PRepository<DIGIPORT_ADMIN_MENU_NAME>.EntityGetir("DT_WORKFLOW", id);
                    if (entity != null)
                    {
                        // Ensure GetSet_ID is properly set for EntityUpdateEt
                        entity.GetSet_ID = id;
                        System.Diagnostics.Debug.WriteLine($"GetEntityById fallback - Entity retrieved and ID set: {entity.ID}, GetSet_ID: {entity.GetSet_ID}");
                        return entity;
                    }
                }
                catch (Exception repositoryEx)
                {
                    System.Diagnostics.Debug.WriteLine($"EntityGetir fallback error: {repositoryEx.Message}");
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GetEntityById error: {ex.Message}");
                LogError("GetEntityById", ex);
                return null;
            }
        }
        public override void Sil(int ID)
        {
            try
            {
                var entity = GetEntityById(ID); if (entity != null)
                {
                    PRepository<DIGIPORT_ADMIN_MENU_NAME>.EntitySil("DT_WORKFLOW", entity);
                    ShowSuccessMessage(MSG_DELETE_SUCCESS);
                    Grid_Doldur();

                    // Force UpdatePanel refresh and client-side grid refresh
                    upnlNames.Update();
                    ScriptManager.RegisterStartupScript(this, GetType(), "RefreshGrid",
                        "if(typeof(gridNames) !== 'undefined') { gridNames.Refresh(); }", true);
                }
                else
                {
                    ShowErrorMessage(MSG_RECORD_NOT_FOUND);
                }

                Temizle();
            }
            catch (Exception ex)
            {
                LogError("Sil", ex);
                ShowErrorMessage(ERR_DELETE);
            }
        }
        public override void Temizle()
        {
            try
            {
                // Reset form fields
                drpType.ClearSelection();
                if (drpType.Items.FindByValue("0") != null)
                    drpType.SelectedValue = "0"; txtName.Text = "";
                txtDescription.Text = "";
                txtNameEn.Text = "";
                txtDescriptionEn.Text = "";
                txtPagePath.Text = "";
                chkAktif.Checked = true;// Reset control state
                AdminKaydet1.RecordId = 0;
                AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
            }
            catch (Exception ex)
            {
                LogError("Temizle", ex);
                // Don't show message for this internal operation
            }
        }
        #endregion

        #region Helper Methods

        private void RegisterClientScripts()
        {
            // Register any client-side scripts that need to be available
            // after page is loaded or controls are initialized
            try
            {
                ScriptManager.RegisterStartupScript(this, GetType(), "InitializeUIComponents",
                    "if(typeof(initializeInterface) === 'function') { initializeInterface(); }", true);
            }
            catch (Exception ex)
            {
                LogError("RegisterClientScripts", ex);
                // Don't show message for this internal operation
            }
        }

        private void ShowSuccessMessage(string message)
        {
            CurrentMaster?.PopupGoster(MSG_SUCCESS, message, false);
        }

        private void ShowErrorMessage(string message)
        {
            CurrentMaster?.PopupGoster(MSG_ERROR, message, true);
        }

        private void LogError(string methodName, Exception ex)
        {
            // Replace with proper logging mechanism as needed
            System.Diagnostics.Debug.WriteLine($"ERROR in {methodName}: {ex.Message}");
            System.Diagnostics.Debug.WriteLine(ex.StackTrace);
        }
        protected string GetStatusIcon(string status)
        {
            if (status != null && status.Contains("AKTİF"))
            {
                return "<i class='fas fa-check-circle'></i>";
            }
            else
            {
                return "<i class='fas fa-times-circle'></i>";
            }
        }

        protected string GetStatusText(string status)
        {
            if (status != null && status.Contains("AKTİF"))
            {
                // Retrieves the resource string for "Aktif"
                return GetGlobalResourceObject("DigiportAdminResource", "Aktif") as string;
            }
            else if (status != null && status.Contains("PASİF"))
            {
                // Retrieves the resource string for "Pasif"
                return GetGlobalResourceObject("DigiportAdminResource", "Pasif") as string;
            }
            else
            {
                // Returns the original status if it's neither
                return status;
            }
        }

        #endregion
    }
}