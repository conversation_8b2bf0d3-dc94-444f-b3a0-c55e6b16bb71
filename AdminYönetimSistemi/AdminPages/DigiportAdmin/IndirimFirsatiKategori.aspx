﻿<%@ Page Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="IndirimFirsatiKategori.aspx.cs" Inherits="AracTakipSistemi.AdminPages.DigiportAdmin.IndirimFirsatiKategori" %>

<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>
<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ MasterType VirtualPath="~/Site.Master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="/css/DigiportAdmin.css?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>" rel="stylesheet" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="margin: auto;">
        <asp:Panel ID="Pnl1" GroupingText='<%$ Resources:DigiportAdminResource, IndirimFirsatiKategoriIslemleri %>' runat="server" Width="100%" Style="border-style: none;">
            <table style="width: 100%;">
                <tr>
                    <td style="width:145px;padding:5px;">
                        <asp:HiddenField runat="server" ID="hdnIndirimFirsatKategoriId" />
                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, KategoriAdiTr %>" runat="server" /></td>
                    <td style="padding:5px;">
                        <asp:TextBox ID="txtKategoriAdiTr" MaxLength="100" autocomplete="off" runat="server" placeholder="<%$ Resources:DigiportAdminResource, KategoriAdiTr %>"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfv_KategoriAdiTr" runat="server" ControlToValidate="txtKategoriAdiTr" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, ZorunluAlan %>"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td style="padding:5px;">
                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, KategoriAdiEn %>" runat="server" /></td>
                    <td style="padding:5px;">
                        <asp:TextBox ID="txtKategoriAdiEn" MaxLength="100" autocomplete="off" runat="server" placeholder="<%$ Resources:DigiportAdminResource, KategoriAdiEn %>"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfv_KategoriAdiEn" runat="server" ControlToValidate="txtKategoriAdiEn" Display="Dynamic" ValidationGroup="grp1" ForeColor="#d62108" ErrorMessage="<%$ Resources:DigiportAdminResource, ZorunluAlan %>"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td style="padding:5px;">
                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, Aktif %>" runat="server" /></td>
                    <td style="padding:5px;">
                        <div>
                            <label class="switch">
                                <asp:CheckBox ID="chkAktif" runat="server" />
                                <span class="switch-slider round"></span>
                            </label>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" style="padding:5px;">
                        <asp:Button runat="server" ID="btnKaydet" OnClientClick="return showloader();" Text="<%$ Resources:DigiportAdminResource, Kaydet %>" ValidationGroup="grp1" OnClick="btnKaydet_Click" />
                        <asp:Button runat="server" ID="btnKayitIptal" OnClientClick="return showloader();" Text="<%$ Resources:DigiportAdminResource, Geri %>" OnClick="btnKayitIptal_Click" />
                    </td>
                </tr>
            </table>
        </asp:Panel>
        <div style="margin-top: 5px;">
            <dx:ASPxGridView ID="gridViewKategori" runat="server" SettingsBehavior-AllowGroup="true" KeyFieldName="ID" Width="100%" OnAfterPerformCallback="ASPxGridView_AfterPerformCallback">
                <Columns>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, KategoriAdiTr %>" FieldName="KATEGORI_ADI" VisibleIndex="1" />
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, KategoriAdiEn %>" FieldName="KATEGORI_ADI_EN" VisibleIndex="2" />
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Aktif %>" FieldName="" VisibleIndex="3" Width="70px" Settings-AllowHeaderFilter="False" Settings-AllowAutoFilter="False">
                        <DataItemTemplate>
                            <label class="switch">
                                <asp:CheckBox ID="chkActiveGrid" AutoPostBack="true" OnCheckedChanged="chkActiveGrid_CheckedChanged" Checked='<%# Eval("AKTIF").ToString()=="1" %>' runat="server" />
                                <span class="switch-slider round" onclick="return showloader();"></span>
                            </label>
                        </DataItemTemplate>
                        <CellStyle HorizontalAlign="Center">
                        </CellStyle>
                    </dx:GridViewDataColumn>
                    <dx:GridViewDataColumn Caption="<%$ Resources:DigiportAdminResource, Duzenle %>" FieldName="" VisibleIndex="4" Width="40px" Settings-AllowHeaderFilter="False">
                        <DataItemTemplate>
                             <asp:LinkButton ID="lnkduzenle" Text="" OnClientClick="return showloader();" OnClick="lnkduzenle_Click" CausesValidation="false" runat="server">
                                    <i class="fas fa-edit icon" style="font-size:25px;"></i>
                                </asp:LinkButton>
                        </DataItemTemplate>
                    </dx:GridViewDataColumn>
                </Columns>
                <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" ShowGroupPanel="false" />
                <SettingsText EmptyDataRow="<%$ Resources:DigiportAdminResource, GridKayitBulunmadi %>" FilterBarClear="<%$ Resources:DigiportAdminResource, TemizleFiltre %>" />
                <SettingsBehavior AllowSelectSingleRowOnly="true" ConfirmDelete="true" />
                <Styles>
                    <Header BackColor="#C60C30" ForeColor="White"></Header>
                    <Cell>
                        <border bordercolor="#42145F" />
                    </Cell>
                </Styles>
            </dx:ASPxGridView>
        </div>
    </div>
    <script>
        function load() {
            Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
            Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(BeginRequestHandler);
            Sys.WebForms.PageRequestManager.getInstance().add_initializeRequest(InitializeRequestHandler);
        }
        function InitializeRequestHandler(e) {
            try {
                showloader();
            }
            catch (e) {
            }
        }
        function BeginRequestHandler(e) {
            try {
                showloader();
            }
            catch (e) {
            }
        }
        function EndRequestHandler(e) {
            try {
                hideloader();
            }
            catch (e) {
            }
        }
        window.onload = function () {
            load();
        };
       
    </script>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>
