﻿using FormHelper.DigiportAdmin;
using System;
using System.Diagnostics;
using System.Web;
using System.Web.UI;

namespace AracTakipSistemi.AdminPages.DigiportAdmin
{
    /// <summary>
    /// Base page class for all Digiport admin pages that require authorization
    /// </summary>
    public class DigiportSecurePage : Page
    {
        #region Properties

        /// <summary>
        /// Gets the current user's login ID
        /// </summary>
        protected long LoginId => DigiportAuthorizationService.Instance.GetCurrentLoginId();

        /// <summary>
        /// Gets the current user's Windows username
        /// </summary>
        protected string Username => DigiportAuthorizationService.Instance.GetCurrentUsername();

        /// <summary>
        /// Gets the menu ID from the query string if available
        /// </summary>
        protected int? MenuId
        {
            get
            {
                if (Request.QueryString["MID"] != null && int.TryParse(Request.QueryString["MID"], out int mid))
                {
                    return mid;
                }
                return null;
            }
        }

        /// <summary>
        /// Gets a reference to the master page
        /// </summary>
        protected SiteMaster CurrentMaster => Master as SiteMaster;

        #endregion

        #region Page Lifecycle Events

        /// <summary>
        /// Override OnInit to perform authorization check early in the page lifecycle
        /// </summary>
        protected override void OnInit(EventArgs e)
        {
            try
            {
                // Check if user is authorized to access this page
                if (!IsUserAuthorized())
                {
                    // Log unauthorized access attempt
                    LogUnauthorizedAccess();

                    // Redirect to error page
                    RedirectToErrorPage("Yetkisiz Erişim");
                    return;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DigiportSecurePage.OnInit: {ex.Message}");
                RedirectToErrorPage("Sistem Hatası");
                return;
            }

            base.OnInit(e);
        }

        #endregion

        #region Authorization Methods

        /// <summary>
        /// Checks if the current user is authorized to access this page
        /// </summary>
        protected virtual bool IsUserAuthorized()
        {
            try
            {
                // Get the authorization service
                var authService = DigiportAuthorizationService.Instance;

                // Check if user has permission to access this page
                return authService.HasPagePermission(Request.Path, MenuId);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in IsUserAuthorized: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Logs unauthorized access attempts
        /// </summary>
        protected virtual void LogUnauthorizedAccess()
        {
            try
            {
                string username = Username;
                string pagePath = Request.Path;
                string queryString = Request.QueryString.ToString();
                string ipAddress = Request.UserHostAddress;

                // Log to application event log
                string logMessage = $"Unauthorized access attempt: User={username}, Page={pagePath}, QueryString={queryString}, IP={ipAddress}";
                Debug.WriteLine(logMessage);

                // TODO: Add database logging if needed
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LogUnauthorizedAccess: {ex.Message}");
            }
        }

        /// <summary>
        /// Redirects to the error page with a message
        /// </summary>
        protected virtual void RedirectToErrorPage(string message)
        {
            try
            {
                Session["ErrorPageMessage"] = message;
                Response.Redirect("~/AdminPages/Exception/Hata.aspx", false);
                Context.ApplicationInstance.CompleteRequest();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in RedirectToErrorPage: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Shows an error message using the master page popup
        /// </summary>
        protected void ShowErrorMessage(string message)
        {
            try
            {
                CurrentMaster?.PopupGoster("Hata", message, true);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ShowErrorMessage: {ex.Message}");
            }
        }

        /// <summary>
        /// Shows a success message using the master page popup
        /// </summary>
        protected void ShowSuccessMessage(string message)
        {
            try
            {
                CurrentMaster?.PopupGoster("Bilgi", message, false);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ShowSuccessMessage: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs an error message
        /// </summary>
        protected void LogError(string methodName, Exception ex)
        {
            try
            {
                string pageName = System.IO.Path.GetFileName(Request.Path);
                string logMessage = $"Error in {pageName}.{methodName}: {ex.Message}";
                Debug.WriteLine(logMessage);

                // TODO: Add database logging if needed
            }
            catch
            {
                // Suppress errors in the error logger
            }
        }

        #endregion
    }
}
