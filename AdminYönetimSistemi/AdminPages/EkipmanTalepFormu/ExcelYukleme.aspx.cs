﻿using ClosedXML.Excel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.IO;
using OfficeOpenXml;
using System.Data.Common;
using System.Collections;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using CoreHelpers;
using Digiturk.Workflow.Digiflow.WebCore;
using Digiturk.Workflow.Digiflow.Authentication;
using WebCore;
using FormHelper.YetkiHelper;
using Oracle.DataAccess.Client;


namespace AracTakipSistemi.AdminPages.EkipmanTalepFormu
{
    public partial class ExcelYukleme : UICorePage
    {
        protected void Page_Load(object sender, EventArgs e)
        {

            ((SiteMaster)this.Master).SayfaBaslikAt("Excel Yükleme");


        }


        public string dosyaUploadOkKarakterler = CoreHelpers.GenericIslemler.dosyaUploadOkKarakterler;


        public string UploadTempFile(System.Web.UI.WebControls.FileUpload FileUpp)
        {
            string FilePath = "";
            string ResultFile = "";
            string uploadPath = System.IO.Path.GetTempPath();
            string dosya_adi = FileUpp.FileName;
            string[] FileList = dosya_adi.Split('.');
            string uzanti = FileList[FileList.Length - 1];
            dosya_adi = "";

            for (int i = 0; i < FileList.Length - 1; i++)
            {
                dosya_adi = dosya_adi + FileList[i];
            }

            dosya_adi = CoreHelpers.GenericIslemler.turkceKarakterYokEt(dosya_adi, true);
            dosya_adi = dosya_adi + "_" + System.Guid.NewGuid().ToString().Replace(".", "").Replace("-", "") + '.' + uzanti;
            foreach (char item in dosya_adi)
            {
                if (dosyaUploadOkKarakterler.IndexOf(item) == -1)
                {
                    return null;
                }
            }
            if (FileUpp.HasFile)
            {
                FileUpp.SaveAs(uploadPath + dosya_adi);
                FilePath = uploadPath + dosya_adi;


            }
            return FilePath;
        }

        public static void DeleteTempFile(string TmpFile)
        {
            if (System.IO.File.Exists(TmpFile))
            {
                System.IO.File.Delete(TmpFile);
            }
        }

        protected void btnUpload_Click(object sender, EventArgs e)
        {

            if (ExcelYüklemeUpload.HasFile)
            {
                string SpPath = System.Configuration.ConfigurationManager.AppSettings["SharePointEkipmanTalepFormuDocs"].ToString();
                string strPath = CoreHelpers.GenericIslemler.SaveFileF(ExcelYüklemeUpload, SpPath);


                string TmpFile = UploadTempFile(ExcelYüklemeUpload);


                if (ValidasyonALL(TmpFile))
                {

                    Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA excelDosya = new Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA();
                    excelDosya.DOSYA_ADI_LINK = strPath;
                    excelDosya.CREATED_BY = LoginId.ToString();
                    excelDosya.CREATED = DateTime.Now;
                    string YeniID = FormHelper.EkipmanTalepFormu.ExcelYuklemeHelper.InsertToExcelDosya(excelDosya);

                    MalzExcelBilgiInsert(TmpFile, YeniID);

                    ((SiteMaster)this.Master).PopupGoster("Bilgi", "Excel dosyası başarıyla yüklendi.", false);
                }
                else
                {
                    CoreHelpers.GenericIslemler.SharepointRemove(strPath);
                }

            }
            else
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Lütfen yükleme yapmak için bir dosya seçiniz.", false);
            }
        }       

        public bool ValidasyonALL(string TmpFile)
        {
            bool sonuc = true;
            string bayi_kodu = "";
            string malzeme_kodu = "";
            string ortalamaStr = "";
            string depoAdetStr = "";
            int rowAdress;
            string ortcolumnAdress = "";
            string depoadetcolumnAdress = "";
            int sutunSayisi;
            int malzemeId;
            int bayiId;

            DataTable dtbToValid = MalzExcelBilgiOkumaForValid(TmpFile);
            if (dtbToValid == null)
            {
                sonuc = false;
            }
            else
            {
                List<string> bayiler = new List<string>();
                List<string> malzemeler = new List<string>();

                for (int i = 0; i < dtbToValid.Rows.Count; i = i + (dtbToValid.Columns.Count * 2))
                {
                    bayiler.Add(dtbToValid.Rows[i]["BAYI_KODU"].ToString());

                }
                for (int i = 0; i < dtbToValid.Columns.Count; i++)
                {
                    malzemeler.Add(dtbToValid.Rows[i]["MALZEME_KODU"].ToString());
                }

                int sayac = 2;

                foreach (string item in bayiler)
                {
                    DataTable dtb = FormHelper.EkipmanTalepFormu.ExcelYuklemeHelper.BayiKoduIsExist(item);//.AsEnumerable().Select(x => x["STOK_UYG_DURUM"].ToString()).FirstOrDefault() == "AKTIF"                    
                    sayac++;

                    if (chckIceriAktar.Checked == true)//ozaman içeri aktar
                    {
                        if (dtb.AsEnumerable().Select(x => x["STOK_UYG_DURUM"].ToString()).FirstOrDefault() == "AKTIF" && dtb.AsEnumerable().Select(x => x["ADMIN_DURUM"].ToString()).FirstOrDefault() == "PASIF")
                        {
                            try
                            {
                                FormHelper.EkipmanTalepFormu.ExcelYuklemeHelper.BayiAktiflestir(item);
                            }
                            catch (Exception) // STOK UYG
                            {
                                ((SiteMaster)this.Master).PopupGoster("Hata", item.ToString() + " kodlu bayi stok uygulamasında bulunmadığı için aktarım gerçekleşmedi.", true);
                                sonuc = false;
                                break;
                            }
                        }
                        if (dtb.AsEnumerable().Select(x => x["STOK_UYG_DURUM"].ToString()).FirstOrDefault() == "INAKTIF" && dtb.AsEnumerable().Select(x => x["ADMIN_DURUM"].ToString()).FirstOrDefault() == "PASIF")
                        {
                            ((SiteMaster)this.Master).PopupGoster("Hata", item.ToString() + " kodlu bayi stok uygulamasında İnaktif durumda olduğu için aktarım gerçekleşmedi.", true);
                            sonuc = false;
                            break;
                        }
                        if (dtb.AsEnumerable().Select(x => x["STOK_UYG_DURUM"].ToString()).FirstOrDefault() == "CEZALI" && dtb.AsEnumerable().Select(x => x["ADMIN_DURUM"].ToString()).FirstOrDefault() == "PASIF")
                        {
                            ((SiteMaster)this.Master).PopupGoster("Hata", item.ToString() + " kodlu bayi stok uygulamasında Cezalı durumda olduğu için aktarım gerçekleşmedi.", true);
                            sonuc = false;
                            break;
                        }

                    }
                    else //İÇERİ AKTAR ISARETLENMEMIS
                    {
                        if (dtb.AsEnumerable().Select(x => x["STOK_UYG_DURUM"].ToString()).FirstOrDefault() == "AKTIF" && dtb.AsEnumerable().Select(x => x["ADMIN_DURUM"].ToString()).FirstOrDefault() == "AKTIF")
                        {
                            sonuc = true;
                        }
                        else
                        {
                            ((SiteMaster)this.Master).PopupGoster("Hata", "Yüklediğiniz Excel Dosyasının '" + sayac.ToString() + "' Satırında Belirtilen '" + item + "' Kodlu Bayi, Bayi Tanımlama Ekranında Tanımlanmamış ve Pasif Haldedir.", true);
                            sonuc = false;
                            break;
                        }

                    }
                }
                if (sonuc)
                {
                    sayac = 1;
                    foreach (string item in malzemeler)
                    {
                        DataTable dtb = FormHelper.EkipmanTalepFormu.ExcelYuklemeHelper.MalzemeKoduIsExist(item);
                        sayac++;

                        if (chckIceriAktar.Checked == true)//ozaman içeri aktar
                        {
                            if (dtb.AsEnumerable().Select(x => x["STOK_UYG_DURUM"].ToString()).FirstOrDefault() == "AKTIF" && dtb.AsEnumerable().Select(x => x["ADMIN_DURUM"].ToString()).FirstOrDefault() == "PASIF")
                            {
                                try
                                {
                                    FormHelper.EkipmanTalepFormu.ExcelYuklemeHelper.MalzemeAktiflestir(item);
                                }
                                catch (Exception)
                                {
                                    ((SiteMaster)this.Master).PopupGoster("Hata", item.ToString() + " kodlu malzeme stok uygulamasında bulunmadığı için aktarım gerçekleşmedi.", true);
                                    sonuc = false;
                                    break;
                                }
                            }
                            if (dtb.AsEnumerable().Select(x => x["STOK_UYG_DURUM"].ToString()).FirstOrDefault() == "PASIF" && dtb.AsEnumerable().Select(x => x["ADMIN_DURUM"].ToString()).FirstOrDefault() == "PASIF")
                            {
                                ((SiteMaster)this.Master).PopupGoster("Hata", item.ToString() + " kodlu malzeme stok uygulamasında Pasif olduğu için aktarım gerçekleşmedi.", true);
                                sonuc = false;
                                break;
                            }

                        }
                        else
                        {
                            if (dtb.AsEnumerable().Select(x => x["STOK_UYG_DURUM"].ToString()).FirstOrDefault() == "AKTIF" && dtb.AsEnumerable().Select(x => x["ADMIN_DURUM"].ToString()).FirstOrDefault() == "AKTIF")
                            {
                                sonuc = true;
                            }
                            else
                            {
                                ((SiteMaster)this.Master).PopupGoster("Hata", "Yüklediğiniz Excel Dosyasının '" + sayac + "' Sütununda Belirtilen '" + item + "' Kodlu Malzeme, Malzeme Tanımlama Ekranında Tanımlanmamış ve Pasif Haldedir.", true);
                                sonuc = false;
                                break;
                            }

                        }

                    }
                }

                if (sonuc)
                {
                    for (int i = 0; i < dtbToValid.Rows.Count; i++)
                    {
                        bayi_kodu = dtbToValid.Rows[i]["BAYI_KODU"].ToString();
                        bayiId = ConvertionHelper.ConvertValue<int>(dtbToValid.Rows[i]["BAYI_ID"]);
                        malzeme_kodu = dtbToValid.Rows[i]["MALZEME_KODU"].ToString();
                        malzemeId = ConvertionHelper.ConvertValue<int>(dtbToValid.Rows[i]["MALZEME_ID"]);
                        ortalamaStr = dtbToValid.Rows[i]["ORTALAMA"].ToString();
                        depoAdetStr = dtbToValid.Rows[i]["DEPO_ADET"].ToString();
                        rowAdress = ConvertionHelper.ConvertValue<int>(dtbToValid.Rows[i]["EXCEL_ROW_ADRESS"]);
                        ortcolumnAdress = dtbToValid.Rows[i]["EXCEL_ORT_COLUMN_ADRESS"].ToString();
                        sutunSayisi = ConvertionHelper.ConvertValue<int>(dtbToValid.Rows[i]["SUTUN_SAYISI"]);
                        depoadetcolumnAdress = dtbToValid.Rows[i]["EXCEL_DEPO_ADET_COLUMN_ADRESS"].ToString();

                        if (bayi_kodu == null || bayi_kodu.Trim() == "")
                        {
                            ((SiteMaster)this.Master).PopupGoster("Hata", "Yüklediğiniz Excel Dosyasının '" + rowAdress.ToString() + "'.Satırında Bayi Kodu Eksik Veya Boş Bırakılmış.", true);
                            sonuc = false;
                            break;
                        }
                        if (malzeme_kodu == null || malzeme_kodu.Trim() == "")
                        {
                            ((SiteMaster)this.Master).PopupGoster("Hata", "Yüklediğiniz Excel Dosyasının '" + ortcolumnAdress + "' veya '" + depoadetcolumnAdress + "'.Sütünunda Malzeme Kodu Eksik Veya Boş Bırakılmış.", true);
                            sonuc = false;
                            break;
                        }
                        if (ortalamaStr == null || ortalamaStr.Trim() == "")
                        {
                            ((SiteMaster)this.Master).PopupGoster("Hata", "Yüklediğiniz Excel Dosyasının '" + rowAdress.ToString() + "'.Satır '" + ortcolumnAdress + "' Sütunundak Hücrede Ortalama Değeri Eksik Veya Boş Bırakılmış.", true);
                            sonuc = false;
                            break;
                        }
                        if (depoAdetStr == null || depoAdetStr.Trim() == "")
                        {
                            ((SiteMaster)this.Master).PopupGoster("Hata", "Yüklediğiniz Excel Dosyasının '" + rowAdress.ToString() + "'.Satır '" + depoadetcolumnAdress + "'.Sütunundakı Depo Adet Değeri Eksik Boş Bırakılmış.", true);
                            sonuc = false;
                            break;
                        }

                        if (!FormHelper.EkipmanTalepFormu.ExcelYuklemeHelper.IsConvertibleToDecimal(ortalamaStr))
                        {
                            ((SiteMaster)this.Master).PopupGoster("Hata", "Yüklediğiniz Excel Dosyasının '" + rowAdress.ToString() + "'.Satır '" + ortcolumnAdress + "'.Sütunundaki '" + ortalamaStr + "' Değer Uygun Formatta Değil, Lütfen Sayı Giriniz.", true);
                            sonuc = false;
                            break;
                        }
                        if (!FormHelper.EkipmanTalepFormu.ExcelYuklemeHelper.IsNumeric(depoAdetStr))
                        {
                            ((SiteMaster)this.Master).PopupGoster("Hata", "Yüklediğiniz Excel Dosyasının '" + rowAdress.ToString() + "'.Satır '" + depoadetcolumnAdress + "'.Sütunundaki Değer Uygun Formatta Değil, Lütfen Sayı Giriniz.", true);
                            sonuc = false;
                            break;
                        }

                    }

                }
            }
            return sonuc;
        }

        public void MalzExcelBilgiInsert(string TmpFile, string yeniID)
        {

            try
            {
                List<string> insertCumleleri = new List<string>();

                string strConn = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + TmpFile + ";Extended Properties=Excel 8.0;";
                string strSorgu = "Select * From [GENEL$]";
                using (OleDbConnection oleConn = new OleDbConnection(strConn))
                {

                    oleConn.Open();

                    OleDbCommand oleCmd = new OleDbCommand(strSorgu, oleConn);
                    OleDbDataReader oleReader = oleCmd.ExecuteReader();
                    int sayac = -1;
                    var listMalzemeKodlari = new ArrayList();
                    var listMalzemeIdleri = new ArrayList();
                    var listBayiIdleri = new ArrayList();



                    if (oleReader.HasRows)
                    {

                        do
                        {

                            foreach (DbDataRecord satir in oleReader)// yukarıdan asagı satırları sayıyor yanı BAYI KODLARI
                            {
                                sayac++;
                                if (sayac == 0)
                                {
                                    for (int i = 0; i < satir.FieldCount; i++)
                                    {
                                        listMalzemeKodlari.Add(satir[i]); // columnn sayısı 

                                        if (i <= satir.FieldCount / 2) // yarısından sonra malzeme kodları tekrar edıyo cunku
                                        {
                                            listMalzemeIdleri.Add(FormHelper.EkipmanTalepFormu.ExcelYuklemeHelper.GetMalzemeId(satir[i].ToString()));
                                        }

                                    }
                                }
                                else
                                {
                                    Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA_VERI excelDosyaDetay = new Entities.EkipmanTalepFormu.DF_EKIPMAN_EXCEL_DOSYA_VERI();
                                    excelDosyaDetay.DOSYA_ID = ConvertionHelper.ConvertValue<int>(yeniID);
                                    if (string.IsNullOrEmpty(satir[0].ToString()))//bayı kodu satırı boşsa
                                    {
                                        //break;
                                        goto CikisEtiketi;
                                    }
                                    else
                                    {
                                        string bayiKoduColumn = satir[0].ToString();
                                        excelDosyaDetay.BAYI_ID = FormHelper.EkipmanTalepFormu.ExcelYuklemeHelper.GetBayiId(bayiKoduColumn);

                                        for (int i = 1; i <= listMalzemeKodlari.Count / 2; i++) // her satırda kolonları sayıyor yanı MALZEME KODLARI
                                        {
                                            excelDosyaDetay.MALZEME_ID = ConvertionHelper.ConvertValue<int>(listMalzemeIdleri[i]);
                                            excelDosyaDetay.ORTALAMA = ConvertionHelper.ConvertValue<decimal>(satir[i]);
                                            int depoAdetIndex = (listMalzemeKodlari.Count / 2) + i;
                                            excelDosyaDetay.DEPO_ADET = ConvertionHelper.ConvertValue<decimal>(satir[depoAdetIndex]);
                                            excelDosyaDetay.CREATED = DateTime.Now;
                                            excelDosyaDetay.CREATED_BY = LoginId.ToString();
                                            insertCumleleri.Add(FormHelper.EkipmanTalepFormu.ExcelYuklemeHelper.InsertToExcelDosyaVeri2(excelDosyaDetay));
                                        }
                                    }
                                }

                            }
                        } while (oleReader.Read());

                    }
                CikisEtiketi:
                    oleReader.Close();
                    oleConn.Close();
                    using (OracleConnection Con = DataAccessLayer.DAL.GetConnection_Oracle("DT_WORKFLOW", true))
                    {
                        foreach (string item in insertCumleleri)
                        {
                            DataAccessLayer.DAL.ExecuteNonQuery_Oracle(Con, item);
                        }
                    }
                }
            }
            catch (Exception ex)
            {

                throw;
            }

        }


        public DataTable MalzExcelBilgiOkumaForValid(string TmpFile)
        {


            DataTable excelDtb = new DataTable();

            try
            {
                string strConn = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + TmpFile + ";Extended Properties=Excel 12.0";
                string strSorgu = "Select * From [GENEL$]";
                using (OleDbConnection oleConn = new OleDbConnection(strConn))
                {

                    oleConn.Open();

                    OleDbCommand oleCmd = new OleDbCommand(strSorgu, oleConn);
                    OleDbDataReader oleReader = oleCmd.ExecuteReader();
                    if (oleReader.HasRows)
                    {


                        excelDtb = ExcelDataTableOlusturma(oleReader);

                    }

                    oleReader.Close();
                    oleConn.Close();
                }

                return excelDtb;
            }
            catch (Exception ex)
            {

                ((SiteMaster)this.Master).PopupGoster("Hata", "Yüklediğiniz Excel Dosyasının İçeriği Tek Sheet'den Oluşmalı ve Sheet Adı 'GENEL' Olmalıdır.", true);
                return null;

            }



        }

        private static DataTable ExcelDataTableOlusturma(OleDbDataReader oleReader)
        {
            List<string> harflerListe = ColumnsHarfListe();
            List<string> sutunlarListe = new List<string>();
            string kolonAdi = "";
            string kolonHarf = "";
            bool hataVar = false;
            int row_sayac = -1;
            var listMalzemeKodlari = new ArrayList();
            var listMalzemeIdleri = new ArrayList();

            DataTable excelDataTable = DataTableOlustur();
            if (oleReader.HasRows)
            {
                do
                {
                    foreach (DbDataRecord satir in oleReader)
                    {
                        int j = 0;
                        row_sayac++;
                        if (row_sayac == 0)
                        {
                            for (int i = 0; i < satir.FieldCount; i++)
                            {
                                listMalzemeKodlari.Add(satir[i]);
                                listMalzemeIdleri.Add(FormHelper.EkipmanTalepFormu.ExcelYuklemeHelper.GetMalzemeId(satir[i].ToString()));
                                kolonHarf = harflerListe[i];
                                sutunlarListe.Add(kolonHarf);
                            }
                        }
                        else
                        {
                            if (string.IsNullOrEmpty(satir[0].ToString()))//bayi kodu boşsa basma
                            {
                                hataVar = true;
                                break;
                            }
                            else
                            {
                                excelDataTable = DatatableRowEkle_2(satir, listMalzemeKodlari, row_sayac, excelDataTable, sutunlarListe, listMalzemeIdleri); // sıkıntı buranın ıcınde

                            }
                        }
                    }
                    if (hataVar) break;
                } while (oleReader.Read());
            }
            return excelDataTable;
        }

        private static List<string> ColumnsHarfListe()
        {
            string[] harfler = { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z" };
            List<string> harflerListe = new List<string>(harfler);
            List<string> columnHarflerListe = new List<string>();
            int j = 0;

            for (int i = 0; i < 255; i++)
            {
                if (i < harfler.Length)
                {
                    columnHarflerListe.Add(harflerListe[i]);
                }
                else
                {
                    if (i % harflerListe.Count == 0 && i != harflerListe.Count)
                    {
                        j = i % harflerListe.Count == 0 ? j + 1 : j;
                    }
                    columnHarflerListe.Add(harflerListe[j] + harflerListe[i % harflerListe.Count]);
                }
            }
            return columnHarflerListe;
        }

        private static DataTable DataTableOlustur()
        {
            DataTable excelDataTable = new DataTable();
            excelDataTable.Columns.Add(new DataColumn("ID", typeof(long)));
            excelDataTable.Columns.Add(new DataColumn("BAYI_KODU", typeof(string)));
            excelDataTable.Columns.Add(new DataColumn("BAYI_ID", typeof(int)));
            excelDataTable.Columns.Add(new DataColumn("MALZEME_KODU", typeof(string)));
            excelDataTable.Columns.Add(new DataColumn("MALZEME_ID", typeof(int)));
            excelDataTable.Columns.Add(new DataColumn("ORTALAMA", typeof(string)));
            excelDataTable.Columns.Add(new DataColumn("DEPO_ADET", typeof(string)));
            excelDataTable.Columns.Add(new DataColumn("EXCEL_ROW_ADRESS", typeof(int)));
            excelDataTable.Columns.Add(new DataColumn("EXCEL_ORT_COLUMN_ADRESS", typeof(string)));
            excelDataTable.Columns.Add(new DataColumn("EXCEL_DEPO_ADET_COLUMN_ADRESS", typeof(string)));
            excelDataTable.Columns.Add(new DataColumn("SUTUN_SAYISI", typeof(int)));
            excelDataTable.Columns["ID"].AutoIncrement = true;
            excelDataTable.Columns["ID"].AutoIncrementSeed = 1;
            return excelDataTable;

        }



        private static DataTable DatatableRowEkle_2(DbDataRecord satir, ArrayList listMalzemeKodlari, int sayac, DataTable excelDataTable, List<string> sutunlarListe, ArrayList listMalzemeIdleri)
        {
            int bayiId = FormHelper.EkipmanTalepFormu.ExcelYuklemeHelper.GetBayiId(satir[0].ToString());
           
            for (int i = 1; i <= listMalzemeKodlari.Count / 2; i++)
            {
                DataRow excelRow = excelDataTable.NewRow();
                if (i > listMalzemeKodlari.Count / 2)
                {

                    excelRow["BAYI_KODU"] = satir[0].ToString();                    
                    excelRow["BAYI_ID"] = bayiId;
                    excelRow["MALZEME_KODU"] = listMalzemeKodlari[i].ToString();                    
                    excelRow["MALZEME_ID"] = listMalzemeIdleri[(i - listMalzemeIdleri.Count) - 1];
                    excelRow["ORTALAMA"] = satir[i - (listMalzemeKodlari.Count / 2)].ToString();
                    excelRow["DEPO_ADET"] = satir[i].ToString();
                    excelRow["EXCEL_ROW_ADRESS"] = sayac + 2;
                    excelRow["EXCEL_ORT_COLUMN_ADRESS"] = sutunlarListe[i - (listMalzemeKodlari.Count / 2)];
                    excelRow["EXCEL_DEPO_ADET_COLUMN_ADRESS"] = sutunlarListe[i];
                    excelRow["SUTUN_SAYISI"] = satir.FieldCount;
                }
                else
                {

                    excelRow["BAYI_KODU"] = satir[0].ToString();                    
                    excelRow["BAYI_ID"] = bayiId;
                    excelRow["MALZEME_KODU"] = listMalzemeKodlari[i].ToString();                    
                    excelRow["MALZEME_ID"] = listMalzemeIdleri[i];
                    excelRow["ORTALAMA"] = satir[i].ToString();
                    excelRow["DEPO_ADET"] = satir[(listMalzemeKodlari.Count / 2) + i].ToString();
                    excelRow["EXCEL_ROW_ADRESS"] = sayac + 2;
                    excelRow["EXCEL_ORT_COLUMN_ADRESS"] = sutunlarListe[i];
                    excelRow["EXCEL_DEPO_ADET_COLUMN_ADRESS"] = sutunlarListe[i + (listMalzemeKodlari.Count / 2)];
                    excelRow["SUTUN_SAYISI"] = satir.FieldCount;
                }

                excelDataTable.Rows.Add(excelRow);
            }

            return excelDataTable;
        }



    }
}