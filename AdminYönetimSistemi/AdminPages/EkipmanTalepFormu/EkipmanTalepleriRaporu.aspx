﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="EkipmanTalepleriRaporu.aspx.cs" Inherits="AracTakipSistemi.AdminPages.EkipmanTalepFormu.EkipmanTalepleriRaporu" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>
<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">       
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

     <%--<script type="text/javascript" src="../../Scripts/jquery.min.js"></script>--%>
    <link rel="stylesheet" href="../../css/select2.min.css" />
    <script type="text/javascript" src="../../Scripts/select2.min.js"></script>

    <script type="text/javascript">
        $(function () {
            $("[id*=drpBayi]").select2();
            $("[id*=drpMalzeme]").select2();
        });
        function saatControl(txt) {

            var value = txt.value;
            if (value != null) {
                var saatVeDk = value.split(':');
                if (0 <= saatVeDk[0] && saatVeDk[0] <= 23 && 0 <= saatVeDk[1] && saatVeDk[1] <= 59) {
                    valid_mi = true;
                }
                else {
                    alert("Lütfen Saati Doğru Giriniz.");
                    valid_mi = false;
                }
            }
            else {
                alert('Lütfen Saat Alanını Giriniz.')
                valid_mi = false;
            }

        }
        function validasyonGenel() {

            if (typeof (Page_ClientValidate) == 'function') {
                Page_ClientValidate();
            }
            if (Page_IsValid) {
                return true;
            }
            else {
                alert('Lütfen Gerekli Alanları Doldurunuz');
                return false;
            }
        }
    </script>
    <style type="text/css">
        /* Modern Report Page Styling */
        .report-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            padding: 10px 20px;
            margin-bottom: 10px;
        }

        .report-title {
            font-size: 20px;
            font-weight: 600;
            color: #5c2d91;
            margin-bottom: 10px;
            padding-bottom: 10px;
            margin-top: 5px;
            border-bottom: 1px solid #e9ecef;
        }

        .filter-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .filter-section {
                grid-template-columns: 1fr;
            }
        }

        .filter-label {
            display: block;
            font-weight: 500;
            margin-bottom: 6px;
            color: #495057;
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .date-time-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .validation-message {
            font-size: 12px;
            color: #dc3545;
            margin-top: 5px;
        }

        .action-buttons {
            display: flex;
            justify-content: flex-start;
            gap: 15px;
            margin: 20px 0;
        }

        .btn-primary {
            background-color: #5c2d91;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 20px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background-color: #4b2173;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .btn-export {
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 20px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-export:hover {
            background-color: #218838;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .btn-export i {
            font-size: 16px;
        }

        /* Form Control Styles */
        input[type="text"], select {
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 8px 12px;
            width: 100%;
            font-size: 14px;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        input[type="text"]:focus, select:focus {
            border-color: #5c2d91;
            box-shadow: 0 0 0 0.2rem rgba(92, 45, 145, 0.25);
            outline: 0;
        }

        /* Calendar Styles */
        .ajax__calendar {
            border-radius: 4px !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
        }

        /* Results Section */
        .results-section {
            margin-top: 30px;
        }

        .grid-container {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }

        /* DevExpress Grid Overrides */
        .dxgvHeader {
            background-color: #5c2d91 !important;
            color: white !important;
            font-weight: 500 !important;
        }

        .dxgvDataRow:hover {
            background-color: #f8f6fc !important;
        }

        /* Keep original styles as fallback */
        .excelbutton {
            float: left;
            margin: 10px 0;
            width: auto;
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 4px;
        }

        #tblControls td {
            padding: 10px;
        }

        .listelebutton {
            margin: 20px 0;
            width: auto;
            padding: 8px 16px;
            font-size: 14px;
            min-width: 120px;
            border-radius: 4px;
        }

        .auto-style2 {
            height: 50px;
        }

        .auto-style3 {
            height: 30px;
        }
    </style>

    <asp:Panel ID="pnlTop" Width="100%" runat="server">
        <div class="report-container">
            <h2 class="report-title">Ekipman Talepleri Raporu</h2>

            <div class="filter-section">
                <div class="filter-group">
                    <label class="filter-label">Malzeme</label>
                    <asp:DropDownList ID="drpMalzeme" DataTextField="EKIPMAN_SECIMI" DataValueField="EKIPMAN_SECIMI" Width="100%" CssClass="form-control" runat="server"></asp:DropDownList>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Bayi</label>
                    <asp:DropDownList ID="drpBayi" DataTextField="YTS_ADI_2" DataValueField="YTS_KODU" runat="server" Width="100%" CssClass="form-control"></asp:DropDownList>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Başlangıç Tarihi / Saati <span style="color:#dc3545">*</span></label>
                    <div class="date-time-group">
                        <asp:TextBox ID="txtBaslangicTarihi" onkeypress="return DateControl(this);" runat="server"
                            MaxLength="10" autocomplete="off" placeholder="GG.AA.YYYY" CssClass="form-control" />
                        <asp:Label Text="/" runat="server" />
                        <asp:TextBox ID="txtBaslangicSaati" runat="server" onchange="return saatControl(this)" Width="80px"
                            CssClass="form-control" placeholder="SS:DD" />
                    </div>

                    <div class="validation-message">
                        <asp:RequiredFieldValidator ID="rqBaslangicTarih" ErrorMessage="Lütfen Tarih Alanını Doldurunuz"
                            ForeColor="#dc3545" ControlToValidate="txtBaslangicTarihi"
                            Display="Dynamic" ValidationGroup="vG0" runat="server" />
                        <asp:RangeValidator ID="rvStartDate" runat="server" ControlToValidate="txtBaslangicTarihi"
                            ErrorMessage="Tarih formatı gg.aa.yyyy olmalı" MaximumValue="01.01.2199" MinimumValue="01.01.1900"
                            Type="Date" Display="Dynamic" ValidationGroup="vG0" ForeColor="#dc3545"></asp:RangeValidator>
                        <asp:MaskedEditValidator ID="MaskedEditValidatorBas" runat="server"
                            ControlExtender="MaskedBitisSaati" ControlToValidate="txtBaslangicSaati"
                            IsValidEmpty="True"
                            InvalidValueMessage="Lütfen Saat Alanını Eksiksiz ve Doğru Doldurunuz."
                            ForeColor="#dc3545"
                            ValidationGroup="vG0"
                            ValidationExpression="^(2[0-3]|[01]?[0-9]):([0-5]?[0-9])$"
                            Display="Dynamic">
                        </asp:MaskedEditValidator>
                        <asp:ValidatorCalloutExtender ID="rvStartDate_ValidatorCalloutExtender" runat="server"
                            Enabled="True" TargetControlID="rvStartDate">
                        </asp:ValidatorCalloutExtender>
                        <asp:CalendarExtender ID="CalendarExtender1" runat="server" CssClass="ajax__calendar"
                            Format="dd.MM.yyyy" TargetControlID="txtBaslangicTarihi"></asp:CalendarExtender>
                        <asp:MaskedEditExtender ID="MaskedBaslangicSaati" runat="server"
                            TargetControlID="txtBaslangicSaati"
                            ClearMaskOnLostFocus="false"
                            MaskType="Time"
                            Mask="99:99"
                            MessageValidatorTip="true"
                            InputDirection="LeftToRight"
                            ErrorTooltipEnabled="True"></asp:MaskedEditExtender>
                    </div>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Bitiş Tarihi / Saati <span style="color:#dc3545">*</span></label>
                    <div class="date-time-group">
                        <asp:TextBox ID="txtBitisTarihi" onkeypress="return DateControl(this);" runat="server"
                            MaxLength="10" autocomplete="off" placeholder="GG.AA.YYYY" CssClass="form-control" />
                        <asp:Label Text="/" runat="server" />
                        <asp:TextBox ID="txtBitisSaati" runat="server" Width="80px" CssClass="form-control" placeholder="SS:DD" />
                    </div>

                    <div class="validation-message">
                        <asp:RequiredFieldValidator ErrorMessage="Lütfen Tarih Alanını Doldurunuz"
                            ForeColor="#dc3545" ControlToValidate="txtBitisTarihi"
                            Display="Dynamic" ValidationGroup="vG0" runat="server" />
                        <asp:RangeValidator ID="rvEndDate" runat="server" ControlToValidate="txtBitisTarihi"
                            ErrorMessage="Tarih formatı gg.aa.yyyy olmalı" MaximumValue="01.01.2199" MinimumValue="01.01.1900"
                            Type="Date" Display="Dynamic" ValidationGroup="vG0" ForeColor="#dc3545"></asp:RangeValidator>
                        <asp:MaskedEditValidator ID="MaskedEditValidatorBitis" runat="server"
                            ControlExtender="MaskedBitisSaati" ControlToValidate="txtBitisSaati"
                            IsValidEmpty="True"
                            ForeColor="#dc3545"
                            ValidationGroup="vG0"
                            InvalidValueMessage="Lütfen Saat Alanını Eksiksiz ve Doğru Doldurunuz."
                            ValidationExpression="^(2[0-3]|[01]?[0-9]):([0-5]?[0-9])$"
                            Display="Dynamic">
                        </asp:MaskedEditValidator>
                        <asp:ValidatorCalloutExtender ID="rvEndDate_ValidatorCalloutExtender" runat="server"
                            Enabled="True" TargetControlID="rvEndDate">
                        </asp:ValidatorCalloutExtender>
                        <asp:CalendarExtender ID="EndDatePicker_CalendarExtender" runat="server" CssClass="ajax__calendar"
                            Format="dd.MM.yyyy" TargetControlID="txtBitisTarihi"></asp:CalendarExtender>
                        <asp:MaskedEditExtender ID="MaskedBitisSaati" runat="server"
                            TargetControlID="txtBitisSaati"
                            ClearMaskOnLostFocus="false"
                            MaskType="Time"
                            Mask="99:99"
                            MessageValidatorTip="true"
                            InputDirection="LeftToRight"
                            ErrorTooltipEnabled="True"></asp:MaskedEditExtender>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <asp:Button ID="btnListele" Text="Listele" BackColor="#5c2d91" ForeColor="White"
                    CssClass="btn-primary" OnClick="btnListele_Click"
                    OnClientClick="validasyonGenel()" runat="server" />
            </div>
        </div>

        <div class="report-container results-section">
            <div class="grid-container">
                <dx:ASPxGridView ID="grdTalepler" KeyFieldName="REQUEST_ID" Width="100%" runat="server">
                    <Columns>
                        <dx:GridViewDataColumn Caption="TALEP ID" FieldName="AKIS_ID" VisibleIndex="1"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="TS KODU" FieldName="TS_KODU" VisibleIndex="2"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="DEPO KODU" FieldName="DEPO_KODU" VisibleIndex="3"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="TALEP TARİHİ" FieldName="TARIH" VisibleIndex="4"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="TALEP EDEN" FieldName="NAME_SURNAME" VisibleIndex="5"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="BÖLGE" FieldName="BOLGE" VisibleIndex="6"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="İL" FieldName="IL" VisibleIndex="7"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="TS ADI" FieldName="TS_ADI" VisibleIndex="8"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="MALZEME ADI" FieldName="MALZEME_ADI" VisibleIndex="9"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="MALZEME KODU" FieldName="MALZEME_KODU" VisibleIndex="10"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="MALZEME ADEDİ" FieldName="EKIPMAN_ADET" VisibleIndex="11"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="ACIKLAMA" FieldName="ACIKLAMA" VisibleIndex="12"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="DEPO ADET" FieldName="DEPO_ADET" VisibleIndex="13" Visible="false" ></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="ORTALAMA" FieldName="ORTALAMA" VisibleIndex="14" Visible="false"></dx:GridViewDataColumn>
                    </Columns>
                    <Settings ShowFilterRow="True" ShowGroupPanel="True" ShowFilterBar="Auto" ShowFilterRowMenu="True" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                    <SettingsBehavior AllowSelectSingleRowOnly="true" ConfirmDelete="true" />
                    <Styles>
                        <Header BackColor="#5c2d91" ForeColor="White"></Header>
                        <FilterRow Paddings-Padding="4px"></FilterRow>
                        <Row Paddings-Padding="6px"></Row>
                    </Styles>
                </dx:ASPxGridView>
            </div>

            <div class="action-buttons" style="justify-content: flex-end;">
                <dx:ASPxGridViewExporter ID="gveExportToExcel" GridViewID="grdTalepler" runat="server"></dx:ASPxGridViewExporter>
                <asp:Button BackColor="#28a745" ForeColor="White" CssClass="btn-export" ID="excelBtn" OnClick="excelBtn_Click" runat="server" Text="Excel'e Aktar" />
            </div>
        </div>

        <!-- Hidden controls to maintain compatibility -->
        <div style="display:none;">
            <table id="tblControls" runat="server"></table>
            <table id="tblGrid" runat="server"></table>
            <asp:Panel ID="pnlExcelButton" runat="server"></asp:Panel>
        </div>
    </asp:Panel>

</asp:Content>

