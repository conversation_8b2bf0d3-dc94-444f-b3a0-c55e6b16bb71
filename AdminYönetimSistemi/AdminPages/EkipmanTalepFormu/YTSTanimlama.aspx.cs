﻿using DevExpress.Web;
using FormHelper.YetkiHelper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using WebCore;

namespace AracTakipSistemi.AdminPages.EkipmanTalepFormu
{
    public partial class YTSTanimlama : UICorePage
    {
        protected void Page_Load(object sender, EventArgs e)
        {

            if (!IsPostBack)
            {
                DataSourceRefresh();
                DataTable dtbInaktifeCekilmisler = FormHelper.EkipmanTalepFormu.YtsTanimlamaHelper.StokUygInaktifeCekilmisMi();

                if (dtbInaktifeCekilmisler.Rows.Count > 0) // Inaktife cekilmis demektir
                {
                    string mesaj = "";
                    foreach (DataRow dr in dtbInaktifeCekilmisler.Rows)
                    {
                        mesaj += dr["SALES_AGENT_CODE"].ToString() + " Kodlu  " + dr["UNVAN"].ToString() + " Bayisi Stok Uygulamasında Pasife Çekilmiştir. \\n";
                    }
                    mesaj += "Lütfen kontrol ediniz.";
                    ScriptManager.RegisterStartupScript(this.Page, GetType(), "javascript", "alert('" + mesaj + "')", true);
                }


            }
            if (IsCallback)
            {
                DataSourceRefresh();
            }

                ((SiteMaster)this.Master).SayfaBaslikAt("YTS Tanımlama");

        }

        private void DataSourceRefresh()
        {
            grdYtsTanimlari.DataSource = FormHelper.EkipmanTalepFormu.YtsTanimlamaHelper.GetYtsFromDBS();
            grdYtsTanimlari.DataBind();
        }

        protected void lnkGuncelle_Click(object sender, EventArgs e)
        {
            bool retVal = true;
            LinkButton lnkButton = (LinkButton)sender;
            int index = (lnkButton.NamingContainer as GridViewDataItemTemplateContainer).VisibleIndex;
            GridViewDataItemTemplateContainer templateContainer = (GridViewDataItemTemplateContainer)lnkButton.NamingContainer;

            int rowVisibleIndex = templateContainer.VisibleIndex;
            string yts_Id = templateContainer.Grid.GetRowValues(rowVisibleIndex, "ID").ToString();
            string durum = templateContainer.Grid.GetRowValues(rowVisibleIndex, "ADMIN_DURUM").ToString();
            string stokDurum = templateContainer.Grid.GetRowValues(rowVisibleIndex, "STOK_UYG_DURUM").ToString();

            //durum = "AKTIF";

            if (stokDurum == "INAKTIF" || stokDurum == "CEZALI") // DÜZENLEMEYE İZİN VERME
            {
                if (durum == "AKTIF")// STOKTA PASIFE CEKILDI, BIZDE AKTIF KALDI BIZDE DE PASİFE CEKILECEK
                {
                    try
                    {
                        FormHelper.EkipmanTalepFormu.YtsTanimlamaHelper.YTS_Sil(yts_Id);
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Başarıyla Pasifleştirildi.", false);
                        DataSourceRefresh();
                        return;
                    }
                    catch (Exception ex)
                    {
                        string exx = ex.Message;
                        ((SiteMaster)this.Master).PopupGoster("Hata", "Kayıt Pasifleştirilirken Hata Oluştu.", true);
                    }
                }
                else
                {
                    retVal = false;
                }
            }

            if (durum == "PASIF")
            {
                if (retVal)
                {
                    //aktif hale gelecek -- insert 
                    try
                    {
                        FormHelper.EkipmanTalepFormu.YtsTanimlamaHelper.YTS_Ekle(yts_Id);
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Başarıyla Aktifleştirildi.", false);
                    }
                    catch (Exception)
                    {
                        ((SiteMaster)this.Master).PopupGoster("Hata", "Kayıt Aktifleştirilirken Hata Oluştu.", true);

                    }
                }
                else
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Stok Uygulamasında Inaktif Olan Bayiler Aktifleştirilemez.", true);
                }

            }
            if (durum == "AKTIF") // aktifse tablodan silinecek
            {
                if (retVal)
                {
                    try
                    {
                        FormHelper.EkipmanTalepFormu.YtsTanimlamaHelper.YTS_Sil(yts_Id);
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Başarıyla Pasifleştirildi.", false);
                    }
                    catch (Exception ex)
                    {
                        string exx = ex.Message;
                        ((SiteMaster)this.Master).PopupGoster("Hata", "Kayıt Pasifleştirilirken Hata Oluştu.", true);
                    }
                }
                else
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Stok Uygulamasında Inaktif Olan Bayiler Pasifleştirilemez.", true);
                }
            }


            DataSourceRefresh();
        }

        protected void btnExcelAktar_Click(object sender, EventArgs e)
        {
            DataSourceRefresh();
            gveExportToExcel.GridViewID = "grdYtsTanimlari";
            gveExportToExcel.WriteXlsxToResponse();
        }
    }
}