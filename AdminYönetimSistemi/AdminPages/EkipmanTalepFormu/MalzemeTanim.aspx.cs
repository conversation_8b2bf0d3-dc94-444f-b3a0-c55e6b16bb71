﻿using CoreHelpers;
using DevExpress.Web;
using FormHelper.YetkiHelper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using WebCore;

namespace AracTakipSistemi.AdminPages.EkipmanTalepFormu
{
    public partial class MalzemeTanım : UICorePage
    {
        protected void Page_Load(object sender, EventArgs e)
        {

            //malzeme gridinin doldurulması
            if (!IsPostBack)
            {
                DataSourceRefresh();
            }

            if (IsCallback)
            {
                DataSourceRefresh();
            }

                ((SiteMaster)this.Master).SayfaBaslikAt("Malzeme Tanımlama");

        }

        private void DataSourceRefresh()
        {
            grdMalzemeTanimlari.DataSource = FormHelper.EkipmanTalepFormu.MalzemeTanimHelper.GetGridDataFromDBS();
            grdMalzemeTanimlari.DataBind();
        }

        protected void lnkGuncelle_Click(object sender, EventArgs e)
        {
            bool retVal = true;
            LinkButton lnkButton = (LinkButton)sender;
            int index = (lnkButton.NamingContainer as GridViewDataItemTemplateContainer).VisibleIndex;
            GridViewDataItemTemplateContainer templateContainer = (GridViewDataItemTemplateContainer)lnkButton.NamingContainer;

            int rowVisibleIndex = templateContainer.VisibleIndex;
            string MalzemeId = templateContainer.Grid.GetRowValues(rowVisibleIndex, "STOCK_SPEC_ID").ToString();
            string durum = templateContainer.Grid.GetRowValues(rowVisibleIndex, "ADMIN_DURUM").ToString();
            string stokDurum = templateContainer.Grid.GetRowValues(rowVisibleIndex, "STOK_UYG_DURUM").ToString();

            
            if (stokDurum == "PASIF") // DÜZENLEMEYE İZİN VERME
            {
                if (durum == "AKTIF")// STOKTA PASIFE CEKILDI, BIZDE AKTIF KALDI BIZDE DE PASİFE CEKILECEK
                {
                    try
                    {
                        FormHelper.EkipmanTalepFormu.MalzemeTanimHelper.MalzemeSil(MalzemeId);
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Başarıyla Pasifleştirildi.", false);
                        DataSourceRefresh();
                        return;
                    }
                    catch (Exception ex)
                    {
                        string exx = ex.Message;
                        ((SiteMaster)this.Master).PopupGoster("Hata", "Kayıt Pasifleştirilirken Hata Oluştu.", true);
                    }
                }
                else
                {
                    retVal = false;
                }
                
            }
            
            if (durum == "PASIF")
            {
                if (retVal)//aktif hale gelecek -- insert 
                {
                    try
                    {

                        FormHelper.EkipmanTalepFormu.MalzemeTanimHelper.MalzemeEkle(MalzemeId);
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Başarıyla Aktifleştirildi.", false);
                    }
                    catch (Exception)
                    {
                        ((SiteMaster)this.Master).PopupGoster("Hata", "Kayıt Aktifleştirilirken Hata Oluştu.", true);

                    }
                }
                else
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Stok Uygulamasında Pasif Olan Malzemeler Aktifleştirilemez.", true);
                }


            }
            if (durum == "AKTIF") // aktifse tablodan silinecek
            {
                if (retVal)
                {
                    try
                    {
                        FormHelper.EkipmanTalepFormu.MalzemeTanimHelper.MalzemeSil(MalzemeId);
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Başarıyla Pasifleştirildi.", false);
                    }
                    catch (Exception ex)
                    {
                        string exx = ex.Message;
                        ((SiteMaster)this.Master).PopupGoster("Hata", "Kayıt Pasifleştirilirken Hata Oluştu.", true);
                    }
                }
                else
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Stok Uygulamasında Pasif Olan Malzemeler Pasifleştirilemez.", true);
                }


            }


            DataSourceRefresh();
        }

        protected void btnExcelAktar_Click(object sender, EventArgs e)
        {
            DataSourceRefresh();
            gveExportToExcel.GridViewID = "grdMalzemeTanimlari";
            gveExportToExcel.WriteXlsxToResponse();
        }
    }
}