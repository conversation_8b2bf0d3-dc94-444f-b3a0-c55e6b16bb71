﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="ExcelYukleme.aspx.cs" Inherits="AracTakipSistemi.AdminPages.EkipmanTalepFormu.ExcelYukleme" %>


<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %><%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>

<%@ MasterType VirtualPath="~/Site.master" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        /* Modern Upload Panel Styling */
        .upload-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            padding: 30px;
            width: 500px;
            margin: 20px auto;
        }

        .upload-title {
            font-size: 20px;
            font-weight: 600;
            color: #5c2d91;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }

        .upload-instruction {
            font-size: 16px;
            color: #495057;
            margin-bottom: 20px;
        }

        .upload-control {
            margin-bottom: 20px;
        }

        .file-upload {
            display: block;
            width: 100%;
            padding: 8px 0;
        }

        .checkbox-option {
            display: block;
            margin-bottom: 25px;
            font-size: 14px;
            color: #495057;
        }

        .upload-button {
            background-color: #5c2d91;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 0;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            width: 100%;
            font-size: 16px;
        }

        .upload-button:hover {
            background-color: #4b2173;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="upload-container">
        <h2 class="upload-title">Excel Yükleme</h2>

        <p class="upload-instruction">
            <asp:Literal Text="Excel dosyası yüklemek için dosya seçiniz." runat="server" />
        </p>

        <div class="upload-control">
            <asp:UpdatePanel ID="UpFormUpload" runat="server">
                <ContentTemplate>
                    <asp:FileUpload ID="ExcelYüklemeUpload" Visible="true" CssClass="file-upload" runat="server" />
                </ContentTemplate>
                <Triggers>
                    <asp:PostBackTrigger ControlID="btnUpload" />
                </Triggers>
            </asp:UpdatePanel>
        </div>

        <div class="checkbox-option">
            <asp:CheckBox ID="chckIceriAktar" Text="Tanımlı Olmayan Bayi ve Malzemeleri İçeri Aktar" runat="server" />
        </div>

        <asp:Button ID="btnUpload" CssClass="upload-button" Text="Yükle" OnClick="btnUpload_Click" runat="server" />
    </div>

    <!-- Hidden original panel to maintain compatibility -->
    <asp:Panel ID="pnlTop" Width="500px" Style="display:none;" runat="server">
    </asp:Panel>
</asp:Content>

