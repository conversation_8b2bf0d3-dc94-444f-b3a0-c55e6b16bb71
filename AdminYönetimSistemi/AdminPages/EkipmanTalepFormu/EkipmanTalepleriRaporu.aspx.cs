﻿using AdminPages;
using CoreHelpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using DataAccessLayer;
using FormHelper.YetkiHelper;
using WebCore;

namespace AracTakipSistemi.AdminPages.EkipmanTalepFormu
{
    public partial class EkipmanTalepleriRaporu : UICorePage
    {

        protected void Page_Load(object sender, EventArgs e)
        {
            ((SiteMaster)this.Master).SayfaBaslikAt("Ekipman Talepleri Raporu");

            if (IsCallback)
            {
                DropDoldur("Liste");
            }
            if (!IsPostBack)
            {
                BosDropDoldur();
            }

        }

        private void BosDropDoldur()
        {
            drpMalzeme.DataSource = FormHelper.EkipmanTalepFormu.EkipmanTalepleriRaporuHelper.GetMalzemeDropInfo(); ;
            drpMalzeme.DataBind();
            drpBayi.DataSource = FormHelper.EkipmanTalepFormu.EkipmanTalepleriRaporuHelper.GetBayiDropInfo(); ;
            drpBayi.DataBind();
        }

        protected void btnListele_Click(object sender, EventArgs e)
        {           
            DropDoldur("Liste");
        }

        private void DropDoldur(string tip)
        {           
            try
            {
                if (tip == "Excel")
                {
                    using (DataTable dtbGridTalep = kayitGetir())
                    {
                        grdTalepler.Columns[12].Visible = true;                        
                        grdTalepler.Columns[13].Visible = true;
                        grdTalepler.DataSource = dtbGridTalep;
                        grdTalepler.DataBind();
                    }
                }
                if (tip == "Liste")
                {
                    using (DataTable dtbGridTalep = kayitGetir())
                    {
                        grdTalepler.DataSource = dtbGridTalep;
                        grdTalepler.DataBind();
                    }
                }
                
            }
            catch (Exception ex)
            {
                ScriptManager.RegisterStartupScript(this.Page, GetType(), "javascript", "alert('Kayıtlar getirilirken bir hata oluştu.')",true);
            }   
        }
      
        private DataTable kayitGetir()
        {
            string malzeme = drpMalzeme.SelectedItem.Text;
            string textBaslangicTarihi = txtBaslangicTarihi.Text;
            string textBaslangicSaati = txtBaslangicSaati.Text;
            string textBitisTarihi = txtBitisTarihi.Text;
            string textBitisSaati = txtBitisSaati.Text;            
            string bayiKodu = drpBayi.SelectedValue;
            DataTable dtbGridTalep = FormHelper.EkipmanTalepFormu.EkipmanTalepleriRaporuHelper.GetMalzemeGridInfo(textBaslangicTarihi, textBitisTarihi, malzeme, bayiKodu, textBaslangicSaati, textBitisSaati);                        
            return dtbGridTalep;
        }

        protected void excelBtn_Click(object sender, EventArgs e)
        {
            if (grdTalepler == null || grdTalepler.VisibleRowCount == 0)
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Excele Aktarmak için önce rapor listelemelisiniz.", true);
            }
            else
            {
                DropDoldur("Excel");
                gveExportToExcel.GridViewID = "grdTalepler";
                gveExportToExcel.WriteXlsxToResponse();
            }
            
        }        

    }
}