﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="MalzemeTanim.aspx.cs" Inherits="AracTakipSistemi.AdminPages.EkipmanTalepFormu.MalzemeTanım" %>

<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>

<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ MasterType VirtualPath="~/Site.master" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>
        #tdExcel{
            position:relative;

        }
        #btnExcelAktar{
            position:absolute;
            left:0px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:Panel runat="server">
        <table>
            <tr>
                <td>
                    <dx:ASPxGridView ID="grdMalzemeTanimlari" KeyFieldName="STOCK_SPEC_ID" Width="100%" runat="server">
                        <Columns>
                            <dx:GridViewDataColumn Caption="ID" FieldName="STOCK_SPEC_ID" VisibleIndex="1"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="MALZEME TÜRÜ" FieldName="RESOURCE_SPEC_TYPE_CD" VisibleIndex="2"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="MALZEME KODU" FieldName="STOCK_SPEC_CD" VisibleIndex="3"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="MALZEME ADI" FieldName="NAME" VisibleIndex="4"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="AÇIKLAMA" FieldName="DESCRIPTION" VisibleIndex="5"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="STOK UYGULAMASI DURUMU" FieldName="STOK_UYG_DURUM" VisibleIndex="6"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="EKRANDAKI DURUMU" FieldName="ADMIN_DURUM" VisibleIndex="7"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="" FieldName="" VisibleIndex="7">
                                <DataItemTemplate>
                                    <asp:LinkButton ID="lnkGuncelle" Text='<%#DataBinder.Eval(Container.DataItem, "COMMAND")%>' OnClick="lnkGuncelle_Click"  runat="server" />
                                </DataItemTemplate>
                            </dx:GridViewDataColumn>
                        </Columns>
                        <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" />
                        <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                        <SettingsBehavior AllowSelectSingleRowOnly="true" ConfirmDelete="true" />
                        <Styles>
                            <Header BackColor="#C60C30" ForeColor="White"></Header>
                        </Styles>
                    </dx:ASPxGridView>
                </td>
            </tr>
            <tr>
                <td id="tdExcel">
                    <asp:Button Text="Excel'e Aktar" ID="btnExcelAktar" BackColor="#C60C30" ForeColor="White" Width="100px" Height="35px"  OnClick="btnExcelAktar_Click" runat="server" />
                    <dx:ASPxGridViewExporter ID="gveExportToExcel" GridViewID="grdMalzemeTanimlari" runat="server"></dx:ASPxGridViewExporter>
                </td>
            </tr>
        </table>
    </asp:Panel>

</asp:Content>
<%--<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>--%>
