﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="YTSTanimlama.aspx.cs" Inherits="AracTakipSistemi.AdminPages.EkipmanTalepFormu.YTSTanimlama" %>

<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>

<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>



<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>         
        #tdExcel{
            position:relative;
        }
        #btnExcelAktar{
            position:absolute;
            left:0px;           
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <asp:Panel ID="pnlTop" runat="server">
        <table id="tblTop">
            <tr>
                <td>
                    <dx:aspxgridview id="grdYtsTanimlari" keyfieldname="ID" width="100%" runat="server">
                        <columns>
                            <dx:gridviewdatacolumn caption="ID" fieldname="ID" visibleindex="1"></dx:gridviewdatacolumn>
                            <dx:gridviewdatacolumn caption="YTS KODU" fieldname="SALES_AGENT_CODE" visibleindex="2"></dx:gridviewdatacolumn>
                            <dx:gridviewdatacolumn caption="YTS ADI" fieldname="UNVAN" visibleindex="3"></dx:gridviewdatacolumn>
                            <dx:gridviewdatacolumn caption="BOLGE" fieldname="BOLGE_KODU_ACIKLAMA" visibleindex="4"></dx:gridviewdatacolumn>
                            <dx:gridviewdatacolumn caption="IL" fieldname="IL" visibleindex="5"></dx:gridviewdatacolumn>
                            <dx:gridviewdatacolumn caption="DEPO KODU" fieldname="DEPO_KODU" visibleindex="6"></dx:gridviewdatacolumn>
                            <dx:gridviewdatacolumn caption="STOK UYGULAMASI DURUMU" fieldname="STOK_UYG_DURUM" visibleindex="8"></dx:gridviewdatacolumn>
                            <dx:gridviewdatacolumn caption="EKRANDAKI DURUMU" fieldname="ADMIN_DURUM" visibleindex="9"></dx:gridviewdatacolumn>
                            <dx:gridviewdatacolumn caption="" fieldname="" visibleindex="10">
                                <dataitemtemplate>
                                    <asp:LinkButton ID="lnkGuncelle" Text='<%#DataBinder.Eval(Container.DataItem,"COMMAND")%>' OnClick="lnkGuncelle_Click" runat="server" />
                                </dataitemtemplate>
                            </dx:gridviewdatacolumn>
                        </columns>
                        <settings showfilterrow="true" showfilterbar="Auto" showfilterrowmenu="true" />
                        <settingstext groupcontinuedonnextpage="(Devamı sonraki sayfada)" grouppanel="Gruplamak istediğiniz alanları buraya sürükleyin" emptydatarow="Herhangi bir kayıt bulunmamaktadır" filterbarclear="Temizle" commandselect="İçerir" />
                        <settingsbehavior allowselectsinglerowonly="true" confirmdelete="true" />
                        <styles>
                            <header backcolor="#C60C30" forecolor="White"></header>
                        </styles>
                    </dx:aspxgridview>
                    
                </td>
            </tr>
            <tr>
                <td id="tdExcel">
                    <asp:Button Text="Excel'e Aktar" ID="btnExcelAktar" OnClick="btnExcelAktar_Click" BackColor="#C60C30" Height="35px" Width="100px" ForeColor="White"  runat="server" />
                    <dx:ASPxGridViewExporter ID="gveExportToExcel" GridViewID="grdYtsTanimlari" runat="server"></dx:ASPxGridViewExporter>
                </td>
            </tr>
        </table>
    </asp:Panel>
</asp:Content>
<%--<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>--%>
