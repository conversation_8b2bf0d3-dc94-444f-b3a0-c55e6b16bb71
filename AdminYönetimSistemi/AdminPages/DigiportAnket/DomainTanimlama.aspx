﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="DomainTanimlama.aspx.cs" Inherits="AracTakipSistemi.AdminPages.DigiportAnket.DomainTanimlama" %>


<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>

<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>


<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <style>
        .container {
            display: grid;
            grid-template-rows: 500px;
            grid-template-columns: 1200px;
        }

        .domainGroup {
            display: flex;
            justify-content: center;
            gap: 2rem;
            font-size: large;
            align-items: flex-start
        }

        .contentMain {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            gap: 4rem;
        }

        .adminKaydet {
            padding: 0 500px;
        }

        .domainTextBox {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="container">
        <div class="contentMain">
            <div class="domainGroup">
                <div class="domainBaslik">
                    Domain:                  
                </div>
                <div class="domainTextBox">
                    <div>
                        <asp:TextBox ID="txtDomain" runat="server" />
                    </div>
                    <div>
                        <asp:RequiredFieldValidator ErrorMessage="Lütfen Bu Alanı Doldurunuz." ValidationGroup="vg1" ForeColor="#C60C30" ControlToValidate="txtDomain" runat="server" />
                    </div>
                </div>
            </div>

            <div class="adminKaydet">
                <uc1:AdminKaydet runat="server" ID="AdminKaydet1" validasyonGrubu="vg1" />
            </div>

            <div class="grid">
                <dx:ASPxGridView ID="grdDomain" KeyFieldName="ID" Width="50%" runat="server">
                    <Columns>
                        <dx:GridViewDataColumn Caption="DOMAIN" FieldName="DOMAIN" VisibleIndex="1"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="DÜZENLE" FieldName="" VisibleIndex="2">
                            <DataItemTemplate>
                                <asp:LinkButton ID="lnkGuncelle" Text="Düzenle" OnClick="lnkGuncelle_Click" runat="server" />
                            </DataItemTemplate>
                        </dx:GridViewDataColumn>
                    </Columns>
                    <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                    <SettingsBehavior AllowSelectSingleRowOnly="true" ConfirmDelete="true" />
                    <Styles>
                        <Header BackColor="#C60C30" ForeColor="White"></Header>
                    </Styles>
                </dx:ASPxGridView>
            </div>

        </div>        
    </div>
</asp:Content>

<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>
