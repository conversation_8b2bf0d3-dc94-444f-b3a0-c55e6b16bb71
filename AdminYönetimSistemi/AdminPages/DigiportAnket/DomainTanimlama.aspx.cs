﻿using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using DevExpress.Web;
using Entities;
using FormHelper.DigiportAnket;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using static AdminPages.AdminAbstract;

namespace AracTakipSistemi.AdminPages.DigiportAnket
{
    public partial class DomainTanimlama : AdminAbstract
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            //CoreHelpers.Yetki.yetkiTipiKontrol(ConvertionHelper.ConvertValue<bool>(Session["YetkiTipDigiportAnketAdmin"]), "Bu sayfayı görüntülemeye yetkiniz yoktur.Lütfen <EMAIL> ile irtibata geçiniz.");
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            ((SiteMaster)this.Master).SayfaBaslikAt("Digiport Anket Domain Ekleme");
           
            if (!IsPostBack)
            {
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
            }
            else { }
            Grid_Doldur();
        }

        //protected void lnkduzenle_Click(object sender, EventArgs e)
        //{
        //    int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
        //    int deger = ConvertionHelper.ConvertValue<int>(grdDomain.GetRowValues(index, "ID"));
        //    Kayit_Getir(deger);
        //}
        public override void Grid_Doldur()
        {
            grdDomain.DataSource = DIGIPORT_ANKET_DOMAIN_Helper.grdListe();
            grdDomain.DataBind();
        }
        public override void Kaydet()
        {
            Page.Validate();
            if (Page.IsValid)
            {
                Entities.DIGIPORT_ANKET_DOMAIN nesnem = new Entities.DIGIPORT_ANKET_DOMAIN();
                Esitle(nesnem);
                nesnem.KAYIT_TARIHI = DateTime.Now;
                nesnem.KAYDEDEN = LoginId;
                string YeniID = PRepository<DIGIPORT_ANKET_DOMAIN>.EntityKaydet("DT_WORKFLOW", nesnem);
                Temizle();
                nesnem = null;
                ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Edildi", false);
            }
        }
        private void Esitle(Entities.DIGIPORT_ANKET_DOMAIN nesnem)
        {
            nesnem.DOMAIN = txtDomain.Text;                  
        }

        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                Entities.DIGIPORT_ANKET_DOMAIN nesnem = PRepository<DIGIPORT_ANKET_DOMAIN>.EntityGetir("DT_WORKFLOW", ID);
                Esitle(nesnem);
                nesnem.DEGISTIRME_TARIHI = DateTime.Now;
                nesnem.DEGISTIREN = LoginId;
                PRepository<DIGIPORT_ANKET_DOMAIN>.EntityUpdateEt("DT_WORKFLOW", nesnem);
                Temizle();
                nesnem = null;
                ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Güncellendi", false);
            }
        }

        public override void Kayit_Getir(int ID)
        {
            Entities.DIGIPORT_ANKET_DOMAIN nesnem = PRepository<DIGIPORT_ANKET_DOMAIN>.EntityGetir("DT_WORKFLOW", ID);
            if (nesnem != null)
            {
                txtDomain.Text = nesnem.DOMAIN;
                AdminKaydet1.RecordId = nesnem.ID;
                AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
            }
            nesnem = null;
        }
        public override void Sil(int ID)
        {
            try
            {
                if (!FormHelper.DigiportAnket.DIGIPORT_ANKET_DOMAIN_Helper.DomainAltindaUserKaydiVarmi(ID)) // alt kırılımda kullanıcı yoksa domain silinebilir.
                {
                    PRepository<DIGIPORT_ANKET_DOMAIN>.EntitySil("DT_WORKFLOW", ID);
                    ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Silindi", false);
                }
                else // alt kırılımda kullanıcı varsa
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Domain altında tanımlı kullanıcı / kullanıcılar olduğu için silinemedi !", false);
                }
                
                Temizle();
                
            }
            catch (Exception ex)
            {
                ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt silinirken bir hata oluştu.", true);
            }
        }
        public override void Temizle()
        {
            txtDomain.Text = "";
            AdminKaydet1.RecordId = 0;
            Grid_Doldur();
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
        }

        protected void lnkGuncelle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(grdDomain.GetRowValues(index, "ID"));
            Kayit_Getir(deger);
        }
    }
}