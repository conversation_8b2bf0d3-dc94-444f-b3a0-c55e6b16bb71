﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="DomainKullaniciTanimlama.aspx.cs" Inherits="AracTakipSistemi.AdminPages.DigiportAnket.DomainKullaniciTanimlama" %>

<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>

<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>
        .container {
            display: grid;
            grid-template-rows: 500px;
            grid-template-columns: 1200px;
        }

        /*.domainGroup {
            display: flex;
            justify-content: center;
            gap: 2rem;
            font-size: large;
            align-items: center
        }*/

        .domainGroup {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            font-size: large;
            gap: 60px;
            margin-left: 40%;
        }

        .userGroup {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            font-size: large;
            gap: 3rem;
            margin-left: 40%;
        }

        .contentMain {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            gap: 5rem;
        }

        .contentEntries {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
         .adminKaydet{
            padding: 0 500px;
        }
    </style>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="container">
        <div class="contentMain">
            <div class="contentEntries">

                <div class="domainGroup">
                    <div class="domainBaslik">Domain:</div>
                    <div class="domainTextBox">
                        <asp:DropDownList ID="drpDomainList" DataTextField="DOMAIN" Width="175px" DataValueField="ID" runat="server">
                        </asp:DropDownList>
                        <asp:RequiredFieldValidator ID="reqDomainList" ErrorMessage="Lütfen Seçim Yapınız." ControlToValidate="drpDomainList" ForeColor="#C60C30" ValidationGroup="vg1" InitialValue="0" runat="server" />
                    </div>
                </div>

                <div class="userGroup">
                    <div class="domainBaslik">Kullanıcı :</div>
                    <div class="domainTextBox">
                        <asp:DropDownList ID="drpUsers" DataTextField="NAME_SURNAME" Width="175px" DataValueField="F_LOGIN_ID" runat="server">
                        </asp:DropDownList>
                        <asp:RequiredFieldValidator ID="reqUserList" ErrorMessage="Lütfen Seçim Yapınız." ValidationGroup="vg1" ControlToValidate="drpUsers" ForeColor="#C60C30" InitialValue="0" runat="server" />
                    </div>
                </div>
            </div>


            <div class="adminKaydet">
                <uc1:AdminKaydet runat="server" validasyonGrubu="vg1" ID="AdminKaydet1" />
            </div>

            <div class="grid">

                <dx:ASPxGridView ID="grdDomainUsers" KeyFieldName="DOMAIN_USER_ID" Width="50%" runat="server">

                    <Columns>
                        <dx:GridViewDataColumn Caption="DOMAIN" FieldName="DOMAIN" VisibleIndex="1"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="KULLANICI" FieldName="NAME_SURNAME" VisibleIndex="2"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="DÜZENLE" FieldName="" VisibleIndex="3">
                            <DataItemTemplate>
                                <asp:LinkButton ID="lnkGuncelle" Text="Düzenle" OnClick="lnkGuncelle_Click" runat="server" />
                            </DataItemTemplate>
                        </dx:GridViewDataColumn>
                    </Columns>
                    <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                    <SettingsBehavior AllowSelectSingleRowOnly="true" ConfirmDelete="true" />
                    <Styles>
                        <Header BackColor="#C60C30" ForeColor="White"></Header>
                    </Styles>

                </dx:ASPxGridView>
                
            </div>

        </div>        
    </div>

</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>
