﻿using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using DevExpress.Web;
using Entities;
using FormHelper.DigiportAnket;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using static AdminPages.AdminAbstract;

namespace AracTakipSistemi.AdminPages.DigiportAnket
{
    public partial class DomainKullaniciTanimlama : AdminAbstract
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            //CoreHelpers.Yetki.yetkiTipiKontrol(ConvertionHelper.ConvertValue<bool>(Session["YetkiTipDigiportAnketAdmin"]), "Bu sayfayı görüntülemeye yetkiniz yoktur.Lütfen <EMAIL> ile irtibata geçiniz.");
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            ((SiteMaster)this.Master).SayfaBaslikAt("Digiport Anket Domain Kullanıcı Ekleme");
            if (!IsPostBack)
            {
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
            }
            else { }
            Grid_Doldur();
        }

        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(grdDomainUsers.GetRowValues(index, "ID"));
            Kayit_Getir(deger);
        }
        public override void Grid_Doldur()
        {
            grdDomainUsers.DataSource = DIGIPORT_ANKET_DOMAIN_USERS_Helper.grdListe();
            grdDomainUsers.DataBind();
        }
        public override void Kaydet()
        {
            Page.Validate();
            if (Page.IsValid)
            {
                Entities.DIGIPORT_ANKET_DOMAIN_USERS nesnem = new Entities.DIGIPORT_ANKET_DOMAIN_USERS();
                Esitle(nesnem);
                nesnem.KAYIT_TARIHI = DateTime.Now;
                nesnem.KAYDEDEN = LoginId;
                string YeniID = PRepository<DIGIPORT_ANKET_DOMAIN_USERS>.EntityKaydet("DT_WORKFLOW", nesnem);
                Temizle();
                nesnem = null;
                ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Edildi", false);
            }
        }
        private void Esitle(Entities.DIGIPORT_ANKET_DOMAIN_USERS nesnem)
        {
            nesnem.DOMAIN_ID = ConvertionHelper.ConvertValue<int>(drpDomainList.SelectedValue);
            nesnem.DOMAIN_USER_LOGIN_ID = ConvertionHelper.ConvertValue<int>(drpUsers.SelectedValue);
        }
        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                Entities.DIGIPORT_ANKET_DOMAIN_USERS nesnem = PRepository<DIGIPORT_ANKET_DOMAIN_USERS>.EntityGetir("DT_WORKFLOW", ID);
                Esitle(nesnem);
               
                PRepository<DIGIPORT_ANKET_DOMAIN_USERS>.EntityUpdateEt("DT_WORKFLOW", nesnem);
                Temizle();
                nesnem = null;
                ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Güncellendi", false);
            }
        }

        public override void Kayit_Getir(int ID)
        {
            Entities.DIGIPORT_ANKET_DOMAIN_USERS nesnem = PRepository<DIGIPORT_ANKET_DOMAIN_USERS>.EntityGetir("DT_WORKFLOW", ID);
            if (nesnem != null)
            {
                GenericIslemler.KutuEsitle(drpDomainList, nesnem.DOMAIN_ID.ToString());
                GenericIslemler.KutuEsitle(drpUsers, nesnem.DOMAIN_USER_LOGIN_ID.ToString());
                AdminKaydet1.RecordId = nesnem.ID;
                AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
            }
            nesnem = null;
        }
        public override void Sil(int ID)
        {
            try
            {
                PRepository<DIGIPORT_ANKET_DOMAIN_USERS>.EntitySil("DT_WORKFLOW", ID);
                Temizle();
                ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Silindi", false);
            }
            catch (Exception)
            {
                ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt silinemedi. Alt kırılım kayıtları mevcut olabilir.<br>Ekranlarda göster seçeneğini kullanabilirsiniz.", true);
            }
        }
        public override void Temizle()
        {
            drpDomainList.DataSource = DIGIPORT_ANKET_DOMAIN_USERS_Helper.DomainDropDoldur();
            drpDomainList.DataBind();
            drpDomainList.Items.Insert(0,(new ListItem("Seçiniz..", "0")));
            drpDomainList.SelectedValue = "0";

            drpUsers.DataSource = DIGIPORT_ANKET_DOMAIN_USERS_Helper.UsersDropDoldur();
            drpUsers.DataBind();            
            drpUsers.Items.Insert(0, (new ListItem("Seçiniz..", "0")));
            drpUsers.SelectedValue = "0";

            AdminKaydet1.RecordId = 0;
            Grid_Doldur();
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
        }

        protected void lnkGuncelle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(grdDomainUsers.GetRowValues(index, "DOMAIN_USER_ID"));
            Kayit_Getir(deger);
        }
    }
}