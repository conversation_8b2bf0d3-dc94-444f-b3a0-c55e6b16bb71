﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AracModel.aspx.cs" Inherits="AracTakipSistemi.AdminPages.AracModel" %>

<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ MasterType VirtualPath="~/Site.master" %>
<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>
<%-- Export functionality is now part of DevExpress.Web.v16.2 --%>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <script language=javascript>

   function UcaseTxt(UpCstr) {
      var UCStr = UpCstr.value;
      UpCstr.value = UCStr.toUpperCase();
   }
</script>

    <asp:Panel ID="PnlDonem" GroupingText=" " runat="server" Width="100%" style="border-style: none;">
<table width="100%"><tr><td class="sagbaslik" valign="top">Araç Markası</td><td>
<asp:DropDownList ID="drp_ARAC_MARKA_ID" CssClass="drop" runat="server" OnSelectedIndexChanged="drp_ARAC_MARKA_ID_SelectedIndexChanged" style="height:35px;"></asp:DropDownList>
    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ErrorMessage="Araç Marka Alanı Boş Bırakılamaz" ValidationGroup="vg0" ControlToValidate="drp_ARAC_MARKA_ID" Display="Dynamic" InitialValue="0" ForeColor="#C60C30" SetFocusOnError=""></asp:RequiredFieldValidator><br><br>
</td></tr>
<tr><td class="sagbaslik" valign="top">Araç Modeli</td><td>
<asp:TextBox ID="txt_ARAC_MODELI" MaxLength="20" CssClass="textbox" onkeyup="UcaseTxt(this)" runat="server"></asp:TextBox>
    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ErrorMessage="Araç Modeli Alanı Boş Bırakılamaz" ValidationGroup="vg0" BorderColor="#C60C30" ControlToValidate="txt_ARAC_MODELI" ForeColor="#C60C30" SetFocusOnError=""></asp:RequiredFieldValidator>
    <br><br>
</td></tr>

<tr><td class="sagbaslik" valign="top">Aktif</td><td>
<asp:CheckBox ID="chkAktif" runat="server" /><br>
</td></tr>
    <tr> 
        <td>
            <a href="AracMarka.aspx">Araç Marka Tanımı</a>
        </td>
    </tr>

     <tr>
            <td colspan="2"><uc1:AdminKaydet runat="server" ID="AdminKaydet1" silmeUyarisi="Silmek istediğinize emin misiniz ?" validasyonGrubu="vg0" UyariGoster="true" guncellemeUyarisi="Güncellemek istediğinize emin misiniz ?" /> </td>
        </tr>

</table>
        </asp:Panel>

    <tr>
        <td>
            <dx:ASPxGridView ID="grd" runat="server" SettingsBehavior-AllowGroup="true" KeyFieldName="ID" Width="100%">
                <Columns>

                    <dx:GridViewDataColumn Caption="Araç Markası" FieldName="ARAC_MARKASI" VisibleIndex="2" />
                    <dx:GridViewDataColumn Caption="Araç Modeli" FieldName="ARAC_MODELI" VisibleIndex="3" />
                    <dx:GridViewDataColumn Caption="Aktif" FieldName="AKTIF_DURUM" VisibleIndex="4" />
                    <dx:GridViewDataTextColumn FieldName="Düzenle" VisibleIndex="5">
                        <DataItemTemplate>
                            <asp:LinkButton ID="lnkduzenle" runat="server" Text="Düzenle" OnClick="lnkduzenle_Click"></asp:LinkButton>
                        </DataItemTemplate>
                    </dx:GridViewDataTextColumn>
                </Columns>
                <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true"/>
                <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                <Styles>
                    <Header BackColor="#C60C30" ForeColor="White"></Header>
                    <Cell>
                        <Border BorderColor="#42145F"/>
                    </Cell>
                </Styles>
            </dx:ASPxGridView>
        </td>
    </tr>
</asp:Content>


