﻿using System;
using System.Web.UI;
using System.Web.UI.WebControls;
using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using Entities;
using FormHelper;
using System.Data;
using DataAccessLayer;
using FormHelper.YetkiHelper;
using DevExpress.Web;

namespace AracTakipSistemi.AdminPages
{
    public partial class AracPlaka : AdminAbstract
    {
        protected void Page_Load(object sender, EventArgs e)
        {

            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);

            this.Master.SayfaBaslikAt("Araç Plaka Tanımı");

            if (!IsPostBack)
            {
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
            }

            Grid_Doldur();

        }

        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(grd.GetRowValues(index, "ID"));
            Kayit_Getir(deger);
        }



        public override void Kaydet()
        {
            Page.Validate();
            if (Page.IsValid)
            {
                if (AracPlakaHelper.ValidateAracPlaka(txt_ARAC_PLAKASI.Text.Replace(" ", String.Empty), 0))
                {
                    this.Master.PopupGoster("Hata", "Aynı Araç Plakası Tekrar Kaydedilemez !", false);
                    return; //true dene
                }
                Entities.ATS_ARAC_PLAKASI nesnem = new Entities.ATS_ARAC_PLAKASI();
                Esitle(nesnem);
                nesnem.KAYIT_TARIHI = DateTime.Now;
                nesnem.KAYDEDEN = LoginId;
                string YeniID = PRepository<ATS_ARAC_PLAKASI>.EntityKaydet("DT_WORKFLOW", nesnem);
                Temizle();
                nesnem = null;
                this.Master.PopupGoster("Bilgi", "Kayıt Edildi", false);
            }

        }

        private void Esitle(Entities.ATS_ARAC_PLAKASI nesnem)
        {
            nesnem.ARAC_SINIFI_ID = ConvertionHelper.ConvertValue<int>(drp_ARAC_SINIFI_ID.SelectedValue);
            nesnem.ARAC_RENGI_ID = ConvertionHelper.ConvertValue<int>(drp_ARAC_RENGI_ID.SelectedValue);
            nesnem.ARAC_YILI_ID = ConvertionHelper.ConvertValue<int>(drp_ARAC_YILI_ID.SelectedValue);
            nesnem.ARAC_MARKASI_ID = ConvertionHelper.ConvertValue<int>(drp_ARAC_MARKASI_ID.SelectedValue);
            nesnem.ARAC_MODELI_ID = ConvertionHelper.ConvertValue<int>(drp_ARAC_MODELI_ID.SelectedValue);
            nesnem.ARAC_PLAKASI = txt_ARAC_PLAKASI.Text.Replace(" ", String.Empty);
            nesnem.AKTIF = ConvertionHelper.ConvertValue<int>(chkAktif.Checked).ToString();
        }

        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                Entities.ATS_ARAC_PLAKASI nesnem = PRepository<ATS_ARAC_PLAKASI>.EntityGetir("DT_WORKFLOW", ID);
                Esitle(nesnem);
                nesnem.DEGISTIRME_TARIHI = DateTime.Now;
                nesnem.DEGISTIREN = LoginId;
                PRepository<ATS_ARAC_PLAKASI>.EntityUpdateEt("DT_WORKFLOW", nesnem);
                Temizle();
                nesnem = null;
                this.Master.PopupGoster("Bilgi", "Kayıt Güncellendi", false);
            }

        }


        public override void Kayit_Getir(int ID)
        {
            Entities.ATS_ARAC_PLAKASI nesnem = PRepository<ATS_ARAC_PLAKASI>.EntityGetir("DT_WORKFLOW", ID);
            if (nesnem != null)
            {
                GenericIslemler.KutuEsitle(drp_ARAC_SINIFI_ID, nesnem.ARAC_SINIFI_ID.ToString());
                GenericIslemler.KutuEsitle(drp_ARAC_RENGI_ID, nesnem.ARAC_RENGI_ID.ToString());
                GenericIslemler.KutuEsitle(drp_ARAC_YILI_ID, nesnem.ARAC_YILI_ID.ToString());
                GenericIslemler.KutuEsitle(drp_ARAC_MARKASI_ID, nesnem.ARAC_MARKASI_ID.ToString());
                drp_ARAC_MARKASI_ID_SelectedIndexChanged(this,null);
                GenericIslemler.KutuEsitle(drp_ARAC_MODELI_ID, nesnem.ARAC_MODELI_ID.ToString());
                //drp_ARAC_MODELI_ID.SelectedValue = nesnem.ARAC_MODELI_ID.ToString();
                txt_ARAC_PLAKASI.Text = nesnem.ARAC_PLAKASI;
                chkAktif.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.AKTIF));
                AdminKaydet1.RecordId = nesnem.ID;
                AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
            }

            nesnem = null;

        }
        public override void Sil(int ID)
        {
            try
            {
                PRepository<ATS_ARAC_PLAKASI>.EntitySil("DT_WORKFLOW", ID);
                Temizle();
                this.Master.PopupGoster("Bilgi", "Kayıt Silindi", false);
            }
            catch (Exception)
            {
                this.Master.PopupGoster("Bilgi", "Kayıt silinemedi. Alt kırılım kayıtları mevcut olabilir.<br>Ekranlarda göster seçeneğini kullanabilirsiniz.", true);
            }

        }

        public override void Grid_Doldur()
        {
            grd.DataSource = AracPlakaHelper.GridListele();
            grd.DataBind();
        }

        public override void Temizle()
        {
            //drp_ARAC_SINIFI_ID.DataSource = ATS_ARAC_Helper.DTARAC_SINIFI_ID();
            //drp_ARAC_SINIFI_ID.DataBind();
            //drp_ARAC_RENGI_ID.DataSource = ATS_ARAC_PLAKASIHelper.DTARAC_RENGI_ID();
            //drp_ARAC_RENGI_ID.DataBind();
            //drp_ARAC_YILI_ID.DataSource = ATS_ARAC_PLAKASIHelper.DTARAC_YILI_ID();
            //drp_ARAC_YILI_ID.DataBind();
            //drp_ARAC_MARKASI_ID.DataSource = ATS_ARAC_PLAKASIHelper.DTARAC_MARKASI_ID();
            //drp_ARAC_MARKASI_ID.DataBind();
            //drp_ARAC_MODELI_ID.DataSource = ATS_ARAC_PLAKASIHelper.DTARAC_MODELI_ID();
            //drp_ARAC_MODELI_ID.DataBind();
            txt_ARAC_PLAKASI.Text = "";
            chkAktif.Checked = true;
            AdminKaydet1.RecordId = 0;
            Grid_Doldur();
            if (!IsPostBack)
            {
                GenericIslemler.DropDoldur("DT_WORKFLOW", AracRenkHelper.AracRenkGetirSql(), "ARAC_RENGI","ID", drp_ARAC_RENGI_ID, true);
                GenericIslemler.DropDoldur("DT_WORKFLOW", AracSınıfHelper.AracSınıfGetirSql(), "ARAC_SINIFI", "ID", drp_ARAC_SINIFI_ID, true);
                GenericIslemler.DropDoldur("DT_WORKFLOW", AracYılHelper.AracYılGetirSql(), "ARAC_YILI", "ID", drp_ARAC_YILI_ID, true);
                GenericIslemler.DropDoldur("DT_WORKFLOW", AracMarkaHelper.AracMarkaGetirSql(), "ARAC_MARKASI", "ID", drp_ARAC_MARKASI_ID, true);
                //drp_ARAC_MARKASI_ID.SelectedValue = "";
                //drp_ARAC_MARKASI_ID_SelectedIndexChanged1(this,null);
                //GenericIslemler.DropDoldur("DT_WORKFLOW", AracModelHelper.AracModelGetirSql(), "ARAC_MODELI", "ID", drp_ARAC_MODELI_ID, true);
                //drp_ARAC_MODELI_ID.SelectedValue = "";
               

            }
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;


        }

        protected void drp_ARAC_MARKASI_ID_SelectedIndexChanged(object sender, EventArgs e)
        {
            string ArcMrk = drp_ARAC_MARKASI_ID.SelectedItem.Value;
            drp_ARAC_MODELI_ID.Items.Clear();
            GenericIslemler.DropDoldur("DT_WORKFLOW", AracModelHelper.AracModelGetirSqlPlk(ArcMrk), "ARAC_MODELI", "ID", drp_ARAC_MODELI_ID, true);
            //drp_ARAC_MODELI_ID.DataSource = AracModelHelper.AracModelGetirSqlPlk

        }

        private void islemYap()
        {
            string SQL = "SELECT PLK.ID, SNF.ARAC_SINIFI,RNG.ARAC_RENGI,YIL.ARAC_YILI,MRK.ARAC_MARKASI ,MDL.ARAC_MODELI,PLK.ARAC_PLAKASI, PLK.AKTIF,PLK.KAYIT_TARIHI,PLK.DEGISTIRME_TARIHI,PLK.KAYDEDEN,PLK.DEGISTIREN, (CASE WHEN PLK.AKTIF=1 THEN 'Aktif' ELSE 'Pasif' END) AS AKTIF_DURUM FROM DT_WORKFLOW.ATS_ARAC_PLAKASI PLK JOIN DT_WORKFLOW.ATS_ARAC_SINIFI SNF ON PLK.ARAC_SINIFI_ID = SNF.ID JOIN DT_WORKFLOW.ATS_ARAC_RENGI RNG ON PLK.ARAC_RENGI_ID = RNG.ID JOIN ATS_ARAC_YILI YIL ON PLK.ARAC_YILI_ID = YIL.ID JOIN ATS_ARAC_MARKASI MRK ON PLK.ARAC_MARKASI_ID = MRK.ID JOIN ATS_ARAC_MODELI MDL ON PLK.ARAC_MODELI_ID = MDL.ID";
            DataTable dt = DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);

            string attachment = "attachment; filename=grd" + DateTime.Now.ToShortDateString().Replace(".", "") + "_" + DateTime.Now.ToShortTimeString().Replace(":", "") + ".xls";
            Response.ClearContent();
            Response.AddHeader("content-disposition", attachment);
            Response.ContentType = "application/vnd.ms-excel";
            Response.Charset = "utf-8";
            Response.ContentEncoding = System.Text.Encoding.GetEncoding("windows-1254");
            string tab = "";
            foreach (DataColumn dc in dt.Columns)
            {
                Response.Write(tab + dc.ColumnName);
                tab = "\t";
            }
            Response.Write("\n");
            int i;

            foreach (DataRow dr in dt.Rows)
            {
                tab = "";
                for (i = 0; i < dt.Columns.Count; i++)
                {
                    Response.Write(tab + dr[i].ToString());
                    tab = "\t";
                }
                Response.Write("\n");
            }
            dt = null;

            Response.End();

        }

        protected void excelBtn_Click(object sender, EventArgs e)
        {
            islemYap();
            gveExportToExcel.GridViewID = "grd";
            gveExportToExcel.WriteXlsxToResponse();
        }
    }
}
