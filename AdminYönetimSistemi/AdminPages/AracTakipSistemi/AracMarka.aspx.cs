﻿using System;
using System.Web.UI;
using System.Web.UI.WebControls;
using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using Entities;
using FormHelper;
using FormHelper.YetkiHelper;
using DevExpress.Web;

namespace AracTakipSistemi.AdminPages
{
    public partial class AracMarka : AdminAbstract
    {
        protected void Page_Load(object sender, EventArgs e)
        {

            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);

            this.Master.SayfaBaslikAt("Araç Marka Tanımı");

            if (!IsPostBack)
            {
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
            }
            Grid_Doldur();

        }

        public override void Kaydet()
        {
            Page.Validate();
            if (Page.IsValid)
            {
                if (AracMarkaHelper.ValidateAracMarka(txt_ARAC_MARKASI.Text, 0))
                {
                    this.Master.PopupGoster("Hata", "Aynı Araç Markası Tekrar Kaydedilemez !", false);
                    return; //true dene
                }

                
                Entities.ATS_ARAC_MARKASI nesnem = new Entities.ATS_ARAC_MARKASI();
                Esitle(nesnem);
                nesnem.KAYIT_TARIHI = DateTime.Now;
                nesnem.KAYDEDEN = LoginId;
                string YeniID = PRepository<ATS_ARAC_MARKASI>.EntityKaydet("DT_WORKFLOW", nesnem);
                Temizle();
                nesnem = null;
                this.Master.PopupGoster("Bilgi", "Kayıt Edildi", false);
            }
        }
        private void Esitle(Entities.ATS_ARAC_MARKASI nesnem)
        {
            nesnem.ARAC_MARKASI = txt_ARAC_MARKASI.Text;
            nesnem.AKTIF = ConvertionHelper.ConvertValue<int>(chkAktif.Checked).ToString();
        }

        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(grd.GetRowValues(index, "ID"));
            Kayit_Getir(deger);
        }


        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                Entities.ATS_ARAC_MARKASI nesnem = PRepository<ATS_ARAC_MARKASI>.EntityGetir("DT_WORKFLOW", ID);
                Esitle(nesnem);
                nesnem.DEGISTIRME_TARIHI = DateTime.Now;
                nesnem.DEGISTIREN = LoginId;
                PRepository<ATS_ARAC_MARKASI>.EntityUpdateEt("DT_WORKFLOW", nesnem);
                Temizle();
                nesnem = null;
                this.Master.PopupGoster("Bilgi", "Kayıt Güncellendi", false);
            }
        }

        public override void Kayit_Getir(int ID)
        {
            Entities.ATS_ARAC_MARKASI nesnem = PRepository<ATS_ARAC_MARKASI>.EntityGetir("DT_WORKFLOW", ID);
            if (nesnem != null)
            {
                txt_ARAC_MARKASI.Text = nesnem.ARAC_MARKASI;
                chkAktif.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.AKTIF));
                AdminKaydet1.RecordId = nesnem.ID;
                AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
            }
            nesnem = null;
        }
        public override void Sil(int ID)
        {
            try
            {
                
                if (!AracMarkaHelper.ValidateDeletingControl(ID) && !AracMarkaHelper.ValidationDeletingControl_Marka(ID))
                {
                    PRepository<ATS_ARAC_MARKASI>.EntitySil("DT_WORKFLOW", ID);
                    Temizle();
                    this.Master.PopupGoster("Bilgi", "Kayıt Silindi", false);
                }
                else
                {
                    this.Master.PopupGoster("Bilgi", "Bu Marka İle Yaplmış Araç Kayıtı Olduğu İçin Kayıt Silinemedi!", false);
                }
            }
            catch (Exception)
            {
                this.Master.PopupGoster("Bilgi", "Kayıt silinemedi. Alt kırılım kayıtları mevcut olabilir.<br>Ekranlarda göster seçeneğini kullanabilirsiniz.", true);
            }
        }
        public override void Temizle()
        {
            txt_ARAC_MARKASI.Text = "";
            chkAktif.Checked = true;
            AdminKaydet1.RecordId = 0;
            Grid_Doldur();
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
        }

        public override void Grid_Doldur()
        {
            grd.DataSource = AracMarkaHelper.GridListele();
            grd.DataBind();
        }

    }
}