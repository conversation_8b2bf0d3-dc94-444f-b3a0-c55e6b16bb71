﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AracYıl.aspx.cs" Inherits="AracTakipSistemi.AdminPages.AracYıl" %>

<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ MasterType VirtualPath="~/Site.master" %>
<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">


    <script type="text/javascript">
        function isNumberKey(evt) {
            var charCode = (evt.which) ? evt.which : evt.keyCode;
            if (charCode > 31 && (charCode < 48 || charCode > 57))
                return false;
            return true;
        }
    </script>

    <%--const textBox = document.getElementById("txt_ARAC_YILI");
    textBox.value = '';--%>

    <asp:Panel ID="PnlDonem" GroupingText=" " runat="server" Width="100%" Style="border-style: none;">

        <table width="100%">
            <tr>
                <td class="sagbaslik" valign="top">Araç Yılı</td>
                <td>
                    <asp:TextBox ID="txt_ARAC_YILI" MaxLength="4" CssClass="textbox" onkeypress="return isNumberKey(event)" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ValidationGroup="vg0" ErrorMessage="Araç Sınıf Alanı Boş Bırakılamaz" ControlToValidate="txt_ARAC_YILI" ForeColor="#C60C30" Display="Dynamic"></asp:RequiredFieldValidator>
                    <br>
                    <br>
                </td>
            </tr>

            <tr>
                <td class="sagbaslik" valign="top">Aktif</td>
                <td>
                    <asp:CheckBox ID="chkAktif" runat="server" /><br>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <uc1:AdminKaydet runat="server" ID="AdminKaydet1" silmeUyarisi="Silmek istediğinize emin misiniz ?" validasyonGrubu="vg0" UyariGoster="true" guncellemeUyarisi="Güncellemek istediğinize emin misiniz ?" />
                </td>
            </tr>
        </table>
    </asp:Panel>
    <table>
        <tr>
            <td>
                <dx:ASPxGridView ID="grd" runat="server" SettingsBehavior-AllowGroup="true" KeyFieldName="ID" Width="100%">
                    <Columns>

                        <dx:GridViewDataColumn Caption="Araç Yılı" FieldName="ARAC_YILI" VisibleIndex="2" />
                        <dx:GridViewDataColumn Caption="Aktif" FieldName="AKTIF_DURUM" VisibleIndex="3" />
                        <dx:GridViewDataTextColumn FieldName="Düzenle" VisibleIndex="4">
                            <DataItemTemplate>
                                <asp:LinkButton ID="LinkButton1" runat="server" Text="Düzenle" OnClick="lnkduzenle_Click"></asp:LinkButton>
                            </DataItemTemplate>
                        </dx:GridViewDataTextColumn>
                    </Columns>
                    <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                    <Styles>
                        <Header BackColor="#C60C30" ForeColor="White"></Header>
                        <Cell>
                            <Border BorderColor="#42145F" />
                        </Cell>
                    </Styles>
                </dx:ASPxGridView>
            </td>
        </tr>
    </table>

</asp:Content>
