﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="AracPlaka.aspx.cs" Inherits="AracTakipSistemi.AdminPages.AracPlaka" %>

<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ MasterType VirtualPath="~/Site.master" %>
<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <script language="javascript">

        function UcaseTxt(UpCstr) {
            var UCStr = UpCstr.value;
            UpCstr.value = UCStr.toUpperCase();
        }
    </script>

    <asp:Panel ID="PnlDonem" GroupingText=" " runat="server" Style="border-style: none;">

        <table>
            <tr>
                <td>
                    <asp:Panel ID="pnlControls" runat="server">
                        <table id="tblControls">
                            <tr>
                                <td class="sagbaslik" valign="top">Araç Sınıfı</td>
                                <td>
                                    <asp:DropDownList ID="drp_ARAC_SINIFI_ID" CssClass="drop" runat="server" style="height:35px;"></asp:DropDownList>
                                    <br>
                                </td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator7" runat="server" ErrorMessage="Araç Sınıfı Alanı Boş Bırakılamaz" ControlToValidate="drp_ARAC_SINIFI_ID" Display="Dynamic" InitialValue="0" ValidationGroup="vg0" ForeColor="#C60C30" SetFocusOnError="True"></asp:RequiredFieldValidator><br>
                                </td>
                            </tr>
                            <tr>
                                <td class="sagbaslik" valign="top">Araç Rengi</td>
                                <td>
                                    <asp:DropDownList ID="drp_ARAC_RENGI_ID" CssClass="drop" runat="server" style="height:35px;"></asp:DropDownList>
                                    <br>
                                </td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator8" runat="server" ErrorMessage="Araç Rengi Alanı Boş Bırakılamaz" ControlToValidate="drp_ARAC_RENGI_ID" Display="Dynamic" InitialValue="0" ValidationGroup="vg0" ForeColor="#C60C30" SetFocusOnError="True"></asp:RequiredFieldValidator><br>
                                </td>
                            </tr>
                            <tr>
                                <td class="sagbaslik" valign="top">Araç Yılı</td>
                                <td>
                                    <asp:DropDownList ID="drp_ARAC_YILI_ID" CssClass="drop" runat="server" style="height:35px;"></asp:DropDownList>
                                    <br>
                                </td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator9" runat="server" ErrorMessage="Araç Yıl Alanı Boş Bırakılamaz" ControlToValidate="drp_ARAC_YILI_ID" Display="Dynamic" InitialValue="0" ValidationGroup="vg0" ForeColor="#C60C30" SetFocusOnError="True"></asp:RequiredFieldValidator><br>
                                </td>
                            </tr>
                            <tr>
                                <td class="sagbaslik" valign="top">Araç Markası</td>
                                <td style="width: auto">
                                    <asp:DropDownList ID="drp_ARAC_MARKASI_ID" CssClass="drop" runat="server" OnSelectedIndexChanged="drp_ARAC_MARKASI_ID_SelectedIndexChanged" AutoPostBack="true" style="height:35px;"></asp:DropDownList>
                                    <br>
                                </td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator10" runat="server" ErrorMessage="Araç Marka Alanı Boş Bırakılamaz" ControlToValidate="drp_ARAC_MARKASI_ID" Display="Dynamic" InitialValue="0" ValidationGroup="vg0" ForeColor="#C60C30" SetFocusOnError="True"></asp:RequiredFieldValidator><br>
                                </td>
                            </tr>
                            <tr>
                                <td class="sagbaslik" valign="top">Araç Modeli</td>
                                <td>
                                    <asp:DropDownList ID="drp_ARAC_MODELI_ID" CssClass="drop" AutoPostBack="true" DataValueField="ID" DataTextField="ARAC_MODELI" runat="server" style="height:35px;"></asp:DropDownList>
                                    <br>
                                </td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator11" runat="server" ErrorMessage="Araç Modeli Alanı Boş Bırakılamaz" ControlToValidate="drp_ARAC_MODELI_ID" Display="Dynamic" InitialValue="0" ForeColor="#C60C30" ValidationGroup="vg0" SetFocusOnError="True"></asp:RequiredFieldValidator><br>
                                </td>
                            </tr>
                            <tr>
                                <td class="sagbaslik" valign="top">Araç Plakası</td>
                                <td>
                                    <asp:TextBox ID="txt_ARAC_PLAKASI" MaxLength="20" CssClass="textbox" onkeyup="UcaseTxt(this)" runat="server"></asp:TextBox>
                                    <br>
                                </td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator12" runat="server" ErrorMessage="Araç Plaka Alanı Boş Bırakılamaz" ControlToValidate="txt_ARAC_PLAKASI" Display="Dynamic" ForeColor="#C60C30" ValidationGroup="vg0" SetFocusOnError="True"></asp:RequiredFieldValidator><br>
                                </td>
                            </tr>
                            <tr>
                                <td class="sagbaslik" valign="top">Aktif</td>
                                <td>
                                    <asp:CheckBox ID="chkAktif" runat="server" /><br>
                                </td>
                            </tr>
                        </table>
                    </asp:Panel>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <uc1:AdminKaydet runat="server" ID="AdminKaydet1" silmeUyarisi="Silmek istediğinize emin misiniz ?" validasyonGrubu="vg0" UyariGoster="true" guncellemeUyarisi="Güncellemek istediğinize emin misiniz ?" />
                </td>
            </tr>

        </table>
    </asp:Panel>
    <table>
        <tr>
            <td>
                <dx:ASPxGridView ID="grd" SettingsBehavior-AllowGroup="true" runat="server" KeyFieldName="ID" Width="100%">
                    <Columns>

                        <dx:GridViewDataColumn Caption="Araç Sınıfı" FieldName="ARAC_SINIFI" VisibleIndex="2" />
                        <dx:GridViewDataColumn Caption="Araç Rengi" FieldName="ARAC_RENGI" VisibleIndex="3" />
                        <dx:GridViewDataColumn Caption="Araç Yılı" FieldName="ARAC_YILI" VisibleIndex="4" />
                        <dx:GridViewDataColumn Caption="Araç Markası" FieldName="ARAC_MARKASI" VisibleIndex="5" />
                        <dx:GridViewDataColumn Caption="Araç Model" FieldName="ARAC_MODELI" VisibleIndex="6" />
                        <dx:GridViewDataColumn Caption="Araç Plakası" FieldName="ARAC_PLAKASI" VisibleIndex="7" />
                        <dx:GridViewDataColumn Caption="Aktif" FieldName="AKTIF_DURUM" VisibleIndex="8" />
                        <dx:GridViewDataTextColumn FieldName="Düzenle" VisibleIndex="9">
                            <DataItemTemplate>
                                <asp:LinkButton ID="lnkduzenle" runat="server" Text="Düzenle" OnClick="lnkduzenle_Click"></asp:LinkButton>
                            </DataItemTemplate>
                        </dx:GridViewDataTextColumn>
                    </Columns>
                    <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                    <Styles>
                        <Header BackColor="#C60C30" ForeColor="White"></Header>
                        <Cell>
                            <Border BorderColor="#42145F" />
                        </Cell>
                    </Styles>
                </dx:ASPxGridView>
            </td>
        </tr>
        <tr>
            <td>
                <dx:ASPxGridViewExporter ID="gveExportToExcel" GridViewID="grd" runat="server"></dx:ASPxGridViewExporter>
            </td>
        </tr>
        <tr>
            <td>
                <asp:Button BackColor="#C2050F" ForeColor="White" ID="excelBtn" OnClick="excelBtn_Click" runat="server" Text="Excel'e Aktar" />
            </td>
        </tr>
    </table>

</asp:Content>
