﻿using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using FormHelper;
using DevExpress.Web;
using FormHelper.YetkiHelper;

namespace AracTakipSistemi.AdminPages
{
    public partial class AracSınıf : AdminAbstract
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            this.Master.SayfaBaslikAt("Araç Sınıf Tanımı");

            if (!IsPostBack)
            {
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
            }
            Grid_Doldur();

        }
        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(grd.GetRowValues(index, "ID"));
            Kayit_Getir(deger);
        }

        public override void Kaydet()
        {
            
            Page.Validate();
            if (Page.IsValid)
            {
                if (AracSınıfHelper.ValidateAracSinif(txt_ARAC_SINIFI.Text,0))
                {
                    this.Master.PopupGoster("Hata", "Aynı Araç Sınıfı Tekrar Kaydedilemez !", false);
                    return; //true dene
                }
                

                Entities.ATS_ARAC_SINIFI nesnem = new Entities.ATS_ARAC_SINIFI();
                Esitle(nesnem);
                nesnem.KAYIT_TARIHI = DateTime.Now;
                nesnem.KAYDEDEN = LoginId;
                string YeniID = PRepository<ATS_ARAC_SINIFI>.EntityKaydet("DT_WORKFLOW", nesnem);
                Temizle();
                nesnem = null;
                this.Master.PopupGoster("Bilgi", "Kayıt Edildi", false);
            }
        }
        private void Esitle(Entities.ATS_ARAC_SINIFI nesnem)
        {
            nesnem.ARAC_SINIFI = txt_ARAC_SINIFI.Text;
            nesnem.AKTIF = ConvertionHelper.ConvertValue<int>(chkAktif.Checked).ToString();
        }
        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                if (AracSınıfHelper.ValidateAracSinif(txt_ARAC_SINIFI.Text, ID))
                {
                    this.Master.PopupGoster("Hata", "Aynı Araç Sınıfı Tekrar Kaydedilemez !", false); //true dene
                }
                Entities.ATS_ARAC_SINIFI nesnem = PRepository<ATS_ARAC_SINIFI>.EntityGetir("DT_WORKFLOW", ID);
                Esitle(nesnem);
                nesnem.DEGISTIRME_TARIHI = DateTime.Now;
                nesnem.DEGISTIREN = LoginId;
                PRepository<ATS_ARAC_SINIFI>.EntityUpdateEt("DT_WORKFLOW", nesnem);
                Temizle();
                nesnem = null;
                this.Master.PopupGoster("Bilgi", "Kayıt Güncellendi", false);
            }
        }

        public override void Kayit_Getir(int ID)
        {
            Entities.ATS_ARAC_SINIFI nesnem = PRepository<ATS_ARAC_SINIFI>.EntityGetir("DT_WORKFLOW", ID);
            if (nesnem != null)
            {
                txt_ARAC_SINIFI.Text = nesnem.ARAC_SINIFI;
                chkAktif.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.AKTIF));
                AdminKaydet1.RecordId = nesnem.ID;
                AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
            }
            nesnem = null;
        }
        public override void Sil(int ID)
        {
            try
            {
                if (!AracSınıfHelper.ValidateDeletingControl(ID))
                {

                    PRepository<ATS_ARAC_SINIFI>.EntitySil("DT_WORKFLOW", ID);
                    Temizle();
                    this.Master.PopupGoster("Bilgi", "Kayıt Silindi", false);
                }
                else
                {
                    this.Master.PopupGoster("Bilgi", "Bu Sınıf İle Yaplmış Araç Kayıtı Olduğu İçin Kayıt Silinemedi!", false);
                }
            }
            catch (Exception)
            {
                this.Master.PopupGoster("Bilgi", "Kayıt silinemedi. Alt kırılım kayıtları mevcut olabilir.<br>Ekranlarda göster seçeneğini kullanabilirsiniz.", true);
            }
        }
        public override void Temizle()
        {
            txt_ARAC_SINIFI.Text = "";
            chkAktif.Checked = true;
            AdminKaydet1.RecordId = 0;
            Grid_Doldur();
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
        }


        public override void Grid_Doldur()
        {
            grd.DataSource = AracSınıfHelper.GridListele();
            grd.DataBind();
        }
    }
}