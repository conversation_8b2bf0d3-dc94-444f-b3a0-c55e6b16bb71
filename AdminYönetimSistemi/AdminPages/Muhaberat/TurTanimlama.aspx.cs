﻿using AdminPages;
using AdminUserCtrl;
using CoreHelpers;
using DevExpress.Web;
using Entities.Muhaberat;
using FormHelper.Muhaberat;
using FormHelper.YetkiHelper;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace AracTakipSistemi.AdminPages.Muhaberat
{
    public partial class TurTanimlama : AdminAbstract
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            AdminKaydet1.btnKaydetClick = new delegeButon(Kaydet);
            ((Button)AdminKaydet1.FindControl("btnKaydet")).CausesValidation = false;
            AdminKaydet1.btnTemizleClick = new delegeButon(Temizle);
            AdminKaydet1.btnSilClick = new delegeButonParametreli(Sil);
            AdminKaydet1.btnGuncelleClick = new delegeButonParametreli(Guncelle);
            ((SiteMaster)Master).SayfaBaslikAt("Tür Tanımlama");
            if (!IsPostBack)
            {
                Temizle();
                AdminKaydet1.Ekranmodu = AdminUserCtrl.AdminKaydet.EkranModuEnum.Kaydet;
            }
            Grid_Doldur();
            if (FormHelper.CoreHelper.isEnglish())
            {
                ((Button)AdminKaydet1.FindControl("btnKaydet")).Text = "Add";
                ((Button)AdminKaydet1.FindControl("btnSil")).Text = "Delete";
                ((Button)AdminKaydet1.FindControl("btnGuncelle")).Text = "Update";
                ((Button)AdminKaydet1.FindControl("btnTemizle")).Text = "Cancel";
            }
        }

        protected void ASPxGridView_AfterPerformCallback(object sender, ASPxGridViewAfterPerformCallbackEventArgs e)
        {
            Grid_Doldur();
        }

        public override void Grid_Doldur()
        {
            try
            {
                gridViewTip.DataSource = ItemTypeHelper.GridListele();
                gridViewTip.DataBind();
            }
            catch
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Tablo Sorgu Hata Verdi.", true);
            }
        }
        protected void lnkduzenle_Click(object sender, EventArgs e)
        {
            int index = (((LinkButton)sender).NamingContainer as GridViewDataRowTemplateContainer).VisibleIndex;
            int deger = ConvertionHelper.ConvertValue<int>(gridViewTip.GetRowValues(index, "ID"));
            Kayit_Getir(deger);
        }
        public override void Kaydet()
        {
            Page.Validate();
            if (Page.IsValid)
            {
                try
                {
                    MHBRT_REGISTRY_ITEM_TYPE nesnem = new MHBRT_REGISTRY_ITEM_TYPE();
                    Esitle(nesnem, true);
                    if (ItemTypeHelper.KayitEklenebilirmi(nesnem, ref ((SiteMaster)Master).UyariMesaji))
                    {
                        string YeniID = PRepository<MHBRT_REGISTRY_ITEM_TYPE>.EntityKaydet("DT_WORKFLOW", nesnem);
                        Temizle();
                        nesnem = null;
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Edildi", false);
                    }
                    else
                        throw new CustomException(((SiteMaster)Master).UyariMesaji);
                }
                catch (CustomException ex)
                {
                    ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
                }
                catch
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Kayıt Hata Verdi.", true);
                }
            }
        }

        private void Esitle(MHBRT_REGISTRY_ITEM_TYPE nesnem, bool IsInsert)
        {
            nesnem.ITEM_TYPE = txt_ITEM_TYPE.Text.Trim();
            nesnem.OFFICIAL = ConvertionHelper.ConvertValue<int>(chkOfficial.Checked).ToString();
            nesnem.INOFFICIAL = ConvertionHelper.ConvertValue<int>(chkInOfficial.Checked).ToString();
            nesnem.IS_ACTIVE = ConvertionHelper.ConvertValue<int>(chkAktif.Checked).ToString();
            if (IsInsert)
            {
                nesnem.CREATED_BY = this.LoginId;
                nesnem.CREATED = DateTime.Now;
            }
            else
            {
                nesnem.UPDATED_BY = this.LoginId;
                nesnem.UPDATED = DateTime.Now;
            }
        }

        public override void Guncelle(int ID)
        {
            Page.Validate();
            if (Page.IsValid)
            {
                try
                {
                    MHBRT_REGISTRY_ITEM_TYPE nesnem = PRepository<MHBRT_REGISTRY_ITEM_TYPE>.EntityGetir("DT_WORKFLOW", ID);
                    Esitle(nesnem, false);
                    if (ItemTypeHelper.KayitGuncellenebilirmi(nesnem, ref ((SiteMaster)Master).UyariMesaji))
                    {
                        PRepository<MHBRT_REGISTRY_ITEM_TYPE>.EntityUpdateEt("DT_WORKFLOW", nesnem);
                        Temizle();
                        nesnem = null;
                        ((SiteMaster)this.Master).PopupGoster("Bilgi", "Kayıt Güncellendi", false);
                    }
                    else
                        throw new CustomException(((SiteMaster)Master).UyariMesaji);
                }
                catch (CustomException ex)
                {
                    ((SiteMaster)this.Master).PopupGoster("Uyarı", ex.Message, true);
                }
                catch
                {
                    ((SiteMaster)this.Master).PopupGoster("Hata", "Güncelleme Hata Verdi.", true);
                }
            }
        }

        public override void Kayit_Getir(int ID)
        {
            try
            {
                MHBRT_REGISTRY_ITEM_TYPE nesnem = PRepository<MHBRT_REGISTRY_ITEM_TYPE>.EntityGetir("DT_WORKFLOW", ID);
                if (nesnem != null)
                {
                    txt_ITEM_TYPE.Text = nesnem.ITEM_TYPE;
                    chkAktif.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.IS_ACTIVE));
                    chkOfficial.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.OFFICIAL));
                    chkInOfficial.Checked = ConvertionHelper.ConvertValue<bool>(ConvertionHelper.ConvertValue<int>(nesnem.INOFFICIAL));
                    AdminKaydet1.RecordId = nesnem.ID;
                    AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Guncelle;
                    ((Button)AdminKaydet1.FindControl("btnSil")).Style.Add("display", "none");
                }
                nesnem = null;
            }
            catch
            {
                ((SiteMaster)this.Master).PopupGoster("Hata", "Hata Meydana Geldi.", true);
            }
        }

        public override void Sil(int ID)
        {
        }

        public override void Temizle()
        {
            txt_ITEM_TYPE.Text = "";
            AdminKaydet1.RecordId = 0;
            AdminKaydet1.Ekranmodu = AdminKaydet.EkranModuEnum.Kaydet;
            chkAktif.Checked = true;
            chkInOfficial.Checked = true;
            chkOfficial.Checked = true;
            Grid_Doldur();
        }
    }
}