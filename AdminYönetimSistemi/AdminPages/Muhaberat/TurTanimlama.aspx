﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="TurTanimlama.aspx.cs" Inherits="AracTakipSistemi.AdminPages.Muhaberat.TurTanimlama" %>

<%@ Register Assembly="DevExpress.Web.v16.2" Namespace="DevExpress.Web" TagPrefix="dx" %>
<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>
        table#tblTipKaydet {
            width: max-content;
        }

            table#tblTipKaydet td {
                padding: 5px 20px;
            }

                table#tblTipKaydet td input {
                    margin: 0;
                }

                table#tblTipKaydet td select {
                    width: 200px;
                }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:Panel ID="pnlAll" Style="width: max-content" runat="server">
        <asp:Panel ID="pnlTipKaydet" runat="server">
            <table id="tblTipKaydet">
                <tr>
                    <td>Tür Adı</td>
                    <td>
                        <asp:TextBox ID="txt_ITEM_TYPE" MaxLength="200" autocomplete="off" CssClass="textbox" runat="server"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfv_ITEM_TYPE" runat="server" ErrorMessage="Tür Adı Alanı Boş Bırakılamaz" ForeColor="#C60C30" ValidationGroup="vg0" ControlToValidate="txt_ITEM_TYPE" Display="Dynamic"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td>Resmi</td>
                    <td>
                        <asp:CheckBox ID="chkOfficial" runat="server" /></td>
                </tr>
                <tr>
                    <td>Gayriresmi</td>
                    <td>
                        <asp:CheckBox ID="chkInOfficial"  runat="server" /></td>
                </tr>
                <tr>
                    <td>Aktif</td>
                    <td>
                        <asp:CheckBox ID="chkAktif" runat="server" /></td>
                </tr>
            </table>
            <table id="tblAdminUserCtrl">
                <tr>
                    <td>
                        <uc1:AdminKaydet runat="server" ID="AdminKaydet1" guncellemeUyarisi="Güncellemek istediğinize emin misiniz?" ValidationGroup="vgMate" />
                    </td>
                </tr>
            </table>
        </asp:Panel>
        <asp:Panel ID="pnlGrid" runat="server">
            <table id="tblGrid">
                <tr>
                    <td>
                        <dx:ASPxGridView ID="gridViewTip" runat="server" SettingsBehavior-AllowGroup="true" KeyFieldName="ID" Width="100%" OnAfterPerformCallback="ASPxGridView_AfterPerformCallback">
                            <Columns>
                                <dx:GridViewDataColumn Caption="Tür" FieldName="ITEM_TYPE" VisibleIndex="1" />
                                <dx:GridViewDataColumn Caption="Resmi" FieldName="OFFICIAL" VisibleIndex="2" />
                                <dx:GridViewDataColumn Caption="Gayriresmi" FieldName="INOFFICIAL" VisibleIndex="3" />
                                <dx:GridViewDataColumn Caption="Aktif" FieldName="ACTIVE" VisibleIndex="4" />
                                <dx:GridViewDataTextColumn FieldName="Düzenle" VisibleIndex="5">
                                    <DataItemTemplate>
                                        <asp:LinkButton ID="lnkduzenle" runat="server" Text="Düzenle" OnClick="lnkduzenle_Click"></asp:LinkButton>
                                    </DataItemTemplate>
                                </dx:GridViewDataTextColumn>
                            </Columns>
                            <Settings ShowFilterRow="true" ShowFilterBar="Auto" ShowFilterRowMenu="true" />
                            <SettingsPager PageSize="50"></SettingsPager>
                            <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" FilterBarClear="Temizle" CommandSelect="İçerir" />
                            <Styles>
                                <Header BackColor="#C60C30" ForeColor="White"></Header>
                                <Cell>
                                    <Border BorderColor="#42145F" />
                                </Cell>
                            </Styles>
                        </dx:ASPxGridView>
                    </td>
                </tr>
            </table>
        </asp:Panel>
    </asp:Panel>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder2" runat="server">
</asp:Content>
