﻿using FormHelper.DigiportAdmin;
using System;
using System.Diagnostics;
using System.Web;

namespace AracTakipSistemi
{
    /// <summary>
    /// HTTP Module for enforcing URL-based access control for Digiport admin pages
    /// </summary>
    public class DigiportAuthorizationModule : IHttpModule
    {
        #region IHttpModule Implementation

        public void Dispose()
        {
            // Nothing to dispose
        }

        public void Init(HttpApplication application)
        {
            application.PreRequestHandlerExecute += Application_PreRequestHandlerExecute;

            //// Log all menu items for debugging
            //DigiportAuthorizationService.Instance.LogAllMenuItems();

            //Debug.WriteLine("DigiportAuthorizationModule: Initialized and logged all menu items");
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the PreRequestHandlerExecute event to check authorization before page execution
        /// </summary>
        private void Application_PreRequestHandlerExecute(object sender, EventArgs e)
        {
            try
            {
                HttpApplication application = (HttpApplication)sender;
                HttpContext context = application.Context;

                // Check if the user session has changed
                if (context.Request.QueryString["LoginId"] != null)
                {
                    // User switching detected - clear any cached authorization data
                    Debug.WriteLine("DigiportAuthorizationModule: User change detected via LoginId parameter");

                    // Force DigiportAuthorizationService to re-evaluate permissions
                    DigiportAuthorizationService.Instance.ClearCache();
                }

                // Also check for timestamp parameter which indicates cache clearing
                if (context.Request.QueryString["t"] != null)
                {
                    Debug.WriteLine("DigiportAuthorizationModule: Cache refresh detected via timestamp parameter");
                    // Force DigiportAuthorizationService to re-evaluate permissions
                    DigiportAuthorizationService.Instance.ClearCache();
                }

                // Only check authorization for Digiport admin pages
                string path = context.Request.Path.ToLower();
                if (path.Contains("/adminpages/digiportadmin/"))
                {
                    // Skip authorization check for the error page to avoid infinite redirects
                    if (path.Contains("/adminpages/exception/hata.aspx"))
                    {
                        return;
                    }

                    // Check if user is authorized to access this page
                    if (!IsUserAuthorized(context))
                    {
                        // Log unauthorized access attempt
                        LogUnauthorizedAccess(context);

                        // Redirect to error page
                        RedirectToErrorPage(context, "Yetkisiz Erişim");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DigiportAuthorizationModule.Application_PreRequestHandlerExecute: {ex.Message}");
            }
        }

        #endregion

        #region Authorization Methods

        /// <summary>
        /// Checks if the current user is authorized to access the requested page
        /// </summary>
        private bool IsUserAuthorized(HttpContext context)
        {
            try
            {
                // Get the authorization service
                var authService = DigiportAuthorizationService.Instance;

                // Check if user is a system admin (they have access to all pages)
                if (authService.IsSystemAdmin())
                {
                    return true;
                }

                // Get the menu ID from the query string if available
                int? menuId = null;
                if (context.Request.QueryString["MID"] != null &&
                    int.TryParse(context.Request.QueryString["MID"], out int mid))
                {
                    menuId = mid;
                }

                // Check if user has permission to access this page
                return authService.HasPagePermission(context.Request.Path, menuId);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in IsUserAuthorized: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Logs unauthorized access attempts
        /// </summary>
        private void LogUnauthorizedAccess(HttpContext context)
        {
            try
            {
                string username = DigiportAuthorizationService.Instance.GetCurrentUsername();
                string pagePath = context.Request.Path;
                string queryString = context.Request.QueryString.ToString();
                string ipAddress = context.Request.UserHostAddress;

                // Log to application event log
                string logMessage = $"Unauthorized access attempt: User={username}, Page={pagePath}, QueryString={queryString}, IP={ipAddress}";
                Debug.WriteLine(logMessage);

                // TODO: Add database logging if needed
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LogUnauthorizedAccess: {ex.Message}");
            }
        }

        /// <summary>
        /// Redirects to the error page with a message
        /// </summary>
        private void RedirectToErrorPage(HttpContext context, string message)
        {
            try
            {
                context.Session["ErrorPageMessage"] = message;
                context.Response.Redirect("~/AdminPages/Exception/Hata.aspx", false);
                context.ApplicationInstance.CompleteRequest();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in RedirectToErrorPage: {ex.Message}");
            }
        }

        #endregion
    }
}
