﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Site.master.cs" Inherits="AracTakipSistemi.SiteMaster" %>

<%@ Import Namespace="Resources" %>

<%@ Register Src="~/UserControl/AdminKaydet.ascx" TagPrefix="uc1" TagName="AdminKaydet" %>
<%@ Register Src="~/UserControl/MsgBoxCtrl.ascx" TagPrefix="uc1" TagName="MsgBoxCtrl" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title></title>

    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8; IE=7" />
    <meta http-equiv="Cache-Control" content="no-cache,no-store,must-revalidate,proxy-revalidate" />
    <meta http-equiv="Expires" content="0" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>

    <link rel="stylesheet" type="text/css" href="/ddlevelsfiles/ddlevelsmenu-base.css" />
    <link rel="stylesheet" type="text/css" href="/ddlevelsfiles/ddlevelsmenu-topbar.css" />
    <%--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />--%>
    <link href="/css/fontawesome_5.14.15.css" rel="stylesheet" />

    <script type="text/javascript">
        function DateControl(e) {
            var charCode = (e.which) ? e.which : event.keyCode;
            if ((charCode >= 48 && charCode <= 57) || charCode === 46) {
                return true;
            }
            return false;
        }
        function NumericControl(evt, input, maxLength, minValue, maxValue) {
            var charCode = (evt.which) ? evt.which : evt.keyCode;

            // Sadece sayı karakterleri (0-9) izin ver
            if (charCode < 48 || charCode > 57) {
                evt.preventDefault();
                return false;
            }
            if (input.value.length > maxLength) {
                if (input.value > maxLength)
                    input.value = maxValue;
                evt.preventDefault();
                return false;
            }

            if (input.value < minValue) {
                input.value = minValue;
                evt.preventDefault();
                return false;
            }
            if (input.value > maxValue) {
                input.value = maxValue;
                evt.preventDefault();
                return false;
            }

            return true;
        }
    </script>
</head>
<body style="background: url(/images/kurumsal_back.jpg) 0px 0px no-repeat fixed;">
    <link rel="stylesheet" href="css/style.css?version=<%= CoreHelpers.GenericIslemler.RastgeleUret(10)%>" type="text/css" />
    <style>
        /* ===== Professional Admin UI with !important Rules ===== */
        :root {
            --primary: #5c2d91;
            --primary-light: #7b4eaf;
            --primary-dark: #4b2173;
            --primary-bg: #f9f7fc;
            --gray-50: #f8f9fa;
            --gray-100: #f1f3f5;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --gray-900: #212529;
            --success: #198754;
            --danger: #dc3545;
            --warning: #ffc107;
            --info: #0dcaf0;
        }

        /* Reset & Base */
        body, input, select, textarea, button {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
            color: var(--gray-800) !important;
        }

        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: var(--gray-100) !important;
            min-height: 100vh !important;
        }

            /* Clean background */
            body[style*="background"] {
                background: var(--gray-100) !important;
            }

        table, tr, td {
            border-spacing: 0 !important;
        }

        /* Layout */
        #form1 {
            display: flex !important;
            flex-direction: column !important;
            min-height: 100vh !important;
        }

        .container {
            width: 100% !important;
            max-width: 1200px !important;
            margin: 0 auto !important;
            padding: 0 15px !important;
        }

        /* Header */
        #tblMenu {
            width: 100% !important;
            background-color: white !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
        }

        #tdTest {
            background-color: var(--primary) !important;
            color: white !important;
            font-weight: 600 !important;
            font-size: 15px !important;
            padding: 24px 48px !important;
            letter-spacing: 0.5px !important;
        }

        .SayfaBaslik {
            background-color: var(--primary) !important;
            color: white !important;
            padding: 12px 15px !important;
        }

        .performansAnaBaslik {
            font-size: 19px !important;
            font-weight: 600 !important;
            letter-spacing: 0.3px !important;
        }

        #kullaniciLabel {
            font-size: 13px !important;
            color: rgba(255, 255, 255, 0.95) !important;
            opacity: 1 !important;
            font-weight: 500 !important;
        }

        /* Navigation */
        #ddtopmenubar {
            background-color: white !important;
            border-radius: 3px !important;
            margin-top: 5px !important;
        }

            #ddtopmenubar ul {
                display: flex !important;
                margin: 0 !important;
                padding: 0 !important;
            }

                #ddtopmenubar ul li {
                    list-style: none !important;
                }

            #ddtopmenubar > ul > li > a {
                display: block !important;
                padding: 10px 16px !important;
                color: white !important;
                text-decoration: none !important;
                font-weight: 500 !important;
                font-size: 13px !important;
                transition: all 0.2s !important;
            }

                #ddtopmenubar > ul > li > a:hover,
                #ddtopmenubar > ul > li > a:focus {
                    color: var(--primary) !important;
                    background-color: var(--primary-bg) !important;
                }

        /* Dropdown Menu */
        .ddsubmenustyle {
            position: absolute !important;
            left: 0 !important;
            top: 100% !important; /* Position it directly below the parent menu item */
            background-color: white !important;
            border-radius: 3px !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
            padding: 5px 0 !important;
            min-width: 200px !important;
            z-index: 1001 !important; /* Higher z-index to ensure it appears above other elements */
            margin-top: 0 !important;
            display: none; /* Hidden by default */
        }

        /* Positioning container for dropdown parent */
        #ddtopmenubar > ul > li {
            position: relative !important;
        }

        /* Override any absolute positioning from the original ddlevelsfiles */
        #ddsubmenu1 {
            position: absolute !important;
            left: 0 !important;
            top: 100% !important;
        }

        /* Create an invisible padding to maintain hover state */
        #ddtopmenubar > ul > li > a[rel] {
            position: relative !important;
        }

            #ddtopmenubar > ul > li > a[rel]::after {
                content: '' !important;
                position: absolute !important;
                height: 20px !important; /* Invisible bridge between menu and submenu */
                width: 100% !important;
                left: 0 !important;
                top: 100% !important;
                z-index: 101 !important;
            }

        /* Buttons */
        input[type="submit"],
        input[type="button"] {
            background-color: var(--primary) !important;
            color: white !important;
            border: none !important;
            border-radius: 3px !important;
            padding: 6px 12px !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            cursor: pointer !important;
            transition: all 0.2s !important;
            height: auto !important;
            min-height: auto !important;
            letter-spacing: 0.2px !important;
            margin: 2px !important;
        }

            input[type="submit"]:hover,
            input[type="button"]:hover {
                background-color: var(--primary-light) !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
            }

            input[type="submit"]:active,
            input[type="button"]:active {
                background-color: var(--primary-dark) !important;
            }

            input[type="submit"]:disabled,
            input[type="button"]:disabled {
                background-color: var(--gray-400) !important;
                color: var(--gray-600) !important;
                cursor: not-allowed !important;
            }

        /* Form Controls */
        input[type="text"],
        input[type="password"],
        input[type="email"],
        input[type="number"],
        textarea,
        select {
            border: 1px solid var(--gray-300) !important;
            border-radius: 3px !important;
            padding: 6px 10px !important;
            background-color: white !important;
            font-size: 13px !important;
            color: var(--gray-800) !important;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
        }

            input[type="text"]:focus,
            input[type="password"]:focus,
            input[type="email"]:focus,
            input[type="number"]:focus,
            textarea:focus,
            select:focus {
                border-color: var(--primary-light) !important;
                box-shadow: 0 0 0 2px rgba(92,45,145,0.2) !important;
                outline: none !important;
            }

        select {
            padding-right: 25px !important;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='6' viewBox='0 0 10 6'%3E%3Cpath fill='%235c2d91' d='M0 0l5 5 5-5z'/%3E%3C/svg%3E") !important;
            background-repeat: no-repeat !important;
            background-position: right 10px center !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            appearance: none !important;
        }

        /* Tables */
        table[align="center"][border="0"] {
            border-radius: 4px !important;
            overflow: hidden !important;
            background-color: white !important;
            margin: 15px auto !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            width: 100% !important;
            max-width: 1200px !important;
        }

        /* Data Grids */
        .dxgvHeader {
            background-color: var(--primary) !important;
            color: white !important;
            font-weight: 500 !important;
            padding: 8px 10px !important;
            border-bottom: 1px solid var(--primary-dark) !important;
            font-size: 13px !important;
        }

        .dxgvDataRow {
            transition: background-color 0.15s !important;
        }

            .dxgvDataRow:nth-child(even) {
                background-color: var(--gray-50) !important;
            }

            .dxgvDataRow:hover {
                background-color: var(--primary-bg) !important;
            }

            .dxgvDataRow td {
                padding: 7px 10px !important;
                border-bottom: 1px solid var(--gray-200) !important;
                font-size: 13px !important;
            }

        .ajax__calendar_container TABLE td {
            padding: 0 !important;
        }

        /* Target all DevExpress header styles */
        .dxgvHeader td,
        .dxgvHeader th,
        td[style*="color:White;background-color:#C60C30"],
        .dxgvHeader td[style*="color:White;background-color:#C60C30"],
        .dxgv td[style*="color:White;background-color:#C60C30"] {
            background-color: var(--primary) !important;
            color: white !important;
            font-weight: 500 !important;
            font-size: 13px !important;
        }

        /* Target any elements with the old red color set inline */
        [style*="background-color:#C60C30"],
        [style*="background-color: #C60C30"],
        [BackColor="#C60C30"] {
            background-color: var(--primary) !important;
        }

        /* Paging */
        .dxpControl {
            padding: 8px !important;
            background-color: var(--gray-50) !important;
            border-top: 1px solid var(--gray-200) !important;
        }

        .dxpPageNumber {
            padding: 4px 8px !important;
            border-radius: 3px !important;
            margin: 0 1px !important;
            font-size: 12px !important;
        }

        .dxpCurrentPageNumber {
            background-color: var(--primary) !important;
            color: white !important;
        }

        /* Modal Popup */
        .modalPopup {
            background-color: white !important;
            border-radius: 4px !important;
            box-shadow: 0 3px 15px rgba(0,0,0,0.2) !important;
            border: none !important;
            overflow: hidden !important;
            width: auto !important;
        }

            .modalPopup .header {
                background-color: var(--primary) !important;
                color: white !important;
                padding: 10px 15px !important;
                font-size: 16px !important;
                font-weight: 500 !important;
            }

        .modalBackground {
            background-color: rgba(0,0,0,0.5) !important;
        }

        /* Message Box */
        #MsgBoxCtrl1_pnlMesajKutusu {
            padding: 0px !important;
        }

        #MsgBoxCtrl1_lblBilgiMesaj {
            font-size: 14px !important;
            line-height: 1.5 !important;
        }

        /* Progress Indicator */
        #UpdProgress {
            background-color: white !important;
            border-radius: 4px !important;
            box-shadow: 0 3px 15px rgba(0,0,0,0.2) !important;
            padding: 20px !important;
            text-align: center !important;
            font-size: 14px !important;
        }

        /* Footer */
        td[style*="background-color: #5c2d91"] {
            background-color: var(--primary) !important;
            color: white !important;
            padding: 10px !important;
            font-size: 12px !important;
            text-align: center !important;
        }

        /* Card Style */
        .card {
            background-color: white !important;
            border-radius: 4px !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            margin-bottom: 16px !important;
            overflow: hidden !important;
        }

        .card-header {
            background-color: var(--gray-100) !important;
            padding: 12px 15px !important;
            border-bottom: 1px solid var(--gray-200) !important;
            font-weight: 500 !important;
            font-size: 15px !important;
            color: var(--gray-800) !important;
        }

        .card-body {
            padding: 15px !important;
        }

        /* Status Messages */
        .status {
            padding: 10px 15px !important;
            border-radius: 3px !important;
            margin-bottom: 15px !important;
            font-size: 13px !important;
        }

        .status-success {
            background-color: rgba(25, 135, 84, 0.1) !important;
            border-left: 3px solid var(--success) !important;
            color: #0a5d36 !important;
        }

        .status-danger {
            background-color: rgba(220, 53, 69, 0.1) !important;
            border-left: 3px solid var(--danger) !important;
            color: #a12635 !important;
        }

        .status-warning {
            background-color: rgba(255, 193, 7, 0.1) !important;
            border-left: 3px solid var(--warning) !important;
            color: #916e01 !important;
        }

        .status-info {
            background-color: rgba(13, 202, 240, 0.1) !important;
            border-left: 3px solid var(--info) !important;
            color: #087990 !important;
        }

        /* Utilities */
        .mt-0 {
            margin-top: 0 !important;
        }

        .mb-0 {
            margin-bottom: 0 !important;
        }

        .ml-0 {
            margin-left: 0 !important;
        }

        .mr-0 {
            margin-right: 0 !important;
        }

        .mt-1 {
            margin-top: 4px !important;
        }

        .mb-1 {
            margin-bottom: 4px !important;
        }

        .ml-1 {
            margin-left: 4px !important;
        }

        .mr-1 {
            margin-right: 4px !important;
        }

        .mt-2 {
            margin-top: 8px !important;
        }

        .mb-2 {
            margin-bottom: 8px !important;
        }

        .ml-2 {
            margin-left: 8px !important;
        }

        .mr-2 {
            margin-right: 8px !important;
        }

        .mt-3 {
            margin-top: 16px !important;
        }

        .mb-3 {
            margin-bottom: 16px !important;
        }

        .ml-3 {
            margin-left: 16px !important;
        }

        .mr-3 {
            margin-right: 16px !important;
        }

        .p-0 {
            padding: 0 !important;
        }

        .p-1 {
            padding: 4px !important;
        }

        .p-2 {
            padding: 8px !important;
        }

        .p-3 {
            padding: 16px !important;
        }

        .text-primary {
            color: var(--primary) !important;
        }

        .text-success {
            color: var(--success) !important;
        }

        .text-danger {
            color: var(--danger) !important;
        }

        .text-warning {
            color: var(--warning) !important;
        }

        .text-info {
            color: var(--info) !important;
        }

        .text-muted {
            color: var(--gray-600) !important;
        }

        .bg-primary {
            background-color: var(--primary) !important;
            color: white !important;
        }

        .bg-light {
            background-color: var(--gray-100) !important;
        }

        .text-left {
            text-align: left !important;
        }

        .text-center {
            text-align: center !important;
        }

        .text-right {
            text-align: right !important;
        }

        .d-flex {
            display: flex !important;
        }

        .align-items-center {
            align-items: center !important;
        }

        .justify-content-between {
            justify-content: space-between !important;
        }

        /* Additional overrides for DevExpress controls */
        .dxeBase {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
            font-size: 13px !important;
        }

        .dxeListBox,
        .dxeTextBox,
        .dxeButtonEdit,
        .dxeEditArea {
            font-size: 13px !important;
            border-radius: 3px !important;
            width: 100%;
        }


        .dxic .dxeEditArea {
            width: calc(100% - 22px) !important
        }

        .dxic {
            padding: 0;
        }

        .dxWeb_pAll {
            color: var(--gray-800) !important;
        }

        /* Main container layout */
        .main-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            max-width: 1400px;
            margin: 0 auto;
            background-color: rgba(255, 255, 255, 0.97);
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        /* Header styling */
        .site-header {
            background-color: var(--primary);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            padding: 0;
            top: 0;
            z-index: 1000;
            position: relative;
        }

        .header-container {
            display: flex;
            align-items: center;
            padding: 0 15px;
            height: 70px;
        }

        .logo-area {
            display: flex;
            align-items: center;
            margin-right: 20px;
            height: 100%;
            padding: 0 10px;
        }

        .logo {
            height: 30px;
            margin-right: 15px;
            filter: brightness(0) invert(1); /* Make the logo white */
        }

        /* Make TEST badge draggable */
        #env-badge-container {
            position: absolute;
            top: 50%;
            left: 10px;
            z-index: 9999;
            cursor: move;
            user-select: none;
        }

        .environment-badge {
            display: inline-block;
            background-color: var(--danger) !important;
            color: white !important;
            font-weight: 600 !important;
            font-size: 12px !important;
            padding: 4px 8px !important;
            border-radius: 3px;
            letter-spacing: 0.5px !important;
        }

        .header-content {
            flex: 1;
            display: flex;
            align-items: center;
        }

            .header-content h1 {
                margin: 5px 0;
            }

        .page-title {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: white;
        }

        .user-info {
            margin-bottom: 5px;
        }

        .user-name {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.85);
        }

        /* Main menu styling */
        .main-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            background-color: var(--primary);
            border-radius: 3px;
        }

            .main-menu li {
                position: relative;
            }

            .main-menu a {
                display: block;
                color: white !important;
                text-decoration: none;
                padding: 10px 15px;
                font-size: 14px;
                font-weight: 500;
                transition: background-color 0.2s;
            }

                .main-menu a:hover {
                    background-color: var(--primary-light);
                }

        .home-link {
            border-radius: 3px 0 0 3px;
        }

        /* Language selector */
        .language-selector {
            margin-right: 15px;
            position: relative;
        }

            .language-selector select {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                background-color: rgba(255, 255, 255, 0.15);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                padding: 6px 30px 6px 10px;
                cursor: pointer;
                font-size: 13px;
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='6' viewBox='0 0 10 6'%3E%3Cpath fill='white' d='M0 0l5 5 5-5z'/%3E%3C/svg%3E");
                background-repeat: no-repeat;
                background-position: right 10px center;
                width: 100px;
            }

                .language-selector select:focus {
                    outline: none;
                    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
                }

                .language-selector select option {
                    background-color: white;
                    color: var(--gray-800);
                }

        /* Main content area */
        .site-main {
            flex: 1;
            padding: 20px;
            background-color: var(--gray-50);
        }

        /* Footer */
        .site-footer {
            padding: 15px;
            background-color: var(--primary);
            color: white;
            text-align: center;
            font-size: 12px;
        }

        /* Main Menu - Modern Dropdown */
        #main-nav {
            border-radius: 0;
            margin: 0 20px 0;
            background-color: transparent;
        }

            #main-nav ul {
                list-style: none;
                margin: 0;
                padding: 0;
                display: flex;
            }

            #main-nav li {
                position: relative;
            }

            #main-nav .dropdown {
                position: relative;
            }

            #main-nav .main-link {
                display: block;
                padding: 8px 16px;
                color: white;
                text-decoration: none;
                font-weight: 500;
                font-size: 14px;
                transition: background-color 0.2s, box-shadow 0.2s;
                position: relative;
                overflow: hidden;
            }

            #main-nav li:first-child .main-link {
                border-radius: 3px 0 0 3px;
            }

            #main-nav .main-link:hover,
            #main-nav .main-link:focus {
                background-color: rgba(255, 255, 255, 0.15);
                box-shadow: inset 0 -3px 0 rgba(255, 255, 255, 0.3);
            }

            #main-nav .main-link:active {
                box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.2);
            }

            #main-nav .dropdown-menu {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                min-width: 220px;
                background-color: white;
                border-radius: 0 0 3px 3px;
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
                z-index: 1001;
                opacity: 0;
                transform: translateY(-10px);
                transition: opacity 0.2s, transform 0.2s;
                border-top: 2px solid rgba(255, 255, 255, 0.3);
                border-left: 1px solid var(--gray-200);
                border-right: 1px solid var(--gray-200);
                border-bottom: 1px solid var(--gray-200);
            }

                #main-nav .dropdown-menu.show {
                    display: block;
                    opacity: 1;
                    transform: translateY(0);
                }

                #main-nav .dropdown-menu li {
                    width: 100%;
                    border-bottom: 1px solid var(--gray-200);
                }

                    #main-nav .dropdown-menu li:last-child {
                        border-bottom: none;
                    }

                #main-nav .dropdown-menu a {
                    display: block;
                    padding: 10px 16px;
                    color: var(--gray-800);
                    text-decoration: none;
                    font-size: 13px;
                    transition: all 0.2s;
                    white-space: nowrap;
                    position: relative;
                }

                    #main-nav .dropdown-menu a:hover {
                        background-color: var(--primary-bg);
                        color: var(--primary);
                        padding-left: 18px;
                    }

            #main-nav .dropdown-submenu {
                position: relative;
            }

                #main-nav .dropdown-submenu > a::after {
                    content: '▶';
                    font-size: 8px;
                    position: absolute;
                    right: 15px;
                    top: 50%;
                    transform: translateY(-50%);
                    transition: transform 0.2s;
                }

                #main-nav .dropdown-submenu:hover > a::after {
                    transform: translateY(-50%) translateX(3px);
                }

                #main-nav .dropdown-submenu .dropdown-menu {
                    top: 0;
                    left: 100%;
                    margin-top: 0;
                    border-radius: 0 3px 3px 3px;
                    transform: translateX(-10px);
                    min-width: 250px;
                }

        .dxeButtonEdit td {
            padding: 0 !important;
        }

        /* Responsive adjustments for main menu */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                height: auto;
                padding: 15px;
            }

            .logo-area {
                margin-right: 0;
                margin-bottom: 10px;
            }

            #main-nav {
                margin-top: 15px;
            }

                #main-nav ul {
                    flex-direction: column;
                    width: 100%;
                }

                #main-nav .main-link {
                    border-radius: 0;
                    border: none;
                    padding: 10px;
                }

                #main-nav .dropdown-menu {
                    position: static;
                    width: 100%;
                    box-shadow: none;
                    border: none;
                    background-color: rgba(255, 255, 255, 0.05);
                }

                    #main-nav .dropdown-menu a {
                        color: rgba(255, 255, 255, 0.8);
                        padding-left: 20px;
                    }

                        #main-nav .dropdown-menu a:hover {
                            background-color: rgba(255, 255, 255, 0.1);
                            color: white;
                        }

                #main-nav .dropdown-submenu .dropdown-menu {
                    left: 0;
                    margin-left: 20px;
                    border-left: 2px solid rgba(255, 255, 255, 0.2);
                }
        }

        /* Digiport button */
        .digiport-button {
            display: inline-flex;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.15);
            color: white;
            text-decoration: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 13px;
            margin-left: 15px;
            transition: all 0.2s ease;
        }

            .digiport-button:hover {
                background-color: rgba(255, 255, 255, 0.25);
                text-decoration: none;
            }

            .digiport-button i {
                margin-right: 5px;
            }

        /* User Switcher Styles - UPDATED */
        .user-switcher-container {
            display: flex !important;
            align-items: center !important;
            background-color: rgba(255, 255, 255, 0.15) !important;
            border-radius: 4px !important;
            padding: 5px 10px !important;
            margin-left: 10px !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            box-shadow: none !important;
        }

        .user-switcher-dropdown {
            flex: 1 !important;
            min-width: 180px !important;
            max-width: 220px !important;
            padding: 4px 8px !important;
            margin-right: 6px !important;
            background-color: rgba(255, 255, 255, 0.9) !important;
            border: none !important;
            border-radius: 3px !important;
            color: var(--gray-800) !important;
            font-size: 12px !important;
        }

        .user-switcher-button {
            background-color: var(--success) !important;
            color: white !important;
            padding: 4px 10px !important;
            border-radius: 3px !important;
            text-decoration: none !important;
            font-size: 12px !important;
            display: inline-block !important;
            text-align: center !important;
            border: none !important;
        }

            .user-switcher-button:hover {
                background-color: #0f6b42 !important;
                color: white !important;
            }

        /* Hide the header text */
        .user-switcher-header {
            display: none !important;
        }

        /* Add icon to button */
        .user-switcher-button::before {
            content: "\f007";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            margin-right: 5px;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background-color: rgba(0,0,0,0.6);
            z-index: 999999;
        }

        .divgif {
            position: absolute;
            top: 43%;
            bottom: 0;
            left: 45%;
            right: 0;
            margin: auto;
        }

            .divgif img {
                width: 100px;
                height: 100px;
            }
    </style>
    <script type="text/javascript" src="/ddlevelsfiles/ddlevelsmenu.js"></script>
    <script src="/Scripts/jquery-3.6.0.min.js"></script>
    <script src="/Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            // Make the TEST badge draggable
            $("#env-badge-container").draggable({
                containment: "window",
                scroll: false,
                start: function () {
                    $(this).css("z-index", "10000");
                }
            });

            // Click-based menu system rather than hover-based
            $("#ddtopmenubar > ul > li > a[rel]").on("click", function (e) {
                e.preventDefault();
                e.stopPropagation();

                // Get the submenu
                var targetMenu = $(this).attr("rel");
                var $submenu = $("#" + targetMenu);

                // Position the submenu correctly
                $submenu.css({
                    position: "absolute",
                    top: $(this).parent().position().top + $(this).parent().height() + "px",
                    left: $(this).parent().position().left + "px",
                    zIndex: 1001
                });

                // Toggle the display of the submenu
                if ($submenu.is(":visible")) {
                    $submenu.hide();
                } else {
                    // Hide any other open submenus first
                    $(".ddsubmenustyle").hide();
                    $submenu.show();
                }
            });

            // Close menu when clicking outside
            $(document).on("click", function (e) {
                if (!$(e.target).closest("#ddtopmenubar").length) {
                    $(".ddsubmenustyle").hide();
                }
            });

            // Prevent submenu closing when clicking inside it
            $(".ddsubmenustyle").on("click", function (e) {
                e.stopPropagation();
            });

            // Click-based menu functionality
            // Main dropdown toggle
            $("#main-nav .dropdown-toggle").on("click", function (e) {
                e.preventDefault();
                e.stopPropagation();
                var $dropdown = $(this).siblings(".dropdown-menu");

                // Close all other dropdowns at the root level
                $("#main-nav .dropdown-menu").not($dropdown).removeClass("show");

                // Also close any open submenus
                if (!$(e.target).closest('.dropdown-submenu').length) {
                    $("#main-nav .dropdown-submenu .dropdown-menu").removeClass("show");
                }

                // Toggle current dropdown
                $dropdown.toggleClass("show");
            });

            // Submenu toggle
            $("#main-nav .dropdown-submenu > a").on("click", function (e) {
                e.preventDefault();
                e.stopPropagation();
                var $submenu = $(this).siblings(".dropdown-menu");
                var $parentMenu = $(this).closest('.dropdown-menu');
                var $currentSubmenuParent = $(this).parent();

                // Close all other submenus that are not parents or children of this item
                $("#main-nav .dropdown-submenu").not($currentSubmenuParent)
                    .not($currentSubmenuParent.parents('.dropdown-submenu'))
                    .not($currentSubmenuParent.find('.dropdown-submenu'))
                    .find('.dropdown-menu.show').removeClass("show");

                // Toggle current submenu
                $submenu.toggleClass("show");
            });

            // Close when clicking outside
            $(document).on("click", function (e) {
                if (!$(e.target).closest("#main-nav").length) {
                    $("#main-nav .dropdown-menu").removeClass("show");
                }
            });
        });

        function mesajli_validate(mesaj) {
            if (Page_ClientValidate())
                return confirm(mesaj);
        }

        function goster_gizle_div(deger, bolum, resim, pluspicture, minuspicture) {
            var bolumdiv = document.getElementById(bolum);
            var imgicon = document.getElementById(resim);
            if (bolumdiv.style.display == "block" || bolumdiv.style.display == "") {
                bolumdiv.style.display = "none";
                imgicon.src = pluspicture;
            }
            else {
                bolumdiv.style.display = "";
                imgicon.src = minuspicture;
            }
        }

        ///IE kontrolü
        function tarayici_kontrol() {
            var browserName = navigator.userAgent;
            if (browserName.indexOf("Chrome") != -1 || browserName.indexOf("Firefox") != -1) {
                alert("<%= Resources.DigiportAdminResource.UseInternetExplorer %>" + browserName)
                window.location = 'http://digiport';
            }
        }

        ///yardim dugmesine tiklandiginda popup acar
        function yardim_penceresi_ac(adres) {
            var leftPosition, topPosition, widthh, heighth;
            widthh = 200;
            heighth = 200;
            leftPosition = (window.screen.width / 2) - ((widthh / 2) + 10);
            topPosition = (window.screen.height / 2) - ((heighth / 2) + 50);
            window.open(adres, "_blank", 'toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=yes, copyhistory=no, width=' + widthh + ', height=' + heighth + ', top=' + topPosition);
        }

        // Function to clear browser cache for impersonation
        function clearCacheForImpersonation() {
            // Clear localStorage
            localStorage.clear();

            // Clear sessionStorage
            sessionStorage.clear();

            // Add a unique timestamp to force refresh
            var timestamp = new Date().getTime();
            var currentUrl = window.location.href;

            // Check if URL already has parameters
            if (currentUrl.indexOf('?') > -1) {
                currentUrl = currentUrl.split('?')[0]; // Remove existing query params
            }

            // Add timestamp as query parameter
            window.location.href = currentUrl + '?t=' + timestamp;

            return true;
        }
        function showloader() {
            $('#masteroverlay').css('display', 'block');
        }
        function hideloader() {
            $('#masteroverlay').css('display', 'none');
        }
    </script>
    <%--<script>tarayici_kontrol();</script>--%>
    <form id="form1" runat="server">
        <div>
            <asp:ScriptManager runat="Server" EnableScriptGlobalization="true" OnAsyncPostBackError="ScriptManager1_AsyncPostBackError" EnableScriptLocalization="true" EnableCdn="false" EnablePartialRendering="true" ID="ScriptManager1" />
            <div class="main-container">
                <header class="site-header">
                    <div class="header-container">
                        <div class="logo-area">
                            <img src="/images/digiturk_logo.png" alt="Digiturk Logo" class="logo" />
                        </div>

                        <div class="header-content">
                            <div>
                                <h1 class="page-title">
                                    <asp:Literal ID="lblSayfaBaslik" runat="server"></asp:Literal>
                                </h1>
                                <div class="user-info">
                                    <asp:Label ID="kullaniciLabel" Text="" runat="server" CssClass="user-name" />
                                </div>
                            </div>
                            <nav id="main-nav">
                                <ul>
                                    <li><a href="/Anasayfa.aspx" class="main-link">
                                        <asp:Literal Text="<%$ Resources:DigiportAdminResource, AnaSayfa %>" runat="server" /></a></li>
                                    <li class="dropdown">
                                        <a href="#" class="main-link dropdown-toggle">
                                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, YonetimSayfalari %>" runat="server" /></a>
                                        <ul class="dropdown-menu">
                                            <!-- Digiport Menu Group -->
                                            <asp:Panel ID="menuDigiportAdmin" runat="server">
                                                <li class="dropdown-submenu">
                                                    <a href="#">Digiport</a>
                                                    <ul class="dropdown-menu" id="digiportAdminMenuContainer" runat="server">
                                                        <!-- Menu items will be dynamically added in code-behind -->
                                                    </ul>
                                                </li>
                                            </asp:Panel>

                                            <!-- Diğer (Other) Menu Group -->
                                            <li class="dropdown-submenu" id="liDiger" runat="server">
                                                <a href="#">
                                                    <asp:Literal runat='server' Text="<%$ Resources:DigiportAdminResource, Diger%>" /></a>
                                                <ul class="dropdown-menu">
                                                    <asp:Panel ID="menuDigiportAnket" runat="server">
                                                        <li class="dropdown-submenu">
                                                            <a href="#">
                                                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, DigiportAnket %>" runat="server" /></a>
                                                            <ul class="dropdown-menu">
                                                                <li id="liDigiportAnketDomainTanimlama" runat="server"><a href="/AdminPages/DigiportAnket/DomainTanimlama.aspx">Digiport Anket Domain Tanımlama</a></li>
                                                                <li id="liDigiportAnketDomainKullaniciTanimlama" runat="server"><a href="/AdminPages/DigiportAnket/DomainKullaniciTanimlama.aspx">Digiport Anket Domain Kullanıcı Tanımlama</a></li>
                                                            </ul>
                                                        </li>
                                                    </asp:Panel>
                                                    <asp:Panel ID="mnuAracTalepAdmin" runat="server">
                                                        <li class="dropdown-submenu">
                                                            <a href="#">
                                                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, AracTalepAdmin %>" runat="server" /></a>
                                                            <ul class="dropdown-menu">
                                                                <li><a href="/AdminPages/AracTakipSistemi/AracSınıf.aspx">Araç Sınıf Tanımı</a></li>
                                                                <li><a href="/AdminPages/AracTakipSistemi/AracRenk.aspx">Araç Renk Tanımı</a></li>
                                                                <li><a href="/AdminPages/AracTakipSistemi/AracYıl.aspx">Araç Yıl Tanımı</a></li>
                                                                <li><a href="/AdminPages/AracTakipSistemi/AracMarka.aspx">Araç Marka Tanımı</a></li>
                                                                <li><a href="/AdminPages/AracTakipSistemi/AracModel.aspx">Araç Model Tanımı</a></li>
                                                                <li><a href="/AdminPages/AracTakipSistemi/AracPlaka.aspx">Araç Plaka Tanımı</a></li>
                                                            </ul>
                                                        </li>
                                                    </asp:Panel>
                                                    <asp:Panel ID="mnuEkipmanAdmin" runat="server">
                                                        <li class="dropdown-submenu">
                                                            <a href="#">
                                                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, EkipmanTalepAdmin %>" runat="server" /></a>
                                                            <ul class="dropdown-menu">
                                                                <li><a href="/AdminPages/EkipmanTalepFormu/YTSTanimlama.aspx">YTS Tanımlama</a></li>
                                                                <li><a href="/AdminPages/EkipmanTalepFormu/MalzemeTanim.aspx">Malzeme Tanımlama</a></li>
                                                                <li><a href="/AdminPages/EkipmanTalepFormu/EkipmanTalepleriRaporu.aspx">Ekipman Talepleri Raporu</a></li>
                                                                <li><a href="/AdminPages/EkipmanTalepFormu/ExcelYukleme.aspx">Excel Yükleme Ekranı</a></li>
                                                            </ul>
                                                        </li>
                                                    </asp:Panel>
                                                    <asp:Panel ID="mnuKirtasiyeAdmin" runat="server">
                                                        <li class="dropdown-submenu">
                                                            <a href="#">
                                                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, KirtasiyeAdmin %>" runat="server" /></a>
                                                            <ul class="dropdown-menu">
                                                                <li><a href="/AdminPages/KurumsalKirtasiye/DepoTanimlama.aspx">Depo Tanımlama</a></li>
                                                                <li><a href="/AdminPages/KurumsalKirtasiye/StokGrupTanimlama.aspx">Stok Grup Tanımlama</a></li>
                                                                <li><a href="/AdminPages/KurumsalKirtasiye/FirmaTanimlama.aspx">Firma Tanımlama</a></li>
                                                                <li><a href="/AdminPages/KurumsalKirtasiye/StokTanimlama.aspx">Stok Tanımlama</a></li>
                                                                <li><a id="linkKirtasiyeUygulamasi" runat="server" target="_blank">
                                                                    <asp:Literal Text="<%$ Resources:DigiportAdminResource, KirtasiyeUygulamasi %>" runat="server" /></a></li>
                                                            </ul>
                                                        </li>
                                                    </asp:Panel>
                                                    <asp:Panel ID="menuTestMate" runat="server">
                                                        <li class="dropdown-submenu">
                                                            <a href="#">
                                                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, TestMateAdmin %>" runat="server" /></a>
                                                            <ul class="dropdown-menu">
                                                                <li id="li_TMATE_DomainAdmin" runat="server"><a href="/AdminPages/TestMate/TmateDomainsAdmins.aspx">Domain Tanımlama</a></li>
                                                                <li id="li_TMATE_Project" runat="server"><a href="/AdminPages/TestMate/TmateProjects.aspx">Proje Tanımlama</a></li>
                                                                <li id="li_TMATE_ProjectUser" runat="server"><a href="/AdminPages/TestMate/TmateProjectUsers.aspx">Proje Kullanicisi Tanımlama</a></li>
                                                                <%--<li id="li_TMATE_Status" runat="server"><a href="/AdminPages/TestMate/TmateStatusEntry.aspx">Test Status Tanımlama</a></li>--%>
                                                            </ul>
                                                        </li>
                                                    </asp:Panel>
                                                    <asp:Panel ID="menuMuhaberat" runat="server">
                                                        <li class="dropdown-submenu">
                                                            <a href="#">
                                                                <asp:Literal Text="<%$ Resources:DigiportAdminResource, MuhaberatAdmin %>" runat="server" /></a>
                                                            <ul class="dropdown-menu">
                                                                <li id="li_Muhaberat_TurTanimlama" runat="server"><a href="/AdminPages/Muhaberat/TurTanimlama.aspx">
                                                                    <asp:Literal Text="<%$ Resources:DigiportAdminResource, TurTanimlama %>" runat="server" /></a></li>
                                                            </ul>
                                                        </li>
                                                    </asp:Panel>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                        <div id="TdDilSecimi" class="language-selector">
                            <asp:DropDownList ID="drpDilSecimi" AutoPostBack="true" OnSelectedIndexChanged="drpDilSecimi_SelectedIndexChanged" runat="server">
                                <asp:ListItem Text="Türkçe" Value="1" />
                                <asp:ListItem Text="English" Value="2" />
                            </asp:DropDownList>
                        </div>

                        <!-- User Switcher -->
                        <div class="user-switcher-container" runat="server" id="lbTestPageContainer" visible="false">
                            <asp:DropDownList ID="DrpUserName" runat="server" CssClass="user-switcher-dropdown" AppendDataBoundItems="true" CausesValidation="false">
                                <asp:ListItem Value="0" Text="<%$ Resources:DigiportAdminResource, SecUserText %>"></asp:ListItem>
                            </asp:DropDownList>
                            <asp:LinkButton ID="TestButton" runat="server" Text="<%$ Resources:DigiportAdminResource, SwitchUser %>" OnClick="TestButton_Click"
                                CssClass="user-switcher-button" CausesValidation="false"
                                OnClientClick="if(DrpUserName.value != '0') { return clearCacheForImpersonation(); }" />
                        </div>
                        <!-- Digiport Link -->
                        <a href="http://digiport" target="_blank" class="digiport-button">
                            <i class="fa fa-home"></i>
                            <asp:Literal Text="<%$ Resources:DigiportAdminResource, DigiportPortal %>" runat="server" />
                        </a>
                    </div>
                </header>

                <main class="site-main">
                    <!-- Draggable TEST badge -->
                    <div id="env-badge-container" class="draggable-badge">
                        <table>
                            <tr>
                                <td id="tdTest" runat="server" class="environment-badge">TEST</td>
                            </tr>
                        </table>
                    </div>
                    <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                    </asp:ContentPlaceHolder>
                    <asp:ContentPlaceHolder ID="ContentPlaceHolder2" runat="server">
                    </asp:ContentPlaceHolder>
                </main>

                <footer class="site-footer">
                    <asp:Literal Text="<%$ Resources:TmateResource, MasterPageAltBaslik %>" runat="server" />
                </footer>
            </div>
            <uc1:MsgBoxCtrl runat="server" ID="MsgBoxCtrl1" />
        </div>
        <div class="overlay" id="masteroverlay" style="display: none;">
            <div class="divgif">
                <img src="/images/loading.gif" />
            </div>
        </div>
    </form>
</body>
</html>
