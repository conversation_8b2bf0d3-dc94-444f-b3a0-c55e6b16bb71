﻿var popupWindow = null;
function load() {
    Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
    Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(BeginRequestHandler);
    Sys.WebForms.PageRequestManager.getInstance().add_initializeRequest(InitializeRequestHandler);
}
function InitializeRequestHandler(e) {
    try {
        showloader();
    }
    catch (e) {
    }
}
function BeginRequestHandler(e) {
    try {
        showloader();
    }
    catch (e) {
    }
}
function EndRequestHandler(e) {
    try {
        hideloader();
        BindDocumentReady();
    }
    catch (e) {
    }
}
window.onload = function () {
    load();
};
$('form').on('submit', function () {
    if ($(this).valid()) {
        showloader();
    } else {
        e.preventDefault();
    }
});
function ValidateimgSlide(sender, args) {
    var hasImage = $('[id$=imgSlide]').attr('src') != '';
    if (hasImage)
        args.IsValid = true;
    else
        args.IsValid = false;
}
function syncSwitchWithHiddenField() {
    var $checkbox = $('[id$=chkActive]');
    var $hiddenField = $('[id$=hdnActive]');
    $checkbox.prop('checked', $hiddenField.val() === "true");
    $checkbox.off('change').on('change', function () {
        $hiddenField.val($(this).is(':checked') ? "true" : "false");
    });
}
function BindDocumentReady() {
    syncSwitchWithHiddenField();

    $('[id$=btnSelectFile]').unbind('click').click(function () {
        $('[id$=fuSlide]').click();
    });

    $('[id$=fuSlide]').change(function () {
        var fileInput = this;
        var file = fileInput.files[0];

        if (!file) return;

        var fileInput = $(this);
        var filePath = fileInput.val();
        var fileName = filePath.split('\\').pop();
        var fileExtension = fileName.split('.').pop().toLowerCase();

        var allowedExtensions = fileInput.attr('accept');
        var allowedArray = allowedExtensions.split(',').map(function (ext) { return ext.trim().toLowerCase().replace('.', ''); });

        if (allowedArray.includes(fileExtension)) {
            $('#lblSeciliDosyaAdi').text(fileName);

            var reader = new FileReader();
            reader.onload = function (e) {
                $('[id$=imgSlide]').attr('src', e.target.result);
            };
            reader.readAsDataURL(file);
        } else {
            $('#lblSeciliDosyaAdi').text(($('[id$=drpDilSecimi]').val() == '1' ? 'Geçersiz dosya türü. Sadece: ' : 'Invalid file extension. Allowed only: ') + allowedExtensions);
            fileInput.val('');
            $('[id$=imgSlide]').attr('src', '');
        }
    });

    $('[id$=btnClearFileSelection]').unbind('click').click(function () {
        $('[id$=fuSlide]').val('');
        $('#lblSeciliDosyaAdi').text('');
        $('[id$=imgSlide]').attr('src', '');
    });
    $('.clickEventLink').unbind('click').click(function () {
        var slideId = $(this).attr('slideId');
        var clickEventType = $(this).attr('clickEventType');
        var targetLink = $('.txtTargetLinkGrid[slideId=' + slideId + ']').text();
        var digiflowUrl = $('.txtDigiflowUrl[slideId=' + slideId + ']').text();
        var digiportUrl = $('.txtDigiportUrl[slideId=' + slideId + ']').text();
        var ajansUrl = $('.txtAjansUrl[slideId=' + slideId + ']').text();
        var popUpWindowWidth = $('.txtPopupWidthGrid[slideId=' + slideId + ']').text();
        var popUpWindowHeight = $('.txtPopupHeightGrid[slideId=' + slideId + ']').text();
        var linkYokUyari = $('[id$=drpDilSecimi]').val() == '1' ? 'Hedef link belirtilmemiş' : 'Target link is not defined';

        if (clickEventType == '1')//Belirtilen_Linki_Yeni_Sekmede_Aç
        {
            if (targetLink != '') {
                if (targetLink.indexOf('http') < 0)
                    targetLink = "http://" + targetLink;
                $('#anchorClickEvent').attr('href', targetLink);
                $('#anchorClickEvent')[0].click();
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '2')//Belirtilen_Linki_Yeni_Sayfada_Aç
        {
            if (targetLink.indexOf('http') < 0)
                targetLink = "http://" + targetLink;
            if (targetLink != '') {
                if (popupWindow && !popupWindow.closed) {
                    popupWindow.close();
                }
                popupWindow = window.open(targetLink, 'popup', 'width=' + popUpWindowWidth + ',height=' + popUpWindowHeight);
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '3')//Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç  
        {
            if (digiflowUrl != '') {
                $('#anchorClickEvent').attr('href', digiflowUrl);
                $('#anchorClickEvent')[0].click();
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '4')//Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç
        {
            if (digiflowUrl != '') {
                if (popupWindow && !popupWindow.closed) {
                    popupWindow.close();
                }
                popupWindow = window.open(digiflowUrl, 'popup', 'width=' + popUpWindowWidth + ',height=' + popUpWindowHeight);
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '5')//Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç
        {
            if (digiportUrl != '') {
                $('#anchorClickEvent').attr('href', digiportUrl);
                $('#anchorClickEvent')[0].click();
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '6')//Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç
        {
            if (digiportUrl != '') {
                if (popupWindow && !popupWindow.closed) {
                    popupWindow.close();
                }
                popupWindow = window.open(digiportUrl, 'popup', 'width=' + popUpWindowWidth + ',height=' + popUpWindowHeight);
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '7')//Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç
        {
            if (ajansUrl != '') {
                $('#anchorClickEvent').attr('href', ajansUrl);
                $('#anchorClickEvent')[0].click();
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '8')//Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç
        {
            if (ajansUrl != '') {
                if (popupWindow && !popupWindow.closed) {
                    popupWindow.close();
                }
                popupWindow = window.open(ajansUrl, 'popup', 'width=' + popUpWindowWidth + ',height=' + popUpWindowHeight);
            } else
                alert(linkYokUyari);
        }

    });
    
    try {
        BindDocumentReadyDigiportAdminHtmlContent();
    } catch (e) {

    }
}
$(document).ready(function () {
    BindDocumentReady();
});

