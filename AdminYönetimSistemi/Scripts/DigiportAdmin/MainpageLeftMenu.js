﻿var popupWindow = null;
function load() {
    Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
    Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(BeginRequestHandler);
    Sys.WebForms.PageRequestManager.getInstance().add_initializeRequest(InitializeRequestHandler);
}
function InitializeRequestHandler(e) {
    try {
        showloader();
    }
    catch (e) {
    }
}
function BeginRequestHandler(e) {
    try {
        showloader();
    }
    catch (e) {
    }
}
function EndRequestHandler(e) {
    try {
        hideloader();
        BindDocumentReady();
    }
    catch (e) {
    }
}
window.onload = function () {
    load();
};
$('form').on('submit', function () {
    try {
        if ($(this).valid()) {
            showloader();
        } else {
            e.preventDefault();
        }
    } catch (e) {

    }
});
function BindDocumentReady() {
    try {
        BindDocumentReadyDigiportAdminHtmlContent();
    } catch (e) {

    }
    try {
        BindDocumentReadyDigiportAdminHtmlContentNoMedia();
    } catch (e) {

    }
    $('.clickEventLink').unbind('click').click(function () {
        var contentid = $(this).attr('contentid');
        var clickEventType = $(this).attr('clickEventType');
        var targetLink = $('.txtTargetLinkGrid[contentid=' + contentid + ']').text();
        var digiflowUrl = $('.txtDigiflowUrl[contentid=' + contentid + ']').text();
        var digiportUrl = $('.txtDigiportUrl[contentid=' + contentid + ']').text();
        var ajansUrl = $('.txtAjansUrl[contentid=' + contentid + ']').text();
        var popUpWindowWidth = $('.txtPopupWidthGrid[contentid=' + contentid + ']').text();
        var popUpWindowHeight = $('.txtPopupHeightGrid[contentid=' + contentid + ']').text();
        var linkYokUyari = $('[id$=drpDilSecimi]').val() == '1' ? 'Hedef link belirtilmemiş' : 'Target link is not defined';

        if (clickEventType == '1')//Belirtilen_Linki_Yeni_Sekmede_Aç
        {
            if (targetLink != '') {
                if (targetLink.indexOf('http') < 0)
                    targetLink = "http://" + targetLink;
                $('#anchorClickEvent').attr('href', targetLink);
                $('#anchorClickEvent')[0].click();
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '2')//Belirtilen_Linki_Yeni_Sayfada_Aç
        {
            if (targetLink.indexOf('http') < 0)
                targetLink = "http://" + targetLink;
            if (targetLink != '') {
                if (popupWindow && !popupWindow.closed) {
                    popupWindow.close();
                }
                popupWindow = window.open(targetLink, 'popup', 'width=' + popUpWindowWidth + ',height=' + popUpWindowHeight);
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '3')//Belirtilen_Html_İçeriği_Yeni_Sekmede_Aç  
        {
            if (digiflowUrl != '') {
                $('#anchorClickEvent').attr('href', digiflowUrl);
                $('#anchorClickEvent')[0].click();
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '4')//Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç
        {
            if (digiflowUrl != '') {
                if (popupWindow && !popupWindow.closed) {
                    popupWindow.close();
                }
                popupWindow = window.open(digiflowUrl, 'popup', 'width=' + popUpWindowWidth + ',height=' + popUpWindowHeight);
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '5')//Belirtilen_Html_İçeriği_Yeni_Digiport_Sekmede_Aç
        {
            if (digiportUrl != '') {
                $('#anchorClickEvent').attr('href', digiportUrl);
                $('#anchorClickEvent')[0].click();
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '6')//Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç
        {
            if (digiportUrl != '') {
                if (popupWindow && !popupWindow.closed) {
                    popupWindow.close();
                }
                popupWindow = window.open(digiportUrl, 'popup', 'width=' + popUpWindowWidth + ',height=' + popUpWindowHeight);
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '7')//Belirtilen_Html_İçeriği_Yeni_Ajans_Sekmede_Aç
        {
            if (ajansUrl != '') {
                $('#anchorClickEvent').attr('href', ajansUrl);
                $('#anchorClickEvent')[0].click();
            } else
                alert(linkYokUyari);
        }
        else if (clickEventType == '8')//Belirtilen_Html_İçeriği_Yeni_Ajans_Sayfada_Aç
        {
            if (ajansUrl != '') {
                if (popupWindow && !popupWindow.closed) {
                    popupWindow.close();
                }
                popupWindow = window.open(ajansUrl, 'popup', 'width=' + popUpWindowWidth + ',height=' + popUpWindowHeight);
            } else
                alert(linkYokUyari);
        }

    });
}


$(document).ready(function () {
    BindDocumentReady();
});