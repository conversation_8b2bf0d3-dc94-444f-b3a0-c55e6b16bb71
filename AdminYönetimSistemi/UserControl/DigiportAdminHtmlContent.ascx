﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="DigiportAdminHtmlContent.ascx.cs"  Inherits="AracTakipSistemi.UserControl.DigiportAdminHtmlContent" %>
<link href="/Content/SummerNote/summernote-lite.min.css" rel="stylesheet" />
<script src="/Content/SummerNote/summernote-lite.min.js"></script>
<script src="/Content/SummerNote/summernote-tr-TR.min.js"></script>

<asp:TextBox ID="txtHtmlContent" runat="server" Style="display: none;" TextMode="MultiLine" CssClass="d-none"></asp:TextBox>
<asp:TextBox runat="server" ID="summernote" TextMode="MultiLine"></asp:TextBox>

<asp:HiddenField runat="server" ID="hdnDigiportAdminHtmlEditorIzinliResim" />
<asp:HiddenField runat="server" ID="hdnDigiportAdminHtmlEditorIzinliVideo" />
<asp:HiddenField runat="server" ID="hdnDigiportAdminHtmlEditorIzinliDigerDosya" />
<asp:HiddenField runat="server" ID="hdnComponentName" />
<script>
    var lang = $('[id$=drpDilSecimi]').val() == '1' ? 'tr-TR' : 'en-US';
    function BindDocumentReadyDigiportAdminHtmlContent() {
        $('[id$=summernote]').summernote({
            height: 200,
            lang: lang,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'italic', 'underline', 'clear', 'strikethrough', 'superscript', 'subscript']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['picture', 'link', 'video', 'customVideo', 'CustomFile']],
                ['view', ['fullscreen', 'codeview']]
            ],
            popover: {
                image: [
                    ['custom', ['resize25', 'resize50', 'resize60', 'resize70', 'resize80', 'resize90', 'resize100']],
                    //['float', ['floatLeft', 'floatRight', 'floatNone']],
                    ['custom', ['alignLeft', 'alignCenter', 'alignRight']],
                    ['remove', ['removeMedia']]
                ]
            },
            buttons: {
                customVideo: CustomVideoButton,
                CustomFile: CustomFileButton,
                resize25: function (context) {
                    return resizeButton(context, '25%');
                },
                resize50: function (context) {
                    return resizeButton(context, '50%');
                },
                resize60: function (context) {
                    return resizeButton(context, '60%');
                },
                resize70: function (context) {
                    return resizeButton(context, '70%');
                },
                resize80: function (context) {
                    return resizeButton(context, '80%');
                },
                resize90: function (context) {
                    return resizeButton(context, '90%');
                },
                resize100: function (context) {
                    return resizeButton(context, '100%');
                },
                alignLeft: function (context) {
                    var ui = $.summernote.ui;
                    return ui.button({
                        contents: '<i class="note-icon-align-left"/>',
                        tooltip: 'Align left',
                        click: function () {
                            var $img = $(context.invoke('restoreTarget'));
                            $img.css('display', 'block').css('margin', '0').css('text-align', '');
                            $img.css('margin-left', '0').css('margin-right', 'auto');
                        }
                    }).render();
                },
                alignCenter: function (context) {
                    var ui = $.summernote.ui;
                    return ui.button({
                        contents: '<i class="note-icon-align-center"/>',
                        tooltip: 'Align center',
                        click: function () {
                            var $img = $(context.invoke('restoreTarget'));
                            $img.css('display', 'block').css('margin', '0 auto');
                        }
                    }).render();
                },
                alignRight: function (context) {
                    var ui = $.summernote.ui;
                    return ui.button({
                        contents: '<i class="note-icon-align-right"/>',
                        tooltip: 'Align right',
                        click: function () {
                            var $img = $(context.invoke('restoreTarget'));
                            $img.css('display', 'block').css('margin-left', 'auto').css('margin-right', '0');
                        }
                    }).render();
                }
            },
            callbacks: {
                onImageUpload: function (files) {
                    for (var i = 0; i < files.length; i++) {
                        uploadFile(files[i]);
                    }
                },
                onMediaDelete: function ($target) {
                    var componentName = $('[id$=hdnComponentName]').val();
                    var src = $target.attr('src');
                    $.ajax({
                        url: '/Handlers/DeleteMediaHandler.ashx',
                        type: 'POST',
                        data: { fileUrl: src, componentName: componentName },
                        success: function (res) {
                        }
                    });
                }
            }
        });

        function resizeButton(context, percent) {
            var ui = $.summernote.ui;
            return ui.button({
                contents: percent,
                tooltip: 'Resize to ' + percent,
                click: function () {
                    var $img = $(context.invoke('restoreTarget'));
                    if ($img && $img.length > 0) {
                        $img.css({
                            width: percent,
                            height: 'auto'
                        });
                    }
                }
            }).render();
        }
        // Summernote içeriğini TextBox'a aktar
        $("form").submit(function () {
            try {
                $('[id$=txtHtmlContent]').val($('[id$=summernote]').summernote('code'));
            } catch (e) {

            }
            try {
                $('[id$=txtHtmlContentNoMedia]').val($('[id$=summernoteNoMedia]').summernote('code'));
            } catch (e) {

            }
            try {
                $('[id$=txtHtmlContentHrApp]').val($('[id$=summernoteHrApp]').summernote('code'));
            } catch (e) {

            }
        });
        bindVideoDeleteButton();
    }
    function uploadFile(file) {
        var arrayResim = $('[id$=hdnDigiportAdminHtmlEditorIzinliResim]').val().split(',');
        var arrayVideo = $('[id$=hdnDigiportAdminHtmlEditorIzinliVideo]').val().split(',');
        var arrayDiger = $('[id$=hdnDigiportAdminHtmlEditorIzinliDigerDosya]').val().split(',');
        var data = new FormData();
        var componentName = $('[id$=hdnComponentName]').val();
        data.append("file", file);
        data.append("componentName", componentName);
        showloader();
        setTimeout(function () {
            $.ajax({
                url: '/Handlers/UploadMediaHandler.ashx',
                type: "POST",
                data: data,
                contentType: false,
                processData: false,
                success: function (url) {
                    const ext = url.split('.').pop().toLowerCase();
                    if (arrayVideo.includes(ext)) {
                        const videoHtml = '<video controls width="100%" src="' + url+'"></video>';
                        $('[id$=summernote]').summernote('pasteHTML', videoHtml);
                        bindVideoDeleteButton();
                    } else if (arrayResim.includes(ext)) {
                        $('[id$=summernote]').summernote('insertImage', url);
                    }
                    else if (arrayDiger.includes(ext)) {
                        const linkHtml = '<a class="uploadedOtherMedia" href="' + url + '" target="_blank">' + file.name + '</a>';
                        $('[id$=summernote]').summernote('pasteHTML', linkHtml);
                    }
                },
                error: function (error) {
                    var msg = lang == 'tr-TR' ? "Yükleme hatası!" : "Upload Error!";
                    msg += error.responseText;
                    alert(msg);
                },
                complete: function () {
                    hideloader();
                }
            });
        }, 100);
    }
    function bindVideoDeleteButton() {
        var componentName = $('[id$=hdnComponentName]').val();
        $('[id$=summernote]').next('.note-editable video').click(function (e) {
            $('.delete-btn').remove(); // varsa kaldır

            let $el = $(this);
            let offset = $el.offset();
            let $btn = $('<button class="delete-btn mydeletevideobutton">×</button>');
            $btn.css({
                top: offset.top - 10,
                left: offset.left + $el.width() - 10,
                position: 'absolute'
            }).data('target', $el);

            $('body').append($btn);

            $('.mydeletevideobutton').click(function (e) {
                let $target = $(this).data('target');
                let src = $target.attr('src');
                $target.remove();
                $(this).remove();
                $.post('/Handlers/DeleteMediaHandler.ashx', { fileUrl: src, componentName: componentName });
            });
        });

        // Boşluğa tıklanınca butonu gizle
        $(document).on('click', function (e) {
            if (!$(e.target).is('video, .delete-btn')) {
                $('.delete-btn').remove();
            }
        });
    }

    function CustomFileButton(context) {
        var ui = $.summernote.ui;
        var $editor = context.layoutInfo.editor;

        var button = ui.button({
            contents: '<i class="fa fa-file"></i>',
            container: $editor,
            tooltip: lang == 'tr-TR' ? 'Dosya Yükle' : 'Upload File',
            click: function () {
                var array = $('[id$=hdnDigiportAdminHtmlEditorIzinliDigerDosya]').val().split(',');
                var accept = '';
                for (var i = 0; i < array.length; i++) {
                    accept += (accept != '' ? ',' : '') + "." + array[i];
                }
                var fileInput = $('<input type="file" accept="' + accept + '">');
                fileInput.trigger('click');

                fileInput.on('change', function () {
                    var file = this.files[0];
                    var fileName = file.name;
                    var ext = fileName.split('.').pop().toLowerCase();

                    if (file) {
                        if (array.includes(ext)) {
                            uploadFile(file);
                        }
                    }
                });
            }
        });

        return button.render();
    }

    // Custom video upload butonu
    function CustomVideoButton(context) {
        var ui = $.summernote.ui;
        var $editor = context.layoutInfo.editor;
        // buton oluştur
        var button = ui.button({
            contents: '<i class="note-icon-video"/>',
            container: $editor,
            tooltip: lang == 'tr-TR' ? 'Video Yükle' : 'Upload Video',
            click: function () {
                var array = $('[id$=hdnDigiportAdminHtmlEditorIzinliVideo]').val().split(',');
                var accept = '';
                for (var i = 0; i < array.length; i++) {
                    accept += (accept != '' ? ',' : '') + "video/" + array[i];
                }
                var fileInput = $('<input type="file" accept="' + accept + '">');
                fileInput.trigger('click');

                fileInput.on('change', function () {
                    var file = this.files[0];
                    var fileName = file.name;
                    var ext = fileName.split('.').pop().toLowerCase();

                    if (file) {
                        if (array.includes(ext)) {
                            uploadFile(file);
                        }
                    }
                });
            }
        });

        return button.render(); // DOM objesini döner
    }
</script>
