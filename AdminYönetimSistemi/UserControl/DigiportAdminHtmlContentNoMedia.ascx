﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="DigiportAdminHtmlContentNoMedia.ascx.cs"  Inherits="AracTakipSistemi.UserControl.DigiportAdminHtmlContentNoMedia" %>
<link href="/Content/SummerNote/summernote-lite.min.css" rel="stylesheet" />
<script src="/Content/SummerNote/summernote-lite.min.js"></script>
<script src="/Content/SummerNote/summernote-tr-TR.min.js"></script>

<asp:TextBox ID="txtHtmlContentNoMedia" runat="server" Style="display: none;" TextMode="MultiLine" CssClass="d-none"></asp:TextBox>
<asp:TextBox runat="server" ID="summernoteNoMedia" TextMode="MultiLine"></asp:TextBox>

<script>
    var lang = $('[id$=drpDilSecimi]').val() == '1' ? 'tr-TR' : 'en-US';
    function BindDocumentReadyDigiportAdminHtmlContentNoMedia() {
        $('[id$=summernoteNoMedia]').summernote({
            height: 70,
            lang: lang,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'italic', 'underline', 'clear', 'strikethrough', 'superscript', 'subscript']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['view', ['fullscreen', 'codeview']]
            ]
        });
        // Summernote içeriğini TextBox'a aktar
        $("form").submit(function () {
            try {
                $('[id$=txtHtmlContent]').val($('[id$=summernote]').summernote('code'));
            } catch (e) {

            }
            try {
                $('[id$=txtHtmlContentNoMedia]').val($('[id$=summernoteNoMedia]').summernote('code'));
            } catch (e) {

            }
            try {
                $('[id$=txtHtmlContentHrApp]').val($('[id$=summernoteHrApp]').summernote('code'));
            } catch (e) {

            }
        });
    }
</script>