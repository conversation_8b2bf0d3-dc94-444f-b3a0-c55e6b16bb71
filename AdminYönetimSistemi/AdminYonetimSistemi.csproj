﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{2D884432-DE60-4F4C-887E-0716CAD5C328}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AracTakipSistemi</RootNamespace>
    <AssemblyName>AracTakipSistemi</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication>
    </IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>
    </IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <Use64BitIISExpress />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AdminUserCtrl, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\AdminUserCtrl\bin\AdminUserCtrl.dll</HintPath>
    </Reference>
    <Reference Include="AdPortalWeb">
      <HintPath>..\..\..\..\AdportalCC\AdPortalWeb2\bin\AdPortalWeb.dll</HintPath>
    </Reference>
    <Reference Include="AjaxControlToolkit, Version=16.1.1.0, Culture=neutral, PublicKeyToken=28f01b0e84b6d53e, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\AdminUserCtrl\bin\AjaxControlToolkit.dll</HintPath>
    </Reference>
    <Reference Include="ClosedXML, Version=0.96.0.0, Culture=neutral, PublicKeyToken=fd1eb21b62ae805b, processorArchitecture=MSIL">
      <HintPath>..\packages\ClosedXML.0.96.0\lib\net40\ClosedXML.dll</HintPath>
    </Reference>
    <Reference Include="CoreHelpers, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\CoreHelpers.dll</HintPath>
    </Reference>
    <Reference Include="DataAccessLayer, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v16.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\DevExpress 16.2\Components\Bin\Framework\DevExpress.Data.v16.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v16.2.Core, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\DevExpress 16.2\Components\Bin\Framework\DevExpress.Printing.v16.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.v16.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\DevExpress 16.2\Components\Bin\Framework\DevExpress.Web.v16.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPrinting.v16.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\DevExpress 16.2\Components\Bin\Framework\DevExpress.XtraPrinting.v16.2.dll</HintPath>
    </Reference>
    <Reference Include="DigiportMenuDisplayHelpers, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\DigiportMenuDisplayHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.Library, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Framework.Library.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.Repository, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Framework.Repository.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authentication, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Authentication.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.CoreHelpers, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.CoreHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.DataAccessLayer, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WebCore, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WebCore.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowHelpers, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WorkFlowHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Repository, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Repository.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=2.16.0.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.16.0\lib\net40\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="Entity_Base, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Entity_Base.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.Interfaces, Version=6.0.0.0, Culture=neutral, PublicKeyToken=a694d7f3b0907a61, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.Interfaces.6.0.0\lib\net35\EPPlus.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.System.Drawing, Version=6.0.0.0, Culture=neutral, PublicKeyToken=2308d35469c9bac0, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.System.Drawing.6.0.0\lib\net35\EPPlus.System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="MsgBoxCtrl">
      <HintPath>\\dtl1iis3\Deployment\msgboxctrlNET4\bin\MsgBoxCtrl.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess, Version=4.122.19.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\ODPNET\x86\Net4\Oracle.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Xml" />
    <Reference Include="WebCore, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\WebCore.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="AdminPages\AracTakipSistemi\AracMarka.aspx" />
    <Content Include="AdminPages\AracTakipSistemi\AracModel.aspx" />
    <Content Include="AdminPages\AracTakipSistemi\AracPlaka.aspx" />
    <Content Include="AdminPages\AracTakipSistemi\AracRenk.aspx" />
    <Content Include="AdminPages\AracTakipSistemi\AracSınıf.aspx" />
    <Content Include="AdminPages\AracTakipSistemi\AracYıl.aspx" />
    <Content Include="AdminPages\DigiportAdmin\DisplayContent.aspx" />
    <Content Include="AdminPages\DigiportAdmin\HrMediaKategori.aspx" />
    <Content Include="AdminPages\DigiportAdmin\IndirimFirsatiKategori.aspx" />
    <Content Include="AdminPages\DigiportAdmin\Names.aspx" />
    <Content Include="AdminPages\DigiportAdmin\Pages\HrMediaAnnouncementSlides.aspx" />
    <Content Include="AdminPages\DigiportAdmin\Pages\IndirimFirsati.aspx" />
    <Content Include="AdminPages\DigiportAdmin\Pages\Linkler.aspx" />
    <Content Include="AdminPages\DigiportAdmin\Pages\MainpageAnnouncements.aspx" />
    <Content Include="AdminPages\DigiportAdmin\Pages\MainpageLeftMenu.aspx" />
    <Content Include="AdminPages\DigiportAdmin\Pages\MainSubBanner.aspx" />
    <Content Include="AdminPages\DigiportAdmin\Pages\MainTopBanner.aspx" />
    <Content Include="AdminPages\DigiportAdmin\SliderOptions.aspx" />
    <Content Include="AdminPages\DigiportAdmin\Types.aspx" />
    <Content Include="AdminPages\DigiportAdmin\UserAssignment.aspx" />
    <Content Include="AdminPages\DigiportAdmin\UserDebug.aspx" />
    <Content Include="AdminPages\DigiportAnket\DomainKullaniciTanimlama.aspx" />
    <Content Include="AdminPages\DigiportAnket\DomainTanimlama.aspx" />
    <Content Include="AdminPages\EkipmanTalepFormu\EkipmanTalepleriRaporu.aspx" />
    <Content Include="AdminPages\EkipmanTalepFormu\ExcelYukleme.aspx" />
    <Content Include="AdminPages\EkipmanTalepFormu\MalzemeTanim.aspx" />
    <Content Include="AdminPages\EkipmanTalepFormu\YTSTanimlama.aspx" />
    <Content Include="AdminPages\Exception\Hata.aspx" />
    <Content Include="AdminPages\KurumsalKirtasiye\DepoTanimlama.aspx" />
    <Content Include="AdminPages\KurumsalKirtasiye\FirmaTanimlama.aspx" />
    <Content Include="AdminPages\KurumsalKirtasiye\StokGrupTanimlama.aspx" />
    <Content Include="AdminPages\KurumsalKirtasiye\StokTanimlama.aspx" />
    <Content Include="AdminPages\Muhaberat\TurTanimlama.aspx" />
    <Content Include="AdminPages\TestMate\TmateDomainsAdmins.aspx" />
    <Content Include="AdminPages\TestMate\TmateProjects.aspx" />
    <Content Include="AdminPages\TestMate\TmateProjectUsers.aspx" />
    <Content Include="AdminPages\TestMate\TmateStatusEntry.aspx" />
    <Content Include="Anasayfa.aspx" />
    <Content Include="ApplicationInsights.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="App_GlobalResources\DigiportAdminResource.en.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>DigiportAdminResource.en.designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="App_GlobalResources\DigiportAdminResource.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>DigiportAdminResource.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="bin\AdminUserCtrl.dll" />
    <Content Include="bin\AdPortalWeb.dll" />
    <Content Include="bin\AjaxControlToolkit.dll" />
    <Content Include="bin\App_Licenses.dll" />
    <Content Include="bin\ClosedXML.dll" />
    <Content Include="bin\CoreHelpers.dll" />
    <Content Include="bin\DataAccessLayer.dll" />
    <Content Include="bin\DevExpress.Data.v16.2.dll" />
    <Content Include="bin\DevExpress.Office.v16.2.Core.dll" />
    <Content Include="bin\DevExpress.Pdf.v16.2.Core.dll" />
    <Content Include="bin\DevExpress.Printing.v16.2.Core.dll" />
    <Content Include="bin\DevExpress.RichEdit.v16.2.Core.dll" />
    <Content Include="bin\DevExpress.Sparkline.v16.2.Core.dll" />
    <Content Include="bin\DevExpress.Utils.v16.2.dll" />
    <Content Include="bin\DevExpress.Web.v16.2.dll" />
    <Content Include="bin\DevExpress.XtraBars.v16.2.dll" />
    <Content Include="bin\DevExpress.XtraEditors.v16.2.dll" />
    <Content Include="bin\DevExpress.XtraLayout.v16.2.dll" />
    <Content Include="bin\DevExpress.XtraPrinting.v16.2.dll" />
    <Content Include="bin\DevExpress.XtraTreeList.v16.2.dll" />
    <Content Include="bin\Digiturk.Framework.Library.dll" />
    <Content Include="bin\Digiturk.Framework.Repository.dll" />
    <Content Include="bin\Digiturk.Workflow.Digiflow.Authentication.dll" />
    <Content Include="bin\Digiturk.Workflow.Digiflow.CoreHelpers.dll" />
    <Content Include="bin\Digiturk.Workflow.Digiflow.DataAccessLayer.dll" />
    <Content Include="bin\Digiturk.Workflow.Digiflow.WebCore.dll" />
    <Content Include="bin\Digiturk.Workflow.Digiflow.WorkFlowHelpers.dll" />
    <Content Include="bin\Digiturk.Workflow.Repository.dll" />
    <Content Include="bin\DocumentFormat.OpenXml.dll" />
    <Content Include="bin\Entity_Base.dll" />
    <Content Include="bin\EPPlus.Interfaces.dll" />
    <Content Include="bin\EPPlus.System.Drawing.dll" />
    <Content Include="bin\MsgBoxCtrl.dll" />
    <Content Include="bin\Oracle.DataAccess.dll" />
    <Content Include="bin\WebCore.dll" />
    <Content Include="Content\Site.css" />
    <Content Include="Content\SummerNote\font\summernote.eot" />
    <Content Include="Content\SummerNote\font\summernote.hash" />
    <Content Include="Content\SummerNote\font\summernote.ttf" />
    <Content Include="Content\SummerNote\font\summernote.woff" />
    <Content Include="Content\SummerNote\font\summernote.woff2" />
    <Content Include="Content\SummerNote\summernote-lite.min.css" />
    <Content Include="Content\SummerNote\summernote-lite.min.js" />
    <Content Include="Content\SummerNote\summernote-tr-TR.min.js" />
    <Content Include="css\AjaxAutoComplete.css" />
    <Content Include="css\DigiportAdmin.css" />
    <Content Include="css\fontawesome_5.14.15.css" />
    <Content Include="css\select2-4.1.0.min.css" />
    <Content Include="css\select2.min.css" />
    <Content Include="css\style.css" />
    <Content Include="ddlevelsfiles\arrow-down.gif" />
    <Content Include="ddlevelsfiles\arrow-right.gif" />
    <Content Include="ddlevelsfiles\bulletlist.gif" />
    <Content Include="ddlevelsfiles\ddlevelsmenu-base.css" />
    <Content Include="ddlevelsfiles\ddlevelsmenu-sidebar.css" />
    <Content Include="ddlevelsfiles\ddlevelsmenu-topbar.css" />
    <Content Include="ddlevelsfiles\ddlevelsmenu.js" />
    <Content Include="ddlevelsfiles\jquery-3.6.1.js" />
    <Content Include="favicon.ico" />
    <Content Include="Global.asax" />
    <Content Include="Handlers\DeleteMediaHandler.ashx" />
    <Content Include="Handlers\UploadMediaHandler.ashx" />
    <Content Include="images\ajax-loader.gif" />
    <Content Include="images\bgri_ring-01.jpg" />
    <Content Include="images\cancel.png" />
    <Content Include="images\check.png" />
    <Content Include="images\digiturk_logo.png" />
    <Content Include="images\geri.png" />
    <Content Include="images\helpbtn.png" />
    <Content Include="images\kurumsal_back.jpg" />
    <Content Include="images\loading.gif" />
    <Content Include="images\mobilminus.jpg" />
    <Content Include="images\mobilplus.jpg" />
    <Content Include="images\not-check.png" />
    <Content Include="images\ok.png" />
    <Content Include="images\okk.png" />
    <Content Include="images\ring-01.jpg" />
    <Content Include="images\ringKucuk.jpg" />
    <Content Include="images\rubber.png" />
    <Content Include="images\select-icons.png" />
    <Content Include="images\thrash.png" />
    <Content Include="images\Warning.gif" />
    <Content Include="Scripts\DigiportAdmin\HrMediaSlides.js" />
    <Content Include="Scripts\DigiportAdmin\IndirimFirsati.js" />
    <Content Include="Scripts\DigiportAdmin\Linkler.js" />
    <Content Include="Scripts\DigiportAdmin\MainpageAnnouncements.js" />
    <Content Include="Scripts\DigiportAdmin\MainpageLeftMenu.js" />
    <Content Include="Scripts\DigiportAdmin\MainSubBanner.js" />
    <Content Include="bin\ApplicationInsights.config" />
    <Content Include="Handlers\UploadMediaHandlerHrApp.ashx" />
    <Content Include="Handlers\DeleteMediaHandlerHrApp.ashx" />
    <Content Include="Scripts\jquery-3.6.0.min.js" />
    <Content Include="Scripts\jquery-ui.min.js" />
    <Content Include="webfonts\fa-brands-400.svg" />
    <Content Include="webfonts\fa-regular-400.svg" />
    <Content Include="webfonts\fa-solid-900.svg" />
    <None Include="packages.config" />
    <None Include="Properties\PublishProfiles\AdminYonetimSistemiPublish.pubxml" />
    <None Include="Scripts\jquery-1.7.1.intellisense.js" />
    <Content Include="Scripts\DigiportAdmin\MainTopBanner.js" />
    <Content Include="Scripts\jquery-1.7.1.js" />
    <Content Include="Scripts\jquery-1.7.1.min.js" />
    <Content Include="Scripts\jquery-1.9.min.js" />
    <Content Include="Scripts\jquery.min.js" />
    <Content Include="Scripts\select2-4.1.0.min.js" />
    <Content Include="Scripts\select2.min.js" />
    <Content Include="Site.Master" />
    <Content Include="TestPageAdmin.aspx" />
    <Content Include="UserControl\AdminKaydet.ascx" />
    <Content Include="UserControl\DigiportAdminHrAppHtmlContent.ascx" />
    <Content Include="UserControl\DigiportAdminHtmlContent.ascx" />
    <Content Include="UserControl\DigiportAdminHtmlContentNoMedia.ascx" />
    <Content Include="UserControl\MsgBoxCtrl.ascx" />
    <None Include="Web.config" />
    <Content Include="Bundle.config" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <Content Include="webfonts\fa-brands-400.eot" />
    <Content Include="webfonts\fa-brands-400.ttf" />
    <Content Include="webfonts\fa-brands-400.woff" />
    <Content Include="webfonts\fa-brands-400.woff2" />
    <Content Include="webfonts\fa-regular-400.eot" />
    <Content Include="webfonts\fa-regular-400.ttf" />
    <Content Include="webfonts\fa-regular-400.woff" />
    <Content Include="webfonts\fa-regular-400.woff2" />
    <Content Include="webfonts\fa-solid-900.eot" />
    <Content Include="webfonts\fa-solid-900.ttf" />
    <Content Include="webfonts\fa-solid-900.woff" />
    <Content Include="webfonts\fa-solid-900.woff2" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AdminPages\AracTakipSistemi\AracMarka.aspx.cs">
      <DependentUpon>AracMarka.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\AracTakipSistemi\AracMarka.aspx.designer.cs">
      <DependentUpon>AracMarka.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\AracTakipSistemi\AracModel.aspx.cs">
      <DependentUpon>AracModel.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\AracTakipSistemi\AracModel.aspx.designer.cs">
      <DependentUpon>AracModel.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\AracTakipSistemi\AracPlaka.aspx.cs">
      <DependentUpon>AracPlaka.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\AracTakipSistemi\AracPlaka.aspx.designer.cs">
      <DependentUpon>AracPlaka.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\AracTakipSistemi\AracRenk.aspx.cs">
      <DependentUpon>AracRenk.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\AracTakipSistemi\AracRenk.aspx.designer.cs">
      <DependentUpon>AracRenk.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\AracTakipSistemi\AracSınıf.aspx.cs">
      <DependentUpon>AracSınıf.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\AracTakipSistemi\AracSınıf.aspx.designer.cs">
      <DependentUpon>AracSınıf.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\AracTakipSistemi\AracYıl.aspx.cs">
      <DependentUpon>AracYıl.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\AracTakipSistemi\AracYıl.aspx.designer.cs">
      <DependentUpon>AracYıl.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\DigiportSecurePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\DisplayContent.aspx.cs">
      <DependentUpon>DisplayContent.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\DisplayContent.aspx.designer.cs">
      <DependentUpon>DisplayContent.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\HrMediaKategori.aspx.cs">
      <DependentUpon>HrMediaKategori.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\HrMediaKategori.aspx.designer.cs">
      <DependentUpon>HrMediaKategori.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\IndirimFirsatiKategori.aspx.cs">
      <DependentUpon>IndirimFirsatiKategori.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\IndirimFirsatiKategori.aspx.designer.cs">
      <DependentUpon>IndirimFirsatiKategori.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Names.aspx.cs">
      <DependentUpon>Names.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Names.aspx.designer.cs">
      <DependentUpon>Names.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\HrMediaAnnouncementSlides.aspx.cs">
      <DependentUpon>HrMediaAnnouncementSlides.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\HrMediaAnnouncementSlides.aspx.designer.cs">
      <DependentUpon>HrMediaAnnouncementSlides.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\IndirimFirsati.aspx.cs">
      <DependentUpon>IndirimFirsati.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\IndirimFirsati.aspx.designer.cs">
      <DependentUpon>IndirimFirsati.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\Linkler.aspx.cs">
      <DependentUpon>Linkler.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\Linkler.aspx.designer.cs">
      <DependentUpon>Linkler.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\MainpageAnnouncements.aspx.cs">
      <DependentUpon>MainpageAnnouncements.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\MainpageAnnouncements.aspx.designer.cs">
      <DependentUpon>MainpageAnnouncements.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\MainpageLeftMenu.aspx.cs">
      <DependentUpon>MainpageLeftMenu.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\MainpageLeftMenu.aspx.designer.cs">
      <DependentUpon>MainpageLeftMenu.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\MainSubBanner.aspx.cs">
      <DependentUpon>MainSubBanner.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\MainSubBanner.aspx.designer.cs">
      <DependentUpon>MainSubBanner.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\MainTopBanner.aspx.cs">
      <DependentUpon>MainTopBanner.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Pages\MainTopBanner.aspx.designer.cs">
      <DependentUpon>MainTopBanner.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\SliderOptions.aspx.cs">
      <DependentUpon>SliderOptions.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\SliderOptions.aspx.designer.cs">
      <DependentUpon>SliderOptions.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Types.aspx.cs">
      <DependentUpon>Types.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\Types.aspx.designer.cs">
      <DependentUpon>Types.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\UserAssignment.aspx.cs">
      <DependentUpon>UserAssignment.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\UserAssignment.aspx.designer.cs">
      <DependentUpon>UserAssignment.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\UserDebug.aspx.cs">
      <DependentUpon>UserDebug.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAdmin\UserDebug.aspx.designer.cs">
      <DependentUpon>UserDebug.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAnket\DomainKullaniciTanimlama.aspx.cs">
      <DependentUpon>DomainKullaniciTanimlama.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAnket\DomainKullaniciTanimlama.aspx.designer.cs">
      <DependentUpon>DomainKullaniciTanimlama.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\DigiportAnket\DomainTanimlama.aspx.cs">
      <DependentUpon>DomainTanimlama.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\DigiportAnket\DomainTanimlama.aspx.designer.cs">
      <DependentUpon>DomainTanimlama.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\EkipmanTalepFormu\EkipmanTalepleriRaporu.aspx.cs">
      <DependentUpon>EkipmanTalepleriRaporu.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\EkipmanTalepFormu\EkipmanTalepleriRaporu.aspx.designer.cs">
      <DependentUpon>EkipmanTalepleriRaporu.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\EkipmanTalepFormu\ExcelYukleme.aspx.cs">
      <DependentUpon>ExcelYukleme.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\EkipmanTalepFormu\ExcelYukleme.aspx.designer.cs">
      <DependentUpon>ExcelYukleme.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\EkipmanTalepFormu\MalzemeTanim.aspx.cs">
      <DependentUpon>MalzemeTanim.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\EkipmanTalepFormu\MalzemeTanim.aspx.designer.cs">
      <DependentUpon>MalzemeTanim.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\EkipmanTalepFormu\YTSTanimlama.aspx.cs">
      <DependentUpon>YTSTanimlama.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\EkipmanTalepFormu\YTSTanimlama.aspx.designer.cs">
      <DependentUpon>YTSTanimlama.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\Exception\Hata.aspx.cs">
      <DependentUpon>Hata.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\Exception\Hata.aspx.designer.cs">
      <DependentUpon>Hata.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\KurumsalKirtasiye\DepoTanimlama.aspx.cs">
      <DependentUpon>DepoTanimlama.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\KurumsalKirtasiye\DepoTanimlama.aspx.designer.cs">
      <DependentUpon>DepoTanimlama.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\KurumsalKirtasiye\FirmaTanimlama.aspx.cs">
      <DependentUpon>FirmaTanimlama.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\KurumsalKirtasiye\FirmaTanimlama.aspx.designer.cs">
      <DependentUpon>FirmaTanimlama.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\KurumsalKirtasiye\StokGrupTanimlama.aspx.cs">
      <DependentUpon>StokGrupTanimlama.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\KurumsalKirtasiye\StokGrupTanimlama.aspx.designer.cs">
      <DependentUpon>StokGrupTanimlama.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\KurumsalKirtasiye\StokTanimlama.aspx.cs">
      <DependentUpon>StokTanimlama.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\KurumsalKirtasiye\StokTanimlama.aspx.designer.cs">
      <DependentUpon>StokTanimlama.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\Muhaberat\TurTanimlama.aspx.cs">
      <DependentUpon>TurTanimlama.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\Muhaberat\TurTanimlama.aspx.designer.cs">
      <DependentUpon>TurTanimlama.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\TestMate\TmateDomainsAdmins.aspx.cs">
      <DependentUpon>TmateDomainsAdmins.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\TestMate\TmateDomainsAdmins.aspx.designer.cs">
      <DependentUpon>TmateDomainsAdmins.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\TestMate\TmateProjects.aspx.cs">
      <DependentUpon>TmateProjects.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\TestMate\TmateProjects.aspx.designer.cs">
      <DependentUpon>TmateProjects.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\TestMate\TmateProjectUsers.aspx.cs">
      <DependentUpon>TmateProjectUsers.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\TestMate\TmateProjectUsers.aspx.designer.cs">
      <DependentUpon>TmateProjectUsers.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPages\TestMate\TmateStatusEntry.aspx.cs">
      <DependentUpon>TmateStatusEntry.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPages\TestMate\TmateStatusEntry.aspx.designer.cs">
      <DependentUpon>TmateStatusEntry.aspx</DependentUpon>
    </Compile>
    <Compile Include="Anasayfa.aspx.cs">
      <DependentUpon>Anasayfa.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Anasayfa.aspx.designer.cs">
      <DependentUpon>Anasayfa.aspx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\DigiportAdminResource.Designer.cs">
      <DependentUpon>DigiportAdminResource.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="App_GlobalResources\DigiportAdminResource.en.designer.cs">
      <DependentUpon>DigiportAdminResource.en.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="App_GlobalResources\TmateResource.Designer.cs">
      <DependentUpon>TmateResource.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="App_GlobalResources\TmateResource.en.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>TmateResource.en.resx</DependentUpon>
    </Compile>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\IdentityConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\Startup.Auth.cs" />
    <Compile Include="DigiportAuthorizationModule.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Handlers\DeleteMediaHandler.ashx.cs">
      <DependentUpon>DeleteMediaHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\DeleteMediaHandlerHrApp.ashx.cs">
      <DependentUpon>DeleteMediaHandlerHrApp.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\UploadMediaHandler.ashx.cs">
      <DependentUpon>UploadMediaHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Handlers\UploadMediaHandlerHrApp.ashx.cs">
      <DependentUpon>UploadMediaHandlerHrApp.ashx</DependentUpon>
    </Compile>
    <Compile Include="Models\IdentityModels.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Site.Master.cs">
      <DependentUpon>Site.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Master.designer.cs">
      <DependentUpon>Site.Master</DependentUpon>
    </Compile>
    <Compile Include="Startup.cs" />
    <Compile Include="TestPageAdmin.aspx.cs">
      <DependentUpon>TestPageAdmin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TestPageAdmin.aspx.designer.cs">
      <DependentUpon>TestPageAdmin.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserControl\DigiportAdminHrAppHtmlContent.ascx.cs">
      <DependentUpon>DigiportAdminHrAppHtmlContent.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControl\DigiportAdminHrAppHtmlContent.ascx.designer.cs">
      <DependentUpon>DigiportAdminHrAppHtmlContent.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControl\DigiportAdminHtmlContent.ascx.cs">
      <DependentUpon>DigiportAdminHtmlContent.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControl\DigiportAdminHtmlContent.ascx.designer.cs">
      <DependentUpon>DigiportAdminHtmlContent.ascx</DependentUpon>
    </Compile>
    <Compile Include="UserControl\DigiportAdminHtmlContentNoMedia.ascx.cs">
      <DependentUpon>DigiportAdminHtmlContentNoMedia.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControl\DigiportAdminHtmlContentNoMedia.ascx.designer.cs">
      <DependentUpon>DigiportAdminHtmlContentNoMedia.ascx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="bin\bin\en\" />
    <Folder Include="bin\en\" />
    <Folder Include="Content\DigiportAdmin\" />
    <Folder Include="Content\SummerNoteTempUploads\" />
    <Folder Include="Content\SummerNoteUploads\" />
    <Folder Include="ReportPages\" />
    <Folder Include="UserPages\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_GlobalResources\TmateResource.en.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>TmateResource.en.designer.cs</LastGenOutput>
    </Content>
    <Content Include="App_GlobalResources\TmateResource.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>TmateResource.Designer.cs</LastGenOutput>
    </Content>
    <EmbeddedResource Include="licenses.licx" />
    <EmbeddedResource Include="Properties\licenses.licx" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Entities\Entities.csproj">
      <Project>{abc3dda4-168e-4378-84bb-7ca3b8c56d90}</Project>
      <Name>Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\FormHelper\FormHelper.csproj">
      <Project>{68ee2efe-7bfd-4841-856b-a1a9ae4e91c2}</Project>
      <Name>FormHelper</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>1780</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:1780/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>