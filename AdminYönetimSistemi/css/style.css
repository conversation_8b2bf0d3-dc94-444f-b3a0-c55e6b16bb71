﻿@charset "UTF-8";

body {
    margin-top: 0px;
    margin-bottom: 0px;
    margin-right: 0px;
    margin-left: 0px;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 12px;
    color: #333;
    padding: 0;
    text-align: left;
    unicode-bidi: embed;
    line-height: 1.5;
    text-rendering: optimizeLegibility;
}

/* Turkish character support */
@font-face {
    font-family: 'Segoe UI Local';
    src: local('Segoe UI');
    unicode-range: U+0131, U+0130, U+011E, U+011F, U+015E, U+015F, U+00D6, U+00F6, U+00DC, U+00FC, U+00C7, U+00E7;
}

.performansAnaBaslik {
    font-family: 'Segoe UI Local', 'Segoe UI', verdana, sans-serif;
    font-weight: bold;
}

/* Form Controls */
.drop {
    width: 325px;
    height: 35px;
    background-color: #F2F2F2;
    color: black;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
    border: 1px solid #ddd;
    transition: border 0.3s, box-shadow 0.3s;
}

.drop:focus {
    border-color: #5c2d91;
    box-shadow: 0 0 0 3px rgba(92, 45, 145, 0.25);
    outline: none;
}

.uzundrop {
    height: 25px;
    background-color: #F2F2F2;
    color: black;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
    border: 1px solid #ddd;
    transition: border 0.3s, box-shadow 0.3s;
}

.uzundrop:focus {
    border-color: #5c2d91;
    box-shadow: 0 0 0 3px rgba(92, 45, 145, 0.25);
    outline: none;
}

.panel {
    margin-left: auto;
    margin-right: auto;
}

.buton {
    color: #FFFFFF;
    background-color: #5c2d91;
    width: 100px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
    border: none;
    padding: 4px 8px;
    height: 30px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    text-align: center;
}

.buton:hover {
    background-color: #4b2173;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.uzunbuton {
    color: #FFFFFF;
    background-color: #5c2d91;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
    border: none;
    padding: 4px 8px;
    height: 30px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    text-align: center;
}

.uzunbuton:hover {
    background-color: #4b2173;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.textboxmultiline {
    width: 500px;
    height: 100px;
    border: 1px solid #ddd;
    border-radius: 4px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    padding: 8px;
    box-shadow: none;
    transition: border 0.3s, box-shadow 0.3s;
    font-family: inherit;
}

.textboxmultiline:focus {
    border-color: #5c2d91;
    box-shadow: 0 0 0 3px rgba(92, 45, 145, 0.25);
    outline: none;
}

.textboxgradient {
    border: 1px solid #ddd;
    border-radius: 4px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    padding: 8px;
    transition: border 0.3s, box-shadow 0.3s;
}

.textboxgradient:focus {
    border-color: #5c2d91;
    box-shadow: 0 0 0 3px rgba(92, 45, 145, 0.25);
    outline: none;
}

.textbox {
    width: 320px;
    height: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    padding: 8px;
    transition: border 0.3s, box-shadow 0.3s;
}

.textbox:focus {
    border-color: #5c2d91;
    box-shadow: 0 0 0 3px rgba(92, 45, 145, 0.25);
    outline: none;
}

.sagbaslik {
    width: 150px;
    font-weight: bold;
}

/*
	Popup Menü Stilleri - BASLA
*/
.modalBackground {
    background-color: rgba(0, 0, 0, 0.5);
    filter: alpha(opacity=50);
    opacity: 0.5;
}

.modalPopup {
    background-color: #FFFFFF;
    width: 300px;
    border: 1px solid #5c2d91;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    padding: 20px;
}

.modalPopup .header {
    background-color: #5c2d91;
    height: 20px;
    color: White;
    line-height: 20px;
    text-align: center;
    font-weight: bold;
    border-radius: 4px 4px 0 0;
}

.warningCss {
    color: #D8000C;
    background-color: #FFBABA;
}

.okCss {
    color: #000000;
    background-color: #FFFFFF;
}

.ustBaslik {
    background-color: #5c2d91;
    color: #FFFFFF;
}

.sagbaslik {
    width: 150px;
    color: #5c2d91;
    font-weight: bold;
}

.chb {
    border: 1px solid #ddd;
}

#UpdBackground {
    position: fixed;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    background-color: rgba(0, 0, 0, 0.5);
    filter: alpha(opacity=50);
    opacity: 0.6;
    z-index: 9998;
}

#UpdProgress {
    position: fixed;
    top: 40%;
    left: 40%;
    width: 300px;
    height: 75px;
    text-align: center;
    background-color: White;
    border: solid 3px #5c2d91;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 9999;
}

.lstStyle {
    width: 195px;
    height: 400px;
    font-family: Arial, sans-serif;
    font-size: 10px;
}

/*
	Popup Menü Stilleri  - SON
*/
/*...*/

/* Tree stili */
.whitetext {
    font-size: 12px;
    color: #000;
}

.tanımbaslık {
    font-size: 18px;
    padding-left: 80px;
    color: #5c2d91;
}

.linkStyle1 {
    color: #5c2d91;
    font-size: 13px;
    margin-right: 5px;
    text-decoration: none;
    transition: color 0.3s;
}

.linkStyle1:hover {
    text-decoration: underline;
    color: #4b2173;
}