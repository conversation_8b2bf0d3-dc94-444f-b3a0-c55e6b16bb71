﻿.ajax__calendar {
    border-radius: 4px !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
}

table.tblKaydet {
    width: 100%;
}

    table.tblKaydet td {
        padding: 5px 20px;
    }

    table.tblKaydet th {
        padding: 5px 20px;
    }

    table.tblKaydet td input {
        margin: 0;
    }

    table.tblKaydet td select {
        width: 200px;
    }

.divCaption {
    font-weight: bold;
    padding: 0 5px;
}

.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Ekran boyutuna göre ayarlanır */
    gap: 10px;
    padding: 5px 0 5px 0;
}
.grid-item select{
    width:100%;
}
.grid-item {
    background-color: #ffffff;
    padding: 8px;
    border-radius: 8px;
    border: solid 1px #dbdbdb;
}

    .grid-item .inner-div {
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
    }

    .grid-item input[type=text] {
        width: 90%;
    }
    .grid-item.span-2 {
        grid-column: span 2;
    }

fieldset {
    border: solid 1px #d1d1d1;
}

legend {
    font-weight: bold;
    font-size: 20px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

.switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

    .switch-slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

input:checked + .switch-slider {
    background-color: #2196F3;
}

input:focus + .switch-slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .switch-slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}

/* Rounded sliders */
.switch-slider.round {
    border-radius: 34px;
}

    .switch-slider.round:before {
        border-radius: 50%;
    }

.imgChosen {
    max-width: 300px;
    max-height: 300px;
    vertical-align: middle;
    border-radius: 5px;
}
.lblimagesize {
    font-weight: bold;
    font-size: 12px;
    margin-left: 5px;
    color: #fd1c1c;
}

.lblChosen {
    font-weight: bold;
    color: #1575bb;
    font-size: 15px;
}

.note-editor.note-frame.fullscreen {
    background-color: #fff;
}
.grid-slide-img{
    max-width:150px;
    max-height:150px;
}
.clickEventLink {
    color: #5c2d91;
    font-weight: bold;
    text-decoration: underline;
    font-size: 13px;
    cursor: pointer;
}
.linkStyleAsButton {
    background-color: var(--primary) !important;
    color: white !important;
    border: none !important;
    border-radius: 3px !important;
    padding: 6px 12px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
    height: auto !important;
    min-height: auto !important;
    letter-spacing: 0.2px !important;
    margin: 2px !important;
    text-decoration:none;
}
.IFrameDisplay {
    border: none;
    border-radius: 10px;
}
.ContentHeadline {
    color: #770cb7;
    font-size: 20px;
    font-weight: bold;
    border-bottom: groove 1px #770cb7;
    padding-bottom: 10px;
}
.txtMultilineGrid {
    height: 70px;
    width: 95%;
    border: none;
}
.txtMultilineGridEmpty {
    height: 20px;
    width: 95%;
    border: none;
    margin-top: 5px;
}
.txtMultiline {
    height: 70px;
    width: 92%;
}