/* ######### Matt Tabs Main Menu Bar CSS ######### */

.matttabs ul {
    margin: 0;
    padding: 0;
    font: bold 12px Verdana;
    list-style-type: none;
    overflow: hidden;
    text-align: center;
    /*Arkaplan buraya
background-color:aqua;
 */
}

.matttabs li {
    display: inline;
    margin: 0;
}

.matttabs li a {
    float: left;
    display: block;
    text-decoration: none;
    margin: 0;
    padding: 5px 50px 5px 5px;
    /*padding inside each tab*/
    color: white;
    background: #5c2d91;
    /* Changed from #C60C30 to match new color scheme */
    border-top: 1px solid #CCCCCC;
    border-right: 1px solid #333333;
    border-bottom: 1px solid #333333;
    border-left: 1px solid #CCCCCC;
    font-size: 11px;
    font-family: Verdana, Tahoma;
    transition: background-color 0.3s;
    /* Added smooth transition */
}

.matttabs li a:visited {
    color: white;
}

.matttabs li a:hover {
    background: #4b2173;
    /* Changed to darker purple for hover state */
}

.matttabs a.selected {
    background: #5c2d91;
    /* Changed from #C60C30 to match new color scheme */
}