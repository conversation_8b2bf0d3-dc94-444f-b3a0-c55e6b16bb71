/* ######### Marker List Vertical Menu ######### */

.markermenu
{
    width: 235px; /*width of side bar menu*/
    clear: left;
    position: relative; /*Preserve this for "right" arrow images (added by script) to be positioned correctly*/
}

    .markermenu ul
    {
        list-style-type: none;
        margin: 5px 0;
        padding: 0;
        border: 1px solid #9A9A9A;
    }

        .markermenu ul li a
        {
            background: #F2F2F2 url(bulletlist.gif) no-repeat 3px center; /*light gray background*/
            font: bold 13px "Lucida Grande", "Trebuchet MS", Verdana, Helvetica, sans-serif;
            color: #00014e;
            display: block;
            width: auto;
            padding: 3px 0;
            padding-left: 20px;
            text-decoration: none;
            border-bottom: 1px solid #B5B5B5;
        }

        * html .markermenu ul li a
        { /*IE6 hack*/
            width: 215px;
        }

            .markermenu ul li a:visited, .markermenu ul li a:active
            {
                color: #00014e;
            }

            .markermenu ul li a:hover, .markermenu ul li a.selected
            {
                color: white;
                background-color: #C60C30;
            }

/* ######### Customized Drop Down ULs CSS (inherits from ddlevelsmenu-base.css) ######### */

.blackwhite li a
{
    background: white;
}

    .blackwhite li a:hover
    {
        background: #C60C30;
        color: white;
    }