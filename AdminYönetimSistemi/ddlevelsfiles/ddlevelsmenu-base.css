/* ######### Drop Down ULs CSS ######### */

.ddsubmenustyle,
.ddsubmenustyle ul {
    /*topmost and sub ULs, respectively*/
    font: normal 13px Verdana;
    margin: 0;
    padding: 0;
    position: absolute;
    left: 0;
    top: 0;
    list-style-type: none;
    border: 1px solid #333;
    border-bottom-width: 0;
    visibility: hidden;
    z-index: 100;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ddsubmenustyle li a {
    display: block;
    width: 170px;
    /*width of menu (not including side paddings)*/
    color: #FFFFFF;
    background-color: #5c2d91;
    /* Changed from #C60C30 to match new color scheme */
    text-decoration: none;
    padding: 4px 5px;
    border-bottom: 1px solid #333;
    font-weight: normal;
    font-size: 11px;
    font-family: Verdana, Tahoma;
    transition: background-color 0.3s, color 0.3s;
    /* Added for smooth transitions */
}

* html .ddsubmenustyle li {
    /*IE6 CSS hack*/
    display: inline-block;
    width: 180px;
    /*width of menu (include side paddings of LI A*/
}

.ddsubmenustyle li a:hover {
    background-color: #4b2173;
    /* Changed to darker purple for hover state */
    color: white;
    font-weight: bold;
}

/* ######### Neutral CSS  ######### */

.downarrowpointer {
    /*CSS for "down" arrow image added to top menu items*/
    padding-left: 4px;
    border: 0;
}

.rightarrowpointer {
    /*CSS for "right" arrow image added to drop down menu items*/
    position: absolute;
    padding-top: 3px;
    left: 100px;
    border: 0;
}

.ddiframeshim {
    position: absolute;
    z-index: 500;
    background: transparent;
    border-width: 0;
    width: 0;
    height: 0;
    display: block;
}