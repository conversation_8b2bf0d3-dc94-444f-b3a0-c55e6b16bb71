﻿<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
	<configSections>
		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
		<sectionGroup name="devExpress">
			<section name="settings" type="DevExpress.Web.SettingsConfigurationSection, DevExpress.Web.v16.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
			<section name="compression" type="DevExpress.Web.CompressionConfigurationSection, DevExpress.Web.v16.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
			<section name="themes" type="DevExpress.Web.ThemesConfigurationSection, DevExpress.Web.v16.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
			<section name="errors" type="DevExpress.Web.ErrorsConfigurationSection, DevExpress.Web.v16.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
		</sectionGroup>
	</configSections>
	<appSettings>
		<!--
    <add key="ESSBDurum" value="H"/>
    <add key="debugMode" value="true"/>

    <add key="SUBSET15_*******_APPSTR" value="Digiflow"/>
    <add key="SUBSET15_*******_UNIQUE" value="4!fedL0w"/>
    <add key="SUBSET15_*******" value="*******"/>

    <add key="DBSLIVE_*******_APPSTR" value="Digiflow"/>
    <add key="DBSLIVE_*******_UNIQUE" value="4!fedL0w"/>
    <add key="DBSLIVE_*******" value="*******"/>

    <add key="smtpServer" value="smtp.digiturk.local"/>
    <add key="LogicalGroupDefinition" value="C:\TFS\DigiflowPM\Digiturk.Workflow.DigiFlow_v3\WFPages\LogicalGroups.xml"/>
    <add key="SharePointEkipmanTalepFormuDocs" value="http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/EkipmanTalepFormuDocs/Test/"/>
    <add key="Web.Services.UserName" value="Digiflow_sa"/>
    <add key="Web.Services.Password" value="Digif16up+-"/>
    <add key="Web.Services.Domain" value="DIGITURK"/>
    <add key="Web.Services.IsProxyUsing" value="True"/>
    <add key="Web.Services.IsCredentialUsing" value="True"/>
    <add key="Web.Services.ProxyServicesIp" value="http://************:8080"/>-->

		<!--live-->




		<!-- Corrected domain settings to match AdPortalCC -->
		<add key="DC1" value="DIGITURK" />
		<add key="DC2" value="LOCAL" />
		<add key="OTHERDC" value="DIGITURKCC" />
		<add key="GroupsOU1" value="Digiturk Groups" />
		<add key="CCGroupsOU" value="CC Groups" />


		<add key="ESSBDurum" value="E"/>
		<add key="debugMode" value="true"/>
		<add key="smtpServer" value="smtp.digiturk.local"/>

		<add key="SUBSET15_*******_APPSTR" value="Digiflow"/>
		<add key="SUBSET15_*******_UNIQUE" value="4!fedL0w"/>
		<add key="SUBSET15_*******" value="*******"/>

		<add key="DBSLIVE_*******_APPSTR" value="Digiflow" />
		<add key="DBSLIVE_*******_UNIQUE" value="4!fedL0w" />
		<add key="DBSLIVE_*******" value="*******"/>

		<add key="LogicalGroupDefinition" value="C:\TFS\DigiflowPM\Digiturk.Workflow.DigiFlow_v3\WFPages\LogicalGroups.xml"/>
		<add key="SharePointEkipmanTalepFormuDocs" value="http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/EkipmanTalepFormuDocs/Test/"/>

	<add key="Web.Services.UserName" value="Digiflow_sa"/>
	<add key="Web.Services.Password" value="Digif16up+-"/>
	<add key="Web.Services.Domain" value="DIGITURK"/>
		<add key="Web.Services.IsProxyUsing" value="True"/>
		<add key="Web.Services.IsCredentialUsing" value="True"/>
		<add key="Web.Services.ProxyServicesIp" value="http://************:8080"/>
		<add key="ParaBirimi" value="TL,USD,EUR,GBP" />
		<add key="AdGroup_TechCorp" value="TECH CORP"/>
		<add key="AdGroup_PDOSTB" value="PDO STB&amp;CA TEST"/>
		<add key="AdGroup_TechQA" value="TECH QA"/>
		<add key="AdGroup_TechQA2" value="TECH QA TEST TOD 2"/>
		<add key="ReadConfigFilesFromXML" value="false" />
		<add key="AdGroup_Muhaberat" value="IS MUHABERAT"/>
		<!-- ADPortal Config-->
		<add key="AdPortalServices" value="http://adportal/AdPortalServices.asmx"/>
		<add key="AdPortalCCServices" value="http://adportalcc/AdPortalServices.asmx"/>
		
		<add key="DigiflowTestDomain" value="http://digiflowtest/"/>
		<add key="DigiflowLiveDomain" value="http://digiflow/"/>
		<add key="DigiportDisplayPagePath" value="http://digiport/sitepages/DisplayContent.aspx"/>
		<add key="AjansDisplayPagePath" value="http://ajans/SitePages/DisplayContent.aspx"/>
		<add key="DigiportAdminAnasayfaUstBannerID" value="1"/>
		<add key="DigiportAdminAnasayfaSolSagKucukBannerID" value="2-3"/>
		<add key="DigiportAdminHrMediaSlideID" value="4"/>
		<add key="DigiportAdminAjansSlideID" value="5"/>
		<add key="DigiportAdminEducationSlideID" value="6"/>
		<add key="DigiportAdminIndirimFirsatiID" value="7"/>
		<add key="DigiportAdminLinklerID" value="8"/>
		<add key="DigiportAdminAnaSayfaSolUstMenuID" value="9"/>
		<add key="DigiportAdminAnaSayfaSolAltMenuID" value="10"/>
		<add key="DigiportAdminAnaSayfaDuyuruBunlariBiliyormuydunID" value="17"/>
		<add key="DigiportAdminAnaSayfaDuyuruYardimMasasiID" value="18"/>
		<add key="DigiportAdminAnasayfaUstBannerIzinliTipler" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminAnasayfaUstBannerIzinliBoyut" value="1025x260"/>
		<add key="DigiportAdminAnasayfaUstBannerThumbnailIzinliTipler" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminAnasayfaUstBannerThumbnailIzinliBoyut" value="110x60"/>
		<add key="DigiportAdminAnasayfaSolSagkucukBannerIzinliTipler" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminAnasayfaSolSagkucukBannerIzinliBoyut" value="470x310"/>
		<add key="DigiportAdminHrMediaSlideIzinliTipler" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminHrMediaSlideIzinliBoyut" value="560x290"/>
		<add key="DigiportAdminHrMediaSlideIzinliTiplerHrApp" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminHrMediaSlideIzinliBoyutHrApp" value="390x525"/>
		<add key="DigiportAdminHrMediaSlideThumbnailIzinliTipler" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminHrMediaSlideThumbnailIzinliBoyut" value="60x40"/>
		<add key="DigiportAdminHtmlEditorIzinliResim" value="jpg,jpeg,png,gif"/>
		<add key="DigiportAdminHtmlEditorIzinliVideo" value="mp4,webm,ogg"/>
		<add key="DigiportAdminHtmlEditorIzinliDigerDosya" value="pdf,xls,xlsx,zip,rar"/>
		<add key="DigiportAdminHtmlEditorIzinliVideoFileSizeInBytes" value="50000000"/>
		<!--50MB-->
		<add key="DigiportAdminHtmlEditorIzinliResimHrApp" value="jpg,jpeg,png,gif"/>
		<add key="DigiportAdminHtmlEditorIzinliVideoHrApp" value="mp4,webm,ogg"/>
		<add key="DigiportAdminHtmlEditorIzinliDigerDosyaHrApp" value="pdf,xls,xlsx,zip,rar"/>
		<add key="DigiportAdminHtmlEditorIzinliVideoFileSizeInBytesHrApp" value="50000000"/>
		<!--50MB-->
		<add key="DigiportAdminHtmlContentWindowDefaultSize" value="500x700"/>
		<add key="DigiportAdminSliderDefaultAutoSlideInterval" value="4"/>
		<add key="DigiportAdminSliderDefaultTransitionDuration" value="2"/>
		<add key="DigiportAdminAjansSlideIzinliTipler" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminAjansSlideIzinliBoyut" value="660x290"/>
		<add key="DigiportAdminAjansSlideThumbnailIzinliTipler" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminAjansSlideThumbnailIzinliBoyut" value="60x40"/>
		<add key="DigiportAdminAjansSlideIzinliTiplerHrApp" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminAjansSlideIzinliBoyutHrApp" value="390x525"/>
		<add key="DigiportAdminEducationSlideIzinliTipler" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminEducationSlideIzinliBoyut" value="560x290"/>
		<add key="DigiportAdminEducationSlideIzinliTiplerHrApp" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminEducationSlideIzinliBoyutHrApp" value="390x525"/>
		<add key="DigiportAdminEducationSlideThumbnailIzinliTipler" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminEducationSlideThumbnailIzinliBoyut" value="60x40"/>
		<add key="DigiportAdminIndirimFirsatiLogoIzinliTiplerHrApp" value=".jpg,.png,.jpeg"/>
		<add key="DigiportAdminIndirimFirsatiLogoIzinliBoyutHrApp" value="390x525"/>
		
		<add key="AdminGrupUser" value="DTKBAYRAKTAR,DTZKUCUK,DIGIFLOW_SA,SPSMOSS_SA,DTBGUNAY,DTMKASAPOGLU,DTYAELMAS"/>

	</appSettings>
	<connectionStrings>
		<!--<add name="SUBSET15" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=SUBSET15)));User Id={0};Password=***" providerName="System.Data.OracleClient"/>
    <add name="connStrESSB_TEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=SUBSET15)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient"/>
    <add name="connStrESSB_LIVE" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = DPSMS2)(PORT = 1522))(CONNECT_DATA = (SID = DBSLIVE)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient"/>
    <add name="***********_LIVE" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=***********;Password=***********;Pooling=True"/>
    <add name="***********_TEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=***********;Password=***********;Pooling=True"/>
    <add name="***********" connectionString=""/>
    <add name="DefaultConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=DT_APPLICATION_USR;Password=DT_APPLICATION_USR;Pooling=true;Self Tuning=false"/>
    <add name="DBSConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SID=KURUMSAL.DIGITURK.LOCAL)));User Id=*******;Password=*******;Self Tuning=false"/>-->

		<!--live-->

		<add name="SUBSET15" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=SUBSET15)));User Id={0};Password=***" providerName="System.Data.OracleClient"/>
		<add name="connStrESSB_TEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=SUBSET15)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient"/>
		<add name="connStrESSB_LIVE" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = DPSMS2)(PORT = 1522))(CONNECT_DATA = (SID = DBSLIVE)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient"/>

		<add name="***********_LIVE" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=DM02-SCAN)(PORT=1521))(CONNECT_DATA=(SERVER=dedicated)(SERVICE_NAME=DSSFLOW)));User Id=***********;Password=***********;Pooling=true" />
		<add name="***********_TEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=***********;Password=***********;Pooling=True"/>
		<add name="***********" connectionString=""/>
		<add name="DefaultConnection" connectionString=""/>
		<add name="DBSConnection" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = DPSMS2)(PORT = 1522))(CONNECT_DATA = (SID = DBSLIVE)));User Id={0};Password=***" providerName="System.Data.OracleClient"/>
		<add name="DBSLIVE" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = DPSMS2)(PORT = 1522))(CONNECT_DATA = (SID = DBSLIVE)));User Id={0};Password=***" providerName="System.Data.OracleClient" />

	</connectionStrings>
	<!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.5.2" />
      </system.Web>
  -->
	<system.web>
		<authentication mode="None"/>
		<compilation debug="true" targetFramework="4.5.2">
			<assemblies>
				<add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
				<add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
			</assemblies>
		</compilation>
		<httpRuntime requestValidationMode="2.0"  maxRequestLength="**********" executionTimeout="3600" maxQueryStringLength="2097151"/>
		<globalization culture="tr-TR" uiCulture="tr-TR" fileEncoding="utf-8" requestEncoding="utf-8" responseEncoding="utf-8" />
		<!--
            If you are deploying to a cloud environment that has multiple web server instances,
            you should change session state mode from "InProc" to "Custom". In addition,
            change the connection string named "DefaultConnection" to connect to an instance
            of SQL Server (including SQL Azure and SQL  Compact) instead of to SQL Server Express.
      -->
		<sessionState mode="InProc" customProvider="DefaultSessionProvider">
			<providers>
				<add name="DefaultSessionProvider" type="System.Web.Providers.DefaultSessionStateProvider, System.Web.Providers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" connectionStringName="DefaultConnection"/>
			</providers>
		</sessionState>
		<httpModules>
			<add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v16.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule"/>
			<add type="AracTakipSistemi.DigiportAuthorizationModule, AracTakipSistemi" name="DigiportAuthorizationModule"/>
		</httpModules>
		<httpHandlers/>
		<pages controlRenderingCompatibilityVersion="4.0"/>
	</system.web>
	<system.webServer>
		<modules>
			<remove name="FormsAuthentication"/>
			<add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v16.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule"/>
			<add type="AracTakipSistemi.DigiportAuthorizationModule, AracTakipSistemi" name="DigiportAuthorizationModule"/>
		</modules>
		<validation validateIntegratedModeConfiguration="false"/>
		<handlers>
			<remove name="WebServiceHandlerFactory-Integrated"/>
			<remove name="ScriptHandlerFactory"/>
			<remove name="ScriptHandlerFactoryAppServices"/>
			<remove name="ScriptResource"/>
			<add name="ScriptHandlerFactory" verb="*" path="*.asmx" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
			<add name="ScriptHandlerFactoryAppServices" verb="*" path="*_AppService.axd" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
			<add name="ScriptResource" preCondition="integratedMode" verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
		</handlers>
		<security>
			<requestFiltering>
				<requestLimits maxAllowedContentLength="**********" maxQueryString="2097151" />
			</requestFiltering>
		</security>
	</system.webServer>
	<system.web.extensions>
		<scripting>
			<webServices>
				<jsonSerialization maxJsonLength="**********" />
			</webServices>
		</scripting>
	</system.web.extensions>
	<runtime>
		<probing privatePath="bin" />
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" culture="neutral" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="EntityFramework" publicKeyToken="b77a5c561934e089"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin" culture="neutral" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin.Security.OAuth" culture="neutral" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin.Security.Cookies" culture="neutral" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin.Security" culture="neutral" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="AjaxControlToolkit" publicKeyToken="28f01b0e84b6d53e" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-16.1.1.0" newVersion="16.1.1.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
		</assemblyBinding>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Oracle.DataAccess" culture="neutral" publicKeyToken="89b483f429c47342" />
				<bindingRedirect oldVersion="0.0.0.0-4.122.19.1" newVersion="4.122.19.1" />
			</dependentAssembly>
			<!-- DevExpress binding redirects -->
			<dependentAssembly>
				<assemblyIdentity name="DevExpress.Web.v16.2" culture="neutral" publicKeyToken="b88d1754d700e49a" />
				<bindingRedirect oldVersion="0.0.0.0-*********" newVersion="*********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="DevExpress.Web.ASPxGridView.v12.1" culture="neutral" publicKeyToken="b88d1754d700e49a" />
				<bindingRedirect oldVersion="0.0.0.0-12.1.7.0" newVersion="12.1.7.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="DevExpress.Web.ASPxEditors.v12.1" culture="neutral" publicKeyToken="b88d1754d700e49a" />
				<bindingRedirect oldVersion="0.0.0.0-12.1.7.0" newVersion="12.1.7.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<entityFramework>
		<defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
			<parameters>
				<parameter value="mssqllocaldb"/>
			</parameters>
		</defaultConnectionFactory>
		<providers>
			<provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
		</providers>
	</entityFramework>
	<devExpress>
		<settings rightToLeft="false"/>
		<compression enableHtmlCompression="false" enableCallbackCompression="true" enableResourceCompression="true" enableResourceMerging="false"/>
		<errors callbackErrorRedirectUrl=""/>
		<themes enableThemesAssembly="true"/>
	</devExpress>
</configuration>