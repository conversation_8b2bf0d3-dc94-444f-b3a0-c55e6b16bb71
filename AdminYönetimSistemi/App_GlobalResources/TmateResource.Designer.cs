//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class TmateResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal TmateResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.TmateResource", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ad Grubu.
        /// </summary>
        internal static string AdGroup {
            get {
                return ResourceManager.GetString("AdGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ad GrupTipi.
        /// </summary>
        internal static string AdGroupTipi {
            get {
                return ResourceManager.GetString("AdGroupTipi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ad GrupTipi.
        /// </summary>
        internal static string AdGroupType {
            get {
                return ResourceManager.GetString("AdGroupType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aktif.
        /// </summary>
        internal static string Aktif {
            get {
                return ResourceManager.GetString("Aktif", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Domain .
        /// </summary>
        internal static string Domain {
            get {
                return ResourceManager.GetString("Domain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Düzenle.
        /// </summary>
        internal static string Duzenle {
            get {
                return ResourceManager.GetString("Duzenle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı.
        /// </summary>
        internal static string Kullanici {
            get {
                return ResourceManager.GetString("Kullanici", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail Grubu.
        /// </summary>
        internal static string MailGrubu {
            get {
                return ResourceManager.GetString("MailGrubu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copyright 2022 Admin Yönetim Sistemi.
        /// </summary>
        internal static string MasterPageAltBaslik {
            get {
                return ResourceManager.GetString("MasterPageAltBaslik", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Proje.
        /// </summary>
        internal static string Proje {
            get {
                return ResourceManager.GetString("Proje", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raporlama.
        /// </summary>
        internal static string Raporlama {
            get {
                return ResourceManager.GetString("Raporlama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raporlama Yetkisi.
        /// </summary>
        internal static string RaporlamaYetkisi {
            get {
                return ResourceManager.GetString("RaporlamaYetkisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen Bu Alanı Doldurunuz/Seçiniz !.
        /// </summary>
        internal static string ReqValError {
            get {
                return ResourceManager.GetString("ReqValError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status Adı EN.
        /// </summary>
        internal static string StatusNameEn {
            get {
                return ResourceManager.GetString("StatusNameEn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status Adı TR.
        /// </summary>
        internal static string StatusNameTr {
            get {
                return ResourceManager.GetString("StatusNameTr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status Tipi.
        /// </summary>
        internal static string StatusType {
            get {
                return ResourceManager.GetString("StatusType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yazma Yetkisi.
        /// </summary>
        internal static string YazmaYetkisi {
            get {
                return ResourceManager.GetString("YazmaYetkisi", resourceCulture);
            }
        }
    }
}

