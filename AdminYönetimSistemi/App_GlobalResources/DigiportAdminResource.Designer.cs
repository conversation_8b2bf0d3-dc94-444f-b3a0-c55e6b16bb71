//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "********")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class DigiportAdminResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal DigiportAdminResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.DigiportAdminResource", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport Admin Portal yetkilendirme sistemi tarafından erişiminiz engellendi..
        /// </summary>
        internal static string AccessDeniedBySystem {
            get {
                return ResourceManager.GetString("AccessDeniedBySystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eğer bu sayfaya erişim yetkinizin olması gerektiğini düşünüyorsanız, lütfen sistem yöneticinize başvurun..
        /// </summary>
        internal static string AccessDeniedHelp {
            get {
                return ResourceManager.GetString("AccessDeniedHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Erişim engellendi: Kullanıcı={0}, İstenen URL={1}, IP={2}, Mesaj={3}.
        /// </summary>
        internal static string AccessDeniedLogMessage {
            get {
                return ResourceManager.GetString("AccessDeniedLogMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu Sayfaya Erişim Yetkiniz Yoktur!.
        /// </summary>
        internal static string AccessDeniedMessage {
            get {
                return ResourceManager.GetString("AccessDeniedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Erişim Hatası.
        /// </summary>
        internal static string AccessDeniedTitle {
            get {
                return ResourceManager.GetString("AccessDeniedTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tıklandığında Açılan Pencere Boyutları (Genişlik x Yükseklik).
        /// </summary>
        internal static string AcilanPencereBoyutlari {
            get {
                return ResourceManager.GetString("AcilanPencereBoyutlari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlemler.
        /// </summary>
        internal static string Actions {
            get {
                return ResourceManager.GetString("Actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aktif.
        /// </summary>
        internal static string Active {
            get {
                return ResourceManager.GetString("Active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ekle.
        /// </summary>
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni Yetki Ekle.
        /// </summary>
        internal static string AddNewAuthority {
            get {
                return ResourceManager.GetString("AddNewAuthority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni Tip Ekle.
        /// </summary>
        internal static string AddNewType {
            get {
                return ResourceManager.GetString("AddNewType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Grup.
        /// </summary>
        internal static string ADGroup {
            get {
                return ResourceManager.GetString("ADGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Grup Yetkilendirme.
        /// </summary>
        internal static string ADGroupAuthorization {
            get {
                return ResourceManager.GetString("ADGroupAuthorization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active Directory gruplarına toplu yetki atayın.
        /// </summary>
        internal static string ADGroupAuthorizationDesc {
            get {
                return ResourceManager.GetString("ADGroupAuthorizationDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Grubu seçimi zorunludur..
        /// </summary>
        internal static string ADGroupSelectionRequired {
            get {
                return ResourceManager.GetString("ADGroupSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yönetim Sayfaları.
        /// </summary>
        internal static string AdminPages {
            get {
                return ResourceManager.GetString("AdminPages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adres.
        /// </summary>
        internal static string Adres {
            get {
                return ResourceManager.GetString("Adres", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adres giriniz..
        /// </summary>
        internal static string AdresGirin {
            get {
                return ResourceManager.GetString("AdresGirin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ajans Duyuruları.
        /// </summary>
        internal static string AjansDuyuru {
            get {
                return ResourceManager.GetString("AjansDuyuru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aktif.
        /// </summary>
        internal static string Aktif {
            get {
                return ResourceManager.GetString("Aktif", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tümü.
        /// </summary>
        internal static string AllStatuses {
            get {
                return ResourceManager.GetString("AllStatuses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ana Sayfa.
        /// </summary>
        internal static string AnaSayfa {
            get {
                return ResourceManager.GetString("AnaSayfa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anasayfa Sol Alt Menü.
        /// </summary>
        internal static string AnasayfaSolAltMenu {
            get {
                return ResourceManager.GetString("AnasayfaSolAltMenu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anasayfa Sol Üst Menü.
        /// </summary>
        internal static string AnasayfaSolUstMenu {
            get {
                return ResourceManager.GetString("AnasayfaSolUstMenu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duyuru Bilgileri.
        /// </summary>
        internal static string AnnouncementInformation {
            get {
                return ResourceManager.GetString("AnnouncementInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Araç Talep Admin.
        /// </summary>
        internal static string AracTalepAdmin {
            get {
                return ResourceManager.GetString("AracTalepAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki Atama / Düzenleme.
        /// </summary>
        internal static string AuthorityAssignmentEditing {
            get {
                return ResourceManager.GetString("AuthorityAssignmentEditing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçilen kullanıcı veya gruba yetki atamak için Kaydet butonuna tıklayın..
        /// </summary>
        internal static string AuthorityAssignmentHint {
            get {
                return ResourceManager.GetString("AuthorityAssignmentHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirme Bulunamadı.
        /// </summary>
        internal static string AuthorizationNotFound {
            get {
                return ResourceManager.GetString("AuthorizationNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçili sayfa için henüz yetkilendirme yapılmamış veya arama kriterlerinize uygun sonuç bulunamadı..
        /// </summary>
        internal static string AuthorizationNotFoundDesc {
            get {
                return ResourceManager.GetString("AuthorizationNotFoundDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirilen.
        /// </summary>
        internal static string AuthorizedEntity {
            get {
                return ResourceManager.GetString("AuthorizedEntity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Otomatik Geçiş Aktif.
        /// </summary>
        internal static string AutoSlideActive {
            get {
                return ResourceManager.GetString("AutoSlideActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avantaj Koşulları.
        /// </summary>
        internal static string AvantajKosullari {
            get {
                return ResourceManager.GetString("AvantajKosullari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avantaj koşulları giriniz..
        /// </summary>
        internal static string AvantajKosullariSecin {
            get {
                return ResourceManager.GetString("AvantajKosullariSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başarı.
        /// </summary>
        internal static string Basari {
            get {
                return ResourceManager.GetString("Basari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başlangıç.
        /// </summary>
        internal static string Baslangic {
            get {
                return ResourceManager.GetString("Baslangic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başlangıç tarih formatı gg.aa.yyyy olmalı.
        /// </summary>
        internal static string BaslangicTarihFormati {
            get {
                return ResourceManager.GetString("BaslangicTarihFormati", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başlangıç Tarihi.
        /// </summary>
        internal static string BaslangicTarihi {
            get {
                return ResourceManager.GetString("BaslangicTarihi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başlangıç tarihi seçiniz.
        /// </summary>
        internal static string BaslangicTarihiSec {
            get {
                return ResourceManager.GetString("BaslangicTarihiSec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başlık.
        /// </summary>
        internal static string Baslik {
            get {
                return ResourceManager.GetString("Baslik", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başlık giriniz..
        /// </summary>
        internal static string BaslikGirin {
            get {
                return ResourceManager.GetString("BaslikGirin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bitiş.
        /// </summary>
        internal static string Bitis {
            get {
                return ResourceManager.GetString("Bitis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bitiş tarih formatı gg.aa.yyyy olmalı.
        /// </summary>
        internal static string BitisTarihFormati {
            get {
                return ResourceManager.GetString("BitisTarihFormati", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bitiş Tarihi.
        /// </summary>
        internal static string BitisTarihi {
            get {
                return ResourceManager.GetString("BitisTarihi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bitiş tarihi, başlangıç tarihinden ileri olmalıdır..
        /// </summary>
        internal static string BitisTarihiBaslagictanIleriOlsun {
            get {
                return ResourceManager.GetString("BitisTarihiBaslagictanIleriOlsun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bitiş tarihi seçiniz.
        /// </summary>
        internal static string BitisTarihiSec {
            get {
                return ResourceManager.GetString("BitisTarihiSec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İptal.
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Temizle.
        /// </summary>
        internal static string Clear {
            get {
                return ResourceManager.GetString("Clear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tıklanma Olayı.
        /// </summary>
        internal static string ClickEvent {
            get {
                return ResourceManager.GetString("ClickEvent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Konfigürasyon Hatası.
        /// </summary>
        internal static string ConfigurationError {
            get {
                return ResourceManager.GetString("ConfigurationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaydın silinmesini onaylıyor musunuz?.
        /// </summary>
        internal static string ConfirmeDelete {
            get {
                return ResourceManager.GetString("ConfirmeDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İçerik Bilgisi.
        /// </summary>
        internal static string ContentInformation {
            get {
                return ResourceManager.GetString("ContentInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copyright © 2023 Admin Yönetim Sistemi.
        /// </summary>
        internal static string Copyright {
            get {
                return ResourceManager.GetString("Copyright", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oluşturma Tarihi.
        /// </summary>
        internal static string CreationDate {
            get {
                return ResourceManager.GetString("CreationDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sil.
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu kaydı silmek istediğinizden emin misiniz?.
        /// </summary>
        internal static string DeleteConfirmation {
            get {
                return ResourceManager.GetString("DeleteConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu yetkiyi silmek istediğinize emin misiniz? Bu işlem geri alınamaz..
        /// </summary>
        internal static string DeletePermissionConfirmation {
            get {
                return ResourceManager.GetString("DeletePermissionConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama.
        /// </summary>
        internal static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama giriniz.
        /// </summary>
        internal static string DescriptionPlaceholder {
            get {
                return ResourceManager.GetString("DescriptionPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diğer.
        /// </summary>
        internal static string Diger {
            get {
                return ResourceManager.GetString("Diger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiflow Menu Admin.
        /// </summary>
        internal static string DigiflowMenuAdmin {
            get {
                return ResourceManager.GetString("DigiflowMenuAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uygulama Slide.
        /// </summary>
        internal static string DigiHrAppSlide {
            get {
                return ResourceManager.GetString("DigiHrAppSlide", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport Ana Sayfa Sol-Sağ Galeri.
        /// </summary>
        internal static string DigiportAnaSayfaGaleri {
            get {
                return ResourceManager.GetString("DigiportAnaSayfaGaleri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport Ana Sayfa Sol-Sağ Galeri.
        /// </summary>
        internal static string DigiportAnaSayfaSolSagGaleri {
            get {
                return ResourceManager.GetString("DigiportAnaSayfaSolSagGaleri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport Anket.
        /// </summary>
        internal static string DigiportAnket {
            get {
                return ResourceManager.GetString("DigiportAnket", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport Bannerlar.
        /// </summary>
        internal static string DigiportBanners {
            get {
                return ResourceManager.GetString("DigiportBanners", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport Tablolar.
        /// </summary>
        internal static string DigiportGrids {
            get {
                return ResourceManager.GetString("DigiportGrids", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İndirim Fırsatı.
        /// </summary>
        internal static string DigiportIndirimFirsati {
            get {
                return ResourceManager.GetString("DigiportIndirimFirsati", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bağlantılar.
        /// </summary>
        internal static string DigiportLinkler {
            get {
                return ResourceManager.GetString("DigiportLinkler", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiflow Menu Admin.
        /// </summary>
        internal static string DigiportMenuAdmin {
            get {
                return ResourceManager.GetString("DigiportMenuAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport Menü İsim Yönetimi.
        /// </summary>
        internal static string DigiportMenuNameManagement {
            get {
                return ResourceManager.GetString("DigiportMenuNameManagement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport Sayfa Yetkilendirme.
        /// </summary>
        internal static string DigiportPageAuthorization {
            get {
                return ResourceManager.GetString("DigiportPageAuthorization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DigiPort Sayfa Seçimi.
        /// </summary>
        internal static string DigiPortPageSelection {
            get {
                return ResourceManager.GetString("DigiPortPageSelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport.
        /// </summary>
        internal static string DigiportPortal {
            get {
                return ResourceManager.GetString("DigiportPortal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İndirim Fırsatı Bilgileri.
        /// </summary>
        internal static string DiscountOpportunityInformation {
            get {
                return ResourceManager.GetString("DiscountOpportunityInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Domain Seçiniz.
        /// </summary>
        internal static string DomainSeciniz {
            get {
                return ResourceManager.GetString("DomainSeciniz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dosya yüklenemedi..
        /// </summary>
        internal static string DosyaYuklenemedi {
            get {
                return ResourceManager.GetString("DosyaYuklenemedi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duyuru Adı.
        /// </summary>
        internal static string DuyuruAdi {
            get {
                return ResourceManager.GetString("DuyuruAdi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duyuru adı giriniz.
        /// </summary>
        internal static string DuyuruAdiGirin {
            get {
                return ResourceManager.GetString("DuyuruAdiGirin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &quot;Bunları Biliyor muydunuz?&quot; Duyuruları.
        /// </summary>
        internal static string DuyuruBunlarıBiliyormuydunuz {
            get {
                return ResourceManager.GetString("DuyuruBunlarıBiliyormuydunuz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duyuru Listesi.
        /// </summary>
        internal static string DuyuruListe {
            get {
                return ResourceManager.GetString("DuyuruListe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yardım Masası Duyuruları.
        /// </summary>
        internal static string DuyuruYardimMasasi {
            get {
                return ResourceManager.GetString("DuyuruYardimMasasi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Düzenle.
        /// </summary>
        internal static string Duzenle {
            get {
                return ResourceManager.GetString("Duzenle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Düzenle.
        /// </summary>
        internal static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Düzenlenen Yetki:.
        /// </summary>
        internal static string EditedAuthority {
            get {
                return ResourceManager.GetString("EditedAuthority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip Düzenle.
        /// </summary>
        internal static string EditType {
            get {
                return ResourceManager.GetString("EditType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bülten.
        /// </summary>
        internal static string EgitimDuyuru {
            get {
                return ResourceManager.GetString("EgitimDuyuru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ekipman Talep Admin.
        /// </summary>
        internal static string EkipmanTalepAdmin {
            get {
                return ResourceManager.GetString("EkipmanTalepAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerekli AD Grup ayarları bulunamadı..
        /// </summary>
        internal static string ERR_CONFIG {
            get {
                return ResourceManager.GetString("ERR_CONFIG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Form verileri atanırken hata oluştu..
        /// </summary>
        internal static string ERR_DATA_MAPPING {
            get {
                return ResourceManager.GetString("ERR_DATA_MAPPING", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt silinirken hata oluştu..
        /// </summary>
        internal static string ERR_DELETE {
            get {
                return ResourceManager.GetString("ERR_DELETE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt düzenleme sırasında bir hata oluştu..
        /// </summary>
        internal static string ERR_EDIT {
            get {
                return ResourceManager.GetString("ERR_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt bilgileri getirilirken hata oluştu..
        /// </summary>
        internal static string ERR_GET_RECORD {
            get {
                return ResourceManager.GetString("ERR_GET_RECORD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt listesi yüklenirken hata oluştu..
        /// </summary>
        internal static string ERR_LOAD_GRID {
            get {
                return ResourceManager.GetString("ERR_LOAD_GRID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Grup listesi yüklenirken hata oluştu..
        /// </summary>
        internal static string ERR_LOAD_GROUPS {
            get {
                return ResourceManager.GetString("ERR_LOAD_GROUPS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfa listesi yüklenirken hata oluştu..
        /// </summary>
        internal static string ERR_LOAD_PAGES {
            get {
                return ResourceManager.GetString("ERR_LOAD_PAGES", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip listesi yüklenirken hata oluştu..
        /// </summary>
        internal static string ERR_LOAD_TYPES {
            get {
                return ResourceManager.GetString("ERR_LOAD_TYPES", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı listesi yüklenirken hata oluştu..
        /// </summary>
        internal static string ERR_LOAD_USERS {
            get {
                return ResourceManager.GetString("ERR_LOAD_USERS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfa değiştirme işlemi sırasında bir hata oluştu..
        /// </summary>
        internal static string ERR_PAGE_CHANGE {
            get {
                return ResourceManager.GetString("ERR_PAGE_CHANGE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfa yüklenirken beklenmeyen bir hata oluştu..
        /// </summary>
        internal static string ERR_PAGE_LOAD {
            get {
                return ResourceManager.GetString("ERR_PAGE_LOAD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfalama işlemi sırasında bir hata oluştu..
        /// </summary>
        internal static string ERR_PAGINATION {
            get {
                return ResourceManager.GetString("ERR_PAGINATION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki kontrolü sırasında bir hata oluştu..
        /// </summary>
        internal static string ERR_PERMISSION {
            get {
                return ResourceManager.GetString("ERR_PERMISSION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filtre temizleme sırasında bir hata oluştu..
        /// </summary>
        internal static string ERR_RESET_SEARCH {
            get {
                return ResourceManager.GetString("ERR_RESET_SEARCH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt eklenirken hata oluştu..
        /// </summary>
        internal static string ERR_SAVE {
            get {
                return ResourceManager.GetString("ERR_SAVE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arama işlemi sırasında bir hata oluştu..
        /// </summary>
        internal static string ERR_SEARCH {
            get {
                return ResourceManager.GetString("ERR_SEARCH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt güncellenirken hata oluştu..
        /// </summary>
        internal static string ERR_UPDATE {
            get {
                return ResourceManager.GetString("ERR_UPDATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mevcut Yetkilendirmeler.
        /// </summary>
        internal static string ExistingAuthorizations {
            get {
                return ResourceManager.GetString("ExistingAuthorizations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mevcut Tipler.
        /// </summary>
        internal static string ExistingTypes {
            get {
                return ResourceManager.GetString("ExistingTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Form verileri atanırken hata oluştu..
        /// </summary>
        internal static string FormVeriAtamaHatasi {
            get {
                return ResourceManager.GetString("FormVeriAtamaHatasi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Galeri Tipi Belirsiz.
        /// </summary>
        internal static string GaleriTipiBelirsiz {
            get {
                return ResourceManager.GetString("GaleriTipiBelirsiz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Galeri Tipi Yanlış.
        /// </summary>
        internal static string GaleriTipiYanlis {
            get {
                return ResourceManager.GetString("GaleriTipiYanlis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geçerlilik Aralığı.
        /// </summary>
        internal static string GecerlilikAraligi {
            get {
                return ResourceManager.GetString("GecerlilikAraligi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geçersiz bir popup genişliği girdiniz.
        /// </summary>
        internal static string GecersizPopupGenislik {
            get {
                return ResourceManager.GetString("GecersizPopupGenislik", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geçersiz bir popup yüksekliği girdiniz.
        /// </summary>
        internal static string GecersizPopupYukseklik {
            get {
                return ResourceManager.GetString("GecersizPopupYukseklik", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geçersiz bir sıra numarası girdiniz..
        /// </summary>
        internal static string GecersizSiraNo {
            get {
                return ResourceManager.GetString("GecersizSiraNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geri.
        /// </summary>
        internal static string Geri {
            get {
                return ResourceManager.GetString("Geri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Herhangi bir kayıt bulunmamaktadır.
        /// </summary>
        internal static string GridKayitBulunmadi {
            get {
                return ResourceManager.GetString("GridKayitBulunmadi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt listesi yüklenirken hata oluştu..
        /// </summary>
        internal static string GridYuklemeHatasi {
            get {
                return ResourceManager.GetString("GridYuklemeHatasi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerekli AD Grup ayarları bulunamadı..
        /// </summary>
        internal static string GrupAyarlariHatasi {
            get {
                return ResourceManager.GetString("GrupAyarlariHatasi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Güncelleme için kayıt seçilmemiş..
        /// </summary>
        internal static string GuncellemeKayitSecilmemis {
            get {
                return ResourceManager.GetString("GuncellemeKayitSecilmemis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hata.
        /// </summary>
        internal static string Hata {
            get {
                return ResourceManager.GetString("Hata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hata meydana geldi..
        /// </summary>
        internal static string HataOldu {
            get {
                return ResourceManager.GetString("HataOldu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hedef Link.
        /// </summary>
        internal static string HedefLink {
            get {
                return ResourceManager.GetString("HedefLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hedef Link Giriniz.
        /// </summary>
        internal static string HedefLinkGirin {
            get {
                return ResourceManager.GetString("HedefLinkGirin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HR Uygulama Kaydı Geçerli.
        /// </summary>
        internal static string HrAppEnabled {
            get {
                return ResourceManager.GetString("HrAppEnabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HrApp İçin Hedef Link Giriniz.
        /// </summary>
        internal static string HrAppHedefLinkGirin {
            get {
                return ResourceManager.GetString("HrAppHedefLinkGirin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçilen HR uygulama logo dosyasının boyutları {0}x{1}. Olması gereken ise {2}x{3}.
        /// </summary>
        internal static string HrAppLogoBoyutlari {
            get {
                return ResourceManager.GetString("HrAppLogoBoyutlari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçilen HR uygulama resim dosyasının boyutları {0}x{1}. Olması gereken ise {2}x{3}.
        /// </summary>
        internal static string HrAppSlideBoyutlari {
            get {
                return ResourceManager.GetString("HrAppSlideBoyutlari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HrApp İçin Tıklanınca Olacak İşlemi Seçiniz.
        /// </summary>
        internal static string HrAppTiklanmaIslemiSecin {
            get {
                return ResourceManager.GetString("HrAppTiklanmaIslemiSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duyuru Kategori Yönetimi.
        /// </summary>
        internal static string HrMediaKategoriIslemleri {
            get {
                return ResourceManager.GetString("HrMediaKategoriIslemleri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İçerik.
        /// </summary>
        internal static string Icerik {
            get {
                return ResourceManager.GetString("Icerik", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen içerik giriniz..
        /// </summary>
        internal static string IcerikGirin {
            get {
                return ResourceManager.GetString("IcerikGirin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İçerik Listesi.
        /// </summary>
        internal static string IcerikListe {
            get {
                return ResourceManager.GetString("IcerikListe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ID.
        /// </summary>
        internal static string ID {
            get {
                return ResourceManager.GetString("ID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DigiHr Uygulama Bilgileri.
        /// </summary>
        internal static string IKAppInformation {
            get {
                return ResourceManager.GetString("IKAppInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İç İletişim.
        /// </summary>
        internal static string IKMedyaDuyuru {
            get {
                return ResourceManager.GetString("IKMedyaDuyuru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başlangıç olarak bugün veya ileri bir tarih seçin..
        /// </summary>
        internal static string IleriTarihBaslangic {
            get {
                return ResourceManager.GetString("IleriTarihBaslangic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bitiş olarak bugünden ileri bir tarih seçin..
        /// </summary>
        internal static string IleriTarihBitis {
            get {
                return ResourceManager.GetString("IleriTarihBitis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasif.
        /// </summary>
        internal static string Inactive {
            get {
                return ResourceManager.GetString("Inactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İndirim Fırsatı Kategori İşlemleri.
        /// </summary>
        internal static string IndirimFirsatiKategoriIslemleri {
            get {
                return ResourceManager.GetString("IndirimFirsatiKategoriIslemleri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İndirim Fırsatı Listesi.
        /// </summary>
        internal static string IndirimFirsatiListe {
            get {
                return ResourceManager.GetString("IndirimFirsatiListe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İndirim Oranı.
        /// </summary>
        internal static string IndirimOrani {
            get {
                return ResourceManager.GetString("IndirimOrani", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İndirim oranı giriniz..
        /// </summary>
        internal static string IndirimOraniSecin {
            get {
                return ResourceManager.GetString("IndirimOraniSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İsim Tanımlama.
        /// </summary>
        internal static string IsimTanimlama {
            get {
                return ResourceManager.GetString("IsimTanimlama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem Başarılı.
        /// </summary>
        internal static string IslemBasarili {
            get {
                return ResourceManager.GetString("IslemBasarili", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kategori.
        /// </summary>
        internal static string Kategori {
            get {
                return ResourceManager.GetString("Kategori", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kategori Adı İngilizce.
        /// </summary>
        internal static string KategoriAdiEn {
            get {
                return ResourceManager.GetString("KategoriAdiEn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kategori Adı Türkçe.
        /// </summary>
        internal static string KategoriAdiTr {
            get {
                return ResourceManager.GetString("KategoriAdiTr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kategori İşlemleri.
        /// </summary>
        internal static string KategoriIslemleri {
            get {
                return ResourceManager.GetString("KategoriIslemleri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kategori Seçiniz.
        /// </summary>
        internal static string KategoriSecin {
            get {
                return ResourceManager.GetString("KategoriSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaydet.
        /// </summary>
        internal static string Kaydet {
            get {
                return ResourceManager.GetString("Kaydet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt Başarılı.
        /// </summary>
        internal static string KayitBasarili {
            get {
                return ResourceManager.GetString("KayitBasarili", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem yapılacak kayıt bulunamadı..
        /// </summary>
        internal static string KayitBulunamadi {
            get {
                return ResourceManager.GetString("KayitBulunamadi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt eklenirken hata oluştu..
        /// </summary>
        internal static string KayitEklemeHatasi {
            get {
                return ResourceManager.GetString("KayitEklemeHatasi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt bilgileri getirilirken hata oluştu..
        /// </summary>
        internal static string KayitGetirmeHatasi {
            get {
                return ResourceManager.GetString("KayitGetirmeHatasi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt güncellenirken hata oluştu..
        /// </summary>
        internal static string KayitGuncellemeHatasi {
            get {
                return ResourceManager.GetString("KayitGuncellemeHatasi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt silinirken hata oluştu..
        /// </summary>
        internal static string KayitSilmeHatasi {
            get {
                return ResourceManager.GetString("KayitSilmeHatasi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kırtasiye Admin.
        /// </summary>
        internal static string KirtasiyeAdmin {
            get {
                return ResourceManager.GetString("KirtasiyeAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kırtasiye Uygulaması.
        /// </summary>
        internal static string KirtasiyeUygulamasi {
            get {
                return ResourceManager.GetString("KirtasiyeUygulamasi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfa Yetkilendirme.
        /// </summary>
        internal static string KullaniciAtama {
            get {
                return ResourceManager.GetString("KullaniciAtama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kurum.
        /// </summary>
        internal static string Kurum {
            get {
                return ResourceManager.GetString("Kurum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kurum giriniz..
        /// </summary>
        internal static string KurumSeciniz {
            get {
                return ResourceManager.GetString("KurumSeciniz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to --Tüm Tipler--.
        /// </summary>
        internal static string LBL_ALL_TYPES {
            get {
                return ResourceManager.GetString("LBL_ALL_TYPES", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Kayıt.
        /// </summary>
        internal static string LBL_RECORD_COUNT {
            get {
                return ResourceManager.GetString("LBL_RECORD_COUNT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to --Tip Seçiniz--.
        /// </summary>
        internal static string LBL_SELECT_TYPE {
            get {
                return ResourceManager.GetString("LBL_SELECT_TYPE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bağlantı.
        /// </summary>
        internal static string Link {
            get {
                return ResourceManager.GetString("Link", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Link Adı.
        /// </summary>
        internal static string LinkAdi {
            get {
                return ResourceManager.GetString("LinkAdi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Link adı giriniz..
        /// </summary>
        internal static string LinkAdiGirin {
            get {
                return ResourceManager.GetString("LinkAdiGirin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Link Bilgisi.
        /// </summary>
        internal static string LinkInformation {
            get {
                return ResourceManager.GetString("LinkInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Link Listesi.
        /// </summary>
        internal static string LinkListe {
            get {
                return ResourceManager.GetString("LinkListe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bağlantı giriniz..
        /// </summary>
        internal static string LinkSecin {
            get {
                return ResourceManager.GetString("LinkSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LogError&apos;da hata: {0}.
        /// </summary>
        internal static string LogErrorMessage {
            get {
                return ResourceManager.GetString("LogErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DigiHrApp Logo için seçilen dosya formatı yanlış.İzin verilen formatlar =&gt;.
        /// </summary>
        internal static string LogoHrAppDosyaFormatiYanlis {
            get {
                return ResourceManager.GetString("LogoHrAppDosyaFormatiYanlis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lokasyon.
        /// </summary>
        internal static string Lokasyon {
            get {
                return ResourceManager.GetString("Lokasyon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lokasyon giriniz..
        /// </summary>
        internal static string LokasyonGirin {
            get {
                return ResourceManager.GetString("LokasyonGirin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen önce domain seçiniz.
        /// </summary>
        internal static string LutfenDomainSeciniz {
            get {
                return ResourceManager.GetString("LutfenDomainSeciniz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt başarıyla silindi..
        /// </summary>
        internal static string MSG_DELETE_SUCCESS {
            get {
                return ResourceManager.GetString("MSG_DELETE_SUCCESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirme başarıyla silindi..
        /// </summary>
        internal static string MSG_DELETE_SUCCESS_UA {
            get {
                return ResourceManager.GetString("MSG_DELETE_SUCCESS_UA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aynı tip ve isim kombinasyonu zaten mevcut..
        /// </summary>
        internal static string MSG_DUPLICATE {
            get {
                return ResourceManager.GetString("MSG_DUPLICATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hata.
        /// </summary>
        internal static string MSG_ERROR {
            get {
                return ResourceManager.GetString("MSG_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu AD grup bu sayfa için zaten yetkilendirilmiş!.
        /// </summary>
        internal static string MSG_GROUP_DUPLICATE {
            get {
                return ResourceManager.GetString("MSG_GROUP_DUPLICATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İsim alanı boş olamaz..
        /// </summary>
        internal static string MSG_NAME_REQUIRED {
            get {
                return ResourceManager.GetString("MSG_NAME_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem yapılacak kayıt bulunamadı..
        /// </summary>
        internal static string MSG_RECORD_NOT_FOUND {
            get {
                return ResourceManager.GetString("MSG_RECORD_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt başarıyla eklendi..
        /// </summary>
        internal static string MSG_SAVE_SUCCESS {
            get {
                return ResourceManager.GetString("MSG_SAVE_SUCCESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirme başarıyla kaydedildi..
        /// </summary>
        internal static string MSG_SAVE_SUCCESS_UA {
            get {
                return ResourceManager.GetString("MSG_SAVE_SUCCESS_UA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen bir AD grup seçiniz..
        /// </summary>
        internal static string MSG_SELECT_GROUP {
            get {
                return ResourceManager.GetString("MSG_SELECT_GROUP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen önce bir sayfa seçiniz..
        /// </summary>
        internal static string MSG_SELECT_PAGE {
            get {
                return ResourceManager.GetString("MSG_SELECT_PAGE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen bir tip seçiniz..
        /// </summary>
        internal static string MSG_SELECT_TYPE {
            get {
                return ResourceManager.GetString("MSG_SELECT_TYPE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen bir kullanıcı seçiniz..
        /// </summary>
        internal static string MSG_SELECT_USER {
            get {
                return ResourceManager.GetString("MSG_SELECT_USER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem Başarılı.
        /// </summary>
        internal static string MSG_SUCCESS {
            get {
                return ResourceManager.GetString("MSG_SUCCESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt başarıyla güncellendi..
        /// </summary>
        internal static string MSG_UPDATE_SUCCESS {
            get {
                return ResourceManager.GetString("MSG_UPDATE_SUCCESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirme başarıyla güncellendi..
        /// </summary>
        internal static string MSG_UPDATE_SUCCESS_UA {
            get {
                return ResourceManager.GetString("MSG_UPDATE_SUCCESS_UA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu kullanıcı bu sayfa için zaten yetkilendirilmiş!.
        /// </summary>
        internal static string MSG_USER_DUPLICATE {
            get {
                return ResourceManager.GetString("MSG_USER_DUPLICATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Muhaberat Admin.
        /// </summary>
        internal static string MuhaberatAdmin {
            get {
                return ResourceManager.GetString("MuhaberatAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aynı isimde tip zaten mevcut..
        /// </summary>
        internal static string MukerrerKayit {
            get {
                return ResourceManager.GetString("MukerrerKayit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlemler.
        /// </summary>
        internal static string Names_Actions {
            get {
                return ResourceManager.GetString("Names_Actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni Kayıt Ekle.
        /// </summary>
        internal static string Names_AddNewRecord {
            get {
                return ResourceManager.GetString("Names_AddNewRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Temizle.
        /// </summary>
        internal static string Names_Clear {
            get {
                return ResourceManager.GetString("Names_Clear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oluşturma Tarihi.
        /// </summary>
        internal static string Names_CreatedColumn {
            get {
                return ResourceManager.GetString("Names_CreatedColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sil.
        /// </summary>
        internal static string Names_Delete {
            get {
                return ResourceManager.GetString("Names_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama.
        /// </summary>
        internal static string Names_Description {
            get {
                return ResourceManager.GetString("Names_Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama.
        /// </summary>
        internal static string Names_DescriptionColumn {
            get {
                return ResourceManager.GetString("Names_DescriptionColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama (İngilizce).
        /// </summary>
        internal static string Names_DescriptionEn {
            get {
                return ResourceManager.GetString("Names_DescriptionEn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İngilizce Açıklama.
        /// </summary>
        internal static string Names_DescriptionEnColumn {
            get {
                return ResourceManager.GetString("Names_DescriptionEnColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İngilizce açıklama alanı boş olamaz..
        /// </summary>
        internal static string Names_DescriptionEnRequired {
            get {
                return ResourceManager.GetString("Names_DescriptionEnRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Düzenle.
        /// </summary>
        internal static string Names_Edit {
            get {
                return ResourceManager.GetString("Names_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama girin.
        /// </summary>
        internal static string Names_EnterDescription {
            get {
                return ResourceManager.GetString("Names_EnterDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İngilizce açıklama giriniz.
        /// </summary>
        internal static string Names_EnterDescriptionEn {
            get {
                return ResourceManager.GetString("Names_EnterDescriptionEn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İsim girin.
        /// </summary>
        internal static string Names_EnterName {
            get {
                return ResourceManager.GetString("Names_EnterName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İngilizce isim giriniz.
        /// </summary>
        internal static string Names_EnterNameEn {
            get {
                return ResourceManager.GetString("Names_EnterNameEn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filtreleme.
        /// </summary>
        internal static string Names_Filter {
            get {
                return ResourceManager.GetString("Names_Filter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to -- Tümü --.
        /// </summary>
        internal static string Names_FilterAll {
            get {
                return ResourceManager.GetString("Names_FilterAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bilgileri girdikten sonra kaydetmek için formun sağındaki butonları kullanın..
        /// </summary>
        internal static string Names_FormHint {
            get {
                return ResourceManager.GetString("Names_FormHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İsim Tanımlama / Düzenleme.
        /// </summary>
        internal static string Names_FormTitle {
            get {
                return ResourceManager.GetString("Names_FormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ID.
        /// </summary>
        internal static string Names_ID {
            get {
                return ResourceManager.GetString("Names_ID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İsim Listesi.
        /// </summary>
        internal static string Names_List {
            get {
                return ResourceManager.GetString("Names_List", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İsim.
        /// </summary>
        internal static string Names_Name {
            get {
                return ResourceManager.GetString("Names_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İsim.
        /// </summary>
        internal static string Names_NameColumn {
            get {
                return ResourceManager.GetString("Names_NameColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İsim (İngilizce).
        /// </summary>
        internal static string Names_NameEn {
            get {
                return ResourceManager.GetString("Names_NameEn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İngilizce İsim.
        /// </summary>
        internal static string Names_NameEnColumn {
            get {
                return ResourceManager.GetString("Names_NameEnColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İngilizce isim alanı boş olamaz..
        /// </summary>
        internal static string Names_NameEnRequired {
            get {
                return ResourceManager.GetString("Names_NameEnRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İsim girişi zorunludur..
        /// </summary>
        internal static string Names_NameRequired {
            get {
                return ResourceManager.GetString("Names_NameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt Bulunamadı.
        /// </summary>
        internal static string Names_NoRecordsFound {
            get {
                return ResourceManager.GetString("Names_NoRecordsFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arama kriterleri ile eşleşen kayıt bulunamadı veya hiç kayıt mevcut değil..
        /// </summary>
        internal static string Names_NoRecordsMessage {
            get {
                return ResourceManager.GetString("Names_NoRecordsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu sayfadan Digiport menülerinde kullanılan isim ve yolları yönetebilirsiniz. İsimler, menü elemanlarının görünen adlarını ve bağlantı noktalarını belirler..
        /// </summary>
        internal static string Names_PageDescription {
            get {
                return ResourceManager.GetString("Names_PageDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfa Yolu.
        /// </summary>
        internal static string Names_PagePath {
            get {
                return ResourceManager.GetString("Names_PagePath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Örn: /AdminPages/DigiportAdmin/Names.aspx.
        /// </summary>
        internal static string Names_PagePathExample {
            get {
                return ResourceManager.GetString("Names_PagePathExample", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfanın tam yolunu &apos;/&apos; karakteri ile başlayacak şekilde belirtin..
        /// </summary>
        internal static string Names_PagePathHint {
            get {
                return ResourceManager.GetString("Names_PagePathHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfa yolu girişi zorunludur..
        /// </summary>
        internal static string Names_PagePathRequired {
            get {
                return ResourceManager.GetString("Names_PagePathRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport Menü İsim Yönetimi.
        /// </summary>
        internal static string Names_PageTitle {
            get {
                return ResourceManager.GetString("Names_PageTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} Kayıt.
        /// </summary>
        internal static string Names_RecordCount {
            get {
                return ResourceManager.GetString("Names_RecordCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt silinirken hata oluştu..
        /// </summary>
        internal static string Names_RecordDeletedError {
            get {
                return ResourceManager.GetString("Names_RecordDeletedError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt başarıyla silindi..
        /// </summary>
        internal static string Names_RecordDeletedSuccess {
            get {
                return ResourceManager.GetString("Names_RecordDeletedSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ara.
        /// </summary>
        internal static string Names_Search {
            get {
                return ResourceManager.GetString("Names_Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İsim ara.
        /// </summary>
        internal static string Names_SearchName {
            get {
                return ResourceManager.GetString("Names_SearchName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İsim ara....
        /// </summary>
        internal static string Names_SearchPlaceholder {
            get {
                return ResourceManager.GetString("Names_SearchPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to -- Seçiniz --.
        /// </summary>
        internal static string Names_SelectType {
            get {
                return ResourceManager.GetString("Names_SelectType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Durum.
        /// </summary>
        internal static string Names_Status {
            get {
                return ResourceManager.GetString("Names_Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aktif.
        /// </summary>
        internal static string Names_StatusActive {
            get {
                return ResourceManager.GetString("Names_StatusActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hepsi.
        /// </summary>
        internal static string Names_StatusAll {
            get {
                return ResourceManager.GetString("Names_StatusAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Durum.
        /// </summary>
        internal static string Names_StatusColumn {
            get {
                return ResourceManager.GetString("Names_StatusColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasif.
        /// </summary>
        internal static string Names_StatusInactive {
            get {
                return ResourceManager.GetString("Names_StatusInactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip.
        /// </summary>
        internal static string Names_Type {
            get {
                return ResourceManager.GetString("Names_Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip.
        /// </summary>
        internal static string Names_TypeColumn {
            get {
                return ResourceManager.GetString("Names_TypeColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip seçimi zorunludur..
        /// </summary>
        internal static string Names_TypeRequired {
            get {
                return ResourceManager.GetString("Names_TypeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notlar.
        /// </summary>
        internal static string Notlar {
            get {
                return ResourceManager.GetString("Notlar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt bulunamadı veya hiç tip tanımlanmamış..
        /// </summary>
        internal static string NoTypesFound {
            get {
                return ResourceManager.GetString("NoTypesFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu sayfadan Digiport admin sayfaları için kullanıcı ve Active Directory (AD) grup yetkilendirmelerini güvenli ve kolay bir şekilde yönetebilirsiniz. Yetkilendirme işlemleri, kullanıcıların ve grupların belirli sayfalara erişimini kontrol eder..
        /// </summary>
        internal static string PageAuthorizationDescription {
            get {
                return ResourceManager.GetString("PageAuthorizationDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirmek istediğiniz DigiPort sayfasını seçiniz. Seçim yaptığınızda mevcut yetkilendirmeler aşağıda listelenecektir..
        /// </summary>
        internal static string PageSelectionHint {
            get {
                return ResourceManager.GetString("PageSelectionHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasif.
        /// </summary>
        internal static string Pasif {
            get {
                return ResourceManager.GetString("Pasif", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen bir sayfa seçiniz..
        /// </summary>
        internal static string PleaseSelectPage {
            get {
                return ResourceManager.GetString("PleaseSelectPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfanın Başlığı.
        /// </summary>
        internal static string PopupSayfaBaslik {
            get {
                return ResourceManager.GetString("PopupSayfaBaslik", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfanın Başlığını Giriniz.
        /// </summary>
        internal static string PopupSayfaBaslikGirin {
            get {
                return ResourceManager.GetString("PopupSayfaBaslikGirin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfanın Html İçeriği.
        /// </summary>
        internal static string PopupSayfaIcerik {
            get {
                return ResourceManager.GetString("PopupSayfaIcerik", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt Düzenleniyor.
        /// </summary>
        internal static string RecordBeingEdited {
            get {
                return ResourceManager.GetString("RecordBeingEdited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ana Sayfaya Dön.
        /// </summary>
        internal static string ReturnToHomePage {
            get {
                return ResourceManager.GetString("ReturnToHomePage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sadece resim ve video dosyalarına izin verilir..
        /// </summary>
        internal static string SadeceResimVideo {
            get {
                return ResourceManager.GetString("SadeceResimVideo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sağ Galeri.
        /// </summary>
        internal static string SagGaleri {
            get {
                return ResourceManager.GetString("SagGaleri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaydet.
        /// </summary>
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1-999 Arasında Sayı Seçiniz.
        /// </summary>
        internal static string SayiAraligi1 {
            get {
                return ResourceManager.GetString("SayiAraligi1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1-99999 Arasında Sayı Seçiniz.
        /// </summary>
        internal static string SayiAraligi2 {
            get {
                return ResourceManager.GetString("SayiAraligi2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1-99 Arasında Sayı Seçiniz.
        /// </summary>
        internal static string SayiAraligi3 {
            get {
                return ResourceManager.GetString("SayiAraligi3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirme ara....
        /// </summary>
        internal static string SearchAuthorizations {
            get {
                return ResourceManager.GetString("SearchAuthorizations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçileni Temizle.
        /// </summary>
        internal static string SecileniTemizle {
            get {
                return ResourceManager.GetString("SecileniTemizle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ---Seçiniz---.
        /// </summary>
        internal static string SecUserText {
            get {
                return ResourceManager.GetString("SecUserText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Grup Seçiniz:.
        /// </summary>
        internal static string SelectADGroup {
            get {
                return ResourceManager.GetString("SelectADGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki Tipi Seçiniz:.
        /// </summary>
        internal static string SelectAuthorityType {
            get {
                return ResourceManager.GetString("SelectAuthorityType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Domain Seçiniz:.
        /// </summary>
        internal static string SelectDomain {
            get {
                return ResourceManager.GetString("SelectDomain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to --Domain Seçiniz--.
        /// </summary>
        internal static string SelectDomainDefault {
            get {
                return ResourceManager.GetString("SelectDomainDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirilecek Sayfayı Seçiniz:.
        /// </summary>
        internal static string SelectPageToAuthorize {
            get {
                return ResourceManager.GetString("SelectPageToAuthorize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı Seçiniz:.
        /// </summary>
        internal static string SelectUser {
            get {
                return ResourceManager.GetString("SelectUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı oturumu bulunamadı. Lütfen sisteme yeniden giriş yapın..
        /// </summary>
        internal static string SessionNotFound {
            get {
                return ResourceManager.GetString("SessionNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slider Süre Çubuğu Göster.
        /// </summary>
        internal static string ShowProgressBar {
            get {
                return ResourceManager.GetString("ShowProgressBar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slider Navigasyon Resimleri Göster.
        /// </summary>
        internal static string ShowThumbnails {
            get {
                return ResourceManager.GetString("ShowThumbnails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sil.
        /// </summary>
        internal static string Sil {
            get {
                return ResourceManager.GetString("Sil", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Silme Başarılı.
        /// </summary>
        internal static string SilmeBasarili {
            get {
                return ResourceManager.GetString("SilmeBasarili", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Silme için kayıt seçilmemiş..
        /// </summary>
        internal static string SilmeKayitSecilmemis {
            get {
                return ResourceManager.GetString("SilmeKayitSecilmemis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sıra No.
        /// </summary>
        internal static string SiraNo {
            get {
                return ResourceManager.GetString("SiraNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sıra No Seçiniz.
        /// </summary>
        internal static string SiraNoSecin {
            get {
                return ResourceManager.GetString("SiraNoSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slide.
        /// </summary>
        internal static string Slide {
            get {
                return ResourceManager.GetString("Slide", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slide Adı.
        /// </summary>
        internal static string SlideAdi {
            get {
                return ResourceManager.GetString("SlideAdi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slide adı giriniz.
        /// </summary>
        internal static string SlideAdiGirin {
            get {
                return ResourceManager.GetString("SlideAdiGirin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slide Bilgileri.
        /// </summary>
        internal static string SlideBilgileri {
            get {
                return ResourceManager.GetString("SlideBilgileri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçilen resim dosyasının boyutları {0}x{1}. Olması gereken ise {2}x{3}.
        /// </summary>
        internal static string SlideBoyutlari {
            get {
                return ResourceManager.GetString("SlideBoyutlari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slide için seçilen dosya formatı yanlış.İzin verilen formatlar =&gt;.
        /// </summary>
        internal static string SlideDosyaFormatiYanlis {
            get {
                return ResourceManager.GetString("SlideDosyaFormatiYanlis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DigiHrApp Slide için seçilen dosya formatı yanlış.İzin verilen formatlar =&gt;.
        /// </summary>
        internal static string SlideHrAppDosyaFormatiYanlis {
            get {
                return ResourceManager.GetString("SlideHrAppDosyaFormatiYanlis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slide Listesi.
        /// </summary>
        internal static string SlideListe {
            get {
                return ResourceManager.GetString("SlideListe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slider Gösterim Süresi (sn.).
        /// </summary>
        internal static string SliderAutoSlideInterval {
            get {
                return ResourceManager.GetString("SliderAutoSlideInterval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen slider gösterim süresi seçiniz..
        /// </summary>
        internal static string SliderAutoSlideIntervalSecin {
            get {
                return ResourceManager.GetString("SliderAutoSlideIntervalSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slider Efekt Tipi.
        /// </summary>
        internal static string SliderEfektTipi {
            get {
                return ResourceManager.GetString("SliderEfektTipi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen slider efekt tipi seçiniz..
        /// </summary>
        internal static string SliderEfektTipiSecin {
            get {
                return ResourceManager.GetString("SliderEfektTipiSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slider Genişliği.
        /// </summary>
        internal static string SliderGenislik {
            get {
                return ResourceManager.GetString("SliderGenislik", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen slider genişiği seçiniz.
        /// </summary>
        internal static string SliderGenislikSecin {
            get {
                return ResourceManager.GetString("SliderGenislikSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slider Seçenekleri.
        /// </summary>
        internal static string SliderSecenekleri {
            get {
                return ResourceManager.GetString("SliderSecenekleri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen thumbnail genişliği seçiniz..
        /// </summary>
        internal static string SliderThumbnailGenislikSecin {
            get {
                return ResourceManager.GetString("SliderThumbnailGenislikSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen thumbnail yüksekliği seçiniz..
        /// </summary>
        internal static string SliderThumbnailYukseklikSecin {
            get {
                return ResourceManager.GetString("SliderThumbnailYukseklikSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slider Başlık Modu.
        /// </summary>
        internal static string SliderTitleMode {
            get {
                return ResourceManager.GetString("SliderTitleMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen slider başlık modu seçiniz..
        /// </summary>
        internal static string SliderTitleModeSecin {
            get {
                return ResourceManager.GetString("SliderTitleModeSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slider Geçiş Süresi (sn.).
        /// </summary>
        internal static string SliderTransitionDuration {
            get {
                return ResourceManager.GetString("SliderTransitionDuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen slider geçiş süresi seçiniz..
        /// </summary>
        internal static string SliderTransitionDurationSecin {
            get {
                return ResourceManager.GetString("SliderTransitionDurationSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slider Geçiş Kolaylığı.
        /// </summary>
        internal static string SliderTransitionEasing {
            get {
                return ResourceManager.GetString("SliderTransitionEasing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen slider geçiş kolaylığı seçiniz..
        /// </summary>
        internal static string SliderTransitionEasingSecin {
            get {
                return ResourceManager.GetString("SliderTransitionEasingSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slider Yüksekliği.
        /// </summary>
        internal static string SliderYukseklik {
            get {
                return ResourceManager.GetString("SliderYukseklik", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen slider yüksekliği seçiniz.
        /// </summary>
        internal static string SliderYukseklikSecin {
            get {
                return ResourceManager.GetString("SliderYukseklikSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sol Galeri.
        /// </summary>
        internal static string SolGaleri {
            get {
                return ResourceManager.GetString("SolGaleri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Durum.
        /// </summary>
        internal static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Değiştir.
        /// </summary>
        internal static string SwitchUser {
            get {
                return ResourceManager.GetString("SwitchUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Telefon.
        /// </summary>
        internal static string Telefon {
            get {
                return ResourceManager.GetString("Telefon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Telefon giriniz..
        /// </summary>
        internal static string TelefonGirin {
            get {
                return ResourceManager.GetString("TelefonGirin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filtreleri Temizle.
        /// </summary>
        internal static string TemizleFiltre {
            get {
                return ResourceManager.GetString("TemizleFiltre", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Test Mate Admin.
        /// </summary>
        internal static string TestMateAdmin {
            get {
                return ResourceManager.GetString("TestMateAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thumbnail.
        /// </summary>
        internal static string Thumbnail {
            get {
                return ResourceManager.GetString("Thumbnail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçilen thumbnail dosyasının boyutları {0}x{1}. Olması gereken ise {2}x{3}.
        /// </summary>
        internal static string ThumbnailBoyutlari {
            get {
                return ResourceManager.GetString("ThumbnailBoyutlari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thumbnail için seçilen dosya formatı yanlış.İzin verilen formatlar =&gt;.
        /// </summary>
        internal static string ThumbnailDosyaFormatiYanlis {
            get {
                return ResourceManager.GetString("ThumbnailDosyaFormatiYanlis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thumbnail Yüksekliği.
        /// </summary>
        internal static string ThumbnailHeight {
            get {
                return ResourceManager.GetString("ThumbnailHeight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thumbnail Genişliği.
        /// </summary>
        internal static string ThumbnailWidth {
            get {
                return ResourceManager.GetString("ThumbnailWidth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tıklandığında Hedef Link.
        /// </summary>
        internal static string TiklanmaHedefLink {
            get {
                return ResourceManager.GetString("TiklanmaHedefLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tıklanma İşlemi.
        /// </summary>
        internal static string TiklanmaIslemi {
            get {
                return ResourceManager.GetString("TiklanmaIslemi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tıklanınca Olacak İşlemi Seçiniz.
        /// </summary>
        internal static string TiklanmaIslemiSecin {
            get {
                return ResourceManager.GetString("TiklanmaIslemiSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip.
        /// </summary>
        internal static string Tip {
            get {
                return ResourceManager.GetString("Tip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bileşen Tipi Belirsiz.
        /// </summary>
        internal static string TipBelirsiz {
            get {
                return ResourceManager.GetString("TipBelirsiz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip başarıyla eklendi..
        /// </summary>
        internal static string TipEklemeBasarili {
            get {
                return ResourceManager.GetString("TipEklemeBasarili", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip başarıyla güncellendi..
        /// </summary>
        internal static string TipGuncellemeBasarili {
            get {
                return ResourceManager.GetString("TipGuncellemeBasarili", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip seçiniz..
        /// </summary>
        internal static string TipSecin {
            get {
                return ResourceManager.GetString("TipSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip başarıyla silindi..
        /// </summary>
        internal static string TipSilmeBasarili {
            get {
                return ResourceManager.GetString("TipSilmeBasarili", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bileşen Tipi Yanlış.
        /// </summary>
        internal static string TipYanlis {
            get {
                return ResourceManager.GetString("TipYanlis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Toplam Kayıt: {0}.
        /// </summary>
        internal static string TotalRecords {
            get {
                return ResourceManager.GetString("TotalRecords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Toplam Kayıt: 0.
        /// </summary>
        internal static string TotalRecordsZero {
            get {
                return ResourceManager.GetString("TotalRecordsZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tümü.
        /// </summary>
        internal static string Tumu {
            get {
                return ResourceManager.GetString("Tumu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip Tanımlama.
        /// </summary>
        internal static string TurTanimlama {
            get {
                return ResourceManager.GetString("TurTanimlama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip Yönetimi.
        /// </summary>
        internal static string TypeManagement {
            get {
                return ResourceManager.GetString("TypeManagement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Menü tiplerini oluşturun, güncelleyin ve yönetin..
        /// </summary>
        internal static string TypeManagementDescription {
            get {
                return ResourceManager.GetString("TypeManagementDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip Adı.
        /// </summary>
        internal static string TypeName {
            get {
                return ResourceManager.GetString("TypeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip adını giriniz.
        /// </summary>
        internal static string TypeNamePlaceholder {
            get {
                return ResourceManager.GetString("TypeNamePlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tip Adı zorunludur..
        /// </summary>
        internal static string TypeNameRequired {
            get {
                return ResourceManager.GetString("TypeNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkisiz Erişim.
        /// </summary>
        internal static string UnauthorizedAccess {
            get {
                return ResourceManager.GetString("UnauthorizedAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bilinmiyor.
        /// </summary>
        internal static string Unknown {
            get {
                return ResourceManager.GetString("Unknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Güncelle.
        /// </summary>
        internal static string Update {
            get {
                return ResourceManager.GetString("Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen Internet Explorer tarayıcısı ile işlemlerinizi yapınız! .
        /// </summary>
        internal static string UseInternetExplorer {
            get {
                return ResourceManager.GetString("UseInternetExplorer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı.
        /// </summary>
        internal static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlemler.
        /// </summary>
        internal static string UserAssignment_ActionsColumn {
            get {
                return ResourceManager.GetString("UserAssignment_ActionsColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ekle.
        /// </summary>
        internal static string UserAssignment_AddButton {
            get {
                return ResourceManager.GetString("UserAssignment_AddButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni Yetki Ekle.
        /// </summary>
        internal static string UserAssignment_AddNewButton {
            get {
                return ResourceManager.GetString("UserAssignment_AddNewButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki Atama / Düzenleme.
        /// </summary>
        internal static string UserAssignment_AssignmentTitle {
            get {
                return ResourceManager.GetString("UserAssignment_AssignmentTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirilen.
        /// </summary>
        internal static string UserAssignment_AuthorizedColumn {
            get {
                return ResourceManager.GetString("UserAssignment_AuthorizedColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki Tipi Seçiniz.
        /// </summary>
        internal static string UserAssignment_AuthTypeLabel {
            get {
                return ResourceManager.GetString("UserAssignment_AuthTypeLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İptal.
        /// </summary>
        internal static string UserAssignment_CancelButton {
            get {
                return ResourceManager.GetString("UserAssignment_CancelButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oluşturma Tarihi.
        /// </summary>
        internal static string UserAssignment_CreatedDateColumn {
            get {
                return ResourceManager.GetString("UserAssignment_CreatedDateColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mevcut Yetkilendirmeler.
        /// </summary>
        internal static string UserAssignment_CurrentAuthTitle {
            get {
                return ResourceManager.GetString("UserAssignment_CurrentAuthTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to return confirm(&apos;Bu yetkiyi silmek istediğinize emin misiniz? Bu işlem geri alınamaz.&apos;);.
        /// </summary>
        internal static string UserAssignment_DeleteConfirm {
            get {
                return ResourceManager.GetString("UserAssignment_DeleteConfirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki silinirken hata oluştu..
        /// </summary>
        internal static string UserAssignment_DeleteError {
            get {
                return ResourceManager.GetString("UserAssignment_DeleteError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirme başarıyla silindi..
        /// </summary>
        internal static string UserAssignment_DeleteSuccess {
            get {
                return ResourceManager.GetString("UserAssignment_DeleteSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sil.
        /// </summary>
        internal static string UserAssignment_DeleteTooltip {
            get {
                return ResourceManager.GetString("UserAssignment_DeleteTooltip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiturk.
        /// </summary>
        internal static string UserAssignment_DomainDigiturk {
            get {
                return ResourceManager.GetString("UserAssignment_DomainDigiturk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiturk CC.
        /// </summary>
        internal static string UserAssignment_DomainDigiturkCC {
            get {
                return ResourceManager.GetString("UserAssignment_DomainDigiturkCC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to --Domain Seçiniz--.
        /// </summary>
        internal static string UserAssignment_DomainSelectDefault {
            get {
                return ResourceManager.GetString("UserAssignment_DomainSelectDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Domain Seçiniz.
        /// </summary>
        internal static string UserAssignment_DomainSelectionLabel {
            get {
                return ResourceManager.GetString("UserAssignment_DomainSelectionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Düzenlenen Yetki.
        /// </summary>
        internal static string UserAssignment_EditingAuth {
            get {
                return ResourceManager.GetString("UserAssignment_EditingAuth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt Düzenleniyor.
        /// </summary>
        internal static string UserAssignment_EditingRecord {
            get {
                return ResourceManager.GetString("UserAssignment_EditingRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçili sayfa için henüz yetkilendirme yapılmamış veya arama kriterlerinize uygun sonuç bulunamadı..
        /// </summary>
        internal static string UserAssignment_EmptyStateText {
            get {
                return ResourceManager.GetString("UserAssignment_EmptyStateText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirme Bulunamadı.
        /// </summary>
        internal static string UserAssignment_EmptyStateTitle {
            get {
                return ResourceManager.GetString("UserAssignment_EmptyStateTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD grupları yüklenirken bir hata oluştu.
        /// </summary>
        internal static string UserAssignment_ErrorLoadingGroups {
            get {
                return ResourceManager.GetString("UserAssignment_ErrorLoadingGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki bilgileri getirilirken hata oluştu..
        /// </summary>
        internal static string UserAssignment_GetRecordError {
            get {
                return ResourceManager.GetString("UserAssignment_GetRecordError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active Directory gruplarına toplu yetki atayın.
        /// </summary>
        internal static string UserAssignment_GroupAuthDesc {
            get {
                return ResourceManager.GetString("UserAssignment_GroupAuthDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Grup Yetkilendirme.
        /// </summary>
        internal static string UserAssignment_GroupAuthTitle {
            get {
                return ResourceManager.GetString("UserAssignment_GroupAuthTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu AD grup bu sayfa için zaten yetkilendirilmiş!.
        /// </summary>
        internal static string UserAssignment_GroupDuplicate {
            get {
                return ResourceManager.GetString("UserAssignment_GroupDuplicate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Grup Seçiniz.
        /// </summary>
        internal static string UserAssignment_GroupSelectionLabel {
            get {
                return ResourceManager.GetString("UserAssignment_GroupSelectionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Grubu seçimi zorunludur..
        /// </summary>
        internal static string UserAssignment_GroupSelectionRequired {
            get {
                return ResourceManager.GetString("UserAssignment_GroupSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Grup.
        /// </summary>
        internal static string UserAssignment_GroupType {
            get {
                return ResourceManager.GetString("UserAssignment_GroupType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki listesi yüklenirken hata oluştu..
        /// </summary>
        internal static string UserAssignment_LoadGridError {
            get {
                return ResourceManager.GetString("UserAssignment_LoadGridError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Grup listesi yüklenirken hata oluştu..
        /// </summary>
        internal static string UserAssignment_LoadGroupsError {
            get {
                return ResourceManager.GetString("UserAssignment_LoadGroupsError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfa listesi yüklenirken hata oluştu..
        /// </summary>
        internal static string UserAssignment_LoadPagesError {
            get {
                return ResourceManager.GetString("UserAssignment_LoadPagesError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı listesi yüklenirken hata oluştu..
        /// </summary>
        internal static string UserAssignment_LoadUsersError {
            get {
                return ResourceManager.GetString("UserAssignment_LoadUsersError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu sayfadan Digiport admin sayfaları için kullanıcı ve Active Directory (AD) grup yetkilendirmelerini güvenli ve kolay bir şekilde yönetebilirsiniz. Yetkilendirme işlemleri, kullanıcıların ve grupların belirli sayfalara erişimini kontrol eder..
        /// </summary>
        internal static string UserAssignment_MainDescription {
            get {
                return ResourceManager.GetString("UserAssignment_MainDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport Sayfa Yetkilendirme.
        /// </summary>
        internal static string UserAssignment_MainTitle {
            get {
                return ResourceManager.GetString("UserAssignment_MainTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirmek istediğiniz DigiPort sayfasını seçiniz. Seçim yaptığınızda mevcut yetkilendirmeler aşağıda listelenecektir..
        /// </summary>
        internal static string UserAssignment_PageSelectionHint {
            get {
                return ResourceManager.GetString("UserAssignment_PageSelectionHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirilecek Sayfayı Seçiniz.
        /// </summary>
        internal static string UserAssignment_PageSelectionLabel {
            get {
                return ResourceManager.GetString("UserAssignment_PageSelectionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen bir sayfa seçiniz..
        /// </summary>
        internal static string UserAssignment_PageSelectionRequired {
            get {
                return ResourceManager.GetString("UserAssignment_PageSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DigiPort Sayfa Seçimi.
        /// </summary>
        internal static string UserAssignment_PageSelectionTitle {
            get {
                return ResourceManager.GetString("UserAssignment_PageSelectionTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport Menu - Kullanıcı Yetkilendirme.
        /// </summary>
        internal static string UserAssignment_PageTitle {
            get {
                return ResourceManager.GetString("UserAssignment_PageTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kayıt başarıyla silindi..
        /// </summary>
        internal static string UserAssignment_RecordDeletedSuccess {
            get {
                return ResourceManager.GetString("UserAssignment_RecordDeletedSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki kaydedilirken hata oluştu..
        /// </summary>
        internal static string UserAssignment_SaveError {
            get {
                return ResourceManager.GetString("UserAssignment_SaveError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçilen kullanıcı veya gruba yetki atamak için Kaydet butonuna tıklayın..
        /// </summary>
        internal static string UserAssignment_SaveInstruction {
            get {
                return ResourceManager.GetString("UserAssignment_SaveInstruction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirme başarıyla kaydedildi..
        /// </summary>
        internal static string UserAssignment_SaveSuccess {
            get {
                return ResourceManager.GetString("UserAssignment_SaveSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirme ara....
        /// </summary>
        internal static string UserAssignment_SearchPlaceholder {
            get {
                return ResourceManager.GetString("UserAssignment_SearchPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to --Grup Seçiniz--.
        /// </summary>
        internal static string UserAssignment_SelectGroup {
            get {
                return ResourceManager.GetString("UserAssignment_SelectGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to --Sayfa Seçiniz--.
        /// </summary>
        internal static string UserAssignment_SelectPage {
            get {
                return ResourceManager.GetString("UserAssignment_SelectPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to --Kullanıcı Seçiniz--.
        /// </summary>
        internal static string UserAssignment_SelectUser {
            get {
                return ResourceManager.GetString("UserAssignment_SelectUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfa Bilgilerini Göster.
        /// </summary>
        internal static string UserAssignment_ShowPageInfo {
            get {
                return ResourceManager.GetString("UserAssignment_ShowPageInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aktif.
        /// </summary>
        internal static string UserAssignment_StatusActive {
            get {
                return ResourceManager.GetString("UserAssignment_StatusActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tümü.
        /// </summary>
        internal static string UserAssignment_StatusAll {
            get {
                return ResourceManager.GetString("UserAssignment_StatusAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Durum.
        /// </summary>
        internal static string UserAssignment_StatusColumn {
            get {
                return ResourceManager.GetString("UserAssignment_StatusColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pasif.
        /// </summary>
        internal static string UserAssignment_StatusInactive {
            get {
                return ResourceManager.GetString("UserAssignment_StatusInactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Durum.
        /// </summary>
        internal static string UserAssignment_StatusLabel {
            get {
                return ResourceManager.GetString("UserAssignment_StatusLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Güncelle.
        /// </summary>
        internal static string UserAssignment_UpdateButton {
            get {
                return ResourceManager.GetString("UserAssignment_UpdateButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki güncellenirken hata oluştu..
        /// </summary>
        internal static string UserAssignment_UpdateError {
            get {
                return ResourceManager.GetString("UserAssignment_UpdateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilendirme başarıyla güncellendi..
        /// </summary>
        internal static string UserAssignment_UpdateSuccess {
            get {
                return ResourceManager.GetString("UserAssignment_UpdateSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bireysel kullanıcılara özel yetki atayın.
        /// </summary>
        internal static string UserAssignment_UserAuthDesc {
            get {
                return ResourceManager.GetString("UserAssignment_UserAuthDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı Yetkilendirme.
        /// </summary>
        internal static string UserAssignment_UserAuthTitle {
            get {
                return ResourceManager.GetString("UserAssignment_UserAuthTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu kullanıcı bu sayfa için zaten yetkilendirilmiş!.
        /// </summary>
        internal static string UserAssignment_UserDuplicate {
            get {
                return ResourceManager.GetString("UserAssignment_UserDuplicate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı Seçiniz.
        /// </summary>
        internal static string UserAssignment_UserSelectionLabel {
            get {
                return ResourceManager.GetString("UserAssignment_UserSelectionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı seçimi zorunludur..
        /// </summary>
        internal static string UserAssignment_UserSelectionRequired {
            get {
                return ResourceManager.GetString("UserAssignment_UserSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı.
        /// </summary>
        internal static string UserAssignment_UserType {
            get {
                return ResourceManager.GetString("UserAssignment_UserType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı Yetkilendirme.
        /// </summary>
        internal static string UserAuthorization {
            get {
                return ResourceManager.GetString("UserAuthorization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bireysel kullanıcılara özel yetki atayın.
        /// </summary>
        internal static string UserAuthorizationDesc {
            get {
                return ResourceManager.GetString("UserAuthorizationDesc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı seçimi zorunludur..
        /// </summary>
        internal static string UserSelectionRequired {
            get {
                return ResourceManager.GetString("UserSelectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen geçerli bir kullanıcı seçiniz.
        /// </summary>
        internal static string UserSelectRequired {
            get {
                return ResourceManager.GetString("UserSelectRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı değiştirme sırasında bir hata oluştu: {0}.
        /// </summary>
        internal static string UserSwitchError {
            get {
                return ResourceManager.GetString("UserSwitchError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı değiştirme işlemi başarısız oldu. Lütfen tekrar deneyin..
        /// </summary>
        internal static string UserSwitchFailed {
            get {
                return ResourceManager.GetString("UserSwitchFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Üst Banner.
        /// </summary>
        internal static string UstBanner {
            get {
                return ResourceManager.GetString("UstBanner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiport Ana Sayfa Üst Galeri.
        /// </summary>
        internal static string UstGaleri {
            get {
                return ResourceManager.GetString("UstGaleri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string VAL_DEFAULT_GROUP {
            get {
                return ResourceManager.GetString("VAL_DEFAULT_GROUP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 0.
        /// </summary>
        internal static string VAL_DEFAULT_USER {
            get {
                return ResourceManager.GetString("VAL_DEFAULT_USER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Videonun boyutu izin verilen maksimum limiti aşıyor.Videoyu link olarak belirtebilirsiniz..
        /// </summary>
        internal static string VideoIzinVerilenMaxBoyut {
            get {
                return ResourceManager.GetString("VideoIzinVerilenMaxBoyut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni Duyuru.
        /// </summary>
        internal static string YeniDuyuru {
            get {
                return ResourceManager.GetString("YeniDuyuru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yüklenilecek Duyuru Resmi Seçiniz.
        /// </summary>
        internal static string YeniDuyuruResmiSec {
            get {
                return ResourceManager.GetString("YeniDuyuruResmiSec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni İçerik.
        /// </summary>
        internal static string YeniIcerik {
            get {
                return ResourceManager.GetString("YeniIcerik", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni İndirim Fırsatı.
        /// </summary>
        internal static string YeniIndirimFirsati {
            get {
                return ResourceManager.GetString("YeniIndirimFirsati", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni Slide.
        /// </summary>
        internal static string YeniKayit {
            get {
                return ResourceManager.GetString("YeniKayit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni Link.
        /// </summary>
        internal static string YeniLink {
            get {
                return ResourceManager.GetString("YeniLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni Slide.
        /// </summary>
        internal static string YeniSlide {
            get {
                return ResourceManager.GetString("YeniSlide", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni bir slide kaydı için dosya seçmelisiniz..
        /// </summary>
        internal static string YeniSlideDosyaSec {
            get {
                return ResourceManager.GetString("YeniSlideDosyaSec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni bir slide kaydı için thumbnail seçmelisiniz..
        /// </summary>
        internal static string YeniThumbnailDosyaSec {
            get {
                return ResourceManager.GetString("YeniThumbnailDosyaSec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki kontrolü sırasında bir hata oluştu..
        /// </summary>
        internal static string YetkiKontrolHatasi {
            get {
                return ResourceManager.GetString("YetkiKontrolHatasi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yönetim Sayfaları.
        /// </summary>
        internal static string YonetimSayfalari {
            get {
                return ResourceManager.GetString("YonetimSayfalari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DigiHR Uygulama İçin Yüklenilecek Duyuru Resmi Seçiniz.
        /// </summary>
        internal static string YuklenilecekDuyuruAppResmiSecin {
            get {
                return ResourceManager.GetString("YuklenilecekDuyuruAppResmiSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yüklenilecek Duyuru Resmi Seçiniz.
        /// </summary>
        internal static string YuklenilecekDuyuruResmiSecin {
            get {
                return ResourceManager.GetString("YuklenilecekDuyuruResmiSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yüklenilecek Logo Seçiniz.
        /// </summary>
        internal static string YuklenilecekLogoSecin {
            get {
                return ResourceManager.GetString("YuklenilecekLogoSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yüklenilecek Slide Seçiniz.
        /// </summary>
        internal static string YuklenilecekSlideSecin {
            get {
                return ResourceManager.GetString("YuklenilecekSlideSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yüklenilecek Thumbnail Seçiniz.
        /// </summary>
        internal static string YuklenilecekThumbnailSecin {
            get {
                return ResourceManager.GetString("YuklenilecekThumbnailSecin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boş geçilemez.
        /// </summary>
        internal static string ZorunluAlan {
            get {
                return ResourceManager.GetString("ZorunluAlan", resourceCulture);
            }
        }
    }
}
