﻿using CoreHelpers;
using System;
using System.Collections.Generic;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using WebCore;
using FormHelper.YetkiHelper;
using System.Configuration;
using System.Linq;
using System.Data;

namespace AracTakipSistemi
{
    public partial class SiteMaster : UIMasterPage
    {
        private const string AntiXsrfTokenKey = "__AntiXsrfToken";
        private const string AntiXsrfUserNameKey = "__AntiXsrfUserName";
        private string _antiXsrfTokenValue;

        protected void Page_Init(object sender, EventArgs e)
        {
            // The code below helps to protect against XSRF attacks
            var requestCookie = Request.Cookies[AntiXsrfTokenKey];
            Guid requestCookieGuidValue;
            if (requestCookie != null && Guid.TryParse(requestCookie.Value, out requestCookieGuidValue))
            {
                // Use the Anti-XSRF token from the cookie
                _antiXsrfTokenValue = requestCookie.Value;
                Page.ViewStateUserKey = _antiXsrfTokenValue;
            }
            else
            {
                // Generate a new Anti-XSRF token and save to the cookie
                _antiXsrfTokenValue = Guid.NewGuid().ToString("N");
                Page.ViewStateUserKey = _antiXsrfTokenValue;

                var responseCookie = new HttpCookie(AntiXsrfTokenKey)
                {
                    HttpOnly = true,
                    Value = _antiXsrfTokenValue
                };
                if (FormsAuthentication.RequireSSL && Request.IsSecureConnection)
                {
                    responseCookie.Secure = true;
                }
                Response.Cookies.Set(responseCookie);
            }

            Page.PreLoad += master_Page_PreLoad;
        }

        /// <summary>
        /// Verilen içeriği sayfanın başına yazar.
        /// </summary>
        /// <param name="icerik"></param>
        public void SayfaBaslikAt(string icerik)
        {
            lblSayfaBaslik.Text = icerik;
            Page.Title = icerik;
        }

        protected void master_Page_PreLoad(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Set Anti-XSRF token
                ViewState[AntiXsrfTokenKey] = Page.ViewStateUserKey;
                ViewState[AntiXsrfUserNameKey] = Context.User.Identity.Name ?? String.Empty;
            }
            else
            {
                // Validate the Anti-XSRF token
                if ((string)ViewState[AntiXsrfTokenKey] != _antiXsrfTokenValue
                    || (string)ViewState[AntiXsrfUserNameKey] != (Context.User.Identity.Name ?? String.Empty))
                {
                    throw new InvalidOperationException("Validation of Anti-XSRF token failed.");
                }
            }
        }

        DataTable authorizedMenus;
        FormHelper.DigiportAdmin.DigiportAuthorizationService authService;
        List<string> kullaniciGruplari;
        bool isAdmin = false;
        string absolutePath = string.Empty;
        bool mustBeRedirectedToMainPage = false;
        protected void Page_Load(object sender, EventArgs e)
        {
            absolutePath = HttpUtility.UrlDecode(Request.Url.AbsolutePath);
            // Set TEST environment visibility
            bool isDebugMode = ConfigurationManager.AppSettings["debugMode"].ToLower() == "true";
            tdTest.Visible = isDebugMode;
            tdTest.InnerText = isDebugMode ? "TEST" : "LIVE";
            tdTest.Style.Add("background-color", isDebugMode ? "#dc3545" : "#198754");

            // Verify that user information is properly set
            VerifyUserSession();

            // Get the authorization service
            authService = FormHelper.DigiportAdmin.DigiportAuthorizationService.Instance;
            authorizedMenus = authService.GetUserAuthorizedMenus();

            // Get user groups
            kullaniciGruplari = new YetkiHelper().gruplar();

            // Check if user is an admin or part of tech group
            isAdmin = (kullaniciGruplari.Contains(ConfigurationManager.AppSettings["AdGroup_TechCorp"]));

            // Check if the original user was an admin (before impersonation)
            bool wasOriginalUserAdmin = false;
            if (Session["IsImpersonating"] != null && (bool)Session["IsImpersonating"] && Session["OriginalLoginId"] != null)
            {
                decimal originalLoginId = Convert.ToDecimal(Session["OriginalLoginId"]);
                wasOriginalUserAdmin = YetkiHelper.KullaniciAdminMi(originalLoginId);
            }

            // Show user information with impersonation status if applicable
            DisplayUserInfo(isAdmin);

            // Configure user dropdown for admin users or original admin users
            if (isAdmin || wasOriginalUserAdmin)
            {
                lbTestPageContainer.Visible = true;

                if (!IsPostBack)
                {
                    // Load user dropdown
                    LoadUserDropdown();
                }
            }
            else
            {
                lbTestPageContainer.Visible = false;
            }


            ConfigureTestMateAdminVisibility();
            ConfigureMuhaberatAdminVisibility();
            ConfigureDigiportAnketVisibility();
            ConfigureDigiportAdminVisibility();
            InitializeMenuVisibilityForWorkflowRelated();
            liDiger.Visible = menuDigiportAnket.Visible || mnuAracTalepAdmin.Visible || mnuEkipmanAdmin.Visible || mnuKirtasiyeAdmin.Visible || menuTestMate.Visible || menuMuhaberat.Visible;

            // Configure language dropdown
            if (!IsPostBack)
            {
                string selectedLang = Session["AdminYonetimSistemiSecilenDil" + Session.SessionID] as string;

                // If no language is selected, default to Turkish
                if (string.IsNullOrEmpty(selectedLang))
                {
                    selectedLang = "Turkish";
                    Session["AdminYonetimSistemiSecilenDil" + Session.SessionID] = selectedLang;

                    // Set culture to tr-TR for Turkish
                    System.Threading.Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo("tr-TR");
                    System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo("tr-TR");
                }

                drpDilSecimi.SelectedValue = selectedLang == "English" ? "2" : "1";
            }
            if (mustBeRedirectedToMainPage)
            {
                Session["ErrorPageMessage"] = FormHelper.CoreHelper.isEnglish() ? "Unauthorized Access" : "Yetkisiz Erişim";
                Response.Redirect(@"~\AdminPages\Exception\Hata.aspx");
            }
        }

        /// <summary>
        /// Verifies that the user session contains all required information
        /// </summary>
        private void VerifyUserSession()
        {
            try
            {
                // Only proceed if we have a LoginId but missing other information
                if (Session["LoginId"] != null &&
                    (Session["UserFullName"] == null || string.IsNullOrEmpty(Session["UserFullName"].ToString())))
                {
                    decimal loginId = Convert.ToDecimal(Session["LoginId"]);

                    // Get user details from database
                    DataRow userDetails = FormHelper.CoreHelper.GetKullaniciByLoginId(loginId);

                    if (userDetails != null)
                    {
                        // Update session with complete user information
                        Session["Username"] = userDetails["USERNAME"]?.ToString();

                        if (userDetails.Table.Columns.Contains("NAME_SURNAME"))
                            Session["UserFullName"] = userDetails["NAME_SURNAME"]?.ToString();

                        if (userDetails.Table.Columns.Contains("E_MAIL"))
                            Session["UserEmail"] = userDetails["E_MAIL"]?.ToString();

                        if (userDetails.Table.Columns.Contains("DEPT_NAME"))
                            Session["UserDepartment"] = userDetails["DEPT_NAME"]?.ToString();

                        if (userDetails.Table.Columns.Contains("POZISYON"))
                            Session["UserTitle"] = userDetails["POZISYON"]?.ToString();

                        // Update core helper
                        FormHelper.CoreHelper.SetCurrentUser(loginId, Session["Username"].ToString());
                    }
                }
                // If we don't have LoginId but have Windows identity, try to authenticate
                else if (Session["LoginId"] == null)
                {
                    AracTakipSistemi.Authentication.AuthenticationManager.AuthenticateUser(HttpContext.Current);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error verifying user session: {ex.Message}");
            }
        }


        private void InitializeMenuVisibilityForWorkflowRelated()
        {
            mnuAracTalepAdmin.Visible = false;
            mnuEkipmanAdmin.Visible = false;
            mnuKirtasiyeAdmin.Visible = false;

            // Check permissions for each module
            if (YetkiHelper.AdminlikYetkisiVarmi("AracTakipSistemiAdminLogicalId", LoginId))
            {
                mnuAracTalepAdmin.Visible = true;
            }

            if (YetkiHelper.AdminlikYetkisiVarmi("EkipmanTalepFormuTanımlamaYapabilecekler", LoginId))
            {
                mnuEkipmanAdmin.Visible = true;
            }

            if (YetkiHelper.AdminlikYetkisiVarmi("KirtasiyeAdminlikYetkisiLogicalGrupId", LoginId))
            {
                mnuKirtasiyeAdmin.Visible = true;
                linkKirtasiyeUygulamasi.HRef = ConvertionHelper.ConvertValue<bool>(ConfigurationManager.AppSettings["debugMode"])
                    ? "http://kurumsalkirtasiyetest/anasayfa.aspx"
                    : "http://kurumsalkirtasiyesi/anasayfa.aspx";
            }
            if (absolutePath.ToLower().Contains("kurumsalkirtasiye/depotanimlama.aspx") && !mnuKirtasiyeAdmin.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("kurumsalkirtasiye/stokgruptanimlama.aspx") && !mnuKirtasiyeAdmin.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("kurumsalkirtasiye/firmatanimlama.aspx") && !mnuKirtasiyeAdmin.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("kurumsalkirtasiye/stoktanimlama.aspx") && !mnuKirtasiyeAdmin.Visible)
                mustBeRedirectedToMainPage = true;

            if (absolutePath.ToLower().Contains("aractakipsistemi/aracsınıf.aspx") && !mnuAracTalepAdmin.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("aractakipsistemi/aracrenk.aspx") && !mnuAracTalepAdmin.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("aractakipsistemi/aracyıl.aspx") && !mnuAracTalepAdmin.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("aractakipsistemi/aracmarka.aspx") && !mnuAracTalepAdmin.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("aractakipsistemi/aracmodel.aspx") && !mnuAracTalepAdmin.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("aractakipsistemi/aracplaka.aspx") && !mnuAracTalepAdmin.Visible)
                mustBeRedirectedToMainPage = true;

            if (absolutePath.ToLower().Contains("ekipmantalepformu/ytstanimlama.aspx") && !mnuEkipmanAdmin.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("ekipmantalepformu/malzemetanim.aspx") && !mnuEkipmanAdmin.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("ekipmantalepformu/ekipmantalepleriraporu.aspx") && !mnuEkipmanAdmin.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("ekipmantalepformu/excelyukleme.aspx") && !mnuEkipmanAdmin.Visible)
                mustBeRedirectedToMainPage = true;
        }

        /// <summary>
        /// Displays user information including impersonation status if applicable
        /// </summary>
        private void DisplayUserInfo(bool isAdmin)
        {
            try
            {
                // Debug logging to check session values
                System.Diagnostics.Debug.WriteLine("--- Session Values ---");
                System.Diagnostics.Debug.WriteLine($"LoginId: {Session["LoginId"]}");
                System.Diagnostics.Debug.WriteLine($"Username: {Session["Username"]}");
                System.Diagnostics.Debug.WriteLine($"UserFullName: {Session["UserFullName"]}");
                System.Diagnostics.Debug.WriteLine($"IsImpersonating: {Session["IsImpersonating"]}");
                if (Session["IsImpersonating"] != null && (bool)Session["IsImpersonating"])
                {
                    System.Diagnostics.Debug.WriteLine($"OriginalLoginId: {Session["OriginalLoginId"]}");
                    System.Diagnostics.Debug.WriteLine($"OriginalUserName: {Session["OriginalUserName"]}");
                    System.Diagnostics.Debug.WriteLine($"OriginalUserFullName: {Session["OriginalUserFullName"]}");
                }
                System.Diagnostics.Debug.WriteLine("---------------------");

                bool userInfoDisplayed = false;

                // Check for a logged in user with session data
                if (Session["LoginId"] != null)
                {
                    string displayName = null;
                    string displayUsername = null;

                    // First try to get the values directly from session
                    if (Session["UserFullName"] != null)
                    {
                        displayName = Session["UserFullName"].ToString();
                    }

                    if (Session["Username"] != null)
                    {
                        displayUsername = Session["Username"].ToString();
                    }

                    // If session values are empty, try to get from database
                    if (string.IsNullOrEmpty(displayName) || string.IsNullOrEmpty(displayUsername))
                    {
                        decimal loginId = Convert.ToDecimal(Session["LoginId"]);
                        DataRow userDetails = FormHelper.CoreHelper.GetKullaniciByLoginId(loginId);

                        if (userDetails != null)
                        {
                            // Use correct column name NAME_SURNAME
                            if (userDetails.Table.Columns.Contains("NAME_SURNAME"))
                                displayName = userDetails["NAME_SURNAME"]?.ToString();

                            displayUsername = userDetails["USERNAME"]?.ToString();

                            // Update session for future use
                            if (!string.IsNullOrEmpty(displayName))
                                Session["UserFullName"] = displayName;

                            if (!string.IsNullOrEmpty(displayUsername))
                                Session["Username"] = displayUsername;
                        }
                        // If userDetails is null, try to get user info from direct database lookup
                        else
                        {
                            // Try direct database lookup
                            string username = GetWindowsUsername();
                            if (!string.IsNullOrEmpty(username))
                            {
                                string sql = $"SELECT LOGIN_ID, USERNAME, NAME_SURNAME FROM DT_WORKFLOW.VW_USER_INFORMATION WHERE LOGIN_ID = {loginId}";
                                DataTable dtUser = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", sql);

                                if (dtUser != null && dtUser.Rows.Count > 0)
                                {
                                    DataRow row = dtUser.Rows[0];
                                    displayName = row["NAME_SURNAME"]?.ToString();
                                    displayUsername = row["USERNAME"]?.ToString();

                                    // Update session for future use
                                    if (!string.IsNullOrEmpty(displayName))
                                        Session["UserFullName"] = displayName;

                                    if (!string.IsNullOrEmpty(displayUsername))
                                        Session["Username"] = displayUsername;
                                }
                            }
                        }
                    }

                    // Only proceed if we have valid user information
                    if (!string.IsNullOrEmpty(displayName) && !string.IsNullOrEmpty(displayUsername))
                    {
                        // EXPLICIT IMPERSONATION INDICATOR
                        bool isImpersonating = Session["IsImpersonating"] != null && (bool)Session["IsImpersonating"];

                        // Check if current user is the original user (to avoid showing impersonation style)
                        bool isCurrentUserSameAsOriginal = false;
                        if (isImpersonating && Session["OriginalLoginId"] != null && Session["LoginId"] != null)
                        {
                            decimal originalLoginId = Convert.ToDecimal(Session["OriginalLoginId"]);
                            decimal currentLoginId = Convert.ToDecimal(Session["LoginId"]);
                            isCurrentUserSameAsOriginal = (originalLoginId == currentLoginId);
                        }

                        // Build user display string
                        if (isImpersonating && !isCurrentUserSameAsOriginal)
                        {
                            // Show impersonated user
                            kullaniciLabel.Text = $"<span style='color:#4CAF50;'><i class='fas fa-user-secret'></i> {displayName} ({displayUsername})</span>";

                            // Add original user info in tooltip
                            string originalName = Session["OriginalUserFullName"]?.ToString() ?? "Admin";
                            string originalUsername = Session["OriginalUserName"]?.ToString() ?? "";
                            kullaniciLabel.ToolTip = $"Impersonating as {displayName}. Original user: {originalName} ({originalUsername})";
                        }
                        else
                        {
                            // Normal user display (or original user after switching back)
                            kullaniciLabel.Text = $"{displayName} ({displayUsername})";
                            kullaniciLabel.ToolTip = "";

                            // If we switched back to original user, maintain the session flag
                            // but don't show the impersonation styling
                            if (isCurrentUserSameAsOriginal)
                            {
                                // Keep IsImpersonating true to maintain admin privileges
                                Session["IsImpersonating"] = true;
                            }
                        }

                        kullaniciLabel.Visible = true;
                        userInfoDisplayed = true;
                    }
                }

                // If we weren't able to display user info from session/database, try Windows identity
                if (!userInfoDisplayed)
                {
                    // Try to get Windows identity
                    string windowsIdentity = null;
                    try
                    {
                        windowsIdentity = AracTakipSistemi.Authentication.AuthenticationManager.GetWindowsIdentity();
                    }
                    catch { /* Ignore errors when getting Windows identity */ }

                    if (!string.IsNullOrEmpty(windowsIdentity))
                    {
                        // Extract username from Windows identity
                        string username = windowsIdentity.Contains("\\") ?
                            windowsIdentity.Split('\\')[1] : windowsIdentity;

                        kullaniciLabel.Text = username;
                        kullaniciLabel.Visible = true;
                        userInfoDisplayed = true;
                    }
                }

                // Last resort fallback
                if (!userInfoDisplayed)
                {
                    kullaniciLabel.Text = "User";
                    kullaniciLabel.Visible = true;
                }
            }
            catch (Exception ex)
            {
                // Log but don't crash
                System.Diagnostics.Debug.WriteLine($"Error displaying user info: {ex.Message}");

                // As a fallback, show something
                kullaniciLabel.Text = "User";
                kullaniciLabel.Visible = true;
            }
        }

        /// <summary>
        /// Helper method to get Windows username
        /// </summary>
        private string GetWindowsUsername()
        {
            try
            {
                string windowsIdentity = AracTakipSistemi.Authentication.AuthenticationManager.GetWindowsIdentity();
                if (!string.IsNullOrEmpty(windowsIdentity))
                {
                    return windowsIdentity.Contains("\\") ?
                        windowsIdentity.Split('\\')[1] : windowsIdentity;
                }
            }
            catch { /* Ignore errors */ }

            return null;
        }

        /// <summary>
        /// Loads the user dropdown with all system users
        /// </summary>
        private void LoadUserDropdown()
        {
            try
            {
                // Clear existing items
                DrpUserName.Items.Clear();
                DrpUserName.Items.Add(new ListItem(Resources.DigiportAdminResource.SecUserText, "0"));

                // Load system admin users from database
                string SQL = @"Select LOGIN_ID, NAME_SURNAME from DT_WORKFLOW.VW_USER_INFORMATION Order By NAME_SURNAME ASC";
                DataTable dtUsers = DataAccessLayer.DAL.GetDataTable_Oracle("DT_WORKFLOW", SQL);

                // Add section headers for quicker navigation
                DrpUserName.Items.Add(new ListItem("-------------DEVELOPERS----------------", ""));
                DrpUserName.Items.Add(new ListItem("KEREM BAYRAKTAR", "1763"));
                DrpUserName.Items.Add(new ListItem("ZÜLFİYE TEZEMIR", "2001"));
                DrpUserName.Items.Add(new ListItem("UMUT KORKMAZ (TEST)", "253656762"));
                DrpUserName.Items.Add(new ListItem("UMUT KORKMAZ (LIVE)", "268716477"));
                DrpUserName.Items.Add(new ListItem("-------------TESTERS----------------", ""));
                DrpUserName.Items.Add(new ListItem("İDİL ÇETİN", "1739"));
                DrpUserName.Items.Add(new ListItem("TUBA ÇİLİNGİR", "671307"));
                DrpUserName.Items.Add(new ListItem("BEYZA SAYGINÖZ", "1545"));
                DrpUserName.Items.Add(new ListItem("NEFİSE KULA", "1833"));
                DrpUserName.Items.Add(new ListItem("İBRAHİM AYDIN", "1734"));
                DrpUserName.Items.Add(new ListItem("-------------MANAGERS----------------", ""));
                DrpUserName.Items.Add(new ListItem("TUĞÇE DENİZLERKURDU", "1946"));
                DrpUserName.Items.Add(new ListItem("OSMAN ÇAĞATAY DOĞAN", "1561"));
                DrpUserName.Items.Add(new ListItem("GÖKHAN ÖZTÜRK", "1704"));
                DrpUserName.Items.Add(new ListItem("ESEN SAYGIVAR", "1748"));
                DrpUserName.Items.Add(new ListItem("HATİCE MEMİGÜVEN", "2007"));

                // Add divider
                DrpUserName.Items.Add(new ListItem("-------------ALL USERS----------------", ""));

                // Add all other users from database
                foreach (DataRow row in dtUsers.Rows)
                {
                    string nameSurname = row["NAME_SURNAME"].ToString();
                    string loginId = row["LOGIN_ID"].ToString();

                    // Skip if already added in the preset lists
                    if (DrpUserName.Items.FindByValue(loginId) == null)
                    {
                        DrpUserName.Items.Add(new ListItem(nameSurname, loginId));
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but continue
                System.Diagnostics.Debug.WriteLine("Error loading user dropdown: " + ex.Message);
            }
        }

        private void ConfigureMuhaberatAdminVisibility()
        {
            // Configure Muhaberat module visibility
            li_Muhaberat_TurTanimlama.Visible = isAdmin || authorizedMenus.Rows.Cast<DataRow>().ToList().Exists(x => x["NAME"].ToString() == "Muhaberat Tür Tanımlama");
            menuMuhaberat.Visible = li_Muhaberat_TurTanimlama.Visible;
            if (absolutePath.ToLower().Contains("muhaberat/turtanimlama.aspx") && !li_Muhaberat_TurTanimlama.Visible)
                mustBeRedirectedToMainPage = true;
        }
        /// <summary>
        /// Configures TestMate Admin visibility based on user groups
        /// </summary>
        private void ConfigureTestMateAdminVisibility()
        {
            li_TMATE_DomainAdmin.Visible = /*li_TMATE_Status.Visible =*/ isAdmin;

            li_TMATE_Project.Visible = isAdmin || authorizedMenus.Rows.Cast<DataRow>().ToList().Exists(x => x["NAME"].ToString() == "Testmate Proje Tanımlama");
            li_TMATE_ProjectUser.Visible = isAdmin || authorizedMenus.Rows.Cast<DataRow>().ToList().Exists(x => x["NAME"].ToString() == "Testmate Proje Kullanicisi Tanımlama");

            menuTestMate.Visible =
                li_TMATE_DomainAdmin.Visible ||
                li_TMATE_Project.Visible ||
                li_TMATE_ProjectUser.Visible /*||
                li_TMATE_Status.Visible*/;

            if (absolutePath.ToLower().Contains("testmate/tmatedomainsadmins.aspx") && !li_TMATE_DomainAdmin.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("testmate/tmateprojects.aspx") && !li_TMATE_Project.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("testmate/tmateprojectusers.aspx") && !li_TMATE_ProjectUser.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("testmate/tmatestatusentry.aspx") /*&& !li_TMATE_Status.Visible*/)
                mustBeRedirectedToMainPage = true;
        }

        /// <summary>
        /// Configures Digiport Anket module visibility
        /// </summary>
        private void ConfigureDigiportAnketVisibility()
        {
            liDigiportAnketDomainTanimlama.Visible = isAdmin || authorizedMenus.Rows.Cast<DataRow>().ToList().Exists(x => x["NAME"].ToString() == "Digiport Anket Domain Tanımlama");
            liDigiportAnketDomainKullaniciTanimlama.Visible = isAdmin || authorizedMenus.Rows.Cast<DataRow>().ToList().Exists(x => x["NAME"].ToString() == "Digiport Anket Domain Kullanıcı Tanımlama");
            menuDigiportAnket.Visible = liDigiportAnketDomainTanimlama.Visible || liDigiportAnketDomainKullaniciTanimlama.Visible;
            if (absolutePath.ToLower().Contains("digiportanket/domaintanimlama.aspx") && !liDigiportAnketDomainTanimlama.Visible)
                mustBeRedirectedToMainPage = true;
            if (absolutePath.ToLower().Contains("digiportanket/domainkullanicitanimlama.aspx") && !liDigiportAnketDomainKullaniciTanimlama.Visible)
                mustBeRedirectedToMainPage = true;
        }

        /// <summary>
        /// Configures DigiportAdmin module visibility and builds dynamic menu items
        /// </summary>
        private void ConfigureDigiportAdminVisibility()
        {
            try
            {
                List<DataRow> listAuthorizedMenus = authorizedMenus != null ? authorizedMenus.Rows.Cast<DataRow>().Where(x => x["TYPE_NAME"].ToString() != "Diğer").ToList():new List<DataRow>();
                System.Diagnostics.Debug.WriteLine("ConfigureDigiportAdminVisibility: Starting menu configuration");

                // Check if user has admin permission
                if (Session["YetkiTipDigiportAdmin"] == null)
                {
                    Session["YetkiTipDigiportAdmin"] = CoreHelpers.Yetki.yetkiKontrol(
                        YetkiHelper.UserName(), "DIGIPORT_ADMIN_PERMISSION");
                }

                System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: YetkiTipDigiportAdmin={Session["YetkiTipDigiportAdmin"]}");



                // Check if user is a system admin
                bool isSystemAdmin = authService.IsSystemAdmin();
                System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: isSystemAdmin={isSystemAdmin}");

                // Check if user is in TechCorp group
                bool isInTechCorpGroup = kullaniciGruplari.Select(x => x.ToUpper())
                    .Contains(ConfigurationManager.AppSettings["AdGroup_TechCorp"]?.ToUpper());
                System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: isInTechCorpGroup={isInTechCorpGroup}");

                // Set menu visibility based on admin status or permissions
                bool hasDigiportPermission = Session["YetkiTipDigiportAdmin"]?.ToString() == "True";
                bool shouldShowMenu = isSystemAdmin || hasDigiportPermission || isInTechCorpGroup;

                System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: shouldShowMenu={shouldShowMenu}");

                // Only make the menu visible if the user has any Digiport-related permissions

                // Debug the menu items retrieved
                //if (authorizedMenus != null && authorizedMenus.Rows.Count > 0)
                //{
                //    foreach (DataRow row in authorizedMenus.Rows)
                //    {
                //        System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Menu item - ID={row["ID"]}, NAME={row["NAME"]}, TYPE={row["TYPE_NAME"]}");
                //    }
                //}

                // Set menu visibility based on whether the user has any authorized menus
                menuDigiportAdmin.Visible = isSystemAdmin || (listAuthorizedMenus != null && listAuthorizedMenus.Count > 0);

                System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Setting menuDigiportAdmin.Visible = {menuDigiportAdmin.Visible}");

                // Debug: Log all authorized menus
                if (listAuthorizedMenus != null && listAuthorizedMenus.Count > 0)
                {
                    foreach (DataRow row in listAuthorizedMenus)
                    {
                        string menuName = row.Field<string>("NAME");
                        string typeName = row.Field<string>("TYPE_NAME");
                        System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Authorized menu: {typeName} - {menuName}");
                    }
                }

                // Use the container defined in the markup
                Control menuContainer = digiportAdminMenuContainer;

                if (menuContainer != null)
                {
                    System.Diagnostics.Debug.WriteLine("ConfigureDigiportAdminVisibility: Found menu container");

                    // Clear all existing menu items first
                    ClearAllMenuItems(menuContainer);

                    // If the user has no permissions, add a message
                    if (listAuthorizedMenus == null || listAuthorizedMenus.Count == 0)
                    {
                        System.Diagnostics.Debug.WriteLine("ConfigureDigiportAdminVisibility: No authorized menus, adding message");

                        // For system admins, we should always have menu items
                        if (isSystemAdmin)
                        {
                            System.Diagnostics.Debug.WriteLine("ConfigureDigiportAdminVisibility: WARNING - System admin has no authorized menus!");

                            // Log all menu items to help diagnose the issue
                            FormHelper.DigiportAdmin.DigiportAuthorizationService.Instance.LogAllMenuItems();

                            // Force DigiportAuthorizationService to clear its cache and try again
                            FormHelper.DigiportAdmin.DigiportAuthorizationService.Instance.ClearCache();

                            if (listAuthorizedMenus == null || listAuthorizedMenus.Count == 0)
                            {
                                System.Diagnostics.Debug.WriteLine("ConfigureDigiportAdminVisibility: Still no menus after cache clear, adding admin-only pages directly");
                                // Add admin-only pages directly
                                AddMenuItem(menuContainer, Resources.DigiportAdminResource.TurTanimlama, "/AdminPages/DigiportAdmin/Types.aspx");
                                AddMenuItem(menuContainer, Resources.DigiportAdminResource.IsimTanimlama, "/AdminPages/DigiportAdmin/Names.aspx");
                                AddMenuItem(menuContainer, Resources.DigiportAdminResource.KullaniciAtama, "/AdminPages/DigiportAdmin/UserAssignment.aspx");

                                // Also add banner items directly
                                AddMenuItem(menuContainer, Resources.DigiportAdminResource.UstBanner, "/AdminPages/DigiportAdmin/Pages/MainTopBanner.aspx");
                                AddMenuItem(menuContainer, Resources.DigiportAdminResource.SolGaleri, "/AdminPages/DigiportAdmin/Pages/MainSubBanner.aspx");
                                AddMenuItem(menuContainer, Resources.DigiportAdminResource.SagGaleri, "/AdminPages/DigiportAdmin/Pages/MainSubBanner.aspx");
                                AddMenuItem(menuContainer, Resources.DigiportAdminResource.IKMedyaDuyuru, "/AdminPages/DigiportAdmin/Pages/HrMediaAnnouncementSlides.aspx");
                                return;
                            }

                            System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Retrieved {listAuthorizedMenus.Count} menu items after cache clear");
                        }
                        else
                        {
                            AddMessageItem(menuContainer, Resources.DigiportAdminResource.AccessDeniedMessage);
                            return;
                        }
                    }

                    // Group menu items by type
                    var menuGroups = listAuthorizedMenus.AsEnumerable()
                        .GroupBy(row => row.Field<string>("TYPE_NAME"))
                        .OrderBy(g => g.Key);

                    System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Found {menuGroups.Count()} menu groups");

                    // Add menu items for each group
                    foreach (var group in menuGroups)
                    {
                        List<KeyValuePair<string, HtmlGenericControl>> listBannerLi = new List<KeyValuePair<string, HtmlGenericControl>>();
                        List<KeyValuePair<string, HtmlGenericControl>> listGridLi = new List<KeyValuePair<string, HtmlGenericControl>>();
                        // Skip empty groups
                        if (!group.Any())
                        {
                            System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Skipping empty group");
                            continue;
                        }

                        // Create submenu for the group if it doesn't exist
                        string typeName = group.Key;
                        if (typeName == "Diğer")
                            continue;
                        System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Processing group {typeName}");

                        // Special handling for banner items
                        if (typeName == "Slider")
                        {
                            // Get all items in this group - we want to show all Slider items as banners
                            var bannerItems = group.Where(r =>
                                !string.IsNullOrEmpty(r.Field<string>("PAGE_PATH"))).ToList();

                            System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Found {bannerItems.Count} banner items in group {typeName}");

                            if (bannerItems.Any())
                            {
                                System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Found {bannerItems.Count} banner items");

                                // Create a special parent menu for banner items
                                HtmlGenericControl bannerLi = new HtmlGenericControl("li");
                                bannerLi.Attributes["class"] = "dropdown-submenu";

                                HtmlAnchor bannerAnchor = new HtmlAnchor();
                                bannerAnchor.HRef = "#";

                                // Use resource for the text
                                Literal bannerText = new Literal();
                                bannerText.Text = Resources.DigiportAdminResource.DigiportBanners;
                                bannerAnchor.Controls.Add(bannerText);

                                bannerLi.Controls.Add(bannerAnchor);

                                HtmlGenericControl bannerUl = new HtmlGenericControl("ul");
                                bannerUl.Attributes["class"] = "dropdown-menu";
                                bannerLi.Controls.Add(bannerUl);

                                // For system admins, add all banner items
                                if (isSystemAdmin)
                                {
                                    System.Diagnostics.Debug.WriteLine("ConfigureDigiportAdminVisibility: User is system admin, adding all banner items");

                                    // Add all banner items
                                    foreach (var bannerItem in bannerItems)
                                    {
                                        try
                                        {
                                            string pagePath = bannerItem.Field<string>("PAGE_PATH");
                                            if (string.IsNullOrEmpty(pagePath))
                                            {
                                                System.Diagnostics.Debug.WriteLine("ConfigureDigiportAdminVisibility: Skipping banner item with empty page path");
                                                continue;
                                            }

                                            string menuName = bannerItem.Field<string>("NAME");
                                            int menuId = Convert.ToInt32(bannerItem.Field<decimal>("ID"));

                                            // Determine the display name based on the menu name
                                            string submenuName;
                                            if (menuName.Contains("Sol"))
                                                submenuName = Resources.DigiportAdminResource.SolGaleri;
                                            else if (menuName.Contains("Sağ"))
                                                submenuName = Resources.DigiportAdminResource.SagGaleri;
                                            else if (menuName.Contains("Üst"))
                                                submenuName = Resources.DigiportAdminResource.UstBanner;
                                            else if (menuName.Contains("İç İletişim"))
                                                submenuName = Resources.DigiportAdminResource.IKMedyaDuyuru;
                                            else if (menuName.Contains("Ajans"))
                                                submenuName = Resources.DigiportAdminResource.AjansDuyuru;
                                            else if (menuName.Contains("Bülten"))
                                                submenuName = Resources.DigiportAdminResource.EgitimDuyuru;
                                            else
                                                submenuName = menuName; // Fallback to the original name

                                            System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Adding banner item {submenuName} (ID={menuId}) with path {pagePath}");

                                            string url = AddMidParameter(pagePath, menuId);
                                            System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Generated URL: {url}");

                                            HtmlGenericControl itemLi = new HtmlGenericControl("li");
                                            HtmlAnchor itemAnchor = new HtmlAnchor();
                                            itemAnchor.HRef = url;
                                            Literal itemText = new Literal();
                                            itemText.Text = submenuName;
                                            itemAnchor.Controls.Add(itemText);
                                            itemLi.Controls.Add(itemAnchor);
                                            //bannerUl.Controls.Add(itemLi);
                                            listBannerLi.Add(new KeyValuePair<string, HtmlGenericControl>(submenuName, itemLi));

                                            System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Added banner item {submenuName} for admin (ID={menuId})");
                                        }
                                        catch (Exception ex)
                                        {
                                            System.Diagnostics.Debug.WriteLine($"Error adding banner item: {ex.Message}");
                                        }
                                    }

                                    foreach (var item in listBannerLi.OrderBy(x => x.Key))
                                    {
                                        bannerUl.Controls.Add(item.Value);
                                    }

                                    // Add the banner submenu to the menu container
                                    menuContainer.Controls.Add(bannerLi);
                                }
                                else
                                {
                                    // Flag to track if we added any items
                                    bool addedAnyBannerItems = false;

                                    // Add only the banner items the user has permission for
                                    foreach (var row in bannerItems)
                                    {
                                        try
                                        {
                                            string pagePath = row.Field<string>("PAGE_PATH");
                                            if (string.IsNullOrEmpty(pagePath))
                                            {
                                                System.Diagnostics.Debug.WriteLine("ConfigureDigiportAdminVisibility: Skipping banner item with empty page path");
                                                continue;
                                            }

                                            string menuName = row.Field<string>("NAME");
                                            int menuId = Convert.ToInt32(row.Field<decimal>("ID"));

                                            // Determine the display name based on the menu name
                                            string submenuName;
                                            if (menuName.Contains("Sol"))
                                                submenuName = Resources.DigiportAdminResource.SolGaleri;
                                            else if (menuName.Contains("Sağ"))
                                                submenuName = Resources.DigiportAdminResource.SagGaleri;
                                            else if (menuName.Contains("Üst"))
                                                submenuName = Resources.DigiportAdminResource.UstBanner;
                                            else if (menuName.Contains("İç İletişim"))
                                                submenuName = Resources.DigiportAdminResource.IKMedyaDuyuru;
                                            else if (menuName.Contains("Ajans"))
                                                submenuName = Resources.DigiportAdminResource.AjansDuyuru;
                                            else if (menuName.Contains("Bülten"))
                                                submenuName = Resources.DigiportAdminResource.EgitimDuyuru;
                                            else
                                                submenuName = menuName; // Fallback to the original name

                                            System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Adding banner item {submenuName} (ID={menuId}) with path {pagePath}");

                                            // Add query string parameter using helper method to avoid duplicates
                                            string url = AddMidParameter(pagePath, menuId);
                                            System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Generated URL: {url}");

                                            // Create menu item
                                            HtmlGenericControl itemLi = new HtmlGenericControl("li");

                                            HtmlAnchor itemAnchor = new HtmlAnchor();
                                            itemAnchor.HRef = url;

                                            // Use resource for the text
                                            Literal itemText = new Literal();
                                            itemText.Text = submenuName;
                                            itemAnchor.Controls.Add(itemText);

                                            itemLi.Controls.Add(itemAnchor);
                                            //bannerUl.Controls.Add(itemLi);
                                            listBannerLi.Add(new KeyValuePair<string, HtmlGenericControl>(submenuName, itemLi));

                                            addedAnyBannerItems = true;
                                            System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Added banner item {submenuName} for regular user (ID={menuId})");
                                        }
                                        catch (Exception ex)
                                        {
                                            System.Diagnostics.Debug.WriteLine($"Error adding banner item for regular user: {ex.Message}");
                                        }
                                    }

                                    foreach (var item in listBannerLi.OrderBy(x => x.Key))
                                    {
                                        bannerUl.Controls.Add(item.Value);
                                    }

                                    // Only add the banner submenu if we added any items
                                    if (addedAnyBannerItems)
                                    {
                                        menuContainer.Controls.Add(bannerLi);
                                    }
                                }

                                // Skip these items in the regular processing
                                continue;
                            }
                        }
                        else if (typeName == "Grid")
                        {
                            var gridItems = group.Where(r =>
                                !string.IsNullOrEmpty(r.Field<string>("PAGE_PATH"))).ToList();

                            if (gridItems.Any())
                            {
                                // Create a special parent menu for grid items
                                HtmlGenericControl gridLi = new HtmlGenericControl("li");
                                gridLi.Attributes["class"] = "dropdown-submenu";

                                HtmlAnchor gridAnchor = new HtmlAnchor();
                                gridAnchor.HRef = "#";

                                // Use resource for the text
                                Literal gridText = new Literal();
                                gridText.Text = Resources.DigiportAdminResource.DigiportGrids;
                                gridAnchor.Controls.Add(gridText);

                                gridLi.Controls.Add(gridAnchor);

                                HtmlGenericControl gridUl = new HtmlGenericControl("ul");
                                gridUl.Attributes["class"] = "dropdown-menu";
                                gridLi.Controls.Add(gridUl);

                                // For system admins, add all grid items
                                if (isSystemAdmin)
                                {
                                    // Add all grid items
                                    foreach (var gridItem in gridItems)
                                    {
                                        try
                                        {
                                            string pagePath = gridItem.Field<string>("PAGE_PATH");
                                            if (string.IsNullOrEmpty(pagePath))
                                            {
                                                System.Diagnostics.Debug.WriteLine("ConfigureDigiportAdminVisibility: Skipping grid item with empty page path");
                                                continue;
                                            }

                                            string menuName = gridItem.Field<string>("NAME");
                                            int menuId = Convert.ToInt32(gridItem.Field<decimal>("ID"));

                                            // Determine the display name based on the menu name
                                            string submenuName;
                                            if (menuName.Contains("İndirim"))
                                                submenuName = Resources.DigiportAdminResource.DigiportIndirimFirsati;
                                            else if (menuName.Contains("Linkler"))
                                                submenuName = Resources.DigiportAdminResource.DigiportLinkler;
                                            else if (menuName.Contains("Sol Alt"))
                                                submenuName = Resources.DigiportAdminResource.AnasayfaSolAltMenu;
                                            else if (menuName.Contains("Sol Üst"))
                                                submenuName = Resources.DigiportAdminResource.AnasayfaSolUstMenu;
                                            else if (menuName.Contains("Anasayfa") && menuName.Contains("Bunları Biliyor"))
                                                submenuName = Resources.DigiportAdminResource.DuyuruBunlarıBiliyormuydunuz;
                                            else if (menuName.Contains("Anasayfa") && menuName.Contains("Yardım Masası"))
                                                submenuName = Resources.DigiportAdminResource.DuyuruYardimMasasi;
                                            else
                                                submenuName = menuName; // Fallback to the original name


                                            string url = AddMidParameter(pagePath, menuId);

                                            HtmlGenericControl itemLi = new HtmlGenericControl("li");
                                            HtmlAnchor itemAnchor = new HtmlAnchor();
                                            itemAnchor.HRef = url;
                                            Literal itemText = new Literal();
                                            itemText.Text = submenuName;
                                            itemAnchor.Controls.Add(itemText);
                                            itemLi.Controls.Add(itemAnchor);
                                            //gridUl.Controls.Add(itemLi);
                                            listGridLi.Add(new KeyValuePair<string, HtmlGenericControl>(submenuName, itemLi));
                                        }
                                        catch (Exception ex)
                                        {
                                        }
                                    }
                                    foreach (var item in listGridLi.OrderBy(x => x.Key))
                                    {
                                        gridUl.Controls.Add(item.Value);
                                    }

                                    // Add the grid submenu to the menu container
                                    menuContainer.Controls.Add(gridLi);
                                }
                                else
                                {
                                    // Flag to track if we added any items
                                    bool addedAnyGridItems = false;

                                    // Add only the grid items the user has permission for
                                    foreach (var row in gridItems)
                                    {
                                        try
                                        {
                                            string pagePath = row.Field<string>("PAGE_PATH");
                                            if (string.IsNullOrEmpty(pagePath))
                                            {
                                                continue;
                                            }

                                            string menuName = row.Field<string>("NAME");
                                            int menuId = Convert.ToInt32(row.Field<decimal>("ID"));

                                            // Determine the display name based on the menu name
                                            string submenuName;
                                            if (menuName.Contains("İndirim"))
                                                submenuName = Resources.DigiportAdminResource.DigiportIndirimFirsati;
                                            else if (menuName.Contains("Linkler"))
                                                submenuName = Resources.DigiportAdminResource.DigiportLinkler;
                                            else if (menuName.Contains("Sol Alt"))
                                                submenuName = Resources.DigiportAdminResource.AnasayfaSolAltMenu;
                                            else if (menuName.Contains("Sol Üst"))
                                                submenuName = Resources.DigiportAdminResource.AnasayfaSolUstMenu;
                                            else if (menuName.Contains("Anasayfa") && menuName.Contains("Bunları Biliyor"))
                                                submenuName = Resources.DigiportAdminResource.DuyuruBunlarıBiliyormuydunuz;
                                            else if (menuName.Contains("Anasayfa") && menuName.Contains("Yardım Masası"))
                                                submenuName = Resources.DigiportAdminResource.DuyuruYardimMasasi;
                                            else
                                                submenuName = menuName; // Fallback to the original name


                                            // Add query string parameter using helper method to avoid duplicates
                                            string url = AddMidParameter(pagePath, menuId);

                                            // Create menu item
                                            HtmlGenericControl itemLi = new HtmlGenericControl("li");

                                            HtmlAnchor itemAnchor = new HtmlAnchor();
                                            itemAnchor.HRef = url;

                                            // Use resource for the text
                                            Literal itemText = new Literal();
                                            itemText.Text = submenuName;
                                            itemAnchor.Controls.Add(itemText);

                                            itemLi.Controls.Add(itemAnchor);
                                            //gridUl.Controls.Add(itemLi);
                                            listGridLi.Add(new KeyValuePair<string, HtmlGenericControl>(submenuName, itemLi));

                                            addedAnyGridItems = true;
                                        }
                                        catch (Exception ex)
                                        {
                                        }
                                    }
                                    foreach (var item in listGridLi.OrderBy(x => x.Key))
                                    {
                                        gridUl.Controls.Add(item.Value);
                                    }

                                    // Only add the grid submenu if we added any items
                                    if (addedAnyGridItems)
                                    {
                                        menuContainer.Controls.Add(gridLi);
                                    }
                                }

                                // Skip these items in the regular processing
                                continue;
                            }
                        }

                        Control submenu = FindOrCreateSubmenu(menuContainer, typeName);

                        // Add menu items to the submenu
                        foreach (var row in group)
                        {
                            // Skip if no page path is defined
                            string pagePath = row.Field<string>("PAGE_PATH");
                            if (string.IsNullOrEmpty(pagePath))
                            {
                                System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Skipping item with no page path");
                                continue;
                            }

                            // Skip items from the Slider type as they're handled separately
                            string rowTypeName = row.Field<string>("TYPE_NAME");
                            if (rowTypeName == "Slider")
                                continue;
                            if (rowTypeName == "Grid")
                                continue;

                            // Get the menu name for display
                            string menuName = row.Field<string>("NAME");

                            int menuId = Convert.ToInt32(row.Field<decimal>("ID"));

                            System.Diagnostics.Debug.WriteLine($"ConfigureDigiportAdminVisibility: Adding menu item {menuName} (ID={menuId})");

                            // Add query string parameter using helper method to avoid duplicates
                            string url = AddMidParameter(pagePath, menuId);

                            // Create and add the menu item
                            AddMenuItem(submenu, menuName, url);
                        }
                    }

                    // Add admin-only pages if user is system admin
                    if (isSystemAdmin)
                    {
                        System.Diagnostics.Debug.WriteLine("ConfigureDigiportAdminVisibility: Adding admin-only pages");

                        // Add admin pages directly to the main menu container
                        AddMenuItem(menuContainer, Resources.DigiportAdminResource.TurTanimlama, "/AdminPages/DigiportAdmin/Types.aspx");
                        AddMenuItem(menuContainer, Resources.DigiportAdminResource.IsimTanimlama, "/AdminPages/DigiportAdmin/Names.aspx");
                        AddMenuItem(menuContainer, Resources.DigiportAdminResource.KullaniciAtama, "/AdminPages/DigiportAdmin/UserAssignment.aspx");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ConfigureDigiportAdminVisibility: Could not find menu container");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ConfigureDigiportAdminVisibility: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error details: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Clears all menu items from the menu container
        /// </summary>
        private void ClearAllMenuItems(Control menuContainer)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("ClearAllMenuItems: Clearing all menu items");
                menuContainer.Controls.Clear();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ClearAllMenuItems: {ex.Message}");
            }
        }

        /// <summary>
        /// Adds a message item to the menu container
        /// </summary>
        private void AddMessageItem(Control menuContainer, string message)
        {
            try
            {
                HtmlGenericControl li = new HtmlGenericControl("li");
                li.InnerText = message;
                menuContainer.Controls.Add(li);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in AddMessageItem: {ex.Message}");
            }
        }

        /// <summary>
        /// Adds admin-only pages to the menu container
        /// </summary>
        private void AddAdminOnlyPages(Control menuContainer)
        {
            try
            {
                // Create submenu for admin pages
                Control submenu = FindOrCreateSubmenu(menuContainer, Resources.DigiportAdminResource.AdminPages);

                // Add admin pages
                AddMenuItem(submenu, Resources.DigiportAdminResource.TurTanimlama, "~/AdminPages/DigiportAdmin/Types.aspx");
                AddMenuItem(submenu, Resources.DigiportAdminResource.IsimTanimlama, "~/AdminPages/DigiportAdmin/Names.aspx");
                AddMenuItem(submenu, Resources.DigiportAdminResource.KullaniciAtama, "~/AdminPages/DigiportAdmin/UserAssignment.aspx");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in AddAdminOnlyPages: {ex.Message}");
            }
        }

        /// <summary>
        /// Finds a menu item by its text
        /// </summary>
        private Control FindMenuItemByText(Control container, string text)
        {
            try
            {
                if (container == null)
                    return null;

                foreach (Control child in container.Controls)
                {
                    if (child is HtmlGenericControl && ((HtmlGenericControl)child).TagName.Equals("li", StringComparison.OrdinalIgnoreCase))
                    {
                        HtmlAnchor anchor = FindFirstAnchorSimple(child);
                        if (anchor != null && anchor.InnerText.Trim() == text)
                        {
                            return child;
                        }
                    }

                    Control found = FindMenuItemByText(child, text);
                    if (found != null)
                        return found;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in FindMenuItemByText: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Finds the first HtmlAnchor control in a container
        /// </summary>
        private HtmlAnchor FindFirstAnchor(Control container)
        {
            try
            {
                if (container == null)
                    return null;

                foreach (Control child in container.Controls)
                {
                    if (child is HtmlAnchor)
                        return (HtmlAnchor)child;

                    HtmlAnchor found = FindFirstAnchor(child);
                    if (found != null)
                        return found;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in FindFirstAnchor: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Finds the dropdown-menu container in a menu panel
        /// </summary>
        private Control FindMenuContainer(Control menuPanel)
        {
            foreach (Control child in menuPanel.Controls)
            {
                if (child is HtmlGenericControl && ((HtmlGenericControl)child).TagName.Equals("ul", StringComparison.OrdinalIgnoreCase) &&
                    ((HtmlGenericControl)child).Attributes["class"] != null &&
                    ((HtmlGenericControl)child).Attributes["class"].Contains("dropdown-menu"))
                {
                    return child;
                }

                Control container = FindMenuContainer(child);
                if (container != null)
                    return container;
            }

            return null;
        }

        /// <summary>
        /// Clears non-admin menu items from the menu container
        /// </summary>
        private void ClearNonAdminMenuItems(Control menuContainer)
        {
            // Create a list of controls to remove
            List<Control> controlsToRemove = new List<Control>();

            foreach (Control child in menuContainer.Controls)
            {
                // Skip admin-only menu items
                if (child is HtmlGenericControl && ((HtmlGenericControl)child).TagName.Equals("li", StringComparison.OrdinalIgnoreCase))
                {
                    HtmlAnchor anchor = FindFirstAnchorSimple(child);
                    if (anchor != null)
                    {
                        string href = anchor.HRef.ToLower();
                        if (href.Contains("types.aspx") || href.Contains("names.aspx") || href.Contains("userassignment.aspx"))
                        {
                            continue;
                        }
                    }

                    controlsToRemove.Add(child);
                }
            }

            // Remove the controls
            foreach (Control control in controlsToRemove)
            {
                menuContainer.Controls.Remove(control);
            }
        }

        /// <summary>
        /// Finds the first anchor element in a control (simplified version)
        /// </summary>
        private HtmlAnchor FindFirstAnchorSimple(Control control)
        {
            if (control is HtmlAnchor)
                return (HtmlAnchor)control;

            foreach (Control child in control.Controls)
            {
                HtmlAnchor anchor = FindFirstAnchorSimple(child);
                if (anchor != null)
                    return anchor;
            }

            return null;
        }

        /// <summary>
        /// Finds or creates a submenu for a menu type
        /// </summary>
        private Control FindOrCreateSubmenu(Control menuContainer, string typeName)
        {
            // Look for existing submenu
            foreach (Control child in menuContainer.Controls)
            {
                if (child is HtmlGenericControl && ((HtmlGenericControl)child).TagName.Equals("li", StringComparison.OrdinalIgnoreCase))
                {
                    HtmlAnchor anchor = FindFirstAnchorSimple(child);
                    if (anchor != null && anchor.InnerText.Trim() == typeName)
                    {
                        // Find the dropdown-menu container
                        foreach (Control subchild in child.Controls)
                        {
                            if (subchild is HtmlGenericControl && ((HtmlGenericControl)subchild).TagName.Equals("ul", StringComparison.OrdinalIgnoreCase) &&
                                ((HtmlGenericControl)subchild).Attributes["class"] != null &&
                                ((HtmlGenericControl)subchild).Attributes["class"].Contains("dropdown-menu"))
                            {
                                return subchild;
                            }
                        }
                    }
                }
            }

            // Create new submenu
            HtmlGenericControl li = new HtmlGenericControl("li");
            li.Attributes["class"] = "dropdown-submenu";

            HtmlAnchor a = new HtmlAnchor();
            a.HRef = "#";
            a.InnerText = typeName;
            li.Controls.Add(a);

            HtmlGenericControl ul = new HtmlGenericControl("ul");
            ul.Attributes["class"] = "dropdown-menu";
            li.Controls.Add(ul);

            menuContainer.Controls.Add(li);

            return ul;
        }

        /// <summary>
        /// Adds a menu item to a submenu
        /// </summary>
        private void AddMenuItem(Control submenu, string menuName, string url)
        {
            HtmlGenericControl li = new HtmlGenericControl("li");

            HtmlAnchor a = new HtmlAnchor();
            a.HRef = url;
            a.InnerText = menuName;
            li.Controls.Add(a);

            submenu.Controls.Add(li);
        }

        /// <summary>
        /// Gets a localized resource string
        /// </summary>
        private object GetLocalResourceObject(string className, string resourceKey)
        {
            try
            {
                return GetLocalResourceObject($"{className}.{resourceKey}");
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Adds MID parameter to URL without duplication
        /// </summary>
        private string AddMidParameter(string url, int menuId)
        {
            // Check for null or empty URL
            if (string.IsNullOrEmpty(url))
            {
                System.Diagnostics.Debug.WriteLine("AddMidParameter: URL is null or empty, returning default URL with MID");
                return $"/Default.aspx?MID={menuId}";
            }

            try
            {
                // Parse the URL to handle query string properly
                Uri baseUri;

                // If URL doesn't have a scheme, add a dummy one for parsing
                if (!url.Contains("://"))
                {
                    baseUri = new Uri("http://dummy.com" + (url.StartsWith("/") ? url : "/" + url));
                }
                else
                {
                    baseUri = new Uri(url);
                }

                // Create a collection from existing query parameters
                var queryParams = System.Web.HttpUtility.ParseQueryString(baseUri.Query);

                // Remove any existing MID parameter
                queryParams.Remove("MID");

                // Add the new MID parameter
                queryParams.Add("MID", menuId.ToString());

                // Rebuild the URL
                string path;
                if (url.Contains("://"))
                {
                    path = baseUri.GetLeftPart(UriPartial.Path);
                }
                else
                {
                    // Handle the case where Query might be empty
                    if (string.IsNullOrEmpty(baseUri.Query))
                    {
                        path = baseUri.PathAndQuery;
                    }
                    else
                    {
                        path = baseUri.PathAndQuery.Replace(baseUri.Query, "");
                    }
                }

                // Remove the dummy part if we added it
                if (!url.Contains("://") && path.StartsWith("http://dummy.com"))
                {
                    path = path.Substring("http://dummy.com".Length);
                }

                // Add the query string if we have parameters
                if (queryParams.Count > 0)
                {
                    path += "?" + queryParams.ToString();
                }

                return path;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in AddMidParameter: {ex.Message}");
                // Return a safe default URL with the MID parameter
                return $"/Default.aspx?MID={menuId}";
            }
        }

        /// <summary>
        /// Handles the user switching functionality
        /// </summary>
        protected void TestButton_Click(object sender, EventArgs e)
        {
            string selectedValue = DrpUserName.SelectedValue;

            // Proceed only if a valid user is selected
            if (selectedValue != "0" && !string.IsNullOrEmpty(selectedValue))
            {
                try
                {
                    decimal loginId = Convert.ToDecimal(selectedValue);

                    // Save original user information for reference
                    string originalWindowsUsername = GetWindowsUsername();
                    decimal originalLoginId = 0;
                    string originalUsername = null;
                    string originalUserFullName = null;

                    // If this is our first impersonation, save the original user info
                    if (Session["IsImpersonating"] == null || !(bool)Session["IsImpersonating"])
                    {
                        if (Session["LoginId"] != null)
                            originalLoginId = Convert.ToDecimal(Session["LoginId"]);

                        originalUsername = Session["Username"]?.ToString();
                        originalUserFullName = Session["UserFullName"]?.ToString();

                        // If we don't have original info in session, try to get from database
                        if (originalLoginId == 0 && !string.IsNullOrEmpty(originalWindowsUsername))
                        {
                            originalLoginId = FormHelper.CoreHelper.GetKullaniciLoginIdDecimal(originalWindowsUsername);

                            if (originalLoginId > 0)
                            {
                                DataRow originalUserDetails = FormHelper.CoreHelper.GetKullaniciByLoginId(originalLoginId);
                                if (originalUserDetails != null)
                                {
                                    originalUsername = originalUserDetails["USERNAME"]?.ToString();

                                    if (originalUserDetails.Table.Columns.Contains("NAME_SURNAME"))
                                        originalUserFullName = originalUserDetails["NAME_SURNAME"]?.ToString();
                                }
                            }
                        }
                    }
                    else
                    {
                        // We're already impersonating, get original info from session
                        if (Session["OriginalLoginId"] != null)
                            originalLoginId = Convert.ToDecimal(Session["OriginalLoginId"]);

                        originalUsername = Session["OriginalUserName"]?.ToString();
                        originalUserFullName = Session["OriginalUserFullName"]?.ToString();
                    }

                    // Debug logging
                    System.Diagnostics.Debug.WriteLine($"IMPERSONATION: Switching to user with LoginId {loginId}");
                    System.Diagnostics.Debug.WriteLine($"IMPERSONATION: Original user: {originalUserFullName} ({originalUsername})");

                    // DIRECT METHOD: Instead of redirecting with query params, directly authenticate
                    // Clear all session variables first
                    Session.Clear();

                    // Store original user information
                    Session["OriginalLoginId"] = originalLoginId;
                    Session["OriginalUserName"] = originalUsername;
                    Session["OriginalUserFullName"] = originalUserFullName;
                    Session["OriginalWindowsUsername"] = originalWindowsUsername;
                    Session["IsImpersonating"] = true;

                    // Directly set LoginId for impersonation
                    Session["LoginId"] = loginId;

                    // Force DigiportAuthorizationService to clear its cache
                    FormHelper.DigiportAdmin.DigiportAuthorizationService.Instance.ClearCache();
                    System.Diagnostics.Debug.WriteLine("IMPERSONATION: Cleared DigiportAuthorizationService cache");

                    // Directly call the authentication method
                    bool authResult = AracTakipSistemi.Authentication.AuthenticationManager.AuthenticateUser(HttpContext.Current);

                    if (authResult)
                    {
                        System.Diagnostics.Debug.WriteLine("IMPERSONATION: Authentication successful");

                        // Force a complete refresh of the page with cache control
                        Response.Cache.SetCacheability(HttpCacheability.NoCache);
                        Response.Cache.SetNoStore();
                        Response.Cache.SetExpires(DateTime.MinValue);

                        // Refresh the current page
                        Response.Redirect(Request.RawUrl, true);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("IMPERSONATION: Authentication failed");
                        PopupGoster(Resources.DigiportAdminResource.MSG_ERROR, Resources.DigiportAdminResource.UserSwitchFailed, true);
                    }

                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"IMPERSONATION ERROR: {ex.Message}");
                    PopupGoster(Resources.DigiportAdminResource.MSG_ERROR, string.Format(Resources.DigiportAdminResource.UserSwitchError, ex.Message), true);
                }
            }
            else
            {
                // Show error message if no user selected
                PopupGoster(Resources.DigiportAdminResource.MSG_ERROR, Resources.DigiportAdminResource.UserSelectRequired, true);
            }
        }

        /// <summary>
        /// Popup menü çıkarır
        /// </summary>
        public void PopupGoster(string Baslik, string Mesaj, bool Hata, bool LinkGoster, string Link, string LinkYazi)
        {
            MsgBoxCtrl1.PopupGoster(Baslik, Mesaj, Hata, LinkGoster, Link, LinkYazi);
        }

        /// <summary>
        /// Pupup menü çıkarır (Linksiz)
        /// </summary>
        public void PopupGoster(string Baslik, string Mesaj, bool Hata)
        {
            MsgBoxCtrl1.PopupGoster(Baslik, Mesaj, Hata);
        }

        protected void Unnamed_LoggingOut(object sender, LoginCancelEventArgs e)
        {
            // Clear impersonation when logging out
            Session["IsImpersonating"] = null;
            Session["OriginalLoginId"] = null;
            Session["OriginalUserName"] = null;
            Session["OriginalUserFullName"] = null;
        }

        protected void ScriptManager1_AsyncPostBackError(object sender, AsyncPostBackErrorEventArgs e)
        {
            PopupGoster(Resources.DigiportAdminResource.MSG_ERROR, e.Exception.Message, true);
        }

        protected void BtnStart_Click(object sender, EventArgs e)
        {
            // Method intentionally left empty
        }

        public string UyariMesaji;

        protected void drpDilSecimi_SelectedIndexChanged(object sender, EventArgs e)
        {
            string secilenDil = drpDilSecimi.SelectedItem.Text;
            Session["AdminYonetimSistemiSecilenDil" + Session.SessionID] = secilenDil;

            // Set the culture based on language selection
            System.Threading.Thread.CurrentThread.CurrentCulture =
                new System.Globalization.CultureInfo(secilenDil == "English" ? "en-US" : "tr-TR");
            System.Threading.Thread.CurrentThread.CurrentUICulture =
                new System.Globalization.CultureInfo(secilenDil == "English" ? "en-US" : "tr-TR");

            Page.Response.Redirect(Page.Request.Url.ToString(), true);
        }
    }
}