﻿using System;

namespace AdminUserCtrl
{
    public partial class AdminKaydet : System.Web.UI.UserControl
    {
        protected void Page_Load(object sender, EventArgs e)
        {
        }

        #region property atamaları

        private bool _GuncelleYetkisi = true;

        /// <summary>
        /// Ekran Güncellenmek üzere açıldığında
        /// Güncelle butonunun gözükmesini yönetir.
        /// </summary>
        public bool GuncelleYetkisi
        {
            get { return _GuncelleYetkisi; }

            set
            {
                _GuncelleYetkisi = value;
                if (value == false)
                {
                    btnSil.Enabled = false;
                    btnSil.Style["display"] = "none";
                }
                else
                {
                    btnSil.Enabled = true;
                    btnSil.Style["display"] = "";
                }
            }
        }

        private bool _SilmeYetkisi = true;

        /// <summary>
        /// Ekran Güncellenmek üzere açıldığında
        /// Sil butonunun gözükmesini yönetir.
        /// </summary>
        public bool SilmeYetkisi
        {
            get { return _SilmeYetkisi; }

            set
            {
                _SilmeYetkisi = value;
                if (value == false)
                {
                    btnSil.Enabled = false;
                    btnSil.Style["display"] = "none";
                }
                else
                {
                    btnSil.Enabled = true;
                    btnSil.Style["display"] = "";
                }
            }
        }

        public enum EkranModuEnum
        {
            Kaydet,
            Guncelle
        }

        private EkranModuEnum _Ekranmodu;

        /// <summary>
        /// Açılan moda göre düğmeleri gösterir / gizler
        /// </summary>
        public EkranModuEnum Ekranmodu
        {
            get { return _Ekranmodu; }

            set
            {
                _Ekranmodu = value;
                if (value == EkranModuEnum.Guncelle)
                {
                    btnKaydet.Enabled = false;
                    btnKaydet.Style["display"] = "none";
                    if (_GuncelleYetkisi)
                    {
                        btnGuncelle.Enabled = true;
                        btnGuncelle.Style["display"] = "";
                    }
                    else
                    {
                        btnGuncelle.Enabled = false;
                        btnGuncelle.Style["display"] = "none";
                    }

                    if (_SilmeYetkisi)
                    {
                        btnSil.Enabled = true;
                        btnSil.Style["display"] = "";
                    }
                    else
                    {
                        btnSil.Enabled = false;
                        btnSil.Style["display"] = "none";
                    }
                }
                else if (value == EkranModuEnum.Kaydet)
                {
                    btnKaydet.Enabled = true;
                    btnKaydet.Style["display"] = "";
                    btnGuncelle.Enabled = false;
                    btnGuncelle.Style["display"] = "none";
                    btnSil.Enabled = false;
                    btnSil.Style["display"] = "none";
                }
            }
        }

        /// <summary>
        /// Verilen Kayıdın ID sini tutar.
        /// </summary>
        private int _RecordId;

        public int RecordId
        {
            get
            {
                if (_RecordId == 0)
                {
                    if (Convert.ToInt32(hdnID.Text) != _RecordId)
                    {
                        _RecordId = Convert.ToInt32(hdnID.Text);
                    }
                }
                return _RecordId;
            }
            set
            {
                hdnID.Text = value.ToString();
                _RecordId = value;
            }
        }

        /// <summary>
        /// Silme işlemi başlamadan önce yapılacak uyarı mesajı
        /// </summary>
        private string _silmeUyarisi;

        public string silmeUyarisi
        {
            get { return _silmeUyarisi; }

            set
            {
                _silmeUyarisi = value;

                btnSil.OnClientClick = "return us_control_validate('" + value + "','silmeli_validasyon_1');";
            }
        }

        /// <summary>
        /// Silme işlemi olmadan önce ID kontrolü boşsa çıkacak uyarı
        /// </summary>
        private string _IdBosUyarisi;

        public string IdBosUyarisi
        {
            get { return _IdBosUyarisi; }

            set
            {
                if (value == null)
                {
                    btnSil.ValidationGroup = "";
                    btnSil.OnClientClick = "";
                }
                else
                {
                    btnSil.ValidationGroup = "vg0";
                    btnSil.OnClientClick = "return us_control_validate('" + value + "','vg1');";
                }
                _IdBosUyarisi = value;
            }
        }

        /// <summary>
        /// Güncelleme işlemi başlamadan önce yapılacak uyarı mesajı
        /// </summary>
        private string _guncellemeUyarisi;

        public string guncellemeUyarisi
        {
            get { return _guncellemeUyarisi; }

            set
            {
                _guncellemeUyarisi = value;
                if (validasyonGrubu == null)
                {
                    btnGuncelle.OnClientClick = "return confirm('" + value + "');";
                }
                else
                {
                    btnGuncelle.OnClientClick = "return us_control_validate('" + value + "','" + validasyonGrubu + "');";
                }
            }
        }

        /// <summary>
        /// Formun Validasyon grubunu tutar.
        /// </summary>
        private string _validasyonGrubu;

        public string validasyonGrubu
        {
            get { return _validasyonGrubu; }

            set
            {
                _validasyonGrubu = value;
                vldSumAdminCtrl.ValidationGroup = value;
                btnKaydet.ValidationGroup = value;
                btnGuncelle.ValidationGroup = value;
                btnGuncelle.OnClientClick = "return us_control_validate('" + guncellemeUyarisi + "','" + validasyonGrubu + "');";
            }
        }

        private bool _UyariGoster;

        public bool UyariGoster
        {
            get { return _UyariGoster; }

            set
            {
                vldSumAdminCtrl.ShowMessageBox = value;
                _UyariGoster = value;
            }
        }

        /// <summary>
        /// Kaydet Butonuna tıklayınca çağrılacak kaydet fonksiyonu
        /// </summary>
        private Delegate _btnKaydetClick;

        public Delegate btnKaydetClick
        {
            set
            {
                _btnKaydetClick = value;
            }
        }

        /// <summary>
        /// Temizle Butonuna tıklayınca çağrılacak temizle fonksiyonu
        /// </summary>
        private Delegate _btnTemizleClick;

        public Delegate btnTemizleClick
        {
            set
            {
                _btnTemizleClick = value;
            }
        }

        /// <summary>
        /// Sil Butonuna tıklayınca çağrılacak silme fonksiyonu
        /// </summary>
        private Delegate _btnSilClick;

        public Delegate btnSilClick
        {
            set
            {
                _btnSilClick = value;
            }
        }

        /// <summary>
        /// Sil Butonuna tıklayınca çağrılacak silme fonksiyonu
        /// </summary>
        private Delegate _btnGuncelleClick;

        public Delegate btnGuncelleClick
        {
            set
            {
                _btnGuncelleClick = value;
            }
        }

        #endregion property atamaları

        protected void btnTemizle_Click(object sender, EventArgs e)
        {
            _btnTemizleClick.DynamicInvoke();
        }

        protected void btnSil_Click(object sender, EventArgs e)
        {
            _btnSilClick.DynamicInvoke(Convert.ToInt32(hdnID.Text));
        }

        protected void btnGuncelle_Click(object sender, EventArgs e)
        {
            _btnGuncelleClick.DynamicInvoke(Convert.ToInt32(hdnID.Text));
        }

        protected void btnKaydet_Click(object sender, EventArgs e)
        {
            _btnKaydetClick.DynamicInvoke();
        }
    }
}