﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="AdminKaydet.ascx.cs" ClassName="AdminKaydet" Inherits="AdminUserCtrl.AdminKaydet" %>
<script type="text/javascript">
    ///Guncelleme butonu oncesi validasyon yapar.
    function us_control_validate(icerik, grup) {
        if (Page_ClientValidate(grup))
            return confirm(icerik);
    }
</script>
<table align="center" width="100%">
    <tr align="center">
        <td>

            <asp:Button ID="btnKaydet" CssClass="buton" runat="server" Text="Kaydet" OnClick="btnKaydet_Click" BackColor="#C60C30" />
        </td>
        <td>
            <asp:Button ID="btnGuncelle" CssClass="buton" runat="server" CausesValidation="true" Text="Güncelle" OnClick="btnGuncelle_Click" BackColor="#C60C30" /></td>
        <td>
            <asp:Button ID="btnSil" CssClass="buton" runat="server" Text="Sil" OnClick="btnSil_Click" CausesValidation="true" ValidationGroup="vgsilmeli_validasyon_1" BackColor="#C60C30" /></td>
        <td>
            <asp:Button ID="btnTemizle" CssClass="buton" runat="server" Text="İptal" OnClick="btnTemizle_Click" BackColor="#C60C30" /></td>
    </tr>
</table>
<asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ValidationGroup="silmeli_validasyon_1" ControlToValidate="hdnID" ErrorMessage="RequiredFieldValidator"></asp:RequiredFieldValidator>

<div style="display: none">
    <asp:Label ID="fixAjaxValidationSummaryErrorLabel" Text="" runat="server" CssClass="ProgramInputLabelColumn" OnPreRender="FixAjaxValidationSummaryErrorLabel_PreRender" />
    <asp:TextBox runat="server" Style="display: none" ID="hdnID" Width="1px"></asp:TextBox>
    <asp:ValidationSummary ID="vldSumAdminCtrl" runat="server" />
</div>