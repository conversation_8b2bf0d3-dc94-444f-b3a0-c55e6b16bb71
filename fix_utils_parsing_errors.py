#!/usr/bin/env python3
"""
Fix parsing errors in utility files
"""

import os
import re

def fix_parsing_errors(content):
    """Fix common parsing errors"""

    # Fix the === pattern (should be ===)
    content = re.sub(r'==\s*=', '===', content)

    # Fix the != = pattern (should be !==)
    content = re.sub(r'!=\s*=', '!==', content)

    # Fix the <= = pattern (should be <=)
    content = re.sub(r'<=\s*=(?!=)', '<=', content)

    # Fix the >= = pattern (should be >=)
    content = re.sub(r'>=\s*=(?!=)', '>=', content)

    return content

def fix_unused_params(content):
    """Fix unused parameters by prefixing with underscore"""

    # Fix common patterns
    patterns = [
        # postMessage parameters
        (r'postMessage: \(message: string\) => void', 'postMessage: (_message: string) => void'),
        # Catch error patterns
        (r'catch \(error\)', 'catch (_error)'),
        (r'catch \(err\)', 'catch (_err)'),
        (r'catch \(e\)', 'catch (_e)'),
        # Function parameters
        (r'\(authenticated: boolean\)', '(_authenticated: boolean)'),
        (r'\(userData: any\)', '(_userData: any)'),
        (r'\(type: string, data: any\)', '(_type: string, _data: any)'),
        # Array method callbacks
        (r'\.forEach\(\(key\)', '.forEach((_key)'),
        (r'\.forEach\(\(value, key\)', '.forEach((_value, _key)'),
        (r'\.filter\(\(args: any\)\)', '.filter((_args: any))'),
        (r'\.map\(\(args: any\)\)', '.map((_args: any))'),
    ]

    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)

    return content

def fix_specific_files(filepath, content):
    """Fix specific issues based on file"""

    if 'promiseSequence.ts' in filepath:
        # Likely missing closing brace
        # Count opening and closing braces
        open_braces = content.count('{')
        close_braces = content.count('}')
        if open_braces > close_braces:
            content += '\n' + '}' * (open_braces - close_braces)

    elif 'runPromiseWithTimeout.ts' in filepath:
        # Same issue
        open_braces = content.count('{')
        close_braces = content.count('}')
        if open_braces > close_braces:
            content += '\n' + '}' * (open_braces - close_braces)

    elif 'exportTableToExcel.ts' in filepath:
        # Fix forEach with unused key
        content = re.sub(r'\.forEach\(\(key\) =>', '.forEach((_key) =>', content)
        content = re.sub(r'\.forEach\(\(key: string\) =>', '.forEach((_key: string) =>', content)

    elif 'recursiveTableTitleGenerator.ts' in filepath:
        # Fix forEach with unused key
        content = re.sub(r'\.forEach\(\(([\w]+), key\) =>', r'.forEach((\1, _key) =>', content)

    elif 'tableHelpers.ts' in filepath:
        # Fix function parameters
        content = re.sub(
            r'export const formatWorkflowField = \(rowData: any, field: string, isEnglish: boolean\): any =>',
            'export const formatWorkflowField = (_rowData: any, _field: string, _isEnglish: boolean): any =>',
            content
        )

    elif 'webViewDetection.ts' in filepath:
        # Fix unused parameters in postMessage interface
        content = re.sub(
            r'postMessage: \(message: string\) => void',
            'postMessage: (_message: string) => void',
            content
        )
        # Fix callback parameters
        content = re.sub(
            r'const handleWebViewAuth = \(authenticated: boolean, userData: any\) =>',
            'const handleWebViewAuth = (_authenticated: boolean, _userData: any) =>',
            content
        )
        content = re.sub(
            r'const handleWebViewMessage = \(type: string, data: any\) =>',
            'const handleWebViewMessage = (_type: string, _data: any) =>',
            content
        )

    elif 'index.ts' in filepath and 'utils' in filepath:
        # Fix the debounce and throttle implementations
        content = re.sub(
            r'return function \(\.\.\.args: any\[\]\)',
            'return function (..._args: any[])',
            content
        )
        content = re.sub(
            r'func\.apply\(this, args\)',
            'func.apply(this, _args)',
            content
        )

    return content

def process_file(filepath):
    """Process a single file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # Apply general fixes
        content = fix_parsing_errors(content)
        content = fix_unused_params(content)

        # Apply file-specific fixes
        content = fix_specific_files(filepath, content)

        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed {filepath}")
            return True
        return False
    except Exception as e:
        print(f"Error processing {filepath}: {e}")
        return False

# Files with parsing errors
target_files = [
    'src/utils/helpers/promises/promiseSequence.ts',
    'src/utils/helpers/promises/retryPromise.ts',
    'src/utils/helpers/promises/runPromiseWithTimeout.ts',
    'src/utils/mobileAuth.ts',
    'src/utils/mobileBridge.ts',
    'src/utils/securityInitializer.ts',
    'src/utils/webViewDetection.ts',
    'src/utils/index.ts',
    'src/utils/helpers/wface/WTableHelpers/exportTableToExcel.ts',
    'src/utils/helpers/wface/WTableHelpers/recursiveTableTitleGenerator.ts',
    'src/utils/helpers/workflow/tableHelpers.ts',
]

fixed_count = 0
for filepath in target_files:
    if os.path.exists(filepath) and process_file(filepath):
        fixed_count += 1

print(f"\nTotal files fixed: {fixed_count}")