﻿///
///Generated by KB Entity Generator 
///17.03.2022 11:41:47
///
using System;
namespace Entities
{
    public class ATS_ARAC_MARKASI : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public string ARAC_MARKASI { get; set; }
        public string AKTIF { get; set; }
        public System.Nullable<DateTime> KAYIT_TARIHI { get; set; }
        public System.Nullable<DateTime> DEGISTIRME_TARIHI { get; set; }
        public System.Nullable<decimal> KAYDEDEN { get; set; }
        public System.Nullable<decimal> DEGISTIREN { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.ATS_ARAC_MARKASI (ARAC_MARKASI,AKTIF,KAYIT_TARIHI,DEGISTIRME_TARIHI,KAYDEDEN,DEGISTIREN) values (:ARAC_MARKASI,:AKTIF,:KAY<PERSON>_TARIHI,:DEGISTIRME_TARIHI,:KAYDEDEN,:DE<PERSON>STIREN)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.ATS_ARAC_MARKASI set  ARAC_MARKASI=:ARAC_MARKASI,AKTIF=:AKTIF,KAYIT_TARIHI=:KAYIT_TARIHI,DEGISTIRME_TARIHI=:DEGISTIRME_TARIHI,KAYDEDEN=:KAYDEDEN,DEGISTIREN=:DEGISTIREN where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.ATS_ARAC_MARKASI  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.ATS_ARAC_MARKASI  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
