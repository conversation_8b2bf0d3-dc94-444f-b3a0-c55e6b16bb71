﻿///
///Generated by KB Entity Generator 
///31.03.2022 13:53:15
///
using System;
namespace Entities
{
    public class ATS_ARAC_PLAKASI : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public System.Nullable<decimal> ARAC_SINIFI_ID { get; set; }
        public System.Nullable<decimal> ARAC_RENGI_ID { get; set; }
        public System.Nullable<decimal> ARAC_YILI_ID { get; set; }
        public System.Nullable<decimal> ARAC_MARKASI_ID { get; set; }
        public System.Nullable<decimal> ARAC_MODELI_ID { get; set; }
        public string ARAC_PLAKASI { get; set; }
        public string AKTIF { get; set; }
        public System.Nullable<DateTime> KAYIT_TARIHI { get; set; }
        public System.Nullable<DateTime> DEGISTIRME_TARIHI { get; set; }
        public System.Nullable<decimal> KAYDEDEN { get; set; }
        public System.Nullable<decimal> DEGISTIR<PERSON> { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.ATS_ARAC_PLAKASI (ARAC_SINIFI_ID,ARAC_RENGI_ID,ARAC_YILI_ID,ARAC_MARKASI_ID,ARAC_MODELI_ID,ARAC_PLAKASI,AKTIF,KAYIT_TARIHI,DEGISTIRME_TARIHI,KAYDEDEN,DEGISTIREN) values (:ARAC_SINIFI_ID,:ARAC_RENGI_ID,:ARAC_YILI_ID,:ARAC_MARKASI_ID,:ARAC_MODELI_ID,:ARAC_PLAKASI,:AKTIF,:KAYIT_TARIHI,:DEGISTIRME_TARIHI,:KAYDEDEN,:DEGISTIREN)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.ATS_ARAC_PLAKASI set  ARAC_SINIFI_ID=:ARAC_SINIFI_ID,ARAC_RENGI_ID=:ARAC_RENGI_ID,ARAC_YILI_ID=:ARAC_YILI_ID,ARAC_MARKASI_ID=:ARAC_MARKASI_ID,ARAC_MODELI_ID=:ARAC_MODELI_ID,ARAC_PLAKASI=:ARAC_PLAKASI,AKTIF=:AKTIF,KAYIT_TARIHI=:KAYIT_TARIHI,DEGISTIRME_TARIHI=:DEGISTIRME_TARIHI,KAYDEDEN=:KAYDEDEN,DEGISTIREN=:DEGISTIREN where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.ATS_ARAC_PLAKASI  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.ATS_ARAC_PLAKASI  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
