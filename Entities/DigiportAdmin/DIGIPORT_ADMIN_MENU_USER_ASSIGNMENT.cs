using System;

namespace Entities
{
    [Serializable]
    public partial class DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT : Entity_Base.EntityBase
    {
        #region Properties

        public decimal ID { get; set; }
        public decimal NAME_ID { get; set; }
        public decimal F_LOGIN_ID { get; set; }
        public string AD_GROUP { get; set; }
        public string DISPLAY_NAME { get; set; }
        public string AKTIF { get; set; }
        public DateTime CREATED { get; set; }
        public decimal CREATED_BY { get; set; }
        public DateTime? LAST_UPDATED { get; set; }
        public decimal? LAST_UPDATED_BY { get; set; }

        #endregion

        #region SQL Generators
        public override string DELETE_SQL()
        {
            return @"
                DELETE FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT
                 WHERE ID = :ID";
        }

        public override string INSERT_SQL()
        {
            return @"
                INSERT INTO DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT
                   (NAME_ID, F_LOGIN_ID, AD_GROUP, DISPLAY_NAME, AKTIF, CREATED_BY)
                 VALUES
                   (:NAME_ID,
                    :F_LOGIN_ID,
                    :AD_GROUP,
                    :DISPLAY_NAME,
                    :AKTIF,
                    :CREATED_BY)";
        }

        public override string SELECT_SQL()
        {
            return @"
                SELECT ID,
                       NAME_ID,
                       F_LOGIN_ID,
                       AD_GROUP,
                       DISPLAY_NAME,
                       AKTIF,
                       CREATED,
                       CREATED_BY,
                       LAST_UPDATED,
                       LAST_UPDATED_BY
                  FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT";
        }

        public override string UPDATE_SQL()
        {
            return @"
                UPDATE DT_WORKFLOW.DIGIPORT_ADMIN_MENU_USER_ASSIGNMENT
                   SET NAME_ID        = :NAME_ID,
                       F_LOGIN_ID     = :F_LOGIN_ID,
                       AD_GROUP       = :AD_GROUP,
                       DISPLAY_NAME   = :DISPLAY_NAME,
                       AKTIF          = :AKTIF,
                       LAST_UPDATED   = SYSDATE,
                       LAST_UPDATED_BY = :LAST_UPDATED_BY
                 WHERE ID = :ID";
        }

        #endregion
    }
}
