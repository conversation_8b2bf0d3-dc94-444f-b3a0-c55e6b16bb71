///
///Generated for DigiflowMenu module
///April 14, 2025
///
using System;
namespace Entities
{
    [Serializable]
    public partial class DIGIPORT_ADMIN_MENU_TYPE : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public decimal ID { get; set; }
        public string TYPE_NAME { get; set; }
        public string DESCRIPTION { get; set; }
        public string AKTIF { get; set; }
        public DateTime CREATED { get; set; }
        public decimal CREATED_BY { get; set; }
        public System.Nullable<DateTime> LAST_UPDATED { get; set; }
        public System.Nullable<decimal> LAST_UPDATED_BY { get; set; }

        #endregion
        #region Metodlar
        public override string DELETE_SQL()
        {
            return string.Format(@"DELETE FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE
                               WHERE ID = {0}", ID);
        }

        public override string INSERT_SQL()
        {
            return string.Format(@"INSERT INTO DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE
                              (TYPE_NAME, DESCRIPTION, AKTIF, CREATED_BY)
                              VALUES
                              ('{0}', '{1}', '{2}', {3})",
                              TYPE_NAME.Replace("'", "''"),
                              DESCRIPTION?.Replace("'", "''") ?? string.Empty,
                              AKTIF ?? "Y",
                              CREATED_BY);
        }

        public override string SELECT_SQL()
        {
            return "SELECT ID, TYPE_NAME, DESCRIPTION, AKTIF, CREATED, CREATED_BY, LAST_UPDATED, LAST_UPDATED_BY FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE";
        }

        public override string UPDATE_SQL()
        {
            return string.Format(@"UPDATE DT_WORKFLOW.DIGIPORT_ADMIN_MENU_TYPE
                               SET TYPE_NAME = '{0}',
                                   DESCRIPTION = '{1}',
                                   AKTIF = '{2}',
                                   LAST_UPDATED = SYSDATE,
                                   LAST_UPDATED_BY = {3}
                               WHERE ID = {4}",
                               TYPE_NAME.Replace("'", "''"),
                               DESCRIPTION?.Replace("'", "''") ?? string.Empty,
                               AKTIF,
                               LAST_UPDATED_BY,
                               ID);
        }
        #endregion Instance Properties
    }
}