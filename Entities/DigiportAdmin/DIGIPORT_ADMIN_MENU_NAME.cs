///
///Generated for DigiflowMenu module
///April 14, 2025
///
using System;
namespace Entities
{
    [Serializable]
    public partial class DIGIPORT_ADMIN_MENU_NAME : Entity_Base.EntityBase
    {
        #region Entity De�erleri
        public decimal ID { get; set; }
        public decimal TYPE_ID { get; set; }
        public string NAME { get; set; }
        public string DESCRIPTION { get; set; }
        public string NAME_EN { get; set; }
        public string DESCRIPTION_EN { get; set; }
        public string PAGE_PATH { get; set; }
        public string AKTIF { get; set; }
        public DateTime CREATED { get; set; }
        public decimal CREATED_BY { get; set; }
        public System.Nullable<DateTime> LAST_UPDATED { get; set; }
        public System.Nullable<decimal> LAST_UPDATED_BY { get; set; }

        #endregion
        #region Metodlar
        public override string DELETE_SQL()
        {
            return string.Format(@"DELETE FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME
                               WHERE ID = {0}", ID);
        }
        public override string INSERT_SQL()
        {
            return string.Format(@"INSERT INTO DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME
                              (TYPE_ID, NAME, DESCRIPTION, PAGE_PATH, AKTIF, CREATED_BY, NAME_EN, DESCRIPTION_EN)
                              VALUES
                              ({0}, '{1}', '{2}', '{3}', '{4}', '{5}', '{6}', '{7}')",
                              TYPE_ID,
                              NAME?.Replace("'", "''") ?? string.Empty,
                              DESCRIPTION?.Replace("'", "''") ?? string.Empty,
                              PAGE_PATH?.Replace("'", "''") ?? string.Empty,
                              AKTIF ?? "Y",
                              CREATED_BY,
                              NAME_EN?.Replace("'", "''") ?? string.Empty,
                              DESCRIPTION_EN?.Replace("'", "''") ?? string.Empty);
        }
        public override string SELECT_SQL()
        {
            return "SELECT ID, TYPE_ID, NAME, NAME_EN, DESCRIPTION, DESCRIPTION_EN, PAGE_PATH, AKTIF, CREATED, CREATED_BY, LAST_UPDATED, LAST_UPDATED_BY FROM DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME";
        }
        public override string UPDATE_SQL()
        {
            return string.Format(@"UPDATE DT_WORKFLOW.DIGIPORT_ADMIN_MENU_NAME
                               SET TYPE_ID = {0},
                                   NAME = '{1}',
                                   DESCRIPTION = '{2}',
                                   PAGE_PATH = '{3}',
                                   AKTIF = '{4}',
                                   LAST_UPDATED = SYSDATE,
                                   LAST_UPDATED_BY = {5},
                                   NAME_EN = '{7}',
                                   DESCRIPTION_EN = '{8}'
                               WHERE ID = {6}",
                               TYPE_ID,
                               NAME?.Replace("'", "''") ?? string.Empty,
                               DESCRIPTION?.Replace("'", "''") ?? string.Empty,
                               PAGE_PATH?.Replace("'", "''") ?? string.Empty,
                               AKTIF,
                               LAST_UPDATED_BY,
                               ID,
                               NAME_EN?.Replace("'", "''") ?? string.Empty,
                               DESCRIPTION_EN?.Replace("'", "''") ?? string.Empty);
        }
        #endregion Instance Properties
    }
}