﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DigiportAdmin
{
    public class DIGIPORT_ADMIN_SLIDER_OPTIONS : Entity_Base.EntityBase
    {
        #region Entity Değerleri
        public decimal MENU_NAME_ID { get; set; }
        public decimal SLIDE_WIDTH { get; set; }
        public decimal SLIDE_HEIGHT { get; set; }
        public decimal AUTO_SLIDE_INTERVAL { get; set; }
        public decimal TRANSITION_DURATION { get; set; }
        public decimal EFFECT_TYPE { get; set; }
        public decimal TRANSITION_EASING { get; set; }
        public string SHOW_PROGRESS_BAR { get; set; }
        public string SHOW_THUMBNAILS { get; set; }
        public System.Nullable<decimal> THUMBNAIL_WIDTH { get; set; }
        public System.Nullable<decimal> THUMBNAIL_HEIGHT { get; set; }
        public decimal TITLE_MODE { get; set; }
        public DateTime CREATED { get; set; }
        public decimal CREATED_BY { get; set; }
        public System.Nullable<DateTime> LAST_UPDATED { get; set; }
        public System.Nullable<decimal> LAST_UPDATED_BY { get; set; }
        public string AUTO_SLIDE_ACTIVE { get; set; }
        #endregion
        #region Metodlar
        public override string INSERT_SQL()
        {
            string Insert_SQL = "insert into DT_WORKFLOW.DIGIPORT_ADMIN_SLIDER_OPTIONS (MENU_NAME_ID,SLIDE_WIDTH,SLIDE_HEIGHT,AUTO_SLIDE_INTERVAL,TRANSITION_DURATION,EFFECT_TYPE,TRANSITION_EASING,SHOW_PROGRESS_BAR,SHOW_THUMBNAILS,THUMBNAIL_WIDTH,THUMBNAIL_HEIGHT,TITLE_MODE,CREATED,CREATED_BY,LAST_UPDATED,LAST_UPDATED_BY,AUTO_SLIDE_ACTIVE) values (:MENU_NAME_ID,:SLIDE_WIDTH,:SLIDE_HEIGHT,:AUTO_SLIDE_INTERVAL,:TRANSITION_DURATION,:EFFECT_TYPE,:TRANSITION_EASING,:SHOW_PROGRESS_BAR,:SHOW_THUMBNAILS,:THUMBNAIL_WIDTH,:THUMBNAIL_HEIGHT,:TITLE_MODE,:CREATED,:CREATED_BY,:LAST_UPDATED,:LAST_UPDATED_BY,:AUTO_SLIDE_ACTIVE)";
            return Insert_SQL;
        }
        public override string UPDATE_SQL()
        {
            string Update_SQL = "update DT_WORKFLOW.DIGIPORT_ADMIN_SLIDER_OPTIONS set  MENU_NAME_ID=:MENU_NAME_ID,SLIDE_WIDTH=:SLIDE_WIDTH,SLIDE_HEIGHT=:SLIDE_HEIGHT,AUTO_SLIDE_INTERVAL=:AUTO_SLIDE_INTERVAL,TRANSITION_DURATION=:TRANSITION_DURATION,EFFECT_TYPE=:EFFECT_TYPE,TRANSITION_EASING=:TRANSITION_EASING,SHOW_PROGRESS_BAR=:SHOW_PROGRESS_BAR,SHOW_THUMBNAILS=:SHOW_THUMBNAILS,THUMBNAIL_WIDTH=:THUMBNAIL_WIDTH,THUMBNAIL_HEIGHT=:THUMBNAIL_HEIGHT,TITLE_MODE=:TITLE_MODE,CREATED=:CREATED,CREATED_BY=:CREATED_BY,LAST_UPDATED=:LAST_UPDATED,LAST_UPDATED_BY=:LAST_UPDATED_BY,AUTO_SLIDE_ACTIVE=:AUTO_SLIDE_ACTIVE where ID=:ID ";
            return Update_SQL;
        }
        public override string SELECT_SQL()
        {
            string Select_Sql = "Select * from  DT_WORKFLOW.DIGIPORT_ADMIN_SLIDER_OPTIONS  where ID=:ID ";
            return Select_Sql;
        }
        public override string DELETE_SQL()
        {
            string Delete_Sql = "Delete from  DT_WORKFLOW.DIGIPORT_ADMIN_SLIDER_OPTIONS  where ID=:ID ";
            return Delete_Sql;
        }
        #endregion
    }
}
