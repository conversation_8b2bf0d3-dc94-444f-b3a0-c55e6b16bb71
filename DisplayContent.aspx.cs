﻿using DevExpress.Web;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Digiport_DisplayContent : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        string domain = ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True" ? "http://digiflowtest" : "http://digiflow";
        string content = string.Empty;
        divContent.Visible = false;
        try
        {
            if (!string.IsNullOrEmpty(Request.QueryString["component-type"]))
            {
                string componentType = Request.QueryString["component-type"];
                int contentId = 0;
                if (!string.IsNullOrEmpty(Request.QueryString["content-id"]))
                {
                    if (Int32.TryParse(Request.QueryString["content-id"], out contentId))
                    {
                        if (componentType.ToLower() == "anasayfasolsagslide")
                            content = DigiportMenuDisplayHelpers.AnaSayfaSolSagSlideHelper.GetSlideTargetContent(contentId, domain);
                        else if (componentType.ToLower() == "anasayfaustslide")
                            content = DigiportMenuDisplayHelpers.AnaSayfaUstSlideHelper.GetSlideTargetContent(contentId, domain);
                        else if (componentType.ToLower() == "hrmediaannouncements" || componentType.ToLower() == "agencyannouncements" || componentType.ToLower() == "educationannouncements")
                            content = DigiportMenuDisplayHelpers.HrMediaSlideHelper.GetSlideTargetContent(contentId, domain);
                        else if (componentType.ToLower() == "discountopportunity")
                            content = DigiportMenuDisplayHelpers.IndirimFirsatiHelper.GetTargetContent(contentId, domain);
                        else if (componentType.ToLower() == "links")
                            content = DigiportMenuDisplayHelpers.LinklerHelper.GetTargetContent(contentId, domain);
                        else if (componentType.ToLower() == "anasayfasolmenu")
                            content = DigiportMenuDisplayHelpers.AnaSayfaSolMenuHelper.GetTargetContent(contentId, domain);

                        divContent.Visible = true;
                    }
                }
            }
        }
        catch
        {
        }
        divContent.InnerHtml = content;
    }


}