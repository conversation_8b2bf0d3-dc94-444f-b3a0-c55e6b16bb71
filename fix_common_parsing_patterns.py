#!/usr/bin/env python3
"""
Fix common parsing error patterns in the codebase.
"""

import os
import re
import subprocess
from typing import List, <PERSON><PERSON>

def fix_tohavebeencalledwith_pattern(content: str) -> str:
    """Fix toHaveBeenCalledWith({) pattern to toHaveBeenCalledWith({."""
    pattern = r'\.toHaveBeenCalledWith\(\{\)'
    replacement = r'.toHaveBeenCalledWith({'
    return re.sub(pattern, replacement, content)

def fix_extra_closing_braces(content: str) -> str:
    """Fix lines with just }) at the end of the file."""
    lines = content.split('\n')
    
    # Remove lines that are just closing braces at the end
    while lines and lines[-1].strip() in ['})' * i for i in range(1, 50)]:
        lines.pop()
    
    return '\n'.join(lines)

def fix_misplaced_closing_parens(content: str) -> str:
    """Fix misplaced closing parentheses in test files."""
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # Look for patterns like })
        if line.strip() == '})' and i + 1 < len(lines):
            next_line = lines[i + 1].strip()
            # If next line starts a new describe or similar block
            if next_line.startswith('describe(') or next_line.startswith('it(') or next_line.startswith('test('):
                # This }) is probably misplaced
                fixed_lines.append('              }')
                fixed_lines.append('            })')
                continue
        
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_missing_commas_in_objects(content: str) -> str:
    """Fix missing commas in object literals."""
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # Check if line ends with a property value but no comma
        if ':' in line and not line.strip().endswith(',') and not line.strip().endswith('{') and not line.strip().endswith('('):
            # Check if next line starts a new property or closes object
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if (next_line.startswith('}') or ':' in next_line) and not line.strip().endswith(','):
                    line = line.rstrip() + ','
        
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_file(filepath: str) -> bool:
    """Apply fixes to a file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply fixes
        content = fix_tohavebeencalledwith_pattern(content)
        content = fix_misplaced_closing_parens(content)
        content = fix_missing_commas_in_objects(content)
        content = fix_extra_closing_braces(content)
        
        # Write back if changed
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
    except Exception as e:
        print(f"Error fixing {filepath}: {e}")
    
    return False

def get_files_with_parsing_errors() -> List[Tuple[str, int, str]]:
    """Get list of files with parsing errors and their details."""
    files = []
    
    # Run ESLint and capture output
    cmd = ["yarn", "lint", "--format", "stylish"]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    current_file = None
    for line in result.stdout.split('\n'):
        # Check if this is a file path
        if line.strip() and '/src/' in line and '.ts' in line and not line.startswith(' '):
            current_file = line.strip()
        # Check if this is an error line
        elif current_file and 'error' in line and 'Parsing error' in line:
            # Extract line number and error message
            match = re.search(r'(\d+):(\d+)\s+error\s+Parsing error:\s*(.+)', line)
            if match:
                line_num = int(match.group(1))
                error_msg = match.group(3).strip()
                files.append((current_file, line_num, error_msg))
    
    return files

def main():
    print("🔍 Finding files with parsing errors...")
    
    error_details = get_files_with_parsing_errors()
    
    # Group by file
    files_to_fix = {}
    for filepath, line_num, error_msg in error_details:
        if filepath not in files_to_fix:
            files_to_fix[filepath] = []
        files_to_fix[filepath].append((line_num, error_msg))
    
    print(f"Found {len(files_to_fix)} files with parsing errors")
    
    fixed_count = 0
    for filepath, errors in files_to_fix.items():
        print(f"\n  Checking {filepath}...")
        for line_num, error_msg in errors:
            print(f"    Line {line_num}: {error_msg}")
        
        if fix_file(filepath):
            print(f"    ✅ Applied fixes")
            fixed_count += 1
    
    print(f"\n✨ Fixed {fixed_count} files")

if __name__ == "__main__":
    main()