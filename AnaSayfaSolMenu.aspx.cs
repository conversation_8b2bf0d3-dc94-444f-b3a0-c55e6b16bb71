﻿using Digiturk.Workflow.Digiflow.WebCore.Digiport;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Digiport_AnaSayfaSolMenu : ComponentBasePage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        try
        {
            divAnaSayfaSolMenuCaption.InnerHtml = componentTitle;
            RepeaterYukle();
        }
        catch
        {
            divContainer_Repeater.Visible = false;
        }
    }
    private void RepeaterYukle()
    {
        divContainer_Repeater.Visible = true;
        DataTable dt = DigiportMenuDisplayHelpers.AnaSayfaSolMenuHelper.SolMenuTabloGetir(componentType);
        repeaterSolMenu.DataSource = dt;
        repeaterSolMenu.DataBind();
        divContainer_Repeater.Visible = dt.Rows.Count > 0;
    }
    public string LinkClickEvent(string contentId, int componentClickAction, string contentTargetLink, string popupWindowWidth, string popupWindowHeight)
    {
        return componentBase.GetComponentClickEvent("AnaSayfaSolMenu", componentClickAction, contentId, contentTargetLink, popupWindowWidth, popupWindowHeight, ConfigurationManager.AppSettings["DigiportContentLink"], ConfigurationManager.AppSettings["AjansContentLink"]);
    }
    public string LiKlas(int componentClickAction)
    {
        string donen = "";
        if (((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Olay_Yok) != componentClickAction)
            donen = donen + " underline_cursor_color2";
        return donen;
    }
}