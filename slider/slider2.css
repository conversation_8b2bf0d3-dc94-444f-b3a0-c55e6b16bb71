﻿body {
    margin: 0;
    padding: 0;
}
#divSliderContainer{
    padding:2px;
}
#galleryContainer {
    width: 100%;
    max-width: 875px;
    font-family: sans-serif;
}

#mainArea {
    /* IE 10-11 flexbox support */
    display: -ms-flexbox;
    display: flex;
    position: relative;
    border: solid 1px #cfcfcf;
    padding: 3px;
    border-radius: 3px;
}

#mainImageContainer {
    position: relative;
    overflow: hidden;
    background: #000;
}

#mainImage {
    width: 100%;
    height: 100%;
    /* object-fit not supported in IE */
    display: block;
    cursor:pointer;
}

#imageTitle {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
    color: white;
    background: rgba(0, 0, 0, 0.5);
    padding: 10px 0;
    font-size: 16px;
    opacity: 0;
    filter: alpha(opacity=0); /* IE 8 */
    transition: opacity 0.5s ease;
    -ms-transition: opacity 0.5s ease; /* IE 10 */
    z-index: 9;
}

#mainImageContainer:hover #imageTitle {
    opacity: 1;
    filter: alpha(opacity=100); /* IE 8 */
}

#progressBar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: #d1d1d1; /* IE compatible color */
    overflow: hidden;
    z-index: 9;
}

    #progressBar .bar {
        height: 100%;
        width: 0%;
        background: #882db6;
    }

#thumbList {
    width: 295px;
    height: 285px;
    overflow-y: auto;
    overflow-x: hidden;
    list-style: none;
    margin: 0 0 0 3px;
    padding: 0px;
    /* IE 10-11 flexbox support */
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column; /* IE 10 */
    flex-direction: column;
    /* gap not supported in IE - use margins on children instead */
    background: #f7f7f7;
    -ms-flex-pack: start; /* IE 10 */
    justify-content: flex-start;
    -ms-flex-align: start; /* IE 10 */
    align-items: flex-start;
    -ms-flex-wrap: nowrap; /* IE 10 */
    flex-wrap: nowrap;
    padding: 3px;
}

    #thumbList li {
        width: 100%;
        cursor: pointer;
        border: 1px solid transparent;
        box-sizing: border-box;
        position: relative;
        font-weight: 500;
        padding: 3px;
        border-bottom: solid 1px #e9e9e9;
        /* Margin to replace gap in IE */
        margin-bottom: 5px;
        /* IE 10-11 flexbox support */
        display: -ms-flexbox;
        display: flex;
        -ms-flex-align: start; /* IE 10 */
        align-items: flex-start;
    }

        #thumbList li span {
            font-size: 12px;
        }

        #thumbList li.selected {
            background-color: #e6deea;
            border-radius: 5px;
            border-left: none;
        }

        #thumbList li img {
            max-width: 60px;
            /* object-fit not supported in IE */
            max-height: 40px;
            display: block;
        }

        /* Dikey Çubuk */
        #thumbList li:after { /* IE8 compat */
            content: '';
            position: absolute;
            left: 0px;
            top: 0;
            width: 2px; /* Çubuğun genişliği */
            height: 100%;
            background-color: #ccc; /* Normal durumdaki çubuğun rengi */
        }

        /* Seçili öğe için farklı renk */
        #thumbList li.selected:after { /* IE8 compat */
            background-color: #882db6; /* Seçili öğe için çubuğun rengi */
        }

    #thumbList::-webkit-scrollbar {
        width: 4px;
        height: 4px;
    }

    /* Track */
    #thumbList::-webkit-scrollbar-track {
        box-shadow: inset 0 0 2px grey;
        border-radius: 10px;
    }

    /* Handle */
    #thumbList::-webkit-scrollbar-thumb {
        background: #880388;
        border-radius: 10px;
        cursor: pointer;
    }

        /* Handle on hover */
        #thumbList::-webkit-scrollbar-thumb:hover {
            background: #ce1fce;
        }

.clamp {
    /* Webkit-specific clamp not supported in IE - use height-based truncation */
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 5px;
    text-indent: 3px;
    max-height: 3em; /* Approximately 2 lines */
    line-height: 1.5em;
}
