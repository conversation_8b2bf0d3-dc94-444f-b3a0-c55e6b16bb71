﻿function initSlider(options) {
    var settings = $.extend({
        transitionEffect: '',
        transitionDuration: 800,
        easing: '',
        autoSlideInterval: 4000,
        showProgressBar: true,
        titleMode: ''
    }, options);

    var $container = $('#galleryContainer');
    var $mainImage = $('#mainImage');
    var $title = $('#imageTitle');
    var $progressBar = $('#progressBar .bar');
    var $thumbs = $('#thumbList li');
    var current = 0;
    var interval, progressTimeout;
    var isManual = false;

    function resetProgressBar() {
        $progressBar.stop(true, true).css('width', '0%');
        if (settings.showProgressBar) {
            $progressBar.animate({ width: '100%' }, settings.autoSlideInterval);
        }
    }

    //function scrollToSelected(index) {
    //    try {
    //        const $selected = $thumbs.eq(index);
    //        const item = $selected[0]; // jQuery yerine doğrudan DOM öğesini alıyoruz
    //        item.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    //    } catch (e) {

    //    }
    //}

    function scrollToSelected(index) {
        try {
            var $selected = $thumbs.eq(index);
            var item = $selected[0];
            var $containerList = $('#thumbList');

            var containerTop = $containerList.offset().top;
            var containerScrollTop = $containerList.scrollTop();
            var containerHeight = $containerList.height();

            var itemTop = $selected.position().top;
            var itemHeight = $selected.outerHeight();

            // Eğer item yukarıdan görünmüyorsa ya da aşağıdan taşıyorsa scrollTop'u ayarla
            if (itemTop < 0) {
                $containerList.scrollTop(containerScrollTop + itemTop);
            } else if (itemTop + itemHeight > containerHeight) {
                $containerList.scrollTop(containerScrollTop + (itemTop + itemHeight - containerHeight));
            }
        } catch (e) {
            // Sessiz geç
        }
    }

    function showSlide(index) {
        if (index >= $thumbs.length) index = 0;
        current = index;

        var $li = $thumbs.eq(index);
        var src = $li.data('src');
        var title = $li.data('title');
        var fn = $li.data('function');

        var effect = settings.transitionEffect;
        var $clone = $mainImage.clone();

        $clone.attr('src', src).css({ opacity: 0, position: 'absolute', top: 0, left: 0 });
        $mainImage.parent().append($clone);
        if (effect === 'fade') {
            $clone.animate({ opacity: 1 }, settings.transitionDuration, settings.easing, function () {
                $mainImage.remove();
                $mainImage = $clone;
            });
        } else if (effect === 'zoom') {
            $clone.css({ 
                'transform': 'scale(1.2)',
                '-ms-transform': 'scale(1.2)'
            }).animate({ opacity: 1 }, {
                duration: settings.transitionDuration,
                easing: settings.easing,
                step: function (now, fx) {
                    if (fx.prop === 'opacity') {
                        var scaleValue = 1.2 - 0.2 * now;
                        // IE compatibility: use both transform and -ms-transform
                        $clone.css({
                            'transform': 'scale(' + scaleValue + ')',
                            '-ms-transform': 'scale(' + scaleValue + ')'
                        });
                    }
                },
                complete: function () {
                    $mainImage.remove();
                    $mainImage = $clone;
                }
            });
        } else if (effect === 'slideDown') {
            $clone.css({ top: '-100%' }).animate({ top: 0, opacity: 1 }, settings.transitionDuration, settings.easing, function () {
                $mainImage.remove();
                $mainImage = $clone;
            });
        } else { // slide left to right default
            $clone.css({ left: '-100%' }).animate({ left: 0, opacity: 1 }, settings.transitionDuration, settings.easing, function () {
                $mainImage.remove();
                $mainImage = $clone;
            });
        }

        // Highlight selected
        $thumbs.removeClass('selected');
        $li.addClass('selected');
        scrollToSelected(index);

        // Reset progress
        clearTimeout(progressTimeout);
        resetProgressBar();

        // If manual, trigger function
        if (isManual && fn) {
            try {
                eval(fn);
            } catch (e) {
            }
        }

        // Title mode
        if (settings.titleMode === 'None') {
            $title.hide();
        } else {
            $title.text(title).show();
            if (settings.titleMode === 'Always') {
                $title.css('opacity', 1);
            } else if (settings.titleMode === 'Hover') {
                $title.css('opacity', 0); // Başlangıçta başlık gizli
                $clone.on('mouseenter', function () {
                    $title.stop(true, true).fadeIn(); // Hover ile başlık göster
                    $title.css('opacity', 1);
                }).on('mouseleave', function () {
                    $title.stop(true, true).fadeOut(); // Hover bittiğinde başlık gizle
                    $title.css('opacity', 0);
                });
            }
        }
        $clone.on('click', function () {
            eval(fn);
        });
    }

    function nextSlide() {
        isManual = false;
        showSlide((current + 1) % $thumbs.length);
    }

    function startAutoSlide() {
        clearInterval(interval);
        clearTimeout(progressTimeout);
        resetProgressBar();
        interval = setInterval(nextSlide, settings.autoSlideInterval);
    }

    $thumbs.on('mouseenter', function () {
            var index = $thumbs.index(this);
            var src = $(this).data('src');
            var title = $(this).data('title');
            var fn = $(this).data('function');

            currentIndex = index;
            showSlide(index);
            scrollToSelected(index);
    });

    $thumbs.on('click', function () {
        var index = $thumbs.index(this);
        var src = $(this).data('src');
        var title = $(this).data('title');
        var fn = $(this).data('function');

        currentIndex = index;
        if (fn && typeof fn === 'string' && fn.indexOf('(') !== -1) {
            // Sadece tıklama ile çalışacak şekilde
            setTimeout(function() {
                try {
                    eval(fn);
                } catch (e) {
                }
            }, 0);
        }
    });

    // Başlangıçta ilk resmi göster ve başlat
    showSlide(0);
    startAutoSlide();
}

$(document).ready(function () {
    var autoSlideInterval = $('#hdn_autoSlideInterval').length > 0 ? parseInt($('#hdn_autoSlideInterval').val()) : 4000;
    var transitionDuration = $('#hdn_transitionDuration').length > 0 ? parseFloat($('#hdn_transitionDuration').val()) : 0.8;
    var effectType = $('#hdn_effectType').length > 0 ? $('#hdn_effectType').val() : "slide";       // "slide" | "fade" | "zoom" | "slideDown"
    var transitionEasing = $('#hdn_transitionEasing').length > 0 ? $('#hdn_transitionEasing').val() : "linear"; // "ease" | "ease-in-out" | "easeInBack" | "easeOutElastic"
    var titleMode = $('#hdn_titleMode').length > 0 ? $('#hdn_titleMode').val() : "none";       // "always" | "hover" | "none"
    var showProgressBar = $('#hdn_showProgressBar').length > 0 ? $('#hdn_showProgressBar').val().toLowerCase() == "true" : false;

    initSlider({
        transitionEffect: effectType,
        transitionDuration: transitionDuration,
        easing: transitionEasing,
        autoSlideInterval: autoSlideInterval,
        showProgressBar: showProgressBar,
        titleMode: titleMode
    });
});