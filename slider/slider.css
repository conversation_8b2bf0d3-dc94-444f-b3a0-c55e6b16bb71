﻿body {
    margin: 0;
    padding: 0;
}
.slider-container {
    position: relative;
    margin: 3px;
    overflow: hidden;
    border: solid 1px #d1d1d1;
    border-radius: 6px;
}

.slider {
    display: block; /* default, slideEffect ile flex’e dönü<PERSON>ecek */
    width: 100%; /* hep container kadar */
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    /* IE 9 fallback */
    -ms-box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    background: #000;
    position: relative;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 99%;
    opacity: 0;
    filter: alpha(opacity=0); /* IE 8 */
    transform: scale(1);
    -ms-transform: scale(1); /* IE 9 */
    transition: all .5s ease;
    -ms-transition: all .5s ease; /* IE 10 */
    z-index: 0;
    cursor: pointer;
}

    .slide img {
        width: 100%;
        height: 100%;
        /* object-fit not supported in IE - using alternative */
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%); /* IE 9 */
        min-width: 100%;
        min-height: 100%;
    }

    .slide.active {
        opacity: 1;
        filter: alpha(opacity=100); /* IE 8 */
        z-index: 1;
    }

.slide-title {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 12px;
    background: rgba(0,0,0,0.5);
    color: #fff;
    font-size: 20px;
    text-align: center;
    box-sizing: border-box;
    transition: opacity .3s;
}

.arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    -ms-transform: translateY(-50%); /* IE 9 */
    font-size: 20px;
    color: #fff;
    background: rgba(0,0,0,0.4);
    padding: 12px;
    cursor: pointer;
    z-index: 2;
    user-select: none;
    -ms-user-select: none; /* IE 10 */
    border-radius: 50%;
}

.arrow-left {
    left: 20px;
}

.arrow-right {
    right: 20px;
}

/* ─── SLIDE MODU ─── */
.slideEffect {
    /* IE 10-11 flexbox support */
    display: -ms-flexbox;
    display: flex;
    transition: transform .8s ease-in-out; /* default, JS ile override edilebilir */
    -ms-transition: -ms-transform .8s ease-in-out; /* IE 10 */
}

    .slideEffect .slide {
        position: relative; /* absolute'ı maskeliyoruz */
        -ms-flex: 0 0 100%; /* IE 10-11 */
        flex: 0 0 100%; /* her biri container'ın %100'ü kadar */
        opacity: 1; /* tümü görünür, container kaydırılır */
        transform: none;
        -ms-transform: none; /* IE 9 */
    }

/* ─── FADE MODU ─── */
.fade .slide {
    opacity: 0;
}

    .fade .slide.active {
        opacity: 1;
    }

/* ─── ZOOM MODU ─── */
.zoom .slide {
    opacity: 0;
    transform: scale(1);
    -ms-transform: scale(1); /* IE 9 */
}

    .zoom .slide.active {
        opacity: 1;
        transform: scale(1.05);
        -ms-transform: scale(1.05); /* IE 9 */
    }

/* ─── SLIDEDOWN MODU ─── */
.slideDown .slide {
    opacity: 0;
    transform: translateY(-100%);
    -ms-transform: translateY(-100%); /* IE 9 */
}

    .slideDown .slide.active {
        opacity: 1;
        transform: translateY(0);
        -ms-transform: translateY(0); /* IE 9 */
    }

/* ─── TITLE MODU ─── */
.title-Always .slide-title {
    opacity: 1;
}

.title-Hover .slide-title {
    opacity: 0;
}

.title-Hover .slide:hover .slide-title {
    opacity: 1;
}

.title-None .slide-title {
    display: none;
}

/* ─── PROGRESS BAR ─── */
.progress-bar {
    width: 100%;
    height: 5px;
    background: rgba(20, 20, 20, 0.3); /* IE compatible syntax */
    z-index: 3;
    position: absolute;
}

.progress-fill {
    width: 0%;
    height: 100%;
    background: #9923a6;
    transition: width 4s linear;
}

/* ─── THUMBNAIL KONTEYNER ─── */
.thumb-container {
    /* IE 10-11 flexbox support */
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: center; /* IE 10 */
    justify-content: center;
    /* gap not supported in IE - use margins on children instead */
    overflow: auto;
    background-color: rgba(216, 216, 216, 0.85);
    padding: 10px;
}

.thumb {
    overflow: hidden;
    border: 1px solid #636363;
    border-radius: 4px;
    cursor: pointer;
    opacity: .5;
    filter: alpha(opacity=50); /* IE 8 */
    transition: opacity .3s, border-color .3s;
    -ms-flex-negative: 0; /* IE 10 */
    flex-shrink: 0;
    padding: 2px;
    /* Add margin for gap replacement in IE */
    margin: 0 5px;
}

    .thumb:hover {
        opacity: 1;
        filter: alpha(opacity=100); /* IE 8 */
    }

    .thumb.active {
        border-color: #8e048e;
        opacity: 1;
        filter: alpha(opacity=100); /* IE 8 */
        border-width: 3px;
    }

    .thumb img {
        width: 100%;
        height: 100%;
        /* object-fit not supported in IE */
        display: block;
    }

.thumb-container::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

/* Track */
.thumb-container::-webkit-scrollbar-track {
    box-shadow: inset 0 0 2px grey;
    border-radius: 10px;
}

/* Handle */
.thumb-container::-webkit-scrollbar-thumb {
    background: #880388;
    border-radius: 10px;
    cursor: pointer;
}

    /* Handle on hover */
    .thumb-container::-webkit-scrollbar-thumb:hover {
        background: #ce1fce;
    }



.navigator-container {
    /* IE 10-11 flexbox support */
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: center; /* IE 10 */
    justify-content: center;
    /* gap not supported in IE - use margins on children instead */
    padding: 10px 0;
}

.navigator-dot {
    width: 12px;
    height: 12px;
    background-color: #c5bfbf;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
    /* Add margin for gap replacement in IE */
    margin: 0 5px;
}

    .navigator-dot.active {
        background-color: #9923a6;
    }
