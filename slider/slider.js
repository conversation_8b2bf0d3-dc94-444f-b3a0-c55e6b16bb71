﻿$(function () {
    // ► Parametreler
    var autoSlideInterval = $('#hdn_autoSlideInterval').length > 0 ? parseInt($('#hdn_autoSlideInterval').val()) : 4000;
    var transitionDuration = $('#hdn_transitionDuration').length > 0 ? parseFloat($('#hdn_transitionDuration').val()) : 0.8;
    var effectType = $('#hdn_effectType').length > 0 ? $('#hdn_effectType').val() : "slide";       // "slide" | "fade" | "zoom" | "slideDown"
    var transitionEasing = $('#hdn_transitionEasing').length > 0 ? $('#hdn_transitionEasing').val() : "ease-in-out"; // "ease" | "ease-in-out" | "easeInBack" | "easeOutElastic"
    var titleMode = $('#hdn_titleMode').length > 0 ? $('#hdn_titleMode').val() : "none";       // "always" | "hover" | "none"
    var showProgressBar = $('#hdn_showProgressBar').length > 0 ? $('#hdn_showProgressBar').val().toLowerCase() == "true" : false;
    var showThumbnails = $('#hdn_showThumbnails').length > 0 ? $('#hdn_showThumbnails').val().toLowerCase() == "true" : false;

    var progressEasing = "linear";

    // ► Seçiciler & State
    var $c = $('.slider-container');
    var $s = $c.find('.slider');
    var $sl = $s.find('.slide');
    var $bar = $c.find('.progress-bar');
    var $fill = $bar.find('.progress-fill');
    var $tc = $c.find('.thumb-container');
    var $th = $tc.find('.thumb');
    var total = $sl.length;
    var $navContainer = $c.find('.navigator-container');

    var idx = 0, autoT;

    // Başlangıç
    $c.addClass('title-' + titleMode);
    if (!showProgressBar) $bar.hide();
    if (!showThumbnails) $tc.hide();

    // Easing map
    var easingMap = {
        "ease": "ease",
        "ease-in-out": "ease-in-out",
        "easeInBack": "cubic-bezier(0.68,-0.55,0.265,1.55)",
        "easeOutElastic": "cubic-bezier(0.47,1.64,0.41,0.8)"
    };
    var cssEase = easingMap[transitionEasing] || "ease";

    // Efekt
    if (effectType === "slide") {
        $s.addClass('slideEffect')
            .css('transition', 'transform ' + transitionDuration + 's ' + cssEase);
    } else {
        $c.addClass(effectType);
        $sl.css('transition', 'all ' + transitionDuration + 's ' + cssEase);
    }

    // Progress animasyonu
    function startProgress() {
        if (!showProgressBar) return;
        $fill.css('transition', 'none').width(0);
        setTimeout(function() {
            $fill.css('transition', 'width ' + autoSlideInterval + 'ms ' + progressEasing)
                .width('100%');
        }, 50);
    }

    for (var i = 0; i < total; i++) {
        $navContainer.append('<div class="navigator-dot"></div>');
    }
    var $dots = $navContainer.find('.navigator-dot');

    // Slayt göster

    function show(i) {
        // thumb vurgusu
        if (showThumbnails) {
            $th.removeClass('active').eq(i).addClass('active');

            // Thumbnail scroll ayarı
            var $activeThumb = $th.eq(i);
            var container = $tc[0];
            var offsetLeft = $activeThumb[0].offsetLeft;
            var thumbWidth = $activeThumb.outerWidth(true);
            var containerWidth = $tc.width();

            $tc.animate({
                scrollLeft: offsetLeft - containerWidth / 2 + thumbWidth / 2
            }, 300);
        }

        try {
            $dots.removeClass('active').eq(i).addClass('active');
        } catch (e) { }

        // efektler
        if (effectType === "slide") {
            $s.css('transform', 'translateX(' + (-i * 100) + '%)');
        } else {
            $sl.removeClass('active');
            var $cur = $sl.eq(i).addClass('active');
            if (effectType === "zoom") {
                $sl.css('transform', 'scale(1)');
                $cur.css('transform', 'scale(1.05)');
            }
            if (effectType === "slideDown") {
                $sl.css('transform', 'translateY(-100%)');
                $cur.css('transform', 'translateY(0)');
            }
        }
        startProgress();
    }

    // Oklara tıkla
    $('.arrow-left').click(function() {
        idx = idx > 0 ? idx - 1 : total - 1; 
        show(idx); 
        resetAuto();
    });
    $('.arrow-right').click(function() {
        idx = idx < total - 1 ? idx + 1 : 0; 
        show(idx); 
        resetAuto();
    });
    // swipe
    $c.on('swipeleft', function() { 
        $('.arrow-right').click();
    });
    $c.on('swiperight', function() { 
        $('.arrow-left').click();
    });
    // thumb click
    $th.click(function () {
        idx = $(this).index();
        show(idx);
        resetAuto();
    });
    // resme click
    $sl.click(function () {
        console.log('Tıklanan indeks:', $(this).index());
    });


    // Dota tıklayınca slide göster
    $dots.click(function () {
        idx = $(this).index();
        show(idx);
        resetAuto();
    });

    // otomatik
    function resetAuto() {
        clearInterval(autoT);
        autoT = setInterval(function() {
            idx = idx < total - 1 ? idx + 1 : 0;
            show(idx);
        }, autoSlideInterval);
    }

    // ilk göster & başlat
    show(idx);
    resetAuto();
});

$(document).ready(function () {
    var height = parseFloat($('.slider').height());
    height = height / 2;
    $('.arrow').css('top', height + 'px');
});

