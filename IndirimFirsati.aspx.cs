﻿using CoreHelpers;
using Digiturk.Workflow.Digiflow.WebCore.Digiport;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Digiport_IndirimFirsati : ComponentBasePage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        string viewMode = string.Empty;
        int pageSize1 = 0, pageSize2 = 0;
        if (!string.IsNullOrEmpty(Request.QueryString["view-mode"]))
            viewMode = Request.QueryString["view-mode"];
        if (!string.IsNullOrEmpty(Request.QueryString["page-size1"]))
            pageSize1 = ConvertionHelper.ConvertValue<int>(Request.QueryString["page-size1"]);
        if (!string.IsNullOrEmpty(Request.QueryString["page-size2"]))
            pageSize2 = ConvertionHelper.ConvertValue<int>(Request.QueryString["page-size2"]);
        lblLinkler.Text = componentTitle;
        hyperIndirimFirsatiFullView.Text = componentTitle;
        GridYukle_IndirimFirsati(viewMode.ToLower() == "all", pageSize1, pageSize2);
    }

    public string IndirimFirsatiClickEvent(string contentId, int componentClickAction, string contentTargetLink, string popupWindowWidth, string popupWindowHeight)
    {
        return componentBase.GetComponentClickEvent("DiscountOpportunity", componentClickAction, contentId, contentTargetLink, popupWindowWidth, popupWindowHeight, ConfigurationManager.AppSettings["DigiportContentLink"], ConfigurationManager.AppSettings["AjansContentLink"]);
    }
    public string BaslikKlas(int componentClickAction)
    {
        string donen = "clamp";
        if (((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Olay_Yok) != componentClickAction)
            donen = donen + " underline_cursor_color1";
        return donen;
    }


    private void GridYukle_IndirimFirsati(bool allColumnsVisible, int pageSize1, int pageSize2)
    {
        DataTable dt = DigiportMenuDisplayHelpers.IndirimFirsatiHelper.IndirimFirsatiTabloGetir();
        gridViewIndirimFirsati.DataSource = dt;
        gridViewIndirimFirsati.DataBind();
        gridViewIndirimFirsati.Columns["BITIS_TARIHI"].Visible = allColumnsVisible;
        gridViewIndirimFirsati.Columns["KURUM_ADI"].Visible = allColumnsVisible;
        gridViewIndirimFirsati.SettingsPager.PageSize = pageSize1;
        lblLinkler.Visible = allColumnsVisible;
        hyperIndirimFirsatiFullView.Visible = !allColumnsVisible;
        hyperIndirimFirsatiFullView.NavigateUrl = ConfigurationManager.AppSettings["DigiportContentLink"] + "?component-type=" + componentType.ToString() + "&component-title=" + componentTitle + "&component-type-string=DiscountOpportunity&view-mode=all&page-size=" + pageSize2.ToString();
        gridViewIndirimFirsati.Visible = dt.Rows.Count > 0;
    }

}