#!/usr/bin/env python3
"""
Fix parsing errors in test files, particularly malformed vi.mock() declarations
and incorrect describe/it block syntax.
"""

import os
import re
import subprocess
from typing import List, Dict

def fix_vi_mock_pattern(content: str) -> str:
    """Fix malformed vi.mock() patterns in test files."""
    # Pattern for vi.mock('module', () => ({}))
    # followed by properties that should be inside the object
    pattern = r"vi\.mock\((.*?),\s*\(\)\s*=>\s*\(\{\}\)\)\s*\n(\s+)(\w+):"
    
    def replace_mock(match):
        module = match.group(1)
        indent = match.group(2)
        prop = match.group(3)
        return f"vi.mock({module}, () => ({{\n{indent}{prop}:"
    
    content = re.sub(pattern, replace_mock, content)
    
    # Fix incorrect arrow function syntax in mocks
    # Pattern: () => () should be () => (
    content = re.sub(r"=>\s*\(\)\s*\n(\s*)<", r"=> (\n\1<", content)
    
    # Fix map functions with incorrect syntax
    content = re.sub(r"\.map\((.*?)\)\s*=>\s*\(\)\)", r".map(\1) => ()", content)
    
    return content

def fix_describe_it_blocks(content: str) -> str:
    """Fix malformed describe() and it() blocks."""
    # Fix patterns like describe('name', () => {)
    content = re.sub(r"(describe|it|beforeEach|afterEach|beforeAll|afterAll)\((.*?)\)\s*=>\s*\{\)", r"\1(\2) => {", content)
    
    return content

def fix_rerender_calls(content: str) -> str:
    """Fix incorrect rerender() calls."""
    # Fix rerender() without call parentheses
    pattern = r"rerender\(\)\s*\n\s*<"
    replacement = r"rerender(\n        <"
    content = re.sub(pattern, replacement, content)
    
    return content

def fix_await_waitfor_blocks(content: str) -> str:
    """Fix malformed await waitFor blocks."""
    # Fix waitFor(() => {)
    content = re.sub(r"await waitFor\(\(\)\s*=>\s*\{\)", r"await waitFor(() => {", content)
    
    return content

def remove_extra_closing_tags(content: str) -> str:
    """Remove extra closing JSX tags and closing braces at the end of files."""
    lines = content.split('\n')
    
    # Find the last actual code line (not just closing tags or braces)
    last_code_line = -1
    for i in range(len(lines) - 1, -1, -1):
        line = lines[i].strip()
        if line and not (line.startswith('</') or line == '}' or line == ')' or line == '})' or line == '});'):
            last_code_line = i
            break
    
    # Check if there are excessive closing lines after the last code
    if last_code_line > 0:
        excessive_closings = []
        for i in range(last_code_line + 1, len(lines)):
            line = lines[i].strip()
            if line and (line.startswith('</') or line == '}'):
                excessive_closings.append(i)
        
        # If we have more than 10 consecutive closing lines, they're probably errors
        if len(excessive_closings) > 10:
            # Keep only necessary closing braces/parentheses
            cleaned_lines = lines[:last_code_line + 1]
            
            # Count open braces/parentheses to determine how many closes we need
            open_count = 0
            for line in cleaned_lines:
                open_count += line.count('{') - line.count('}')
                open_count += line.count('(') - line.count(')')
            
            # Add necessary closing braces
            if open_count > 0:
                cleaned_lines.append('})' * open_count)
            
            return '\n'.join(cleaned_lines)
    
    return content

def fix_test_file(filepath: str) -> bool:
    """Fix parsing errors in a test file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply fixes
        content = fix_vi_mock_pattern(content)
        content = fix_describe_it_blocks(content)
        content = fix_rerender_calls(content)
        content = fix_await_waitfor_blocks(content)
        content = remove_extra_closing_tags(content)
        
        # Write back if changed
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
    except Exception as e:
        print(f"Error fixing {filepath}: {e}")
    
    return False

def find_test_files() -> List[str]:
    """Find all test files with parsing errors."""
    test_files = []
    
    # Run ESLint to get parsing errors
    cmd = ["yarn", "lint", "--format", "json"]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.stdout:
        try:
            import json
            results = json.loads(result.stdout)
            for file_result in results:
                if file_result.get('messages'):
                    for msg in file_result['messages']:
                        if 'Parsing error' in msg.get('message', ''):
                            filepath = file_result['filePath']
                            if '.test.ts' in filepath or '.test.tsx' in filepath:
                                if filepath not in test_files:
                                    test_files.append(filepath)
        except:
            pass
    
    return test_files

def main():
    print("🔍 Finding test files with parsing errors...")
    
    test_files = find_test_files()
    
    if not test_files:
        # Fallback: find all test files
        for root, dirs, files in os.walk('src'):
            for file in files:
                if file.endswith('.test.tsx') or file.endswith('.test.ts'):
                    test_files.append(os.path.join(root, file))
    
    print(f"Found {len(test_files)} test files to check")
    
    fixed_count = 0
    for filepath in test_files:
        if os.path.exists(filepath):
            print(f"  Checking {filepath}...")
            if fix_test_file(filepath):
                print(f"    ✅ Fixed parsing errors")
                fixed_count += 1
    
    print(f"\n✨ Fixed {fixed_count} test files")

if __name__ == "__main__":
    main()