<%@ Page Title="<%$ Resources:DigiportAdminResource, UserAssignment_PageTitle %>" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" EnableEventValidation="false"
    CodeBehind="UserAssignment.aspx.cs" Inherits="AracTakipSistemi.AdminPages.DigiportMenu.UserAssignment" %>

<%@ MasterType VirtualPath="~/Site.Master" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:UpdatePanel ID="upnlUserAssignment" runat="server" UpdateMode="Conditional">
        <ContentTemplate>
            <style>
                /* ===== Base Layout & Components ===== */
                .app-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 1.5rem;
                    font-family: var(--font-sans);
                }

                @media (min-width: 768px) {
                    .app-container {
                        padding: 2rem;
                    }
                }

                /* Card Components */
                .card {
                    background-color: white;
                    border-radius: var(--radius-lg);
                    box-shadow: var(--shadow-md);
                    overflow: hidden;
                    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
                    margin-bottom: 1.5rem;
                    border: 1px solid var(--gray-100);
                    position: relative;
                }

                    .card:hover {
                        box-shadow: var(--shadow-lg);
                    }

                .card-header {
                    padding: 1.25rem 1.5rem;
                    border-bottom: 1px solid var(--gray-100);
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    background-color: white;
                    position: relative;
                }

                    .card-header::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        height: 100%;
                        width: 4px;
                        background-color: var(--primary);
                        border-radius: 4px 0 0 4px;
                    }

                .card-title {
                    margin: 0;
                    font-size: 1.1rem;
                    font-weight: 600;
                    color: var(--gray-800);
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                    .card-title .icon {
                        color: var(--primary);
                        font-size: 1.2em;
                        opacity: 0.9;
                    }

                .card-body {
                    padding: 1.5rem;
                }

                .card-footer {
                    padding: 1.25rem 1.5rem;
                    border-top: 1px solid var(--gray-100);
                    background-color: var(--gray-50);
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                /* Typography */
                .page-title-style {
                    margin: 0 0 0.5rem 0;
                    font-size: 1.75rem;
                    font-weight: 700;
                    color: var(--gray-900);
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                    .page-title-style .icon {
                        color: var(--primary);
                    }

                .page-description {
                    margin: 0 0 2rem 0;
                    color: var(--gray-600);
                    max-width: 750px;
                    line-height: 1.6;
                }

                .section-title {
                    margin: 1.5rem 0 1rem;
                    font-size: 1.1rem;
                    font-weight: 600;
                    color: var(--gray-800);
                }

                /* Form Controls */
                .form-group {
                    margin-bottom: 1.25rem;
                }

                .form-label {
                    display: block;
                    margin-bottom: 0.5rem;
                    font-weight: 500;
                    color: var(--gray-700);
                    font-size: 0.9rem;
                }

                /* Search container styles */
                .search-container {
                    position: relative;
                    width: 100%;
                    max-width: 300px;
                    margin-right: 40px !important;
                }

                .search-icon {
                    position: absolute;
                    left: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: var(--gray-500);
                    z-index: 1;
                }

                .search-input {
                    width: 100%;
                    padding: 0.625rem 0.875rem 0.625rem 35px !important;
                    font-size: 0.9rem;
                    border: 1px solid var(--gray-300);
                    border-radius: var(--radius-md);
                    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
                    padding-left: 34px !important;
                }

                    .search-input:focus {
                        border-color: var(--primary-300);
                        box-shadow: 0 0 0 3px var(--primary-100);
                        outline: 0;
                    }

                .form-hint {
                    margin-top: 0.375rem;
                    font-size: 0.8125rem;
                    color: var(--gray-600);
                }

                .form-control {
                    display: block;
                    width: 100%;
                    padding: 0.625rem 0.875rem;
                    font-size: 0.9rem;
                    font-weight: 400;
                    line-height: 1.5;
                    color: var(--gray-800);
                    background-color: white;
                    background-clip: padding-box;
                    border: 1px solid var(--gray-300);
                    border-radius: var(--radius-md);
                    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
                }

                    .form-control:focus {
                        border-color: var(--primary-300);
                        box-shadow: 0 0 0 3px var(--primary-100);
                        outline: 0;
                    }

                    .form-control::placeholder {
                        color: var(--gray-500);
                        opacity: 1;
                    }

                .form-select {
                    display: block !important;
                    width: 100% !important;
                    padding: 0.625rem 2.25rem 0.625rem 0.875rem !important;
                    font-size: 0.9rem !important;
                    font-weight: 400 !important;
                    line-height: 1.5 !important;
                    color: var(--gray-800) !important;
                    background-color: white !important;
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%235c2d91' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e") !important;
                    background-repeat: no-repeat !important;
                    background-position: right 0.875rem center !important;
                    background-size: 16px 12px !important;
                    border: 1px solid var(--gray-300) !important;
                    border-radius: var(--radius-md) !important;
                    appearance: none !important;
                    transition: border-color var(--transition-fast), box-shadow var(--transition-fast) !important;
                }

                    .form-select:focus {
                        border-color: var(--primary-300);
                        box-shadow: 0 0 0 3px var(--primary-100);
                        outline: 0;
                    }

                /* Button Styles */
                .btn {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 500;
                    line-height: 1.5;
                    color: var(--gray-700);
                    text-align: center;
                    vertical-align: middle;
                    cursor: pointer;
                    user-select: none;
                    background-color: transparent;
                    border: 1px solid transparent;
                    padding: 0.625rem 1.25rem;
                    font-size: 0.9rem;
                    border-radius: var(--radius-md);
                    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                    gap: 0.5rem;
                }

                    .btn:focus {
                        outline: 0;
                        box-shadow: 0 0 0 3px var(--gray-200);
                    }

                .btn-primary {
                    color: white !important;
                    background-color: var(--primary) !important;
                    border-color: var(--primary) !important;
                }

                    .btn-primary:hover {
                        background-color: var(--primary-800) !important;
                        border-color: var(--primary-800) !important;
                    }

                    .btn-primary:focus {
                        box-shadow: 0 0 0 3px var(--primary-200) !important;
                    }

                .btn-success {
                    color: white !important;
                    background-color: var(--success) !important;
                    border-color: var(--success) !important;
                }

                    .btn-success:hover {
                        background-color: var(--success-600) !important;
                        border-color: var (--success-600) !important;
                    }

                    .btn-success:focus {
                        box-shadow: 0 0 0 3px var(--success-100) !important;
                    }

                .btn-danger {
                    color: white !important;
                    background-color: var(--danger) !important;
                    border-color: var(--danger) !important;
                }

                    .btn-danger:hover {
                        background-color: var(--danger-600) !important;
                        border-color: var(--danger-600) !important;
                    }

                    .btn-danger:focus {
                        box-shadow: 0 0 0 3px var(--danger-100) !important;
                    }

                .btn-secondary {
                    color: var(--gray-800) !important;
                    background-color: white !important;
                    border-color: var(--gray-300) !important;
                }

                    .btn-secondary:hover {
                        background-color: var(--gray-50) !important;
                        border-color: var(--gray-400) !important;
                    }

                .btn-sm {
                    padding: 0.4rem 0.75rem !important;
                    font-size: 0.8125rem !important;
                    border-radius: var(--radius-sm) !important;
                }

                .btn-lg {
                    padding: 0.75rem 1.5rem !important;
                    font-size: 1rem !important;
                    border-radius: var(--radius-lg) !important;
                }

                .btn-icon {
                    width: 2.5rem !important;
                    height: 2.5rem !important;
                    padding: 0 !important;
                    display: inline-flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                }

                .btn-icon-sm {
                    width: 2rem;
                    height: 2rem;
                    font-size: 0.8125rem;
                }

                /* Assignment Type Selector */
                .selection-cards {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 1rem;
                    margin-bottom: 1.5rem;
                }

                .selection-card {
                    flex: 1;
                    min-width: 180px;
                    position: relative;
                    padding: 1.25rem;
                    border: 2px solid var(--gray-200);
                    border-radius: var(--radius-lg);
                    cursor: pointer;
                    transition: all var(--transition-normal);
                    text-align: center;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    background-color: white;
                }

                    .selection-card:hover {
                        border-color: var(--primary-300);
                        box-shadow: 0 3px 15px rgba(0,0,0,0.05);
                        transform: translateY(-2px);
                    }

                    .selection-card.active {
                        border-color: var(--primary);
                        background-color: var(--primary-50);
                        box-shadow: 0 4px 20px rgba(92, 45, 145, 0.1);
                    }

                .selection-card-icon {
                    width: 60px;
                    height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: var(--primary-100);
                    color: var(--primary-700);
                    border-radius: 50%;
                    margin-bottom: 1rem;
                    font-size: 1.5rem;
                    transition: all var(--transition-normal);
                }

                .selection-card.active .selection-card-icon {
                    background-color: var(--primary);
                    color: white;
                    transform: scale(1.1);
                }

                .selection-card-title {
                    font-weight: 600;
                    font-size: 1.1rem;
                    color: var(--gray-800);
                    margin-bottom: 0.5rem;
                }

                .selection-card-desc {
                    font-size: 0.875rem;
                    color: var(--gray-600);
                    margin-bottom: 0;
                }

                .selection-card-radio {
                    position: absolute;
                    opacity: 0;
                    width: 0;
                    height: 0;
                }

                .selection-card::after {
                    content: '';
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    border: 2px solid var(--gray-300);
                    background-color: white;
                    transition: all var(--transition-fast);
                }

                .selection-card.active::after {
                    border-color: var(--primary);
                    background-color: var(--primary);
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M4 8l3 3 5-6'/%3e%3c/svg%3e");
                    background-size: 80%;
                    background-repeat: no-repeat;
                    background-position: center;
                }

                .d-none {
                    display: none;
                }

                /* Toggle Switch */
                .toggle-container {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                }

                .toggle-switch {
                    position: relative;
                    display: inline-block;
                    width: 52px;
                    height: 28px;
                }

                    .toggle-switch input {
                        opacity: 0;
                        width: 0;
                        height: 0;
                    }

                .toggle-slider {
                    position: absolute;
                    cursor: pointer;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-color: var(--gray-300);
                    transition: var(--transition-normal);
                    border-radius: 34px;
                }

                    .toggle-slider:before {
                        position: absolute;
                        content: "";
                        height: 20px;
                        width: 20px;
                        left: 4px;
                        bottom: 4px;
                        background-color: white;
                        transition: var(--transition-normal);
                        border-radius: 50%;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                    }

                input:checked + .toggle-slider {
                    background-color: var(--success);
                }

                    input:checked + .toggle-slider:before {
                        transform: translateX(24px);
                    }

                .toggle-label {
                    font-weight: 500;
                    font-size: 0.9rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .toggle-status {
                    padding: 0.25rem 0.75rem;
                    border-radius: var(--radius-full);
                    font-size: 0.8125rem;
                    font-weight: 600;
                    background-color: var(--success-50);
                    color: var(--success-600);
                    transition: var(--transition-fast);
                }

                    .toggle-status.inactive {
                        background-color: var(--danger-50);
                        color: var(--danger-600);
                    }

                /* Data Grid / Table */
                .data-table-container {
                    border-radius: var(--radius-lg);
                    overflow: hidden;
                    border: 1px solid var(--gray-200);
                    background-color: white;
                    margin-bottom: 1.5rem;
                }

                .data-table {
                    width: 100%;
                    border-collapse: separate;
                    border-spacing: 0;
                }

                    .data-table th {
                        background-color: var(--gray-50);
                        color: var(--gray-700);
                        font-size: 0.8125rem;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 0.02em;
                        padding: 1rem;
                        text-align: left;
                        border-bottom: 2px solid var(--gray-200);
                        position: relative;
                    }

                /* Standard GridView Styles */
                .grid-header {
                    background-color: #C60C30;
                    color: white;
                    font-weight: bold;
                    padding: 10px;
                    text-align: left;
                }

                    .grid-header a {
                        color: white;
                        text-decoration: none;
                    }

                        .grid-header a:hover {
                            text-decoration: underline;
                        }

                .grid-row, .grid-row-alt {
                    border-bottom: 1px solid #ddd;
                }

                    .grid-row:hover, .grid-row-alt:hover {
                        background-color: #f5f5f5;
                        cursor: pointer;
                    }

                    .grid-row td, .grid-row-alt td {
                        padding: 10px;
                        vertical-align: middle;
                    }

                .grid-row-alt {
                    background-color: #f9f9f9;
                }

                .grid-row-selected {
                    background-color: #e0e0e0;
                }

                .grid-pager {
                    background-color: #f0f0f0;
                    padding: 8px;
                    text-align: center;
                }

                    .grid-pager a, .grid-pager span {
                        margin: 0 5px;
                        padding: 5px 10px;
                        border-radius: 3px;
                    }

                    .grid-pager a {
                        background-color: white;
                        border: 1px solid #ddd;
                        color: #42145F;
                        text-decoration: none;
                    }

                        .grid-pager a:hover {
                            background-color: #42145F;
                            color: white;
                        }

                    .grid-pager span {
                        background-color: #42145F;
                        border: 1px solid #42145F;
                        color: white;
                    }

                .action-buttons-container {
                    display: flex;
                    justify-content: center;
                }

                .btn-action {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 32px;
                    height: 32px;
                    border-radius: 4px;
                    border: none;
                    cursor: pointer;
                }

                    .btn-action img {
                        width: 16px;
                        height: 16px;
                    }

                .btn-action-primary {
                    background-color: #42145F;
                }

                    .btn-action-primary:hover {
                        background-color: #33104a;
                    }

                .btn-action-danger {
                    background-color: #C60C30;
                }

                    .btn-action-danger:hover {
                        background-color: #a00a28;
                    }

                .empty-data-row td {
                    padding: 20px;
                    text-align: center;
                }

                .data-table th:not(:last-child)::after {
                    content: '';
                    position: absolute;
                    top: 30%;
                    right: 0;
                    height: 40%;
                    width: 1px;
                    background-color: var(--gray-300);
                }

                .data-table td {
                    padding: 1rem;
                    border-bottom: 1px solid var(--gray-200);
                    color: var(--gray-800);
                    vertical-align: middle;
                    font-size: 0.9rem;
                    transition: background-color var(--transition-fast);
                }

                .data-table tr {
                    transition: background-color var(--transition-fast);
                }

                .data-table tbody tr:hover {
                    background-color: var(--primary-50);
                }

                .data-table tr.selected {
                    background-color: var(--primary-100);
                }

                .entity-name {
                    font-weight: 500;
                    color: var(--gray-800);
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .entity-icon {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: var(--primary-100);
                    color: var(--primary-700);
                    font-size: 0.875rem;
                }

                    .entity-icon.group {
                        background-color: var(--gray-100);
                        color: var(--gray-700);
                    }

                /* Status Badge */
                .badge {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0.35em 0.75em;
                    font-size: 0.75rem;
                    font-weight: 600;
                    border-radius: var(--radius-full);
                    white-space: nowrap;
                    text-transform: uppercase;
                    letter-spacing: 0.02em;
                }

                .badge-success {
                    background-color: var (--success-50);
                    color: var (--success-600);
                    border: 1px solid var(--success-100);
                }

                    .badge-success::before {
                        content: '';
                        display: inline-block;
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                        background-color: var(--success);
                        margin-right: 0.5rem;
                    }

                .badge-danger {
                    background-color: var(--danger-50);
                    color: var(--danger-600);
                    border: 1px solid var(--danger-100);
                }

                    .badge-danger::before {
                        content: '';
                        display: inline-block;
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                        background-color: var(--danger);
                        margin-right: 0.5rem;
                    }

                /* Action Buttons */
                .action-group {
                    display: flex;
                    gap: 0.5rem;
                }

                .btn-action {
                    width: 2rem;
                    height: 2rem;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: var(--radius-md);
                    background-color: white;
                    color: var(--gray-700);
                    border: 1px solid var(--gray-300);
                    font-size: 0.875rem;
                    cursor: pointer;
                    transition: all var(--transition-fast);
                }



                .btn-action-danger {
                    color: var(--danger);
                    border-color: var(--danger-200);
                }

                    .btn-action-danger:hover {
                        background-color: var(--danger);
                        color: white;
                        border-color: var(--danger);
                    }

                /* Action buttons container */
                .action-buttons-container {
                    display: flex;
                    gap: 0.5rem;
                    justify-content: center;
                    padding: 0.5rem;
                }

                /* Action buttons */
                .btn-action {
                    width: 2rem;
                    height: 2rem;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: var(--radius-md);
                    background-color: white;
                    color: var(--gray-700);
                    border: 1px solid var(--gray-300);
                    font-size: 0.875rem;
                    cursor: pointer;
                    transition: all var(--transition-fast);
                    text-decoration: none;
                }

                    .btn-action i {
                        font-size: 14px;
                    }



                /* Style for delete button */
                .btn-action-danger {
                    color: var(--danger);
                    border-color: var(--danger-200);
                }

                    .btn-action-danger:hover {
                        background-color: var(--danger);
                        border-color: var(--danger);
                        color: white;
                    }

                        .btn-action-danger:hover i {
                            color: white;
                        }

                /* Style for enable/disable buttons */
                .btn-action-warning {
                    color: var(--warning, #f39c12);
                    border-color: var(--warning-200, #fce4b6);
                }

                    .btn-action-warning:hover {
                        background-color: var(--warning, #f39c12);
                        border-color: var(--warning, #f39c12);
                        color: white;
                    }

                        .btn-action-warning:hover i {
                            color: white;
                        }

                .btn-action-success {
                    color: var(--success);
                    border-color: var(--success-200);
                }

                    .btn-action-success:hover {
                        background-color: var(--success);
                        border-color: var(--success);
                        color: white;
                    }

                        .btn-action-success:hover i {
                            color: white;
                        }


                /* Pagination */
                .pagination {
                    display: flex !important;
                    justify-content: center !important;
                    gap: 0.25rem !important;
                    margin-top: 1.5rem !important;
                }

                .page-item {
                    list-style: none;
                }

                .page-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0.375rem 0.75rem;
                    min-width: 2.25rem;
                    min-height: 2.25rem;
                    line-height: 1.25;
                    color: var(--gray-800);
                    background-color: white;
                    border: 1px solid var(--gray-300);
                    border-radius: var(--radius-md);
                    cursor: pointer;
                    transition: all var(--transition-fast);
                    font-size: 0.875rem;
                    text-decoration: none;
                }

                    .page-link:hover {
                        background-color: var(--gray-100);
                        border-color: var(--gray-400);
                        z-index: 2;
                        color: var(--primary);
                    }

                .page-link-active {
                    color: white;
                    background-color: var(--primary);
                    border-color: var(--primary);
                    z-index: 3;
                }

                    .page-link-active:hover {
                        color: white;
                        background-color: var(--primary-800);
                        border-color: var(--primary-800);
                    }

                /* Empty State */
                .empty-state {
                    padding: 3rem 2rem;
                    text-align: center;
                    background-color: white;
                    border: 1px dashed var(--gray-300);
                    border-radius: var(--radius-lg);
                    margin: 1rem 0;
                }

                .empty-state-icon {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 80px;
                    height: 80px;
                    border-radius: 50%;
                    background-color: var(--primary-50);
                    color: var(--primary);
                    font-size: 2rem;
                    margin-bottom: 1.5rem;
                }

                .empty-state-title {
                    font-size: 1.25rem;
                    font-weight: 600;
                    color: var(--gray-800);
                    margin-bottom: 0.75rem;
                }

                .empty-state-text {
                    color: var(--gray-600);
                    max-width: 350px;
                    margin: 0 auto 1.5rem;
                }

                /* Notification Badge - For Activity */
                .notification-badge {
                    position: absolute;
                    top: -5px;
                    right: -5px;
                    width: 18px;
                    height: 18px;
                    border-radius: 50%;
                    background-color: var(--primary-500);
                    color: white;
                    font-size: 0.6875rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 700;
                    border: 2px solid white;
                    animation: pulse 2s infinite;
                }

                @keyframes pulse {
                    0% {
                        box-shadow: 0 0 0 0 rgba(92, 45, 145, 0.4);
                    }

                    70% {
                        box-shadow: 0 0 0 8px rgba(92, 45, 145, 0);
                    }

                    100% {
                        box-shadow: 0 0 0 0 rgba(92, 45, 145, 0);
                    }
                }

                /* Info Alert */
                .info-alert {
                    display: flex;
                    align-items: flex-start;
                    gap: 1rem;
                    padding: 1rem;
                    background-color: var(--primary-50);
                    border: 1px solid var(--primary-200);
                    border-radius: var(--radius-md);
                    margin-bottom: 1.5rem;
                }

                .info-alert-icon {
                    color: var(--primary);
                    font-size: 1.25rem;
                    flex-shrink: 0;
                    margin-top: 0.125rem;
                }

                .info-alert-content {
                    flex: 1;
                }

                .info-alert-title {
                    font-weight: 600;
                    color: var(--primary-900);
                    font-size: 0.9375rem;
                    margin-bottom: 0.25rem;
                }

                .info-alert-text {
                    color: var(--primary-800);
                    font-size: 0.875rem;
                    margin-bottom: 0;
                }

                /* Grid Layout */
                .grid {
                    display: grid;
                    grid-template-columns: repeat(12, 1fr);
                    gap: 1.5rem;
                }

                .col-span-12 {
                    grid-column: span 12 / span 12;
                }

                .col-span-6 {
                    grid-column: span 6 / span 6;
                }

                .col-span-4 {
                    grid-column: span 4 / span 4;
                }

                .col-span-3 {
                    grid-column: span 3 / span 3;
                }

                @media (max-width: 1024px) {
                    .lg\:col-span-6 {
                        grid-column: span 6 / span 6;
                    }

                    .lg\:col-span-12 {
                        grid-column: span 12 / span 12;
                    }
                }

                @media (max-width: 768px) {
                    .md\:col-span-12 {
                        grid-column: span 12 / span 12;
                    }
                }

                /* Utils */
                .flex {
                    display: flex;
                }

                .items-center {
                    align-items: center;
                }

                .justify-between {
                    justify-content: space-between;
                }

                .gap-2 {
                    gap: 0.5rem;
                }

                .gap-4 {
                    gap: 1rem;
                }

                .mb-0 {
                    margin-bottom: 0;
                }

                .mb-2 {
                    margin-bottom: 0.5rem;
                }

                .mb-4 {
                    margin-bottom: 1rem;
                }

                .mb-6 {
                    margin-bottom: 1.5rem;
                }

                .mt-0 {
                    margin-top: 0;
                }

                .mt-2 {
                    margin-top: 0.5rem;
                }

                .mt-4 {
                    margin-top: 1rem;
                }

                .ml-auto {
                    margin-left: auto;
                }

                .text-sm {
                    font-size: 0.875rem;
                }

                .text-xs {
                    font-size: 0.75rem;
                }

                .text-primary {
                    color: var(--primary);
                }

                .text-success {
                    color: var(--success);
                }

                .text-danger {
                    color: var(--danger);
                }

                .text-gray {
                    color: var(--gray-600);
                }

                .font-medium {
                    font-weight: 500;
                }

                .font-semibold {
                    font-weight: 600;
                }

                .font-bold {
                    font-weight: 700;
                }

                .uppercase {
                    text-transform: uppercase;
                }

                .bg-white {
                    background-color: white;
                }

                .bg-gray-50 {
                    background-color: var(--gray-50);
                }

                .bg-primary-50 {
                    background-color: var(--primary-50);
                }

                .border {
                    border: 1px solid var(--gray-200);
                }

                .rounded {
                    border-radius: var(--radius-md);
                }

                .shadow-sm {
                    box-shadow: var(--shadow-sm);
                }

                .relative {
                    position: relative;
                }

                .line-clamp-1 {
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    line-clamp: 1; /* Standard property for compatibility */
                    max-height: 1.5em; /* Fallback for browsers that don't support line-clamp */
                    text-overflow: ellipsis; /* Show ellipsis for overflowing text */
                }

                /* Filter Controls */
                .filter-container {
                    background-color: white;
                    border: 1px solid var(--gray-200);
                    border-radius: var(--radius-lg);
                    padding: 1rem;
                    margin-bottom: 1rem;
                }

                .filter-group {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    margin-left: 20px !important;
                }

                .filter-label {
                    font-weight: 500;
                    color: var(--gray-700);
                    font-size: 0.9rem;
                    margin-bottom: 0;
                    white-space: nowrap;
                }

                .form-select-sm {
                    padding: 0.375rem 2rem 0.375rem 0.75rem !important;
                    font-size: 0.875rem !important;
                    height: 38px !important;
                    min-width: 120px !important;
                }

                /* Empty state styling */
                .empty-state {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                    padding: 3rem 1rem;
                    background-color: white;
                    border-radius: var(--radius-lg);
                    margin: 1rem 0;
                }

                .empty-state-icon {
                    font-size: 3rem;
                    color: var(--gray-400);
                    margin-bottom: 1rem;
                }

                .empty-state-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: var(--gray-800);
                    margin-bottom: 0.5rem;
                }

                .empty-state-text {
                    color: var(--gray-600);
                    max-width: 400px;
                    margin-bottom: 1.5rem;
                }

                /* Search Box */
                .search-container {
                    position: relative !important;
                    width: 300px !important;
                }

                #txtSearchAssignments {
                    padding-left: 2.5rem !important;
                }

                .search-input {
                    padding-left: 2.5rem !important;
                    border-radius: var(--radius-full) !important;
                    background-color: white !important;
                    border: 1px solid var(--gray-300) !important;
                    height: 38px !important;
                    font-size: 0.875rem !important;
                    width: 100% !important;
                    transition: all var(--transition-fast) !important;
                }

                    .search-input:focus {
                        border-color: var(--primary-300) !important;
                        box-shadow: 0 0 0 3px var(--primary-100) !important;
                        width: 320px !important;
                    }

                #txtSearchAssignments {
                    padding-left: 40px !important;
                }

                .search-icon {
                    position: absolute !important;
                    left: 1rem !important;
                    top: 50% !important;
                    transform: translateY(-50%) !important;
                    color: var(--gray-500) !important;
                    pointer-events: none !important;
                }


                /* Target the delete button */
                .dxgvCommandColumnItem:has(img[src*="delete.png"]) {
                    width: 2rem;
                    height: 2rem;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: var(--radius-md);
                    background-color: white;
                    color: var(--danger);
                    border: 1px solid var(--danger-200);
                }

                    .dxgvCommandColumnItem:has(img[src*="delete.png"]):hover {
                        background-color: var(--danger);
                        border-color: var(--danger);
                    }

                        .dxgvCommandColumnItem:has(img[src*="delete.png"]):hover img {
                            filter: brightness(0) invert(1);
                        }
            </style>
            <div class="app-container">
                <h1 class="page-title-style">
                    <i class="fas fa-user-lock icon"></i>
                    <asp:Literal ID="litPageTitle" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_MainTitle %>" />
                </h1>
                <p class="page-description">
                    <asp:Literal ID="litPageDescription" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_MainDescription %>" />
                </p>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-file-alt icon"></i>
                            <asp:Literal ID="litPageSelectionTitle" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_PageSelectionTitle %>" />
                        </h2>
                    </div>
                    <div class="card-body">
                        <div class="grid">
                            <div class="col-span-12 lg:col-span-6">
                                <div class="form-group mb-0">
                                    <label for="<%= drpName.ClientID %>" class="form-label">
                                        <asp:Literal ID="litPageSelectionLabel" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_PageSelectionLabel %>" />:
                                    </label>
                                    <div class="flex items-center gap-2">
                                        <div class="flex-1">
                                            <asp:DropDownList ID="drpName" runat="server" CssClass="form-select"
                                                AutoPostBack="true" OnSelectedIndexChanged="drpName_SelectedIndexChanged"
                                                DataTextField="NAME" DataValueField="ID">
                                            </asp:DropDownList>
                                            <asp:RequiredFieldValidator ID="rfvName" runat="server"
                                                ControlToValidate="drpName" InitialValue="0"
                                                ErrorMessage="<%$ Resources:DigiportAdminResource, UserAssignment_PageSelectionRequired %>"
                                                Display="Dynamic" ValidationGroup="SaveValidation"
                                                CssClass="text-danger mt-2 text-sm font-medium" />
                                        </div>
                                        <button type="button" class="btn btn-icon btn-secondary" title="<asp:Literal runat='server' Text="<%$ Resources:DigiportAdminResource, UserAssignment_ShowPageInfo%>" />">
                                            <i class="fas fa-info"></i>
                                        </button>
                                    </div>
                                    <p class="form-hint">
                                        <i class="fas fa-info-circle"></i>
                                        <asp:Literal ID="litPageSelectionHint" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_PageSelectionHint %>" />
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <asp:Panel ID="pnlAssignmentSection" runat="server" Visible="false">
                    <div class="card" id="divAssignmentSection">
                        <div class="card-header">
                            <h2 class="card-title">
                                <i class="fas fa-user-plus icon"></i>
                                <asp:Literal ID="litAssignmentTitle" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_AssignmentTitle %>" />
                            </h2>
                        </div>
                        <div class="card-body">
                            <asp:Panel ID="selectedEntityDiv" runat="server" Visible="false" CssClass="info-alert mb-6">
                                <div class="info-alert-icon">
                                    <i class="fas fa-edit"></i>
                                </div>
                                <div class="info-alert-content">
                                    <h3 class="info-alert-title">
                                        <asp:Literal ID="litEditingRecord" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_EditingRecord %>" />
                                    </h3>
                                    <p class="info-alert-text">
                                        <strong>
                                            <asp:Literal ID="litEditingAuth" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_EditingAuth %>" />:
                                        </strong>
                                        <asp:Label ID="lblSelectedEntity" runat="server" CssClass="font-medium" />
                                    </p>
                                </div>
                            </asp:Panel>

                            <div class="form-group">
                                <label class="form-label">
                                    <asp:Literal ID="litAuthTypeLabel" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_AuthTypeLabel %>" />:
                                </label>
                                <div class="selection-cards">
                                    <div class="selection-card active" id="userCard">
                                        <div class="selection-card-icon">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <h3 class="selection-card-title">
                                            <asp:Literal ID="litUserAuthTitle" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_UserAuthTitle %>" />
                                        </h3>
                                        <p class="selection-card-desc">
                                            <asp:Literal ID="litUserAuthDesc" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_UserAuthDesc %>" />
                                        </p>
                                        <asp:RadioButton ID="rbUser" runat="server" GroupName="AssignmentType"
                                            Value="User" Checked="true" AutoPostBack="true" CssClass="selection-card-radio"
                                            OnCheckedChanged="rbAssignmentType_CheckedChanged" />
                                    </div>

                                    <div class="selection-card" id="groupCard">
                                        <div class="selection-card-icon">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <h3 class="selection-card-title">
                                            <asp:Literal ID="litGroupAuthTitle" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_GroupAuthTitle %>" />
                                        </h3>
                                        <p class="selection-card-desc">
                                            <asp:Literal ID="litGroupAuthDesc" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_GroupAuthDesc %>" />
                                        </p>
                                        <asp:RadioButton ID="rbAdGroup" runat="server" GroupName="AssignmentType"
                                            Value="Group" AutoPostBack="true" CssClass="selection-card-radio"
                                            OnCheckedChanged="rbAssignmentType_CheckedChanged" />
                                    </div>
                                </div>
                            </div>

                            <div class="grid">
                                <div class="col-span-12 lg:col-span-6">
                                    <asp:Panel ID="divUserSelection" runat="server" CssClass="form-group">
                                        <label for="<%= drpUsers.ClientID %>" class="form-label">
                                            <asp:Literal ID="litUserSelectionLabel" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_UserSelectionLabel %>" />:
                                        </label>
                                        <asp:DropDownList ID="drpUsers" runat="server" CssClass="form-select"
                                            DataTextField="NAME_SURNAME" DataValueField="F_LOGIN_ID">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvUser" runat="server" Enabled="false"
                                            ControlToValidate="drpUsers" InitialValue="0" ValidationGroup="SaveValidation"
                                            ErrorMessage="<%$ Resources:DigiportAdminResource, UserAssignment_UserSelectionRequired %>" Display="Dynamic"
                                            CssClass="text-danger mt-2 text-sm font-medium" />
                                    </asp:Panel>

                                    <asp:Panel ID="divGroupSelection" runat="server" Visible="false" CssClass="form-group">
                                        <label for="<%= drpDomain.ClientID %>" class="form-label">
                                            <asp:Literal ID="litDomainSelectionLabel" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_DomainSelectionLabel %>" />:
                                        </label>
                                        <asp:DropDownList ID="drpDomain" runat="server" CssClass="form-select"
                                            AutoPostBack="true" OnSelectedIndexChanged="drpDomain_SelectedIndexChanged">
                                            <asp:ListItem Text="<%$ Resources:DigiportAdminResource, UserAssignment_DomainSelectDefault %>" Value=""></asp:ListItem>
                                            <asp:ListItem Text="<%$ Resources:DigiportAdminResource, UserAssignment_DomainDigiturk %>" Value="digiturk"></asp:ListItem>
                                            <asp:ListItem Text="<%$ Resources:DigiportAdminResource, UserAssignment_DomainDigiturkCC %>" Value="digiturk cc"></asp:ListItem>
                                        </asp:DropDownList>

                                        <label for="<%= drpGroups.ClientID %>" class="form-label">
                                            <asp:Literal ID="litGroupSelectionLabel" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_GroupSelectionLabel %>" />:
                                        </label>
                                        <asp:DropDownList ID="drpGroups" runat="server" CssClass="form-select"
                                            DataTextField="AD_GROUP" DataValueField="AD_GROUP">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvGroup" runat="server" Enabled="false"
                                            ControlToValidate="drpGroups" InitialValue="" ValidationGroup="SaveValidation"
                                            ErrorMessage="<%$ Resources:DigiportAdminResource, UserAssignment_GroupSelectionRequired %>" Display="Dynamic"
                                            CssClass="text-danger mt-2 text-sm font-medium" />
                                    </asp:Panel>

                                    <div class="form-group mb-0">
                                        <label class="form-label">
                                            <asp:Literal ID="litStatusLabel" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_StatusLabel %>" />:
                                        </label>
                                        <div class="toggle-container">
                                            <label class="toggle-switch">
                                                <asp:CheckBox ID="chkStatus" runat="server" Checked="true" />
                                                <span class="toggle-slider"></span>
                                            </label>
                                            <span class="toggle-label">
                                                <span id="statusText" class="toggle-status">
                                                    <asp:Literal ID="litStatusActiveDefault" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_StatusActive %>" />
                                                </span>
                                            </span>
                                            <asp:DropDownList ID="drpStatus" runat="server" CssClass="d-none">
                                                <asp:ListItem Text="<%$ Resources:DigiportAdminResource, UserAssignment_StatusActive %>" Value="1" Selected="True"></asp:ListItem>
                                                <asp:ListItem Text="<%$ Resources:DigiportAdminResource, UserAssignment_StatusInactive %>" Value="0"></asp:ListItem>
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div>
                                <p class="text-gray text-sm mb-0">
                                    <i class="fas fa-info-circle"></i>
                                    <asp:Literal ID="litSaveInstruction" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_SaveInstruction %>" />
                                </p>
                            </div>
                            <div class="flex gap-2">
                                <asp:Button ID="btnCancelEdit" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_CancelButton %>"
                                    CssClass="btn btn-secondary"
                                    OnClick="btnCancelEdit_Click" CausesValidation="false"></asp:Button>

                                <asp:Button ID="btnCreateNew" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_AddButton %>"
                                    CssClass="btn btn-success"
                                    OnClick="btnCreateNew_Click" ValidationGroup="SaveValidation"></asp:Button>

                                <asp:Button ID="btnUpdate" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_UpdateButton %>"
                                    CssClass="btn btn-primary"
                                    OnClick="btnUpdate_Click" ValidationGroup="SaveValidation"></asp:Button>
                            </div>
                        </div>
                    </div>
                </asp:Panel>

                <asp:Panel ID="pnlAssignmentList" runat="server" Visible="false">
                    <div class="card" id="divAssignmentList">
                        <div class="card-header">
                            <h2 class="card-title">
                                <i class="fas fa-list-alt icon"></i>
                                <asp:Literal ID="litCurrentAuthTitle" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_CurrentAuthTitle %>" />
                                <span class="badge bg-primary-50 text-primary ml-2 rounded">
                                    <asp:Label ID="lblSelectedPageName" runat="server"></asp:Label>
                                </span>
                            </h2>

                        </div>

                        <div class="filter-container mb-4">
                            <div class="flex items-center gap-4">
                                <div class="search-container">
                                    <i class="fas fa-search search-icon"></i>
                                    <asp:TextBox ID="txtSearchAssignments" ClientIDMode="Static" runat="server" CssClass="search-input"
                                        placeholder="<%$ Resources:DigiportAdminResource, UserAssignment_SearchPlaceholder %>" AutoPostBack="true"
                                        OnTextChanged="txtSearchAssignments_TextChanged"></asp:TextBox>
                                </div>
                                <div class="filter-group">
                                    <label for="drpStatusFilter" class="filter-label">
                                        <asp:Literal ID="litStatusFilterLabel" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_StatusLabel %>" />:
                                    </label>
                                    <asp:DropDownList ID="drpStatusFilter" runat="server" CssClass="form-select form-select-sm"
                                        AutoPostBack="true" OnSelectedIndexChanged="drpStatusFilter_SelectedIndexChanged">
                                        <asp:ListItem Text="<%$ Resources:DigiportAdminResource, UserAssignment_StatusAll %>" Value="" Selected="True"></asp:ListItem>
                                        <asp:ListItem Text="<%$ Resources:DigiportAdminResource, UserAssignment_StatusActive %>" Value="1"></asp:ListItem>
                                        <asp:ListItem Text="<%$ Resources:DigiportAdminResource, UserAssignment_StatusInactive %>" Value="0"></asp:ListItem>
                                    </asp:DropDownList>
                                </div>
                            </div>
                        </div>

                        <div class="data-table-container">
                            <asp:GridView ID="grdUserAssignments" runat="server"
                                CssClass="data-table"
                                AutoGenerateColumns="False"
                                DataKeyNames="ID"
                                Width="100%"
                                AllowPaging="True"
                                AllowSorting="True"
                                PageSize="10"
                                OnRowCommand="grdUserAssignments_RowCommand"
                                OnRowDataBound="grdUserAssignments_RowDataBound"
                                OnPageIndexChanging="grdUserAssignments_PageIndexChanging"
                                OnSorting="grdUserAssignments_Sorting"
                                GridLines="None">
                                <HeaderStyle CssClass="grid-header" BackColor="#C60C30" ForeColor="White" />
                                <RowStyle CssClass="grid-row" />
                                <AlternatingRowStyle CssClass="grid-row-alt" />
                                <SelectedRowStyle CssClass="grid-row-selected" BackColor="#F0F0F0" />
                                <PagerStyle CssClass="grid-pager" HorizontalAlign="Center" />
                                <EmptyDataTemplate>
                                    <div class="empty-state">
                                        <div class="empty-state-icon">
                                            <i class="fas fa-user-shield"></i>
                                        </div>
                                        <h3 class="empty-state-title">
                                            <asp:Literal ID="litEmptyStateTitle" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_EmptyStateTitle %>" />
                                        </h3>
                                        <p class="empty-state-text">
                                            <asp:Literal ID="litEmptyStateText" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_EmptyStateText %>" />
                                        </p>
                                        <asp:Button ID="btnAddNew" runat="server" Text="<%$ Resources:DigiportAdminResource, UserAssignment_AddNewButton %>" CssClass="btn btn-primary"
                                            OnClick="btnAddNew_Click" CausesValidation="false" />
                                    </div>
                                </EmptyDataTemplate>
                                <Columns>
                                    <asp:TemplateField HeaderText="<%$ Resources:DigiportAdminResource, UserAssignment_ActionsColumn %>" HeaderStyle-HorizontalAlign="Center" ItemStyle-HorizontalAlign="Center" ItemStyle-Width="100px">
                                        <ItemTemplate>
                                            <div class="action-buttons-container">
                                                <asp:LinkButton ID="btnToggleStatus" runat="server" CommandName="ToggleStatus" CommandArgument='<%# Eval("ID") %>'
                                                    CssClass='<%# Convert.ToString(Eval("AKTIF")) == "1" ? "btn-action btn-action-warning" : "btn-action btn-action-success" %>' 
                                                    ToolTip='<%# Convert.ToString(Eval("AKTIF")) == "1" ? 
                                                        HttpContext.GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_DisableTooltip") ?? "Devre dışı bırak" : 
                                                        HttpContext.GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_EnableTooltip") ?? "Etkinleştir" %>'>
                                                    <i class='<%# Convert.ToString(Eval("AKTIF")) == "1" ? "fas fa-ban" : "fas fa-check-circle" %>'></i>
                                                </asp:LinkButton>
                                                <asp:LinkButton ID="btnDelete" runat="server" CommandName="DeleteItem" CommandArgument='<%# Eval("ID") %>'
                                                    CssClass="btn-action btn-action-danger" ToolTip="<%$ Resources:DigiportAdminResource, UserAssignment_DeleteTooltip %>"
                                                    OnClientClick="return confirm('<%$ Resources:DigiportAdminResource, DeletePermissionConfirmation %>');">
                                        <i class="fas fa-trash-alt"></i>
                                                </asp:LinkButton>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>

                                    <asp:TemplateField HeaderText="<%$ Resources:DigiportAdminResource, UserAssignment_AuthorizedColumn %>" ItemStyle-Width="60%">
                                        <ItemTemplate>
                                            <div class="entity-name">
                                                <div class='<%# Eval("F_LOGIN_ID") != DBNull.Value && Convert.ToDecimal(Eval("F_LOGIN_ID")) > 0 ? "entity-icon" : "entity-icon group" %>'>
                                                    <i class='<%# Eval("F_LOGIN_ID") != DBNull.Value && Convert.ToDecimal(Eval("F_LOGIN_ID")) > 0 ? "fas fa-user" : "fas fa-users" %>'></i>
                                                </div>
                                                <div>
                                                    <div class="line-clamp-1"><%# Eval("DISPLAY_NAME") %></div>
                                                    <div class="text-xs text-gray">
                                                        <%# Eval("F_LOGIN_ID") != DBNull.Value && Convert.ToDecimal(Eval("F_LOGIN_ID")) > 0 ?
                                                            HttpContext.GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_UserType") :
                                                            HttpContext.GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_GroupType") %>
                                                    </div>
                                                </div>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>

                                    <asp:TemplateField HeaderText="<%$ Resources:DigiportAdminResource, UserAssignment_StatusColumn %>" ItemStyle-Width="120px" ItemStyle-HorizontalAlign="Center">
                                        <ItemTemplate>
                                            <span class='<%# Convert.ToString(Eval("AKTIF")) == "1" ? "badge badge-success" : "badge badge-danger" %>'>
                                                <%# Convert.ToString(Eval("AKTIF")) == "1" ?
                                                    HttpContext.GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_StatusActive") :
                                                    HttpContext.GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_StatusInactive") %>
                                            </span>
                                        </ItemTemplate>
                                    </asp:TemplateField>

                                    <asp:TemplateField HeaderText="<%$ Resources:DigiportAdminResource, UserAssignment_CreatedDateColumn %>" ItemStyle-Width="180px">
                                        <ItemTemplate>
                                            <div class="flex items-center text-gray">
                                                <i class="far fa-calendar-alt mr-2"></i>
                                                <%# Eval("CREATED", "{0:dd.MM.yyyy HH:mm}") %>
                                            </div>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                </Columns>
                            </asp:GridView>
                            <!-- Empty state is now handled by GridView's EmptyDataTemplate -->
                        </div>
                    </div>
                </asp:Panel>
                <asp:HiddenField ID="hdnSelectedAssignmentId" runat="server" Value="0" />
                <asp:HiddenField ID="hdnEditMode" runat="server" Value="0" />
            </div>
        </ContentTemplate>
    </asp:UpdatePanel>

    <!-- Hidden literals for JavaScript localization -->
    <asp:Literal ID="ltlActiveText" runat="server" Text="<%$ Resources:DigiportAdminResource, Active %>" Visible="false" />
    <asp:Literal ID="ltlInactiveText" runat="server" Text="<%$ Resources:DigiportAdminResource, Inactive %>" Visible="false" />

    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
            initializeInterface();
        });

        // Initialize all UI components
        function initializeInterface() {
            setupAssignmentTypeToggle();
            setupStatusToggle();
            setupGridRowHandlers();
            countRecords();
        }

        // Handle grid row click event
        function handleGridRowClick(id) {
            console.log(`Grid row clicked: ID=${id}`);

            // Store the ID in the hidden field
            var hiddenIdField = document.getElementById('<%= hdnSelectedAssignmentId.ClientID %>');
            if (hiddenIdField && !hiddenIdField.getAttribute('data-locked')) {
                hiddenIdField.value = id;

                // Visually select the row
                const rows = document.querySelectorAll('#<%= grdUserAssignments.ClientID %> tbody tr');
                rows.forEach(row => {
                    if (row.getAttribute('data-id') === id) {
                        row.classList.add('selected');
                    } else {
                        row.classList.remove('selected');
                    }
                });
            } else {
                console.log("ID field is locked, not changing selection");
            }

            // Don't set edit mode when just selecting a row
            var editModeField = document.getElementById('<%= hdnEditMode.ClientID %>');
            if (editModeField && editModeField.value === "1") {
                // If we're already in edit mode, don't change it
                console.log("Preserving edit mode");
            } else {
                // Otherwise, ensure we're not in edit mode
                editModeField.value = "0";
            }
        }

        // Edit functionality has been removed - this is kept as a no-op for compatibility
        function handleEditButtonClick(id) {
            console.log(`Edit functionality has been disabled`);
            return false;
        }

        // Function to refresh the grid - now uses a postback
        function refreshGrid() {
            // Use the __doPostBack method to trigger a postback
            if (typeof (__doPostBack) === 'function') {
                __doPostBack('<%= grdUserAssignments.UniqueID %>', 'Refresh');
            }
        }

        // Setup assignment type selection
        function setupAssignmentTypeToggle() {
            const userCard = document.getElementById('userCard');
            const groupCard = document.getElementById('groupCard');
            const userRadio = document.getElementById('<%= rbUser.ClientID %>');
            const groupRadio = document.getElementById('<%= rbAdGroup.ClientID %>');

            if (userCard && groupCard) {
                // Set initial state based on radio buttons
                if (userRadio && userRadio.checked) {
                    userCard.classList.add('active');
                    groupCard.classList.remove('active');
                } else if (groupRadio && groupRadio.checked) {
                    groupCard.classList.add('active');
                    userCard.classList.remove('active');
                }

                // Add click handlers
                userCard.addEventListener('click', function () {
                    if (!userRadio.checked) {
                        userRadio.checked = true;
                        __doPostBack(userRadio.id, '');
                    }
                });

                groupCard.addEventListener('click', function () {
                    if (!groupRadio.checked) {
                        groupRadio.checked = true;
                        __doPostBack(groupRadio.id, '');
                    }
                });
            }
        }        // Setup status toggle with visual feedback
        function setupStatusToggle() {
            const statusToggle = document.getElementById('<%= chkStatus.ClientID %>');
            const statusText = document.getElementById('statusText');
            const statusDropdown = document.getElementById('<%= drpStatus.ClientID %>');

            if (statusToggle && statusText && statusDropdown) {
                // Update visual display based on current value
                updateStatusDisplay();

                // Add change handler
                statusToggle.addEventListener('change', function () {
                    statusDropdown.value = this.checked ? "1" : "0";
                    updateStatusDisplay();
                });
            }            // Update visual display
            function updateStatusDisplay() {
                if (statusToggle && statusToggle.checked) {
                    const activeText = document.getElementById('ltlActiveText');
                    statusText.innerText = activeText ? activeText.innerText || '<%: HttpContext.GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_StatusActive") %>' : '<%: HttpContext.GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_StatusActive") %>';
                    statusText.classList.remove('inactive');
                } else {
                    const inactiveText = document.getElementById('ltlInactiveText');
                    statusText.innerText = inactiveText ? inactiveText.innerText || '<%: HttpContext.GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_StatusInactive") %>' : '<%: HttpContext.GetGlobalResourceObject("DigiportAdminResource", "UserAssignment_StatusInactive") %>';
                    statusText.classList.add('inactive');
                }
            }
        }

        // Count and display record count
        function countRecords() {
            const table = document.getElementById('<%= grdUserAssignments.ClientID %>');
            const countDisplay = document.getElementById('recordCount');

            if (table && countDisplay) {
                const rows = table.querySelectorAll('tbody tr');
                let visibleCount = 0;

                rows.forEach(row => {
                    // Skip if not a data row or hidden
                    if (row.style.display !== 'none' && !row.classList.contains('empty-data-row')) {
                        visibleCount++;
                    }
                });

                countDisplay.innerText = visibleCount;
            }
        }


        // Add click handlers to grid rows
        function setupGridRowHandlers() {
            const grid = document.getElementById('<%= grdUserAssignments.ClientID %>');
            if (!grid) return;

            // Check if we have a selected ID
            const selectedId = document.getElementById('<%= hdnSelectedAssignmentId.ClientID %>').value;
            console.log("Current selected ID: " + selectedId);

            // Highlight the correct row based on selectedId
            const rows = grid.querySelectorAll('tr[data-id]');
            rows.forEach(row => {
                // Get the row's ID
                const rowId = row.getAttribute('data-id');

                // Remove existing selection
                row.classList.remove('selected');

                // Apply selection if this row matches
                if (rowId === selectedId) {
                    row.classList.add('selected');
                    console.log("Row " + rowId + " selected");
                }
            });
        }

        // Add new permission button handler
        function addNewPermission() {
            // Reset form to add mode
            document.getElementById('<%= hdnEditMode.ClientID %>').value = "0";
            document.getElementById('<%= hdnSelectedAssignmentId.ClientID %>').value = "0";
            console.log("Reset to add mode");

            // Toggle button visibility for create mode
            document.getElementById('<%= btnCreateNew.ClientID %>').style.display = '';
            document.getElementById('<%= btnUpdate.ClientID %>').style.display = 'none';

            // Show assignment section
            const assignmentSection = document.getElementById('divAssignmentSection');
            if (assignmentSection) {
                assignmentSection.style.display = 'block';

                // Smooth scroll to section
                setTimeout(() => {
                    assignmentSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }, 100);
            }

            // Reset form fields
            resetForm();
        }

        // Reset form to initial state
        function resetForm() {
            const userDropdown = document.getElementById('<%= drpUsers.ClientID %>');
            const groupDropdown = document.getElementById('<%= drpGroups.ClientID %>');
            const statusToggle = document.getElementById('<%= chkStatus.ClientID %>');

            if (userDropdown) userDropdown.selectedIndex = 0;
            if (groupDropdown) groupDropdown.selectedIndex = 0;
            if (statusToggle) statusToggle.checked = true;

            // Update selected entity display
            const selectedEntity = document.getElementById('<%= selectedEntityDiv.ClientID %>');
            if (selectedEntity) selectedEntity.style.display = 'none';

            // Update status display
            setupStatusToggle();
        }

        // Enhanced grid interactions
        function enhanceGridInteractions() {
            const grid = document.getElementById('<%= grdUserAssignments.ClientID %>');

            if (grid) {
                const rows = grid.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    // Skip empty state row
                    if (row.querySelector('.empty-state')) return;

                    // Add click interaction
                    row.addEventListener('click', function (e) {
                        // Don't trigger when clicking on buttons
                        if (e.target.closest('.btn-action')) return;

                        // Select row
                        rows.forEach(r => r.classList.remove('selected'));
                        this.classList.add('selected');
                    });
                });
            }
        }        // Confirm delete with custom dialog
        function confirmDelete(btn) {
            const confirmMessage = document.getElementById('<%= ltlDeletePermissionConfirm.ClientID %>') ?
                document.getElementById('<%= ltlDeletePermissionConfirm.ClientID %>').innerHTML :
                'Delete permission confirmation';
            return confirm(confirmMessage);
        }

        // Helper for ASP.NET postback
        function doPostBack(controlId) {
            if (typeof (__doPostBack) === 'function') {
                __doPostBack(controlId, '');
            }
        }
        // This function runs before any ASP.NET postback to ensure the ID is preserved
        function pageBeginRequest() {
            const idField = document.getElementById('<%= hdnSelectedAssignmentId.ClientID %>');
            const editModeField = document.getElementById('<%= hdnEditMode.ClientID %>');

            if (idField && idField.getAttribute('data-locked') === 'true') {
                // Store the current ID value in sessionStorage for preservation
                const currentId = idField.value;
                const currentEditMode = editModeField.value;

                if (currentId && currentId !== '0') {
                    console.log(`Preserving edit mode: ID=${currentId}, EditMode=${currentEditMode}`);
                    sessionStorage.setItem('preservedAssignmentId', currentId);
                    sessionStorage.setItem('preservedEditMode', currentEditMode);
                }
            }
        }

        // This function runs after any ASP.NET postback to restore the ID
        function pageEndRequest() {
            const idField = document.getElementById('<%= hdnSelectedAssignmentId.ClientID %>');
            const editModeField = document.getElementById('<%= hdnEditMode.ClientID %>');

            if (idField) {
                const preservedId = sessionStorage.getItem('preservedAssignmentId');
                const preservedEditMode = sessionStorage.getItem('preservedEditMode');

                if (preservedId && preservedId !== '0') {
                    console.log(`Restoring edit mode: ID=${preservedId}, EditMode=${preservedEditMode}`);
                    idField.value = preservedId;
                    editModeField.value = preservedEditMode;

                    // Update UI based on restored edit mode
                    if (preservedEditMode === '1') {
                        const createBtn = document.getElementById('<%= btnCreateNew.ClientID %>');
                const updateBtn = document.getElementById('<%= btnUpdate.ClientID %>');

                        if (createBtn) createBtn.style.display = 'none';
                        if (updateBtn) updateBtn.style.display = '';
                    }
                }
            }
        }

        // Initialize Sys.WebForms event handlers when using UpdatePanel
        function setupPostbackHandlers() {
            if (typeof (Sys) !== 'undefined' && Sys.WebForms) {
                Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(pageBeginRequest);
                Sys.WebForms.PageRequestManager.getInstance().add_endRequest(function () {
                    // Re-initialize interface after each async postback
                    initializeInterface();
                    pageEndRequest();
                });
            }
        }

        // Add these new functions to the document ready event
        document.addEventListener('DOMContentLoaded', function () {
            // Call existing initializations
            initializeInterface();

            // Add the postback event handlers
            setupPostbackHandlers();

            // Check if we need to restore a previous edit mode
            const idField = document.getElementById('<%= hdnSelectedAssignmentId.ClientID %>');
            const editModeField = document.getElementById('<%= hdnEditMode.ClientID %>');
            const preservedId = sessionStorage.getItem('preservedAssignmentId');

            if (idField && preservedId && preservedId !== '0') {
                pageEndRequest(); // Restore if needed
            }

            // Debug edit mode on page load
            const editMode = editModeField.value === "1";
            const editID = idField.value;
            console.log(`Page loaded - Edit Mode: ${editMode}, Assignment ID: ${editID}`);
        });
        // Function to refresh the grid via __doPostBack
        function refreshGrid() {
            if (typeof (__doPostBack) === 'function') {
                __doPostBack('<%= grdUserAssignments.UniqueID %>', 'Refresh');
            }
        }


        function applyButtonStyles() {
            // Apply styles to action buttons
            $('.action-button').each(function () {
                var $btn = $(this);
                if ($btn.hasClass('delete-button')) {
                    $btn.addClass('btn-action btn-action-danger');
                }
            });        }
    </script>
    <%-- Hidden literal for JavaScript resource access --%>
    <asp:Literal ID="ltlDeletePermissionConfirm" runat="server" Text="<%$ Resources:DigiportAdminResource, DeletePermissionConfirmation %>" Visible="false"></asp:Literal>
    </ContentTemplate>
    </asp:UpdatePanel>
</asp:Content>
