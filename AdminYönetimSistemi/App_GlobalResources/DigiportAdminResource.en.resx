﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AcilanPencereBoyutlari" xml:space="preserve">
    <value>Size of The Window Opened on Click (Width x Height)</value>
  </data>
  <data name="AdminPages" xml:space="preserve">
    <value>Admin Pages</value>
  </data>
  <data name="Aktif" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="AnnouncementInformation" xml:space="preserve">
    <value>Announcement Information</value>
  </data>
  <data name="Basari" xml:space="preserve">
    <value>Success</value>
  </data>
  <data name="Baslangic" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="BaslangicTarihFormati" xml:space="preserve">
    <value>Start date format must be like dd.mm.yyyy</value>
  </data>
  <data name="BaslangicTarihiSec" xml:space="preserve">
    <value>Please select start date</value>
  </data>
  <data name="Bitis" xml:space="preserve">
    <value>End</value>
  </data>
  <data name="BitisTarihFormati" xml:space="preserve">
    <value>End date format must be like dd.mm.yyyy</value>
  </data>
  <data name="BitisTarihiBaslagictanIleriOlsun" xml:space="preserve">
    <value>End date must be later than start date.</value>
  </data>
  <data name="BitisTarihiSec" xml:space="preserve">
    <value>Please select end date</value>
  </data>
  <data name="ClickEvent" xml:space="preserve">
    <value>Click Event</value>
  </data>
  <data name="ConfirmeDelete" xml:space="preserve">
    <value>Do you confirm to delete the record?</value>
  </data>
  <data name="DigiflowMenuAdmin" xml:space="preserve">
    <value>Digiflow Menu Admin</value>
  </data>
  <data name="DigiportAnaSayfaSolSagGaleri" xml:space="preserve">
    <value>Digiport Main Page Left-Right Gallery</value>
  </data>
  <data name="DosyaYuklenemedi" xml:space="preserve">
    <value>File couldn't saved.</value>
  </data>
  <data name="DuyuruAdi" xml:space="preserve">
    <value>Announcement Name</value>
  </data>
  <data name="DuyuruAdiGirin" xml:space="preserve">
    <value>Please fill announcement name</value>
  </data>
  <data name="DuyuruListe" xml:space="preserve">
    <value>Announcement List</value>
  </data>
  <data name="Duzenle" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="GaleriTipiBelirsiz" xml:space="preserve">
    <value>Undefined Gallery Type</value>
  </data>
  <data name="GaleriTipiYanlis" xml:space="preserve">
    <value>Incorrect Gallery Type</value>
  </data>
  <data name="TipBelirsiz" xml:space="preserve">
    <value>Undefined Component Type</value>
  </data>
  <data name="TipYanlis" xml:space="preserve">
    <value>Incorrect Component Type</value>
  </data>
  <data name="GecerlilikAraligi" xml:space="preserve">
    <value>Valid Dates</value>
  </data>
  <data name="GecersizPopupGenislik" xml:space="preserve">
    <value>An invalid popup window width has been selected.</value>
  </data>
  <data name="GecersizPopupYukseklik" xml:space="preserve">
    <value>An invalid popup window height has been selected.</value>
  </data>
  <data name="GecersizSiraNo" xml:space="preserve">
    <value>An invalid order no has been selected.</value>
  </data>
  <data name="Geri" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="GridKayitBulunmadi" xml:space="preserve">
    <value>No record found.</value>
  </data>
  <data name="Hata" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="HataOldu" xml:space="preserve">
    <value>Error occured.</value>
  </data>
  <data name="HedefLink" xml:space="preserve">
    <value>Target Link</value>
  </data>
  <data name="HedefLinkGirin" xml:space="preserve">
    <value>Please fill target link</value>
  </data>
  <data name="IKMedyaDuyuru" xml:space="preserve">
    <value>Internal Information</value>
  </data>
  <data name="IsimTanimlama" xml:space="preserve">
    <value>Name Definition</value>
  </data>
  <data name="Kaydet" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="KayitBasarili" xml:space="preserve">
    <value>Successfully Saved</value>
  </data>
  <data name="KullaniciAtama" xml:space="preserve">
    <value>User Assignment</value>
  </data>
  <data name="Pasif" xml:space="preserve">
    <value>Passive</value>
  </data>
  <data name="PopupSayfaBaslik" xml:space="preserve">
    <value>Headline Of Page</value>
  </data>
  <data name="PopupSayfaBaslikGirin" xml:space="preserve">
    <value>Please fill headline of page</value>
  </data>
  <data name="PopupSayfaIcerik" xml:space="preserve">
    <value>Html Content Of Page</value>
  </data>
  <data name="SadeceResimVideo" xml:space="preserve">
    <value>Only image and video files are allowed.</value>
  </data>
  <data name="SagGaleri" xml:space="preserve">
    <value>Right Gallery</value>
  </data>
  <data name="SayiAraligi1" xml:space="preserve">
    <value>Please select a number in between 1-999</value>
  </data>
  <data name="SayiAraligi2" xml:space="preserve">
    <value>Please select a number in between 1-99999</value>
  </data>
  <data name="SecileniTemizle" xml:space="preserve">
    <value>Undo Selection</value>
  </data>
  <data name="ShowProgressBar" xml:space="preserve">
    <value>Show Slider Progress Bar</value>
  </data>
  <data name="ShowThumbnails" xml:space="preserve">
    <value>Show Slider Navigation Thumbnails</value>
  </data>
  <data name="Sil" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="SilmeBasarili" xml:space="preserve">
    <value>Delete Successfull</value>
  </data>
  <data name="SiraNo" xml:space="preserve">
    <value>Order No</value>
  </data>
  <data name="SiraNoSecin" xml:space="preserve">
    <value>Please select order no</value>
  </data>
  <data name="Slide" xml:space="preserve">
    <value>Slide</value>
  </data>
  <data name="SlideAdi" xml:space="preserve">
    <value>Slide Name</value>
  </data>
  <data name="SlideAdiGirin" xml:space="preserve">
    <value>Please fill slide name</value>
  </data>
  <data name="SlideBilgileri" xml:space="preserve">
    <value>Slide Information</value>
  </data>
  <data name="SlideBoyutlari" xml:space="preserve">
    <value>Sizes of selected file  {0}x{1} but must be {2}x{3}</value>
  </data>
  <data name="SlideDosyaFormatiYanlis" xml:space="preserve">
    <value>File extension selected for the slide is incorrect. Allowed formats =&gt;</value>
  </data>
  <data name="SlideListe" xml:space="preserve">
    <value>Slide List</value>
  </data>
  <data name="TemizleFiltre" xml:space="preserve">
    <value>Clear Filters</value>
  </data>
  <data name="TiklanmaHedefLink" xml:space="preserve">
    <value>Target Link On Click</value>
  </data>
  <data name="TiklanmaIslemi" xml:space="preserve">
    <value>Click Event</value>
  </data>
  <data name="TiklanmaIslemiSecin" xml:space="preserve">
    <value>Please select click event</value>
  </data>
  <data name="Tumu" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="YeniKayit" xml:space="preserve">
    <value>New Slide</value>
  </data>
  <data name="YeniSlideDosyaSec" xml:space="preserve">
    <value>You must select a file for new slide.</value>
  </data>
  <data name="YuklenilecekSlideSecin" xml:space="preserve">
    <value>Select Slide To Upload</value>
  </data>
  <data name="AnaSayfa" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="AracTalepAdmin" xml:space="preserve">
    <value>Vehicle Request Admin</value>
  </data>
  <data name="DigiportAnaSayfaGaleri" xml:space="preserve">
    <value>Digiport Home Page Left-Right Gallery</value>
  </data>
  <data name="DigiportAnket" xml:space="preserve">
    <value>Digiport Survey</value>
  </data>
  <data name="DigiportMenuAdmin" xml:space="preserve">
    <value>Digiflow Menu Admin</value>
  </data>
  <data name="DigiportPortal" xml:space="preserve">
    <value>Digiport</value>
  </data>
  <data name="DomainSeciniz" xml:space="preserve">
    <value>Select Domain</value>
  </data>
  <data name="EkipmanTalepAdmin" xml:space="preserve">
    <value>Equipment Request Admin</value>
  </data>
  <data name="KirtasiyeAdmin" xml:space="preserve">
    <value>Stationery Admin</value>
  </data>
  <data name="KirtasiyeUygulamasi" xml:space="preserve">
    <value>Stationery Application</value>
  </data>
  <data name="LutfenDomainSeciniz" xml:space="preserve">
    <value>Please select a domain first</value>
  </data>
  <data name="MuhaberatAdmin" xml:space="preserve">
    <value>Communications Admin</value>
  </data>
  <data name="SecUserText" xml:space="preserve">
    <value>---Select---</value>
  </data>
  <data name="SolGaleri" xml:space="preserve">
    <value>Left Gallery</value>
  </data>
  <data name="SwitchUser" xml:space="preserve">
    <value>Switch</value>
  </data>
  <data name="TestMateAdmin" xml:space="preserve">
    <value>Test Mate Admin</value>
  </data>
  <data name="TurTanimlama" xml:space="preserve">
    <value>Type Registration</value>
  </data>
  <data name="SliderAutoSlideInterval" xml:space="preserve">
    <value>Slider Display Period (sec.)</value>
  </data>
  <data name="SliderAutoSlideIntervalSecin" xml:space="preserve">
    <value>Please select slider display period.</value>
  </data>
  <data name="SliderEfektTipi" xml:space="preserve">
    <value>Slider effect type</value>
  </data>
  <data name="SliderEfektTipiSecin" xml:space="preserve">
    <value>Please select slider effect type.</value>
  </data>
  <data name="SliderGenislik" xml:space="preserve">
    <value>Slider Width</value>
  </data>
  <data name="SliderGenislikSecin" xml:space="preserve">
    <value>Please select slider width</value>
  </data>
  <data name="SliderSecenekleri" xml:space="preserve">
    <value>Slider Options</value>
  </data>
  <data name="SliderThumbnailGenislikSecin" xml:space="preserve">
    <value>Please select slider thumbnail width</value>
  </data>
  <data name="SliderThumbnailYukseklikSecin" xml:space="preserve">
    <value>Please select slider thumbnail height</value>
  </data>
  <data name="SliderTitleMode" xml:space="preserve">
    <value>Slider Title Mode</value>
  </data>
  <data name="SliderTitleModeSecin" xml:space="preserve">
    <value>Please select slider title mode</value>
  </data>
  <data name="SliderTransitionDuration" xml:space="preserve">
    <value>Slider Transition Duration (sn.)</value>
  </data>
  <data name="SliderTransitionDurationSecin" xml:space="preserve">
    <value>Please select slider transition duration.</value>
  </data>
  <data name="SliderTransitionEasing" xml:space="preserve">
    <value>Slider Transition Easing</value>
  </data>
  <data name="SliderTransitionEasingSecin" xml:space="preserve">
    <value>Please select slider transition easing</value>
  </data>
  <data name="SliderYukseklik" xml:space="preserve">
    <value>Slider Height</value>
  </data>
  <data name="SliderYukseklikSecin" xml:space="preserve">
    <value>Please select slider height</value>
  </data>
  <data name="Thumbnail" xml:space="preserve">
    <value>Thumbnail</value>
  </data>
  <data name="ThumbnailBoyutlari" xml:space="preserve">
    <value>Sizes of selected thumbnail  {0}x{1} but must be {2}x{3}</value>
  </data>
  <data name="ThumbnailDosyaFormatiYanlis" xml:space="preserve">
    <value>File extension selected for the thumbnail is incorrect. Allowed formats =&gt;</value>
  </data>
  <data name="ThumbnailHeight" xml:space="preserve">
    <value>Thumbnail Height</value>
  </data>
  <data name="ThumbnailWidth" xml:space="preserve">
    <value>Thumbnail Width</value>
  </data>
  <data name="UstGaleri" xml:space="preserve">
    <value>Digiport Main Page Top Gallery</value>
  </data>
  <data name="VideoIzinVerilenMaxBoyut" xml:space="preserve">
    <value>Video file size exceeds allowed maximum limit.You may define video as a embedded link.</value>
  </data>
  <data name="YeniDuyuru" xml:space="preserve">
    <value>New Announcement</value>
  </data>
  <data name="YeniDuyuruResmiSec" xml:space="preserve">
    <value>You must select an image for new announcement.</value>
  </data>
  <data name="YeniSlide" xml:space="preserve">
    <value>New Slide</value>
  </data>
  <data name="YeniThumbnailDosyaSec" xml:space="preserve">
    <value>You must select a thumbnail for new slide.</value>
  </data>
  <data name="YuklenilecekDuyuruResmiSecin" xml:space="preserve">
    <value>Select Announcement Image To Upload</value>
  </data>
  <data name="AccessDeniedTitle" xml:space="preserve">
    <value>Access Denied</value>
  </data>
  <data name="AccessDeniedMessage" xml:space="preserve">
    <value>You do not have permission to access this page!</value>
  </data>
  <data name="AccessDeniedHelp" xml:space="preserve">
    <value>If you believe you should have access to this page, please contact your system administrator.</value>
  </data>
  <data name="AccessDeniedBySystem" xml:space="preserve">
    <value>Your access has been blocked by the Digiport Admin Portal authorization system.</value>
  </data>
  <data name="ReturnToHomePage" xml:space="preserve">
    <value>Return to Home Page</value>
  </data>
  <data name="Copyright" xml:space="preserve">
    <value>Copyright © 2023 Admin Management System</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="UstBanner" xml:space="preserve">
    <value>Top Banner</value>
  </data>
  <data name="DigiportBanners" xml:space="preserve">
    <value>Digiport Banners</value>
  </data>
  <data name="AccessDeniedLogMessage" xml:space="preserve">
    <value>Access denied: User={0}, RequestedUrl={1}, IP={2}, Message={3}</value>
  </data>
  <data name="LogErrorMessage" xml:space="preserve">
    <value>Error in LogError: {0}</value>
  </data>
  <data name="MSG_SUCCESS" xml:space="preserve">
    <value>Operation Successful</value>
  </data>
  <data name="MSG_ERROR" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="MSG_RECORD_NOT_FOUND" xml:space="preserve">
    <value>Record not found for the operation.</value>
  </data>
  <data name="MSG_DUPLICATE" xml:space="preserve">
    <value>The same type and name combination already exists.</value>
  </data>
  <data name="MSG_SAVE_SUCCESS" xml:space="preserve">
    <value>Record successfully added.</value>
  </data>
  <data name="MSG_UPDATE_SUCCESS" xml:space="preserve">
    <value>Record successfully updated.</value>
  </data>
  <data name="MSG_DELETE_SUCCESS" xml:space="preserve">
    <value>Record successfully deleted.</value>
  </data>
  <data name="ERR_PERMISSION" xml:space="preserve">
    <value>An error occurred during permission check.</value>
  </data>
  <data name="ERR_CONFIG" xml:space="preserve">
    <value>Required AD Group settings not found.</value>
  </data>
  <data name="ERR_LOAD_TYPES" xml:space="preserve">
    <value>Error loading type list.</value>
  </data>
  <data name="ERR_LOAD_GRID" xml:space="preserve">
    <value>Error loading record list.</value>
  </data>
  <data name="ERR_DATA_MAPPING" xml:space="preserve">
    <value>Error mapping form data.</value>
  </data>
  <data name="ERR_SAVE" xml:space="preserve">
    <value>Error adding record.</value>
  </data>
  <data name="ERR_UPDATE" xml:space="preserve">
    <value>Error updating record.</value>
  </data>
  <data name="ERR_DELETE" xml:space="preserve">
    <value>Error deleting record.</value>
  </data>
  <data name="ERR_GET_RECORD" xml:space="preserve">
    <value>Error retrieving record information.</value>
  </data>
  <data name="ERR_PAGE_LOAD" xml:space="preserve">
    <value>An unexpected error occurred while loading the page.</value>
  </data>
  <data name="ERR_SEARCH" xml:space="preserve">
    <value>An error occurred during the search operation.</value>
  </data>
  <data name="ERR_RESET_SEARCH" xml:space="preserve">
    <value>An error occurred while clearing filters.</value>
  </data>
  <data name="ERR_EDIT" xml:space="preserve">
    <value>An error occurred while editing the record.</value>
  </data>
  <data name="ERR_PAGINATION" xml:space="preserve">
    <value>An error occurred during pagination.</value>
  </data>
  <data name="ERR_PAGE_CHANGE" xml:space="preserve">
    <value>An error occurred while changing pages.</value>
  </data>
  <data name="MSG_SELECT_TYPE" xml:space="preserve">
    <value>Please select a type.</value>
  </data>
  <data name="MSG_NAME_REQUIRED" xml:space="preserve">
    <value>Name field cannot be empty.</value>
  </data>
  <data name="LBL_RECORD_COUNT" xml:space="preserve">
    <value>{0} Records</value>
  </data>
  <data name="LBL_SELECT_TYPE" xml:space="preserve">
    <value>--Select Type--</value>
  </data>
  <data name="LBL_ALL_TYPES" xml:space="preserve">
    <value>--All Types--</value>
  </data>
  <data name="DigiportMenuNameManagement" xml:space="preserve">
    <value>Digiport Menu Name Management</value>
  </data>
  <data name="UnauthorizedAccess" xml:space="preserve">
    <value>Unauthorized Access</value>
  </data>
  <data name="YonetimSayfalari" xml:space="preserve">
    <value>Management Pages</value>
  </data>
  <data name="UserSwitchFailed" xml:space="preserve">
    <value>User switch operation failed. Please try again.</value>
  </data>
  <data name="UserSelectRequired" xml:space="preserve">
    <value>Please select a valid user.</value>
  </data>
  <data name="UserSwitchError" xml:space="preserve">
    <value>An error occurred during user switch: {0}</value>
  </data>
  <data name="UseInternetExplorer" xml:space="preserve">
    <value>Please use Internet Explorer browser for your operations! </value>
  </data>
  <data name="YuklenilecekThumbnailSecin" xml:space="preserve">
    <value>Select Thumbnail To Upload</value>
  </data>
  <data name="KategoriIslemleri" xml:space="preserve">
    <value>Category Management</value>
  </data>
  <data name="YeniIndirimFirsati" xml:space="preserve">
    <value>New Discount Opportunity</value>
  </data>
  <data name="AjansDuyuru" xml:space="preserve">
    <value>Agency Announcements</value>
  </data>
  <data name="DigiportGrids" xml:space="preserve">
    <value>Digiport Tables</value>
  </data>
  <data name="DigiportIndirimFirsati" xml:space="preserve">
    <value>Discount Opportunity</value>
  </data>
  <data name="DigiportLinkler" xml:space="preserve">
    <value>Links</value>
  </data>
  <data name="DiscountOpportunityInformation" xml:space="preserve">
    <value>Discount Opportunity Information</value>
  </data>
  <data name="AvantajKosullari" xml:space="preserve">
    <value>Conditions of Advantage</value>
  </data>
  <data name="AvantajKosullariSecin" xml:space="preserve">
    <value>Please fill conditions of advantage.</value>
  </data>
  <data name="BaslangicTarihi" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="Baslik" xml:space="preserve">
    <value>Headline</value>
  </data>
  <data name="BaslikGirin" xml:space="preserve">
    <value>Please fill headline</value>
  </data>
  <data name="BitisTarihi" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="IndirimOrani" xml:space="preserve">
    <value>Discount Rate</value>
  </data>
  <data name="IndirimOraniSecin" xml:space="preserve">
    <value>Please fill discount rate field.</value>
  </data>
  <data name="Kategori" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="KategoriSecin" xml:space="preserve">
    <value>Please select category</value>
  </data>
  <data name="Kurum" xml:space="preserve">
    <value>Corporation</value>
  </data>
  <data name="KurumSeciniz" xml:space="preserve">
    <value>Please fill corporation field.</value>
  </data>
  <data name="Adres" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="AdresGirin" xml:space="preserve">
    <value>Please fill address field.</value>
  </data>
  <data name="IndirimFirsatiKategoriIslemleri" xml:space="preserve">
    <value>Discount Opportunity Category Management</value>
  </data>
  <data name="IndirimFirsatiListe" xml:space="preserve">
    <value>Discount Opportunity List</value>
  </data>
  <data name="KategoriAdiEn" xml:space="preserve">
    <value>Category Name English</value>
  </data>
  <data name="KategoriAdiTr" xml:space="preserve">
    <value>Category Name Turkish</value>
  </data>
  <data name="Link" xml:space="preserve">
    <value>Link</value>
  </data>
  <data name="LinkSecin" xml:space="preserve">
    <value>Please fill link field</value>
  </data>
  <data name="Lokasyon" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="LokasyonGirin" xml:space="preserve">
    <value>Please fill location field.</value>
  </data>
  <data name="SayiAraligi3" xml:space="preserve">
    <value>Please select a number in between 1-99</value>
  </data>
  <data name="Telefon" xml:space="preserve">
    <value>Please fill telephone field.</value>
  </data>
  <data name="TelefonGirin" xml:space="preserve">
    <value>Telephone</value>
  </data>
  <data name="ZorunluAlan" xml:space="preserve">
    <value>Please fill the field.</value>
  </data>
  <data name="IslemBasarili" xml:space="preserve">
    <value>Operation Successful</value>
  </data>
  <data name="KayitBulunamadi" xml:space="preserve">
    <value>Record not found for operation.</value>
  </data>
  <data name="MukerrerKayit" xml:space="preserve">
    <value>A type with the same name already exists.</value>
  </data>
  <data name="TipEklemeBasarili" xml:space="preserve">
    <value>Type successfully added.</value>
  </data>
  <data name="TipGuncellemeBasarili" xml:space="preserve">
    <value>Type successfully updated.</value>
  </data>
  <data name="TipSilmeBasarili" xml:space="preserve">
    <value>Type successfully deleted.</value>
  </data>
  <data name="YetkiKontrolHatasi" xml:space="preserve">
    <value>An error occurred during permission check.</value>
  </data>
  <data name="GrupAyarlariHatasi" xml:space="preserve">
    <value>Required AD Group settings not found.</value>
  </data>
  <data name="GridYuklemeHatasi" xml:space="preserve">
    <value>Error occurred while loading record list.</value>
  </data>
  <data name="FormVeriAtamaHatasi" xml:space="preserve">
    <value>Error occurred while mapping form data.</value>
  </data>
  <data name="KayitEklemeHatasi" xml:space="preserve">
    <value>Error occurred while adding record.</value>
  </data>
  <data name="KayitGuncellemeHatasi" xml:space="preserve">
    <value>Error occurred while updating record.</value>
  </data>
  <data name="KayitSilmeHatasi" xml:space="preserve">
    <value>Error occurred while deleting record.</value>
  </data>
  <data name="KayitGetirmeHatasi" xml:space="preserve">
    <value>Error occurred while retrieving record information.</value>
  </data>
  <data name="GuncellemeKayitSecilmemis" xml:space="preserve">
    <value>No record selected for update.</value>
  </data>
  <data name="SilmeKayitSecilmemis" xml:space="preserve">
    <value>No record selected for deletion.</value>
  </data>
  <data name="TypeManagement" xml:space="preserve">
    <value>Type Management</value>
  </data>
  <data name="TypeManagementDescription" xml:space="preserve">
    <value>Create, update, and manage menu types.</value>
  </data>
  <data name="AddNewType" xml:space="preserve">
    <value>Add New Type</value>
  </data>
  <data name="EditType" xml:space="preserve">
    <value>Edit Type</value>
  </data>
  <data name="TypeName" xml:space="preserve">
    <value>Type Name</value>
  </data>
  <data name="TypeNamePlaceholder" xml:space="preserve">
    <value>Enter type name</value>
  </data>
  <data name="TypeNameRequired" xml:space="preserve">
    <value>Type Name is required.</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="DescriptionPlaceholder" xml:space="preserve">
    <value>Enter description</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="ExistingTypes" xml:space="preserve">
    <value>Existing Types</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="DigiportPageAuthorization" xml:space="preserve">
    <value>Digiport Page Authorization</value>
  </data>
  <data name="PageAuthorizationDescription" xml:space="preserve">
    <value>From this page, you can securely and easily manage user and Active Directory (AD) group authorizations for Digiport admin pages. Authorization processes control the access of users and groups to specific pages.</value>
  </data>
  <data name="DigiPortPageSelection" xml:space="preserve">
    <value>DigiPort Page Selection</value>
  </data>
  <data name="SelectPageToAuthorize" xml:space="preserve">
    <value>Select Page to Authorize:</value>
  </data>
  <data name="PleaseSelectPage" xml:space="preserve">
    <value>Please select a page.</value>
  </data>
  <data name="PageSelectionHint" xml:space="preserve">
    <value>Select the DigiPort page you want to authorize. When you make a selection, existing authorizations will be listed below.</value>
  </data>
  <data name="AuthorityAssignmentEditing" xml:space="preserve">
    <value>Authority Assignment / Editing</value>
  </data>
  <data name="RecordBeingEdited" xml:space="preserve">
    <value>Record Being Edited</value>
  </data>
  <data name="EditedAuthority" xml:space="preserve">
    <value>Edited Authority:</value>
  </data>
  <data name="SelectAuthorityType" xml:space="preserve">
    <value>Select Authority Type:</value>
  </data>
  <data name="UserAuthorization" xml:space="preserve">
    <value>User Authorization</value>
  </data>
  <data name="UserAuthorizationDesc" xml:space="preserve">
    <value>Assign special privileges to individual users</value>
  </data>
  <data name="ADGroupAuthorization" xml:space="preserve">
    <value>AD Group Authorization</value>
  </data>
  <data name="ADGroupAuthorizationDesc" xml:space="preserve">
    <value>Assign bulk privileges to Active Directory groups</value>
  </data>
  <data name="SelectUser" xml:space="preserve">
    <value>Select User:</value>
  </data>
  <data name="UserSelectionRequired" xml:space="preserve">
    <value>User selection is required.</value>
  </data>
  <data name="SelectDomain" xml:space="preserve">
    <value>Select Domain:</value>
  </data>
  <data name="SelectDomainDefault" xml:space="preserve">
    <value>--Select Domain--</value>
  </data>
  <data name="SelectADGroup" xml:space="preserve">
    <value>Select AD Group:</value>
  </data>
  <data name="ADGroupSelectionRequired" xml:space="preserve">
    <value>AD Group selection is required.</value>
  </data>
  <data name="AuthorityAssignmentHint" xml:space="preserve">
    <value>Click the Save button to assign authority to the selected user or group.</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="ExistingAuthorizations" xml:space="preserve">
    <value>Existing Authorizations</value>
  </data>
  <data name="SearchAuthorizations" xml:space="preserve">
    <value>Search authorizations...</value>
  </data>
  <data name="AllStatuses" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="AuthorizationNotFound" xml:space="preserve">
    <value>Authorization Not Found</value>
  </data>
  <data name="AuthorizationNotFoundDesc" xml:space="preserve">
    <value>No authorization has been made for the selected page yet, or no results were found that match your search criteria.</value>
  </data>
  <data name="AddNewAuthority" xml:space="preserve">
    <value>Add New Authority</value>
  </data>
  <data name="AuthorizedEntity" xml:space="preserve">
    <value>Authorized Entity</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="ADGroup" xml:space="preserve">
    <value>AD Group</value>
  </data>
  <data name="CreationDate" xml:space="preserve">
    <value>Creation Date</value>
  </data>
  <data name="DeleteConfirmation" xml:space="preserve">
    <value>Are you sure you want to delete this record?</value>
  </data>
  <data name="MSG_SELECT_PAGE" xml:space="preserve">
    <value>Please select a page first.</value>
  </data>
  <data name="MSG_SELECT_USER" xml:space="preserve">
    <value>Please select a user.</value>
  </data>
  <data name="MSG_SELECT_GROUP" xml:space="preserve">
    <value>Please select an AD group.</value>
  </data>
  <data name="MSG_USER_DUPLICATE" xml:space="preserve">
    <value>This user is already authorized for this page!</value>
  </data>
  <data name="MSG_GROUP_DUPLICATE" xml:space="preserve">
    <value>This AD group is already authorized for this page!</value>
  </data>
  <data name="MSG_SAVE_SUCCESS_UA" xml:space="preserve">
    <value>Authorization saved successfully.</value>
  </data>
  <data name="MSG_UPDATE_SUCCESS_UA" xml:space="preserve">
    <value>Authorization updated successfully.</value>
  </data>
  <data name="MSG_DELETE_SUCCESS_UA" xml:space="preserve">
    <value>Authorization deleted successfully.</value>
  </data>
  <data name="ERR_LOAD_PAGES" xml:space="preserve">
    <value>Error occurred while loading page list.</value>
  </data>
  <data name="ERR_LOAD_USERS" xml:space="preserve">
    <value>Error occurred while loading user list.</value>
  </data>
  <data name="ERR_LOAD_GROUPS" xml:space="preserve">
    <value>Error occurred while loading AD group list.</value>
  </data>
  <data name="VAL_DEFAULT_USER" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="VAL_DEFAULT_GROUP" xml:space="preserve">
    <value />
  </data>
  <data name="UserAssignment_PageTitle" xml:space="preserve">
    <value>Digiport Menu - User Authorization</value>
  </data>
  <data name="UserAssignment_MainTitle" xml:space="preserve">
    <value>Digiport Page Authorization</value>
  </data>
  <data name="UserAssignment_MainDescription" xml:space="preserve">
    <value>From this page, you can securely and easily manage user and Active Directory (AD) group authorizations for Digiport admin pages. Authorization operations control users' and groups' access to specific pages.</value>
  </data>
  <data name="UserAssignment_PageSelectionTitle" xml:space="preserve">
    <value>DigiPort Page Selection</value>
  </data>
  <data name="UserAssignment_PageSelectionLabel" xml:space="preserve">
    <value>Select Page to Authorize</value>
  </data>
  <data name="UserAssignment_PageSelectionHint" xml:space="preserve">
    <value>Select the DigiPort page you want to authorize. When you make a selection, existing authorizations will be listed below.</value>
  </data>
  <data name="UserAssignment_PageSelectionRequired" xml:space="preserve">
    <value>Please select a page.</value>
  </data>
  <data name="UserAssignment_AssignmentTitle" xml:space="preserve">
    <value>Authorization Assignment / Edit</value>
  </data>
  <data name="UserAssignment_EditingRecord" xml:space="preserve">
    <value>Editing Record</value>
  </data>
  <data name="UserAssignment_EditingAuth" xml:space="preserve">
    <value>Editing Authorization</value>
  </data>
  <data name="UserAssignment_AuthTypeLabel" xml:space="preserve">
    <value>Select Authorization Type</value>
  </data>
  <data name="UserAssignment_UserAuthTitle" xml:space="preserve">
    <value>User Authorization</value>
  </data>
  <data name="UserAssignment_UserAuthDesc" xml:space="preserve">
    <value>Assign specific permissions to individual users</value>
  </data>
  <data name="UserAssignment_GroupAuthTitle" xml:space="preserve">
    <value>AD Group Authorization</value>
  </data>
  <data name="UserAssignment_GroupAuthDesc" xml:space="preserve">
    <value>Assign bulk permissions to Active Directory groups</value>
  </data>
  <data name="UserAssignment_UserSelectionLabel" xml:space="preserve">
    <value>Select User</value>
  </data>
  <data name="UserAssignment_UserSelectionRequired" xml:space="preserve">
    <value>User selection is required.</value>
  </data>
  <data name="UserAssignment_DomainSelectionLabel" xml:space="preserve">
    <value>Select Domain</value>
  </data>
  <data name="UserAssignment_DomainSelectDefault" xml:space="preserve">
    <value>--Select Domain--</value>
  </data>
  <data name="UserAssignment_DomainDigiturk" xml:space="preserve">
    <value>Digiturk</value>
  </data>
  <data name="UserAssignment_DomainDigiturkCC" xml:space="preserve">
    <value>Digiturk CC</value>
  </data>
  <data name="UserAssignment_GroupSelectionLabel" xml:space="preserve">
    <value>Select AD Group</value>
  </data>
  <data name="UserAssignment_GroupSelectionRequired" xml:space="preserve">
    <value>AD Group selection is required.</value>
  </data>
  <data name="UserAssignment_StatusLabel" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="UserAssignment_StatusActive" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="UserAssignment_StatusInactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="UserAssignment_StatusAll" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="UserAssignment_SaveInstruction" xml:space="preserve">
    <value>Click the Save button to assign authorization to the selected user or group.</value>
  </data>
  <data name="UserAssignment_CancelButton" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="UserAssignment_AddButton" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="UserAssignment_UpdateButton" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="UserAssignment_CurrentAuthTitle" xml:space="preserve">
    <value>Current Authorizations</value>
  </data>
  <data name="UserAssignment_SearchPlaceholder" xml:space="preserve">
    <value>Search authorizations...</value>
  </data>
  <data name="UserAssignment_EmptyStateTitle" xml:space="preserve">
    <value>No Authorizations Found</value>
  </data>
  <data name="UserAssignment_EmptyStateText" xml:space="preserve">
    <value>No authorizations have been created for the selected page yet or no results found matching your search criteria.</value>
  </data>
  <data name="UserAssignment_AddNewButton" xml:space="preserve">
    <value>Add New Authorization</value>
  </data>
  <data name="UserAssignment_ActionsColumn" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="UserAssignment_DeleteTooltip" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="UserAssignment_DeleteConfirm" xml:space="preserve">
    <value>return confirm('Are you sure you want to delete this authorization? This action cannot be undone.');</value>
  </data>
  <data name="UserAssignment_AuthorizedColumn" xml:space="preserve">
    <value>Authorized</value>
  </data>
  <data name="UserAssignment_UserType" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="UserAssignment_GroupType" xml:space="preserve">
    <value>AD Group</value>
  </data>
  <data name="UserAssignment_StatusColumn" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="UserAssignment_CreatedDateColumn" xml:space="preserve">
    <value>Created Date</value>
  </data>
  <data name="UserAssignment_ShowPageInfo" xml:space="preserve">
    <value>Show Page Information</value>
  </data>
  <data name="Names_TypeRequired" xml:space="preserve">
    <value>Type selection is required.</value>
  </data>
  <data name="Names_NameRequired" xml:space="preserve">
    <value>Name entry is required.</value>
  </data>
  <data name="Names_Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Names_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Names_Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Names_PagePath" xml:space="preserve">
    <value>Page Path</value>
  </data>
  <data name="Names_Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Names_EnterName" xml:space="preserve">
    <value>Enter name</value>
  </data>
  <data name="Names_EnterDescription" xml:space="preserve">
    <value>Enter description</value>
  </data>
  <data name="Names_PagePathExample" xml:space="preserve">
    <value>e.g.: /AdminPages/DigiportAdmin/Names.aspx</value>
  </data>
  <data name="Names_SearchName" xml:space="preserve">
    <value>Search name</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="NoTypesFound" xml:space="preserve">
    <value>No types found</value>
  </data>
  <data name="Names_StatusAll" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="Names_StatusActive" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="Names_StatusInactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="Names_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Names_Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="Names_Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="Names_List" xml:space="preserve">
    <value>Name List</value>
  </data>
  <data name="Names_RecordCount" xml:space="preserve">
    <value>{0} Records</value>
  </data>
  <data name="Names_Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="Names_ID" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="Names_TypeColumn" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Names_NameColumn" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Names_DescriptionColumn" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Names_NameEnColumn" xml:space="preserve">
    <value>English Name</value>
  </data>
  <data name="Names_DescriptionEnColumn" xml:space="preserve">
    <value>English Description</value>
  </data>
  <data name="Names_NameEn" xml:space="preserve">
    <value>Name (English)</value>
  </data>
  <data name="Names_DescriptionEn" xml:space="preserve">
    <value>Description (English)</value>
  </data>
  <data name="Names_EnterNameEn" xml:space="preserve">
    <value>Enter English name</value>
  </data>
  <data name="Names_EnterDescriptionEn" xml:space="preserve">
    <value>Enter English description</value>
  </data>
  <data name="Names_NameEnRequired" xml:space="preserve">
    <value>English name field cannot be empty.</value>
  </data>
  <data name="Names_DescriptionEnRequired" xml:space="preserve">
    <value>English description field cannot be empty.</value>
  </data>
  <data name="Names_StatusColumn" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Names_CreatedColumn" xml:space="preserve">
    <value>Creation Date</value>
  </data>
  <data name="Names_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Names_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Names_NoRecordsFound" xml:space="preserve">
    <value>No Records Found</value>
  </data>
  <data name="Names_NoRecordsMessage" xml:space="preserve">
    <value>No records matching search criteria were found or no records exist.</value>
  </data>
  <data name="Names_AddNewRecord" xml:space="preserve">
    <value>Add New Record</value>
  </data>
  <data name="Names_PageTitle" xml:space="preserve">
    <value>Digiport Menu Name Management</value>
  </data>
  <data name="Names_PageDescription" xml:space="preserve">
    <value>From this page you can manage names and paths used in Digiport menus. Names determine the visible titles and link points of menu elements.</value>
  </data>
  <data name="Names_FormTitle" xml:space="preserve">
    <value>Name Definition / Editing</value>
  </data>
  <data name="Names_PagePathHint" xml:space="preserve">
    <value>Specify the full path of the page starting with '/' character.</value>
  </data>
  <data name="Names_FormHint" xml:space="preserve">
    <value>After entering the information, use the buttons on the right side of the form to save.</value>
  </data>
  <data name="TotalRecords" xml:space="preserve">
    <value>Total Records: {0}</value>
  </data>
  <data name="TotalRecordsZero" xml:space="preserve">
    <value>Total Records: 0</value>
  </data>
  <data name="SessionNotFound" xml:space="preserve">
    <value>User session not found. Please log in to the system again.</value>
  </data>
  <data name="ConfigurationError" xml:space="preserve">
    <value>Configuration Error</value>
  </data>
  <data name="UserAssignment_SelectPage" xml:space="preserve">
    <value>Please select a page first.</value>
  </data>
  <data name="UserAssignment_SelectUser" xml:space="preserve">
    <value>Please select a user.</value>
  </data>
  <data name="UserAssignment_SelectGroup" xml:space="preserve">
    <value>Please select an AD group.</value>
  </data>
  <data name="UserAssignment_UserDuplicate" xml:space="preserve">
    <value>This user is already authorized for this page!</value>
  </data>
  <data name="UserAssignment_GroupDuplicate" xml:space="preserve">
    <value>This AD group is already authorized for this page!</value>
  </data>
  <data name="UserAssignment_SaveSuccess" xml:space="preserve">
    <value>Authorization saved successfully.</value>
  </data>
  <data name="UserAssignment_UpdateSuccess" xml:space="preserve">
    <value>Authorization updated successfully.</value>
  </data>
  <data name="UserAssignment_DeleteSuccess" xml:space="preserve">
    <value>Authorization deleted successfully.</value>
  </data>
  <data name="UserAssignment_LoadPagesError" xml:space="preserve">
    <value>Error occurred while loading page list.</value>
  </data>
  <data name="UserAssignment_LoadUsersError" xml:space="preserve">
    <value>Error occurred while loading user list.</value>
  </data>
  <data name="UserAssignment_LoadGroupsError" xml:space="preserve">
    <value>Error occurred while loading AD group list.</value>
  </data>
  <data name="UserAssignment_LoadGridError" xml:space="preserve">
    <value>Error occurred while loading authorization list.</value>
  </data>
  <data name="UserAssignment_SaveError" xml:space="preserve">
    <value>Error occurred while saving authorization.</value>
  </data>
  <data name="UserAssignment_UpdateError" xml:space="preserve">
    <value>Error occurred while updating authorization.</value>
  </data>
  <data name="UserAssignment_DeleteError" xml:space="preserve">
    <value>Error occurred while deleting authorization.</value>
  </data>
  <data name="UserAssignment_GetRecordError" xml:space="preserve">
    <value>Error occurred while retrieving authorization information.</value>
  </data>
  <data name="DeletePermissionConfirmation" xml:space="preserve">
    <value>Are you sure you want to delete this permission? This operation cannot be undone.</value>
  </data>
  <data name="Names_SelectType" xml:space="preserve">
    <value>-- Select --</value>
  </data>
  <data name="Names_FilterAll" xml:space="preserve">
    <value>-- All --</value>
  </data>
  <data name="Names_SearchPlaceholder" xml:space="preserve">
    <value>Search name...</value>
  </data>
  <data name="UserAssignment_ErrorLoadingGroups" xml:space="preserve">
    <value>An error occurred while loading AD groups</value>
  </data>
  <data name="UserAssignment_EnableTooltip" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="UserAssignment_DisableTooltip" xml:space="preserve">
    <value>Disable</value>
  </data>
  <data name="UserAssignment_EnableSuccess" xml:space="preserve">
    <value>Record enabled successfully.</value>
  </data>
  <data name="UserAssignment_DisableSuccess" xml:space="preserve">
    <value>Record disabled successfully.</value>
  </data>
  <data name="UserAssignment_ToggleStatusError" xml:space="preserve">
    <value>Error occurred while changing status</value>
  </data>
  <data name="Names_RecordDeletedSuccess" xml:space="preserve">
    <value>Record deleted successfully.</value>
  </data>
  <data name="Names_RecordDeletedError" xml:space="preserve">
    <value>Failed to delete record.</value>
  </data>
  <data name="LinkAdi" xml:space="preserve">
    <value>Link Name</value>
  </data>
  <data name="LinkAdiGirin" xml:space="preserve">
    <value>Please fill link name</value>
  </data>
  <data name="LinkInformation" xml:space="preserve">
    <value>Link Information</value>
  </data>
  <data name="LinkListe" xml:space="preserve">
    <value>Link List</value>
  </data>
  <data name="Notlar" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="YeniLink" xml:space="preserve">
    <value>New Link</value>
  </data>
  <data name="ContentInformation" xml:space="preserve">
    <value>Content Information</value>
  </data>
  <data name="Icerik" xml:space="preserve">
    <value>Content</value>
  </data>
  <data name="IcerikGirin" xml:space="preserve">
    <value>Please fill content field.</value>
  </data>
  <data name="IcerikListe" xml:space="preserve">
    <value>Content List</value>
  </data>
  <data name="YeniIcerik" xml:space="preserve">
    <value>New Content</value>
  </data>
  <data name="AnasayfaSolAltMenu" xml:space="preserve">
    <value>Mainpage Left Bottom Menu</value>
  </data>
  <data name="AnasayfaSolUstMenu" xml:space="preserve">
    <value>Mainpage Left Top Menu</value>
  </data>
  <data name="DigiHrAppSlide" xml:space="preserve">
    <value>Application Slide</value>
  </data>
  <data name="EgitimDuyuru" xml:space="preserve">
    <value>Bulletin</value>
  </data>
  <data name="IKAppInformation" xml:space="preserve">
    <value>DigiHr Application Information</value>
  </data>
  <data name="YuklenilecekDuyuruAppResmiSecin" xml:space="preserve">
    <value>Select Announcement Image To Upload For DigiHR Application</value>
  </data>
  <data name="SlideHrAppDosyaFormatiYanlis" xml:space="preserve">
    <value>File extension selected for the slide of DigiHrApp is incorrect. Allowed formats =&gt;</value>
  </data>
  <data name="HrAppHedefLinkGirin" xml:space="preserve">
    <value>Please fill target link for HR App slide click event</value>
  </data>
  <data name="HrAppSlideBoyutlari" xml:space="preserve">
    <value>Sizes of selected HR app slide file  {0}x{1} but must be {2}x{3}</value>
  </data>
  <data name="HrAppTiklanmaIslemiSecin" xml:space="preserve">
    <value>Please select click event for HR App </value>
  </data>
  <data name="IleriTarihBaslangic" xml:space="preserve">
    <value>Please select today or further date as start date.</value>
  </data>
  <data name="IleriTarihBitis" xml:space="preserve">
    <value>Please select a date further than today as end date.</value>
  </data>
  <data name="HrMediaKategoriIslemleri" xml:space="preserve">
    <value>Announcements Category Management</value>
  </data>
  <data name="Tip" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="TipSecin" xml:space="preserve">
    <value>Please select type</value>
  </data>
  <data name="Diger" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="Names_PagePathRequired" xml:space="preserve">
    <value>Page path entry is required.</value>
  </data>
  <data name="YuklenilecekLogoSecin" xml:space="preserve">
    <value>Select Logo To Upload For DigiHR Application</value>
  </data>
  <data name="HrAppLogoBoyutlari" xml:space="preserve">
    <value>Sizes of selected HR app logo file  {0}x{1} but must be {2}x{3}</value>
  </data>
  <data name="LogoHrAppDosyaFormatiYanlis" xml:space="preserve">
    <value>File extension selected for the logo of DigiHrApp is incorrect. Allowed formats =&gt;</value>
  </data>
  <data name="HrAppEnabled" xml:space="preserve">
    <value>HR App Registry Enabled</value>
  </data>
  <data name="DuyuruBunlarıBiliyormuydunuz" xml:space="preserve">
    <value>"Did You Notice These" Announcements</value>
  </data>
  <data name="DuyuruYardimMasasi" xml:space="preserve">
    <value>Help Desk Announcements</value>
  </data>
  <data name="AutoSlideActive" xml:space="preserve">
    <value>Auto Slide Active</value>
  </data>
</root>