#!/usr/bin/env python3
"""
Fix specific errors in Modal.tsx and Modal.test.tsx
"""

import os
import re

def fix_modal_tsx():
    """Fix the Modal.tsx file"""
    filepath = 'src/components/Modal/Modal.tsx'

    if not os.path.exists(filepath):
        return False

    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()

    # Fix the handleCancel function
    # Replace the broken lines with correct preventDefault and stopPropagation calls
    content = re.sub(
        r'const handleCancel = \(e: React\.MouseEvent\) => \{\s*const e = undefined\.preventDefault\(\)\s*const e = undefined\.stopPropagation\(\)',
        'const handleCancel = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()',
        content,
        flags=re.DOTALL
    )

    # Fix the "return }" pattern to "return"
    content = re.sub(r'return\s*}', 'return', content)

    # Fix the nullish coalescing in handleInternalClose
    content = re.sub(
        r"if \(reason === 'backdropClick' \?\? reason === 'escapeKeyDown'\)",
        "if (reason === 'backdropClick' || reason === 'escapeKeyDown')",
        content
    )

    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"Fixed {filepath}")
    return True

def fix_modal_test():
    """Fix the Modal test file"""
    filepath = 'src/components/Modal/__tests__/Modal.test.tsx'

    if not os.path.exists(filepath):
        return False

    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()

    # Fix any parsing errors (likely the === pattern)
    content = re.sub(r'==\s*=', '===', content)
    content = re.sub(r'!=\s*=', '!==', content)

    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"Fixed {filepath}")
    return True

# Fix both files
fixed_count = 0
if fix_modal_tsx():
    fixed_count += 1
if fix_modal_test():
    fixed_count += 1

print(f"\nTotal files fixed: {fixed_count}")