#!/usr/bin/env python3
"""
Fix remaining parsing errors, particularly in test files.
"""

import os
import re
import subprocess
from typing import List, Dict

def fix_test_file_structure(filepath: str) -> bool:
    """Fix test file structure issues."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix malformed expect().toHaveBeenCalledWith({) pattern
        pattern = r'expect\(([^)]+)\)\.toHaveBeenCalledWith\(\{\)'
        def fix_expect_call(match):
            return f'expect({match.group(1)}).toHaveBeenCalledWith({{'
        content = re.sub(pattern, fix_expect_call, content)
        
        # Fix rerender() calls without parentheses
        content = re.sub(r'rerender\(\)\s*\n\s*<', r'rerender(\n        <', content)
        
        # Remove excessive closing braces/tags at end of file
        lines = content.split('\n')
        
        # Find excessive closing patterns at the end
        closing_pattern = re.compile(r'^(\s*\})+\)|(\s*\})+$|^\s*</\w+>$')
        
        # Count from the end to find where excessive closings start
        excessive_start = len(lines)
        consecutive_closings = 0
        
        for i in range(len(lines) - 1, -1, -1):
            line = lines[i].strip()
            if closing_pattern.match(line) or line == '}' or line.startswith('})'):
                consecutive_closings += 1
                if consecutive_closings > 20:  # More than 20 consecutive closing lines is suspicious
                    excessive_start = i
            else:
                if consecutive_closings > 20:
                    break
                consecutive_closings = 0
        
        if excessive_start < len(lines) - 5:
            # Remove excessive closings
            lines = lines[:excessive_start]
            
            # Count open/close braces to add proper closings
            open_braces = 0
            open_parens = 0
            
            for line in lines:
                open_braces += line.count('{') - line.count('}')
                open_parens += line.count('(') - line.count(')')
            
            # Add proper closings
            if open_braces > 0 or open_parens > 0:
                closing_lines = []
                
                # Add closing braces/parens based on the structure
                while open_braces > 0 or open_parens > 0:
                    if open_parens > 0 and open_braces > 0:
                        closing_lines.append('  })' * min(3, open_braces))
                        open_braces -= min(3, open_braces)
                        open_parens -= min(3, open_parens)
                    elif open_braces > 0:
                        closing_lines.append('}')
                        open_braces -= 1
                    elif open_parens > 0:
                        closing_lines.append(')')
                        open_parens -= 1
                
                lines.extend(closing_lines)
            
            content = '\n'.join(lines)
        
        # Write back if changed
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
    except Exception as e:
        print(f"Error fixing {filepath}: {e}")
    
    return False

def fix_object_syntax_errors(filepath: str) -> bool:
    """Fix object syntax errors like missing commas."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        changed = False
        
        for i in range(len(lines) - 1):
            line = lines[i].strip()
            next_line = lines[i + 1].strip() if i + 1 < len(lines) else ''
            
            # Check for missing comma in object properties
            if ':' in line and not line.endswith(',') and not line.endswith('{') and not line.endswith('['):
                if next_line and (':' in next_line or next_line.startswith('}') or next_line.startswith(']')):
                    lines[i] = lines[i].rstrip() + ',\n'
                    changed = True
        
        if changed:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            return True
        
    except Exception as e:
        print(f"Error fixing {filepath}: {e}")
    
    return False

def get_parsing_error_files() -> List[str]:
    """Get files with parsing errors from ESLint."""
    files = []
    
    cmd = ["yarn", "lint", "--format", "json"]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.stdout:
        try:
            import json
            results = json.loads(result.stdout)
            for file_result in results:
                if file_result.get('messages'):
                    for msg in file_result['messages']:
                        if 'Parsing error' in msg.get('message', ''):
                            filepath = file_result['filePath']
                            if filepath not in files:
                                files.append(filepath)
        except:
            pass
    
    return files

def main():
    print("🔍 Finding files with parsing errors...")
    
    error_files = get_parsing_error_files()
    
    print(f"Found {len(error_files)} files with parsing errors")
    
    fixed_count = 0
    
    for filepath in error_files:
        if os.path.exists(filepath):
            print(f"  Checking {filepath}...")
            
            fixed = False
            
            # Apply different fixes based on file type
            if '.test.ts' in filepath or '.test.tsx' in filepath:
                if fix_test_file_structure(filepath):
                    print(f"    ✅ Fixed test file structure")
                    fixed = True
            
            if fix_object_syntax_errors(filepath):
                print(f"    ✅ Fixed object syntax errors")
                fixed = True
            
            if fixed:
                fixed_count += 1
    
    print(f"\n✨ Fixed {fixed_count} files")
    
    # Run linter again to check remaining errors
    print("\n🔍 Checking remaining errors...")
    subprocess.run(["yarn", "lint"])

if __name__ == "__main__":
    main()