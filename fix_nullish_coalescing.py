#!/usr/bin/env python3

import subprocess
import re

def get_nullish_errors():
    """Get all nullish coalescing errors from lint"""
    try:
        result = subprocess.run(['yarn', 'lint'], capture_output=True, text=True, cwd='.')
        output = result.stdout + result.stderr
        
        errors = []
        current_file = None
        
        for line in output.split('\n'):
            # Match file paths
            if line.strip() and not line.startswith(' ') and ('.tsx' in line or '.ts' in line):
                current_file = line.strip()
            # Match nullish coalescing errors
            elif 'prefer-nullish-coalescing' in line and 'error' in line:
                match = re.match(r'\s*(\d+):(\d+)\s+error\s+.*prefer-nullish-coalescing', line)
                if match and current_file:
                    line_num = int(match.group(1))
                    col_num = int(match.group(2))
                    errors.append((current_file, line_num, col_num, line))
        
        return errors
    except Exception as e:
        print(f"Error getting lint output: {e}")
        return []

def fix_nullish_coalescing(file_path, line_num, error_text):
    """Fix nullish coalescing by replacing || with ??"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if line_num > len(lines):
            print(f"Line {line_num} out of range in {file_path}")
            return False
        
        target_line = lines[line_num - 1]
        
        # Fix different patterns
        if '??=' in error_text:
            # Replace assignment pattern: var = var || value  with  var ??= value
            if ' = ' in target_line and ' || ' in target_line:
                # Pattern: variable = variable || value
                parts = target_line.split(' = ')
                if len(parts) >= 2:
                    var_name = parts[0].strip()
                    right_side = ' = '.join(parts[1:])
                    
                    if f'{var_name} || ' in right_side:
                        new_right = right_side.replace(f'{var_name} || ', '').strip()
                        lines[line_num - 1] = f'{var_name} ??= {new_right}'
                    else:
                        lines[line_num - 1] = target_line.replace(' || ', ' ?? ')
                else:
                    lines[line_num - 1] = target_line.replace(' || ', ' ?? ')
            else:
                lines[line_num - 1] = target_line.replace(' || ', ' ?? ')
        else:
            # Simple || to ?? replacement
            lines[line_num - 1] = target_line.replace(' || ', ' ?? ')
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"Fixed nullish coalescing in {file_path}:{line_num}")
        return True
        
    except Exception as e:
        print(f"Error fixing {file_path}:{line_num} - {e}")
        return False

def main():
    print("🔍 Finding nullish coalescing errors...")
    errors = get_nullish_errors()
    
    if not errors:
        print("✅ No nullish coalescing errors found!")
        return
    
    print(f"Found {len(errors)} nullish coalescing errors")
    
    # Sort by line number in reverse order to avoid line shifts
    errors.sort(key=lambda x: x[1], reverse=True)
    
    # Group by file for better processing
    files_to_fix = {}
    for file_path, line_num, col_num, error_text in errors:
        if file_path not in files_to_fix:
            files_to_fix[file_path] = []
        files_to_fix[file_path].append((line_num, col_num, error_text))
    
    print(f"🔧 Fixing {len(errors)} nullish coalescing issues across {len(files_to_fix)} files...")
    
    fixed_count = 0
    for file_path, file_errors in files_to_fix.items():
        # Sort by line number in reverse order to avoid line number shifts
        file_errors.sort(key=lambda x: x[0], reverse=True)
        
        for line_num, col_num, error_text in file_errors:
            if fix_nullish_coalescing(file_path, line_num, error_text):
                fixed_count += 1
    
    print(f"✅ Fixed {fixed_count} nullish coalescing issues")
    
    # Test the fixes
    print("\n🧪 Testing fixes...")
    result = subprocess.run(['yarn', 'lint', '--max-warnings=1000'], capture_output=True, text=True)
    
    nullish_errors = len([line for line in result.stdout.split('\n') if 'prefer-nullish-coalescing' in line and 'error' in line])
    print(f"⚠️  {nullish_errors} nullish coalescing errors remain")
    
    if nullish_errors == 0:
        print("🎉 All nullish coalescing errors fixed!")

if __name__ == '__main__':
    main()