﻿using CoreHelpers;
using Digiturk.Workflow.Digiflow.WebCore.Digiport;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Digiport_Linkler : ComponentBasePage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        string viewMode = string.Empty;
        int pageSizeGrid = 0, pageSizeRepeater = 0;
        if (!string.IsNullOrEmpty(Request.QueryString["view-mode"]))
            viewMode = Request.QueryString["view-mode"];
        if (!string.IsNullOrEmpty(Request.QueryString["page-size-grid"]))
            pageSizeGrid = ConvertionHelper.ConvertValue<int>(Request.QueryString["page-size-grid"]);
        if (!string.IsNullOrEmpty(Request.QueryString["page-size-list"]))
            pageSizeRepeater = ConvertionHelper.ConvertValue<int>(Request.QueryString["page-size-list"]);

        lblLinkler.Text = componentTitle;
        hyperLinkler.Text = componentTitle;

        if (viewMode.ToLower() == "grid")
            GridYukle(pageSizeGrid);
        else if (viewMode.ToLower() == "list")
            RepeaterYukle(pageSizeGrid, pageSizeRepeater);
    }

    private void GridYukle(int pageSize)
    {
        DataTable dt = DigiportMenuDisplayHelpers.LinklerHelper.LinklerTabloGetir(-1);
        divContainer_Grid.Visible = true;
        divContainer_Repeater.Visible = false;
        gridViewLinkler.DataSource = dt;
        gridViewLinkler.DataBind();
        gridViewLinkler.SettingsPager.PageSize = pageSize;
        lblLinkler.Visible = true;
        hyperLinkler.Visible = false;
        gridViewLinkler.Visible = dt.Rows.Count > 0;
    }
    private void RepeaterYukle(int pageSizeGrid, int pageSizeRepeater)
    {
        divContainer_Grid.Visible = false;
        divContainer_Repeater.Visible = true;
        repeaterLinkler.DataSource = DigiportMenuDisplayHelpers.LinklerHelper.LinklerTabloGetir(pageSizeRepeater);
        repeaterLinkler.DataBind();
        lblLinkler.Visible = false;
        hyperLinkler.Visible = true;
        hyperLinkler.NavigateUrl = ConfigurationManager.AppSettings["DigiportContentLink"] + "?component-type=" + componentType.ToString() + "&component-title="+componentTitle+"&component-type-string=Links&view-mode=grid&page-size-grid=" + pageSizeGrid.ToString();
    }

    public string SatirKlas(int componentClickAction)
    {
        string donen = "clamp";
        if (((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Olay_Yok) != componentClickAction)
            donen = donen + " underline_cursor_color1";
        return donen;
    }
    public string LiKlas(int componentClickAction)
    {
        string donen = "";
        if (((int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Olay_Yok) != componentClickAction)
            donen = donen + " underline_cursor_color1";
        return donen;
    }
    public string LinkClickEvent(string contentId, int componentClickAction, string contentTargetLink, string popupWindowWidth, string popupWindowHeight)
    {
        return componentBase.GetComponentClickEvent("Links", componentClickAction, contentId, contentTargetLink, popupWindowWidth, popupWindowHeight, ConfigurationManager.AppSettings["DigiportContentLink"], ConfigurationManager.AppSettings["AjansContentLink"]);
    }
}