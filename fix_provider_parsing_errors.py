#!/usr/bin/env python3
"""
Fix parsing errors in provider files
"""

import os
import re

def fix_parsing_errors(content):
    """Fix common parsing errors"""

    # Fix the === pattern (should be ===)
    content = re.sub(r'==\s*=', '===', content)

    # Fix the != = pattern (should be !==)
    content = re.sub(r'!=\s*=', '!==', content)

    # Fix the <= = pattern (should be <=)
    content = re.sub(r'<=\s*=(?!=)', '<=', content)

    # Fix the >= = pattern (should be >=)
    content = re.sub(r'>=\s*=(?!=)', '>=', content)

    return content

def process_file(filepath):
    """Process a single file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content
        content = fix_parsing_errors(content)

        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed {filepath}")
            return True
        return False
    except Exception as e:
        print(f"Error processing {filepath}: {e}")
        return False

# Files with parsing errors
target_files = [
    'src/app/providers/ErrorBoundaryProvider.tsx',
    'src/app/providers/FeatureFlagProvider.tsx',
    'src/app/providers/QueryProvider.tsx',
    'src/app/providers/ThemeProvider.tsx',
    'src/app/providers/index.tsx',
    'src/components/LazyScreenWrapper/LazyScreenWrapper.tsx',
    'src/components/Loading/__tests__/Loading.test.tsx',
    'src/components/Modal/Modal.tsx',
    'src/components/Modal/__tests__/Modal.test.tsx',
]

fixed_count = 0
for filepath in target_files:
    if os.path.exists(filepath) and process_file(filepath):
        fixed_count += 1

print(f"\nTotal files fixed: {fixed_count}")