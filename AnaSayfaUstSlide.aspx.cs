﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Digiport_AnaSayfaUstSlide : Digiturk.Workflow.Digiflow.WebCore.Digiport.SliderBasePage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        divSliderContainer.Visible = false;

        try
        {
            DataTable dt = DigiportMenuDisplayHelpers.AnaSayfaUstSlideHelper.GetSlidesTable(slideType);
            if (dt.Rows.Count > 0)
            {
                List<DigiportMenuDisplayHelpers.SlideImageInfo> listSlides = new List<DigiportMenuDisplayHelpers.SlideImageInfo>();

                foreach (DataRow row in dt.Rows)
                {
                    string slideImagePath = domain + row["SLIDE_IMAGE_PATH"].ToString();
                    string thumbnailPath = domain + row["THUMBNAIL_IMAGE_PATH"].ToString();
                    string headline = row["SLIDE_TARGET_HEADLINE"].ToString() == string.Empty ? row["SLIDE_NAME"].ToString() : row["SLIDE_TARGET_HEADLINE"].ToString();
                    string title = headline;
                    string slideId = row["ID"].ToString();
                    int slideClickAction = Convert.ToInt32(row["SLIDE_CLICK_ACTION"].ToString());
                    string slideTargetLink = row["SLIDE_TARGET_LINK"].ToString();
                    string popupWindowWidth = row["SLIDE_POPUP_WIDTH"].ToString();
                    string popupWindowHeight = row["SLIDE_POPUP_HEIGHT"].ToString();
                    listSlides.Add(new DigiportMenuDisplayHelpers.SlideImageInfo()
                    {
                        imagePath = slideImagePath,
                        onclickFunc = componentBase.GetComponentClickEvent("AnaSayfaUstSlide", slideClickAction, slideId, slideTargetLink, popupWindowWidth, popupWindowHeight, ConfigurationManager.AppSettings["DigiportContentLink"], ConfigurationManager.AppSettings["AjansContentLink"]),
                        thumbnailPath = thumbnailPath,
                        title = title,
                        headline = headline
                    });
                }
                divSliderContainer.InnerHtml = new DigiportMenuDisplayHelpers.SlideHtmlHelper().GetSlidesHtml(new DigiportMenuDisplayHelpers.SlideOptions()
                {
                    slideWidth = slideWidth,
                    slideHeight = slideHeight,
                    autoSlideInterval = autoSlideInterval,
                    transitionDuration = transitionDuration,
                    effectType = componentBase.GetEffectType(effectType),
                    transitionEasing = componentBase.GetTransitionEasing(transitionEasing),
                    titleMode = componentBase.GetTitleMode(titleMode),
                    showProgressBar = showProgressBar,
                    showThumbnails = showThumbnails,
                    thumbHeight = thumbHeight,
                    thumbWidth = thumbWidth
                }, listSlides);
                divSliderContainer.Visible = true;
            }
        }
        catch
        {
            divSliderContainer.Visible = false;
        }
    }
}